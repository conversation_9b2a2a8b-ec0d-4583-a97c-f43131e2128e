{"dashboard": {"id": null, "title": "Hulu Automation - Playwright Dashboard", "description": "Quick-Wins优化版本 - Hulu自动化系统性能监控面板", "tags": ["hulu", "automation", "playwright", "performance"], "style": "dark", "timezone": "browser", "refresh": "30s", "schemaVersion": 30, "version": 1, "time": {"from": "now-1h", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "templating": {"list": [{"name": "instance", "type": "query", "datasource": "Prometheus", "query": "label_values(hulu_tasks_total, instance)", "refresh": 1, "includeAll": true, "allValue": null, "multi": true, "options": [], "current": {"selected": true, "text": "All", "value": "$__all"}}]}, "panels": [{"id": 1, "title": "系统概览", "type": "stat", "targets": [{"expr": "hulu_system_health", "legendFormat": "系统健康状态", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"0": {"text": "健康", "color": "green"}, "1": {"text": "降级", "color": "yellow"}, "2": {"text": "不健康", "color": "red"}}, "type": "value"}], "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 1}, {"color": "red", "value": 2}]}}}, "gridPos": {"h": 4, "w": 6, "x": 0, "y": 0}}, {"id": 2, "title": "当前活跃任务", "type": "stat", "targets": [{"expr": "hulu_active_tasks", "legendFormat": "活跃任务数", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "short"}}, "gridPos": {"h": 4, "w": 6, "x": 6, "y": 0}}, {"id": 3, "title": "任务成功率 (最近1小时)", "type": "stat", "targets": [{"expr": "rate(hulu_tasks_total{status=\"success\"}[1h]) / rate(hulu_tasks_total[1h]) * 100", "legendFormat": "成功率 %", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "unit": "percent", "min": 0, "max": 100, "thresholds": {"steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 80}, {"color": "green", "value": 95}]}}}, "gridPos": {"h": 4, "w": 6, "x": 12, "y": 0}}, {"id": 4, "title": "最后成功任务", "type": "stat", "targets": [{"expr": "time() - hulu_last_success_timestamp_seconds", "legendFormat": "秒前", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "unit": "s", "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 300}, {"color": "red", "value": 600}]}}}, "gridPos": {"h": 4, "w": 6, "x": 18, "y": 0}}, {"id": 5, "title": "任务执行趋势", "type": "timeseries", "targets": [{"expr": "rate(hulu_tasks_total{status=\"success\"}[5m])", "legendFormat": "成功任务/秒", "refId": "A"}, {"expr": "rate(hulu_tasks_total{status=\"failure\"}[5m])", "legendFormat": "失败任务/秒", "refId": "B"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "reqps", "custom": {"drawStyle": "line", "lineInterpolation": "linear", "barAlignment": 0, "lineWidth": 1, "fillOpacity": 10, "gradientMode": "none", "spanNulls": false, "insertNulls": false, "showPoints": "never", "pointSize": 5, "stacking": {"mode": "none", "group": "A"}, "axisPlacement": "auto", "axisLabel": "", "scaleDistribution": {"type": "linear"}, "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "thresholdsStyle": {"mode": "off"}}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "失败任务/秒"}, "properties": [{"id": "color", "value": {"mode": "fixed", "fixedColor": "red"}}]}]}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 4}}, {"id": 6, "title": "任务执行时间分布", "type": "timeseries", "targets": [{"expr": "histogram_quantile(0.95, rate(hulu_task_duration_seconds_bucket[5m]))", "legendFormat": "95th percentile", "refId": "A"}, {"expr": "histogram_quantile(0.50, rate(hulu_task_duration_seconds_bucket[5m]))", "legendFormat": "50th percentile (median)", "refId": "B"}, {"expr": "rate(hulu_task_duration_seconds_sum[5m]) / rate(hulu_task_duration_seconds_count[5m])", "legendFormat": "平均时间", "refId": "C"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "s"}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 4}}, {"id": 7, "title": "选择器失败热力图", "type": "heatmap", "targets": [{"expr": "increase(hulu_selector_failures_total[5m])", "legendFormat": "{{phase}} - {{selector_name}}", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-spectrum"}}}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 12}}, {"id": 8, "title": "选择器性能分析", "type": "timeseries", "targets": [{"expr": "histogram_quantile(0.95, rate(hulu_selector_duration_seconds_bucket[5m]))", "legendFormat": "选择器查找时间 - 95th percentile", "refId": "A"}, {"expr": "rate(hulu_selector_failures_total[5m])", "legendFormat": "选择器失败率", "refId": "B"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 20}}, {"id": 9, "title": "reCAPTCHA分数分布", "type": "timeseries", "targets": [{"expr": "histogram_quantile(0.90, rate(hulu_recaptcha_score_bucket[5m]))", "legendFormat": "90th percentile score", "refId": "A"}, {"expr": "histogram_quantile(0.50, rate(hulu_recaptcha_score_bucket[5m]))", "legendFormat": "median score", "refId": "B"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "short", "min": 0, "max": 1}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 20}}, {"id": 10, "title": "Layer 0性能优化监控", "type": "timeseries", "targets": [{"expr": "histogram_quantile(0.95, rate(hulu_iframe_detection_duration_seconds_bucket[5m]))", "legendFormat": "iframe检测 - 95th percentile", "refId": "A"}, {"expr": "histogram_quantile(0.95, rate(hulu_host_discovery_duration_seconds_bucket[5m]))", "legendFormat": "宿主发现 - 95th percentile", "refId": "B"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "s"}}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 28}}, {"id": 11, "title": "各阶段任务分布", "type": "piechart", "targets": [{"expr": "sum by (phase) (increase(hulu_tasks_total[1h]))", "legendFormat": "{{phase}}", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "vis": false}}}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 36}}, {"id": 12, "title": "reCAPTCHA处理统计", "type": "piechart", "targets": [{"expr": "sum by (result) (increase(hulu_recaptcha_processed_total[1h]))", "legendFormat": "{{result}}", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 36}}], "annotations": {"list": [{"name": "部署事件", "datasource": "Prometheus", "enable": true, "expr": "changes(hulu_app_info[1h])", "iconColor": "rgba(0, 211, 255, 1)", "titleFormat": "应用部署", "textFormat": "Version: {{version}}"}]}}, "meta": {"type": "db", "canSave": true, "canEdit": true, "canAdmin": true, "canStar": true, "slug": "hulu-automation-playwright-dashboard", "url": "/d/hulu-automation/hulu-automation-playwright-dashboard", "expires": "0001-01-01T00:00:00Z", "created": "2025-07-28T23:00:00Z", "updated": "2025-07-28T23:00:00Z", "updatedBy": "admin", "createdBy": "admin", "version": 1, "hasAcl": false, "isFolder": false, "folderId": 0, "folderTitle": "General", "folderUrl": "", "provisioned": false, "provisionedExternalId": ""}}