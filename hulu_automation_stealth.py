#!/usr/bin/env python3
"""
Hulu登录自动化 - 最大隐身模式入口点
基于4层架构 + MediaCrawler反检测技术的企业级解决方案

特性:
- 真实的45-50 WPM打字速度
- 贝塞尔曲线鼠标轨迹
- MediaCrawler stealth.min.js集成
- 完整的指纹伪装系统
- 智能错误修正模拟
- CDP模式真实浏览器控制

解决的问题:
- "邮箱瞬间填充后瞬间点击"的超人行为
- 120ms打字延迟 = 500+ WPM的机器人特征
- 缺少自然的行为模拟
"""

import sys
import asyncio
import logging
import platform
import subprocess
import random
import time
import os
import re
import tempfile
from typing import Dict, Any, Optional, Tuple, List
from playwright.async_api import async_playwright, Page, Browser, Playwright, BrowserContext

# 隐蔽性功能已整合到主文件中，不再需要外部优化器

# 导入TempMailAPI和状态管理
try:
    from utils import TempMailAPI
    TEMPMAIL_AVAILABLE = True
    print("✅ 导入原始TempMailAPI成功")
except ImportError as e:
    print(f"⚠️ TempMailAPI导入失败: {e}")
    TEMPMAIL_AVAILABLE = False
    TempMailAPI = None

try:
    from hulu_automation.data.state_manager import StateManager
    STATE_MANAGER_AVAILABLE = True
except ImportError:
    STATE_MANAGER_AVAILABLE = False
    print("⚠️ StateManager未安装，登录持久化功能将不可用")

# 导入反检测配置
from infrastructure.anti_detection.config import AntiDetectionPresets

# 导入人类行为模拟器
try:
    from hulu_automation.infrastructure.human_behavior import HumanBehaviorSimulator
    HUMAN_BEHAVIOR_AVAILABLE = True
    # print("✅ 导入HumanBehaviorSimulator成功")  # 减少启动日志
except ImportError as e:
    # print(f"⚠️ HumanBehaviorSimulator导入失败: {e}")  # 移除混乱的警告
    HUMAN_BEHAVIOR_AVAILABLE = False
    HumanBehaviorSimulator = None

# 简化后的按钮处理 - 移除过度设计的四层架构
# 原四层架构已简化为直接的按钮查找和点击逻辑
from infrastructure.dynamic_host_detector import (
    DynamicHostDetector,
    HostDetectorConfig,
    LoginContext
)
from infrastructure.smart_interaction_controller import SmartInteractionController
from infrastructure.intelligent_waiting_manager import IntelligentWaitingManager

# 智能等待上下文常量定义
LOGIN_STATUS_CHECK_CONTEXT = "form_submission"  # 登录状态检查使用表单提交等待
PAGE_OBSERVATION_CONTEXT = "dynamic_content"    # 页面观察使用动态内容等待
BUTTON_SEARCH_CONTEXT = "page_navigation"       # 按钮搜索使用页面导航等待

# 导入工具函数
try:
    # 直接从utils.py文件导入
    import sys
    import os
    sys.path.insert(0, os.path.dirname(__file__))
    from utils import CONFIG
except ImportError:
    # 备用：定义一个最小的CONFIG
    CONFIG = {
        'API_BASE': 'https://testkuroneko.xyz/api',
        'VERIFICATION_TIMEOUT': 300,
        'VERIFICATION_POLL_INTERVAL': 5
    }

# 简化行为模拟器导入 - 移除重复的导入和别名
import logging

# 导入新的反检测系统
try:
    from anti_detection import AntiDetectionManager
    ANTI_DETECTION_AVAILABLE = True
except ImportError:
    ANTI_DETECTION_AVAILABLE = False
    AntiDetectionManager = None
    print("⚠️ 新的反检测系统未安装，将使用传统模式")
import os
import sys

def setup_logging(log_level=logging.INFO):
    """
    统一配置日志记录，确保每次运行覆盖日志文件。
    """
    log_dir = "logs"
    log_file = os.path.join(log_dir, "hulu_creator.log")
    os.makedirs(log_dir, exist_ok=True)

    root_logger = logging.getLogger()
    root_logger.setLevel(log_level)

    # 移除所有现有的处理器
    if root_logger.hasHandlers():
        root_logger.handlers.clear()

    # 创建文件处理器，模式为 'w' (写入，覆盖)
    file_handler = logging.FileHandler(log_file, mode='w', encoding='utf-8')
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(formatter)
    root_logger.addHandler(file_handler)

    # 创建控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(formatter)
    root_logger.addHandler(console_handler)

    return logging.getLogger(__name__)


class CDPChromeManager:
    """CDP Chrome管理器 - 负责Chrome浏览器的启动和连接，支持登录持久化"""

    def __init__(self, debug_port: int = 9222, persistent_session: bool = True):
        self.debug_port = debug_port
        self.persistent_session = persistent_session
        self.chrome_process: Optional[subprocess.Popen] = None
        self.logger = logging.getLogger(__name__)

        # 持久化用户数据目录
        if persistent_session:
            self.user_data_dir = os.path.join(os.getcwd(), "hulu_session_data")
            os.makedirs(self.user_data_dir, exist_ok=True)
            self.logger.info(f"📁 使用持久化用户数据目录: {self.user_data_dir}")
        else:
            self.user_data_dir = None

    def get_chrome_command(self, user_data_dir: Optional[str] = None) -> list:
        """获取当前系统的正确Chrome启动命令"""
        system = platform.system()

        if system == "Darwin":  # macOS
            # 直接使用Chrome可执行文件路径（更可靠）
            chrome_path = "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"
            args = [
                chrome_path,
                f"--remote-debugging-port={self.debug_port}",
                "--remote-debugging-address=127.0.0.1",
                "--no-first-run",
                "--no-default-browser-check",
                "--disable-background-timer-throttling",
                "--disable-backgrounding-occluded-windows",
                "--disable-renderer-backgrounding",
                "--disable-features=TranslateUI",
                "--disable-ipc-flooding-protection",
                "--disable-hang-monitor",
                "--disable-prompt-on-repost",
                "--disable-sync",
                "--disable-web-security",
                "--disable-features=VizDisplayCompositor",
                "--disable-dev-shm-usage",
                "--no-sandbox",
                "--disable-extensions",
                "--disable-plugins"
            ]
        elif system == "Windows":
            chrome_path = "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe"
            args = [
                chrome_path,
                f"--remote-debugging-port={self.debug_port}",
                "--remote-debugging-address=127.0.0.1",
                "--no-first-run",
                "--no-default-browser-check",
                "--disable-background-timer-throttling",
                "--disable-backgrounding-occluded-windows",
                "--disable-renderer-backgrounding",
                "--disable-features=TranslateUI",
                "--disable-ipc-flooding-protection",
                "--disable-hang-monitor",
                "--disable-prompt-on-repost",
                "--disable-sync",
                "--disable-web-security",
                "--disable-features=VizDisplayCompositor",
                "--disable-dev-shm-usage",
                "--no-sandbox",
                "--disable-extensions",
                "--disable-plugins"
            ]
        elif system == "Linux":
            chrome_path = "/usr/bin/google-chrome"
            args = [
                chrome_path,
                f"--remote-debugging-port={self.debug_port}",
                "--remote-debugging-address=127.0.0.1",
                "--no-first-run",
                "--no-default-browser-check",
                "--disable-background-timer-throttling",
                "--disable-backgrounding-occluded-windows",
                "--disable-renderer-backgrounding",
                "--disable-features=TranslateUI",
                "--disable-ipc-flooding-protection",
                "--disable-hang-monitor",
                "--disable-prompt-on-repost",
                "--disable-sync",
                "--disable-web-security",
                "--disable-features=VizDisplayCompositor",
                "--disable-dev-shm-usage",
                "--no-sandbox",
                "--disable-extensions",
                "--disable-plugins"
            ]
        else:
            raise RuntimeError(f"不支持的操作系统: {system}")

        # 添加用户数据目录
        if user_data_dir:
            args.append(f"--user-data-dir={user_data_dir}")

        return args

    def auto_launch_chrome(self, user_data_dir: Optional[str] = None) -> subprocess.Popen:
        """自动启动Chrome浏览器并返回进程对象"""
        try:
            # 优先使用持久化目录，如果没有指定则使用临时目录
            if not user_data_dir:
                if self.persistent_session and self.user_data_dir:
                    user_data_dir = self.user_data_dir
                    self.logger.info(f"📁 使用持久化用户数据目录: {user_data_dir}")
                else:
                    import tempfile
                    user_data_dir = tempfile.mkdtemp(prefix="chrome-debug-")
                    self.logger.info(f"📁 使用临时用户数据目录: {user_data_dir}")

            cmd = self.get_chrome_command(user_data_dir)
            if platform.system() == "Darwin":
                self.logger.info(f"🚀 启动Chrome: /Applications/Google Chrome.app/Contents/MacOS/Google Chrome --remote-debugging-port={self.debug_port}")
            else:
                self.logger.info(f"🚀 启动Chrome: {' '.join(cmd[:3])}...")

            # 启动Chrome进程
            self.chrome_process = subprocess.Popen(
                cmd,
                stdout=subprocess.DEVNULL,
                stderr=subprocess.DEVNULL
            )

            self.logger.info(f"✅ Chrome已启动，PID: {self.chrome_process.pid}")
            return self.chrome_process

        except Exception as e:
            self.logger.error(f"❌ 启动Chrome失败: {e}")
            raise

    async def connect_to_browser(self, playwright: Playwright, cdp_url: str = None) -> Browser:
        """连接到CDP浏览器"""
        if not cdp_url:
            # 明确使用IPv4地址避免IPv6问题
            cdp_url = f"http://127.0.0.1:{self.debug_port}"

        self.logger.info(f"🔗 尝试连接CDP端点: {cdp_url}")

        try:
            browser = await playwright.chromium.connect_over_cdp(cdp_url)
            self.logger.info("✅ 成功连接到Chrome浏览器")
            return browser
        except Exception as e:
            self.logger.error(f"❌ 连接CDP失败: {e}")
            raise ConnectionError(f"无法连接到浏览器: {e}")

    def cleanup(self):
        """清理Chrome进程"""
        if self.chrome_process:
            try:
                self.chrome_process.terminate()
                self.chrome_process.wait(timeout=5)
                self.logger.info("✅ Chrome进程已清理")
            except Exception as e:
                self.logger.warning(f"⚠️ 清理Chrome进程时出错: {e}")


class StealthAntiDetectionService:
    """集成现有反检测功能的CDP服务"""

    def __init__(self, playwright: Playwright):
        self.playwright = playwright
        self.logger = logging.getLogger(__name__)
        self.stealth_script = self._load_stealth_script()

    def _load_stealth_script(self) -> str:
        """加载stealth脚本"""
        try:
            stealth_path = '/Users/<USER>/Desktop/workflow/Account Registrar/infrastructure/anti_detection/stealth/stealth.min.js'
            with open(stealth_path, 'r', encoding='utf-8') as f:
                script = f.read()
            self.logger.info("✅ 成功加载stealth脚本")
            return script
        except Exception as e:
            self.logger.error(f"❌ 加载stealth脚本失败: {e}")
            return ""

    async def create_stealth_page(self, context: BrowserContext) -> Page:
        """创建注入反检测的页面"""
        page = await context.new_page()

        # 注入stealth脚本
        if self.stealth_script:
            await page.add_init_script(self.stealth_script)
            self.logger.info("✅ 反检测脚本已注入")

        return page


# 内部HumanBehaviorSimulator类已移除，使用外部模块
# 从 hulu_automation/infrastructure/human_behavior.py 导入


class HuluStealthAutomation:
    """
    Hulu隐身自动化系统 - CDP模式
    基于CDP + MediaCrawler反检测技术的企业级解决方案
    """

    def __init__(self, debug_port: int = 9222, auto_launch: bool = True, persistent_session: bool = True, account_index: int = 0, account_email: str = None):
        """
        初始化隐身自动化系统

        Args:
            debug_port: CDP调试端口
            auto_launch: 是否自动启动Chrome
            persistent_session: 是否启用登录持久化
            account_index: 使用的账户索引（从0开始）
            account_email: 指定使用的账户邮箱（优先级高于account_index）
        """
        self.logger = setup_logging()
        self.config = AntiDetectionPresets.maximum_stealth()
        
        # 创建截图目录
        self.screenshots_dir = "screenshots"
        os.makedirs(self.screenshots_dir, exist_ok=True)

        # 持久化配置
        self.persistent_session = persistent_session
        self.storage_state_path = "hulu_login_state.json"

        # CDP相关组件
        self.cdp_manager = CDPChromeManager(debug_port, persistent_session=persistent_session)
        self.auto_launch = auto_launch
        self.playwright: Optional[Playwright] = None
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None
        self.page: Optional[Page] = None

        # 服务组件
        self.anti_detection_service: Optional[StealthAntiDetectionService] = None
        # 简化的行为模拟器初始化 - 统一使用单一实现
        if HUMAN_BEHAVIOR_AVAILABLE:
            self.human_behavior_simulator = HumanBehaviorSimulator()
            self.behavior_simulator = self.human_behavior_simulator  # 向后兼容
            self.logger.info("✅ 使用专业人类行为模拟器（45-50 WPM + QWERTY键盘感知）")
        else:
            # 简化的后备 - 移除过度设计的内嵌类，依赖反检测系统的行为模拟
            self.human_behavior_simulator = None
            self.behavior_simulator = None
            self.logger.info("⚠️ 外部行为模拟器不可用，依赖反检测系统的行为模拟")
        
        # 新的反检测管理器
        if ANTI_DETECTION_AVAILABLE:
            self.anti_detection_manager = AntiDetectionManager(
                enable_analytics=True,
                enable_feature_flags=False  # 暂时禁用特性开关，直接使用
            )
            self.logger.info("✅ 增强反检测系统已加载")
        else:
            self.anti_detection_manager = None
            self.logger.info("⚠️ 使用传统反检测模式")
        
        # 智能等待管理器 (将在页面创建后初始化)
        self.intelligent_waiting_manager: Optional[IntelligentWaitingManager] = None
        
        # 移除了未使用的人类行为并行搜索管理器 - 从未被初始化的死代码
        
        # 高级缓存系统 (将在页面创建后初始化)
        self.caching_system: Optional[AdvancedCachingSystem] = None
        
        # 缓存配置
        self.cache_config = {
            "max_element_cache": 2000,      # 增加缓存容量用于按钮搜索优化
            "max_page_states": 150,         # 支持更多页面状态
            "default_ttl": 600.0,           # 10分钟TTL，适合Hulu页面的稳定性
            "button_search_ttl": 300.0,     # 按钮搜索特定TTL
            "login_elements_ttl": 900.0     # 登录相关元素更长TTL
        }

        # 隐蔽性功能已整合到主方法中
        
        # 五层动态自适应架构组件
        # Layer 0: 动态宿主发现器 (新增)
        self.dynamic_host_detector: Optional[DynamicHostDetector] = None
        self.host_detector_config: Optional[HostDetectorConfig] = None
        
        # 简化后的按钮处理 - 移除过度设计的四层架构
        # 原复杂的Layer 1-4架构已简化为直接的按钮查找和点击逻辑
        # 移除了architecture_config - 已删除的统一架构控制器相关配置

        # 状态管理器
        if STATE_MANAGER_AVAILABLE and persistent_session:
            self.state_manager = StateManager()
            self.logger.info("✅ 登录持久化功能已启用")
        else:
            self.state_manager = None
            if persistent_session:
                self.logger.warning("⚠️ StateManager不可用，登录持久化功能已禁用")

        # 多账户支持 - 加载账户信息
        from utils.account_loader import AccountLoader
        try:
            self.account_loader = AccountLoader()
            
            # 根据优先级选择账户
            if account_email:
                selected_account = self.account_loader.get_account_by_email(account_email)
                if not selected_account:
                    self.logger.warning(f"未找到邮箱 {account_email} 对应的账户，使用第一个账户")
                    selected_account = self.account_loader.get_account_by_index(0)
            else:
                selected_account = self.account_loader.get_account_by_index(account_index)
            
            if selected_account:
                self.test_credentials = {
                    "email": selected_account.email,
                    "password": selected_account.password
                }
                self.current_account = selected_account
                self.logger.info(f"📧 已选择账户: {selected_account.email} (索引: {selected_account.index}, 状态: {selected_account.cookies_status})")
            else:
                # 降级到环境变量或默认值
                self.test_credentials = {
                    "email": os.getenv("test_email", "<EMAIL>"),
                    "password": os.getenv("test_password", "Fiona127,.")
                }
                self.current_account = None
                self.logger.warning("⚠️ 未找到有效账户，使用环境变量或默认凭据")
                
        except Exception as e:
            self.logger.error(f"❌ 加载账户信息失败: {e}")
            # 降级到环境变量或默认值
            self.test_credentials = {
                "email": os.getenv("test_email", "<EMAIL>"),
                "password": os.getenv("test_password", "Fiona127,.")
            }
            self.current_account = None
            self.account_loader = None

        # 智能交互控制器 (Phase 1 Enhancement)
        self.smart_interaction_controller = SmartInteractionController(logger=self.logger)
        self.logger.info("⚡ SmartInteractionController已集成 - 支持页面特定交互策略")

        self.logger.info("🛡️ Hulu隐身自动化系统初始化完成")
        self.logger.info(f"📊 反检测配置: AntiDetectionConfig(CDP=True, Stealth=True, Fingerprint=True, Behavior=True)")
        self.logger.info(f"🎯 智能交互策略: Welcome页面保持JS点击，其他页面现代化升级")

    async def safe_fill(self, selectors: list, value: str, timeout: int = 2000, element_type: str = "input") -> str:
        """
        增强的安全填写函数 - 使用SmartInteractionController智能策略
        
        Args:
            selectors: 选择器列表
            value: 要填写的值
            timeout: 超时时间(ms)
            element_type: 元素类型 (用于特殊选择器匹配)
            
        Returns:
            str: 成功的选择器，如果全部失败返回None
        """
        if hasattr(self, 'smart_interaction_controller') and self.smart_interaction_controller:
            # 使用智能交互控制器
            try:
                success = await self.smart_interaction_controller.smart_fill(
                    self.page, selectors, value, element_type, clear_first=True, timeout_ms=timeout
                )
                if success:
                    self.logger.debug(f"✅ 智能交互控制器填写成功")
                    return selectors[0]
            except Exception as e:
                self.logger.debug(f"⚠️ 智能交互控制器失败: {e}")
        
        # 降级到传统方法 (向后兼容)
        for selector in selectors:
            try:
                # 先等待元素可见
                await self.page.wait_for_selector(selector, timeout=timeout, state='visible')
                # 然后填写
                await self.page.fill(selector, value, timeout=timeout)
                self.logger.info(f"✅ 使用选择器 {selector} 填写成功")
                return selector
            except Exception as e:
                self.logger.debug(f"⚠️ 选择器 {selector} 失败: {e}")
                continue
        return None

    async def safe_click(self, selectors: list, timeout: int = 2000, element_type: str = "button") -> str:
        """
        增强的安全点击函数 - 使用SmartInteractionController智能策略
        
        Args:
            selectors: 选择器列表
            timeout: 超时时间(ms)
            element_type: 元素类型 (用于特殊选择器匹配)
            
        Returns:
            str: 成功的选择器，如果全部失败返回None
        """
        if hasattr(self, 'smart_interaction_controller') and self.smart_interaction_controller:
            # 使用智能交互控制器
            success = await self.smart_interaction_controller.smart_click(
                self.page, selectors, element_type, timeout
            )
            return selectors[0] if success else None
        else:
            # 降级到传统方法 (向后兼容)
            for selector in selectors:
                try:
                    await self.page.click(selector, timeout=timeout)
                    self.logger.info(f"✅ 使用选择器 {selector} 点击成功")
                    return selector
                except Exception as e:
                    self.logger.debug(f"⚠️ 选择器 {selector} 失败: {e}")
                    continue
            return None

    async def human_type(self, page: Page, selector: str, text: str):
        """统一的智能打字方法 - 集成专业行为模拟"""
        try:
            self.logger.info(f"🎭 开始人类打字模拟: '{text[:20]}...' 到选择器 {selector}")
            
            # 检查行为模拟器状态
            if self.human_behavior_simulator:
                self.logger.info(f"   ✅ 人类行为模拟器可用 (WPM: {self.human_behavior_simulator.base_wpm:.1f})")
            else:
                self.logger.warning("   ❌ 人类行为模拟器不可用，将使用降级模式")
            
            element = await page.wait_for_selector(selector, timeout=10000)
            self.logger.info("   📍 元素定位成功，开始点击聚焦")
            
            await element.click()
            await page.wait_for_timeout(100)
            
            # 清空字段
            self.logger.info("   🗑️ 清空输入字段")
            await element.fill("")
            await page.wait_for_timeout(200)
            
            self.logger.info(f"   ⌨️ 开始逐字符打字 ({len(text)} 个字符)")
            
            # 使用行为模拟器计算智能打字
            for i, char in enumerate(text):
                await page.keyboard.type(char)
                
                # 计算下一个字符的延迟
                next_char = text[i+1] if i+1 < len(text) else None
                if hasattr(self.human_behavior_simulator, '_calculate_typing_delay'):
                    delay = self.human_behavior_simulator._calculate_typing_delay(char, next_char)
                    if i < 5:  # 只记录前5个字符的延迟以避免日志过多
                        self.logger.info(f"      [{i+1}] '{char}' -> 延迟: {delay:.3f}s")
                    await page.wait_for_timeout(int(delay * 1000))
                else:
                    # 降级到基础延迟
                    basic_delay = random.randint(80, 120)
                    if i < 5:
                        self.logger.info(f"      [{i+1}] '{char}' -> 基础延迟: {basic_delay}ms")
                    await page.wait_for_timeout(basic_delay)
            
            self.logger.info("   ✅ 人类打字模拟完成")
            
            # 记录行为数据到反检测系统
            if self.anti_detection_manager and hasattr(self.anti_detection_manager, 'analytics'):
                behavior_data = {
                    'type': 'typing',
                    'text_length': len(text),
                    'typing_stats': {
                        'wpm': getattr(self.behavior_simulator, 'base_wpm', 45),
                        'error_rate': 0.03
                    }
                }
                fingerprint_data = {'typing_behavior': behavior_data}
                try:
                    self.anti_detection_manager.analytics.record_attempt(
                        session_id=self.anti_detection_manager.session_state.session_id,
                        fingerprint_data=fingerprint_data,
                        behavior_data=behavior_data,
                        success=True
                    )
                except Exception as analytics_error:
                    self.logger.debug(f"⚠️ 行为数据记录失败: {analytics_error}")
                    
        except Exception as e:
            self.logger.warning(f"智能打字失败，降级到填充模式: {e}")
            element = await page.wait_for_selector(selector)
            await element.fill(text)

    async def safe_type(self, selectors: list, value: str, delay: int = 100, timeout: int = 2000, element_type: str = "input") -> str:
        """
        增强的安全打字函数 - 智能人类模拟打字 + SmartInteractionController
        
        Args:
            selectors: 选择器列表
            value: 要输入的值
            delay: 打字延迟(ms) - 保留兼容性，实际由HumanBehaviorSimulator控制
            timeout: 超时时间(ms)
            element_type: 元素类型 (用于特殊选择器匹配)
            
        Returns:
            str: 成功的选择器，如果全部失败返回None
        """
        # 优先使用SmartInteractionController的智能填充
        if hasattr(self, 'smart_interaction_controller') and self.smart_interaction_controller:
            try:
                success = await self.smart_interaction_controller.smart_fill(
                    self.page, selectors, value, element_type, clear_first=True, timeout_ms=timeout
                )
                if success:
                    self.logger.debug(f"✅ 智能交互控制器打字成功")
                    return selectors[0]
            except Exception as e:
                self.logger.debug(f"⚠️ 智能交互控制器打字失败: {e}")
        
        # 降级到传统方法 + 人类行为模拟
        for selector in selectors:
            try:
                locator = self.page.locator(selector).first()
                await locator.wait_for(state='editable', timeout=timeout)
                
                # 使用集成的智能打字功能
                try:
                    await self.human_type(self.page, selector, value)
                    self.logger.info(f"✅ 使用选择器 {selector} 智能打字成功")
                    return selector
                except Exception as typing_error:
                    self.logger.debug(f"⚠️ 智能打字失败: {typing_error}")
                    # 降级到元素填充
                    try:
                        await locator.fill(value)
                        self.logger.info(f"✅ 使用选择器 {selector} 填充成功")
                        return selector
                    except Exception as fill_error:
                        self.logger.debug(f"⚠️ 选择器 {selector} 填充失败: {fill_error}")
                        continue
                    
            except Exception as e:
                self.logger.debug(f"⚠️ 选择器 {selector} 失败: {e}")
                continue
        return None

    async def wait_for_page_stability(self, action_description: str = "页面稳定", 
                                     timeout: int = 15000) -> bool:
        """
        等待页面稳定 - 基于测试验证的多阶段等待策略
        专门用于JavaScript点击后的页面稳定等待
        """
        try:
            self.logger.info(f"⏳ 等待{action_description}稳定...")
            
            # 阶段1：等待初始跳转 (2秒)
            await asyncio.sleep(2)
            current_url = self.page.url
            self.logger.info(f"📍 2秒后URL: {current_url}")
            
            # 阶段2：等待DOM加载稳定
            try:
                await self.page.wait_for_load_state('domcontentloaded', timeout=10000)
                self.logger.info("✅ DOM内容加载完成")
            except Exception as e:
                self.logger.warning(f"⚠️ DOM加载等待超时: {str(e)[:50]}...")
            
            # 阶段3：等待网络稳定
            try:
                await self.page.wait_for_load_state('networkidle', timeout=8000)
                self.logger.info("✅ 网络活动稳定")
            except Exception as e:
                self.logger.warning(f"⚠️ 网络稳定等待超时: {str(e)[:50]}...")
            
            # 阶段4：额外稳定等待 (3秒)
            await asyncio.sleep(3)
            
            final_url = self.page.url
            self.logger.info(f"📍 最终稳定URL: {final_url}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"页面稳定等待失败: {e}")
            return False

    async def wait_for_page_navigation(self, action_description: str = "页面导航", 
                                     timeout: int = 10000, wait_for_stable: bool = True) -> bool:
        """
        优化的页面导航等待机制 - 实现显式等待避免竞态条件
        
        Args:
            action_description: 操作描述，用于日志
            timeout: 超时时间（毫秒）
            wait_for_stable: 是否等待网络稳定
            
        Returns:
            bool: 等待是否成功
        """
        try:
            self.logger.info(f"⏳ 等待{action_description}完成...")
            
            # Step 1: 等待DOM内容加载
            try:
                await self.page.wait_for_load_state('domcontentloaded', timeout=timeout)
                self.logger.debug("✅ DOM内容已加载")
            except Exception as e:
                self.logger.warning(f"⚠️ DOM加载等待超时: {e}")
                return False
            
            # Step 2: 等待网络稳定（可选）
            if wait_for_stable:
                try:
                    await self.page.wait_for_load_state('networkidle', timeout=min(timeout, 5000))
                    self.logger.debug("✅ 网络活动已稳定")
                except Exception as e:
                    self.logger.debug(f"ℹ️ 网络稳定等待超时，继续处理: {e}")
            
            # Step 3: 等待关键元素出现（避免页面内容延迟加载）
            await asyncio.sleep(1)  # 给页面渲染一些时间
            
            # Step 4: 验证页面确实已加载
            page_ready = await self.page.evaluate("""
                () => {
                    return document.readyState === 'complete' || document.readyState === 'interactive';
                }
            """)
            
            if page_ready:
                self.logger.info(f"✅ {action_description}等待完成")
                return True
            else:
                self.logger.warning(f"⚠️ {action_description}页面状态异常")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ {action_description}等待失败: {e}")
            return False

    async def _check_existing_login_state(self) -> bool:
        """
        智能检查是否存在有效的登录状态 - 通过Cookies和会话验证

        Returns:
            bool: 是否存在有效的登录状态
        """
        if not self.persistent_session:
            self.logger.debug("持久化会话功能未启用")
            return False

        try:
            # 检查浏览器Cookies中的关键登录信息
            await self._setup_page_if_needed()
            
            self.logger.info("🔍 检查浏览器中的登录Cookies...")
            
            # 导航到Hulu主页进行状态检测
            await self.page.goto("https://www.hulu.com", wait_until="networkidle", timeout=15000)
            
            # 智能等待替换固定延迟
            if self.intelligent_waiting_manager:
                await self.intelligent_waiting_manager.intelligent_wait(LOGIN_STATUS_CHECK_CONTEXT)
            else:
                await asyncio.sleep(2)  # 降级方案
            
            # 检查关键登录Cookies
            cookies = await self.page.context.cookies()
            login_cookies = [
                "_hulu_session", "_hulu_uid", "_hulu_email", 
                "XSRF-TOKEN", "JSESSIONID", "guid"
            ]
            
            found_cookies = []
            for cookie in cookies:
                if any(login_cookie in cookie['name'] for login_cookie in login_cookies):
                    found_cookies.append(cookie['name'])
            
            self.logger.info(f"📊 发现登录相关Cookies: {len(found_cookies)}个")
            self.logger.debug(f"Cookies列表: {found_cookies}")
            
            # 检查当前页面URL和内容
            current_url = self.page.url
            self.logger.info(f"🌐 当前页面URL: {current_url}")
            
            # 检查是否在登录页面或需要登录
            if "login" in current_url or "signin" in current_url or "welcome" in current_url:
                self.logger.info("⚠️ 页面显示需要登录，Cookies可能已过期")
                return False
                
            # 检查页面是否显示已登录状态
            try:
                # 查找登录用户的标识元素
                profile_indicators = [
                    '[data-testid="profile"]',
                    '.profile',
                    '[aria-label*="profile"]',
                    'button:has-text("Profile")',
                    '[data-testid="user-menu"]',
                    '.user-menu'
                ]
                
                profile_found = False
                for selector in profile_indicators:
                    try:
                        element = await self.page.wait_for_selector(selector, timeout=3000)
                        if element:
                            self.logger.info(f"✅ 发现用户配置文件元素: {selector}")
                            profile_found = True
                            break
                    except:
                        continue
                
                if profile_found and len(found_cookies) >= 3:
                    self.logger.info("✅ 检测到有效登录状态: Cookies + 用户界面元素")
                    return True
                elif len(found_cookies) >= 5:
                    self.logger.info("✅ 检测到有效登录状态: 充足的登录Cookies")
                    return True
                else:
                    self.logger.info("⚠️ 登录状态不明确，建议重新登录")
                    return False
                    
            except Exception as e:
                self.logger.debug(f"检查页面登录状态时出错: {e}")
                
                # 如果有足够的Cookies，认为可能已登录
                if len(found_cookies) >= 4:
                    self.logger.info("✅ 基于Cookies数量判断为已登录状态")
                    return True
                else:
                    self.logger.info("❌ Cookies不足，判断为未登录状态")
                    return False
                    
        except Exception as e:
            self.logger.error(f"❌ 检查登录状态失败: {e}")
            return False

    async def _stealth_content_precheck(self) -> bool:
        """
        隐蔽性内容预检查 - 智能判断页面登录状态
        
        Returns:
            bool: 是否包含登录内容（True=需要登录, False=已登录）
        """
        try:
            current_url = self.page.url
            
            # 第一步：优先检测profile图标（已登录状态的最可靠指标）
            profile_detected = await self._detect_profile_icon()
            if profile_detected:
                self.logger.info("✅ 检测到profile图标，用户已登录，跳过登录内容检查")
                return False  # 已登录，不包含登录内容
            
            # 第二步：Welcome页面特殊处理
            if "welcome" in current_url:
                self.logger.info("🚀 Welcome页面智能检测：检查登录状态")
                
                # Welcome页面的登录状态通过多种方式判断
                welcome_login_needed = await self._check_welcome_page_login_status()
                
                if not welcome_login_needed:
                    self.logger.info("✅ Welcome页面检测：用户已登录（基于页面状态分析）")
                    return False  # 已登录，不包含登录内容
                else:
                    self.logger.info("⚠️ Welcome页面检测：需要登录（基于页面内容分析）")
                    return True  # 需要登录
            
            # 第三步：传统内容检测方法
            page_content = await self.page.content()
            content_size = len(page_content)
            
            # 编译登录内容检测正则表达式
            login_patterns = ["log in", "login", "sign in", "signin", "auth.hulu.com"]
            regex_patterns = []
            for pattern in login_patterns:
                if "." in pattern:  # 域名模式
                    regex_patterns.append(rf'\b{re.escape(pattern)}\b')
                else:  # 文本模式
                    regex_patterns.append(rf'\b{re.escape(pattern)}\b')
            
            combined_pattern = '|'.join(f'({pattern})' for pattern in regex_patterns)
            login_content_regex = re.compile(combined_pattern, re.IGNORECASE | re.MULTILINE)
            
            # 根据内容大小选择最优算法
            if content_size < 50_000:  # 小页面：直接正则搜索
                found = bool(login_content_regex.search(page_content))
                self.logger.debug(f"🔍 小页面内容预检查: {found} ({content_size:,}字符)")
                
            elif content_size < 200_000:  # 中等页面：搜索前半部分
                search_content = page_content[:100_000]
                found = bool(login_content_regex.search(search_content))
                self.logger.debug(f"🔍 中等页面内容预检查: {found} ({content_size:,}字符)")
                
            else:  # 大页面：分块搜索
                found = self._chunked_content_search(page_content, login_content_regex)
                self.logger.debug(f"🔍 大页面内容预检查: {found} ({content_size:,}字符)")
            
            return found
            
        except Exception as e:
            self.logger.warning(f"⚠️ 内容预检查失败: {e}")
            return True  # 失败时假设存在，继续元素搜索

    def _chunked_content_search(self, content: str, regex: re.Pattern) -> bool:
        """分块内容搜索（用于大页面）"""
        try:
            chunk_size = 50000
            
            # 搜索页面开头（登录按钮通常在顶部）
            header_chunk = content[:chunk_size]
            if regex.search(header_chunk):
                return True
            
            # 搜索导航相关部分
            nav_patterns = [r'<nav\b.*?</nav>', r'<header\b.*?</header>']
            for pattern in nav_patterns:
                nav_match = re.search(pattern, content, re.IGNORECASE | re.DOTALL)
                if nav_match and regex.search(nav_match.group()):
                    return True
            
            return False
            
        except Exception as e:
            self.logger.warning(f"⚠️ 分块搜索失败: {e}")
            return True

    async def _detect_profile_icon(self) -> bool:
        """
        检测页面的profile图标（最可靠的已登录状态指标）
        
        Returns:
            bool: 是否检测到profile图标
        """
        try:
            self.logger.info("🔍 开始检测Profile图标...")
            
            # 获取页面视口尺寸用于响应式位置计算
            viewport = await self.page.viewport_size()
            viewport_width = viewport['width'] if viewport else 1920
            right_threshold = viewport_width * 0.6  # 动态计算右侧区域阈值
            
            # 基于调试结果，尝试检测常见的profile图标选择器
            profile_selectors = [
                # 用户相关选择器
                "[class*='user']", "[class*='profile']", "[class*='account']",
                # 导航区域的按钮
                "header [role='button']", "nav [role='button']",
                # 测试ID相关
                "[data-testid*='user']", "[data-testid*='profile']", "[data-testid*='account']",
                # 常见的Avatar/头像相关
                "[class*='avatar']", "[class*='initials']", "[class*='user-icon']",
                "[class*='profile-icon']", "[class*='HeaderUserMenu']", "[class*='UserMenu']",
                # Hulu特定的选择器（如果存在）
                "[class*='ProfileMenu']", "button[class*='user']", "button[class*='profile']"
            ]
            
            for selector in profile_selectors:
                try:
                    elements = await self.page.query_selector_all(selector)
                    for element in elements:
                        is_visible = await element.is_visible()
                        if not is_visible:
                            continue
                            
                        box = await element.bounding_box()
                        text_content = await element.text_content() or ""
                        class_name = await element.get_attribute("class") or ""
                        
                        # 响应式位置检测：检查元素是否在右侧区域
                        is_in_right_area = box and box['x'] > right_threshold
                        
                        self.logger.debug(f"🎯 检测元素: 文本='{text_content.strip()}', 类名='{class_name}', 位置={'右侧' if is_in_right_area else '左侧'}")
                        
                        # Profile图标特征检测
                        profile_indicators = 0
                        
                        # 特征1：用户初始字母（1-2个字符的字母）
                        if len(text_content.strip()) <= 2 and text_content.strip().isalpha():
                            profile_indicators += 2
                            self.logger.debug(f"   ✅ 用户初始字母特征: '{text_content.strip()}'")
                        
                        # 特征2：profile相关类名
                        profile_keywords = ['user', 'profile', 'avatar', 'account', 'menu']
                        if any(keyword in class_name.lower() for keyword in profile_keywords):
                            profile_indicators += 1
                            self.logger.debug(f"   ✅ Profile关键词特征: {class_name}")
                        
                        # 特征3：位置特征（右侧区域加分）
                        if is_in_right_area:
                            profile_indicators += 1
                            self.logger.debug(f"   ✅ 右侧位置特征")
                        
                        # 特征4：按钮角色
                        role = await element.get_attribute("role")
                        if role == "button":
                            profile_indicators += 0.5
                            self.logger.debug(f"   ✅ 按钮角色特征")
                        
                        # 如果特征评分足够高，认为是profile图标
                        if profile_indicators >= 2:
                            self.logger.info(f"✅ 发现profile图标 (评分: {profile_indicators}): 文本='{text_content.strip()}', 类名='{class_name}'")
                            return True
                                    
                except Exception as selector_error:
                    self.logger.debug(f"Profile选择器 {selector} 检测失败: {selector_error}")
                    continue
            
            self.logger.info("🔍 未检测到profile图标 - 用户应该未登录")
            return False
            
        except Exception as e:
            self.logger.warning(f"⚠️ Profile图标检测失败: {e}")
            return False

    async def _check_welcome_page_login_status(self) -> bool:
        """
        检查Welcome页面是否需要登录
        
        Returns:
            bool: 是否需要登录（True=需要登录, False=已登录）
        """
        try:
            # 方法1：检测页面是否包含订阅相关内容（已登录的指标）
            page_content = await self.page.content()
            
            # 已登录的指标：真正的已登录用户界面元素
            logged_in_indicators = [
                "my stuff", "watchlist", "account", "profile", 
                "sign out", "log out", "settings", "manage account"
            ]
            
            logged_in_count = sum(1 for indicator in logged_in_indicators 
                                 if indicator in page_content.lower())
            
            if logged_in_count >= 2:
                self.logger.info(f"✅ Welcome页面包含{logged_in_count}个已登录指标，判断为已登录状态")
                return False  # 已登录，无需登录
            
            # 方法2：检测是否有登录相关的按钮或链接
            login_indicators = ["log in", "login", "sign in", "create account", "start free trial"]
            login_count = sum(1 for indicator in login_indicators 
                             if indicator in page_content.lower())
            
            if login_count >= 2:
                self.logger.info(f"✅ Welcome页面包含{login_count}个登录指标，判断为需要登录")
                return True  # 需要登录
            
            # 方法3：如果没有检测到profile且没有明确的已登录指标，Welcome页面默认为需要登录
            self.logger.info("⚠️ Welcome页面未检测到profile图标且无明确已登录指标，判断为需要登录")
            return True  # 需要登录
            
        except Exception as e:
            self.logger.warning(f"⚠️ Welcome页面登录状态检测失败: {e}")
            return True  # 失败时假设需要登录

    def _get_stealth_selectors(self) -> Dict[int, List[str]]:
        """获取隐蔽性选择器分组 - 按优先级组织"""
        return {
            1: [  # 高优先级：最准确的选择器
                'text="Log In"',
                'nav a:has-text("Log In")',
                '{HOST} >> button:has-text("Log In")',
                '{HOST} >> a:has-text("Log In")'
            ],
            2: [  # 中优先级：通用选择器
                'a:has-text("Log In")',
                'button:has-text("Log In")',
                '#hx-core-auth button:has-text("Log In")',
                '#hx-core-auth a:has-text("Log In")'
            ],
            3: [  # 低优先级：降级选择器
                'text="LOG IN"',
                'text="Login"',
                '[data-testid*="login"]',
                '[data-testid*="signin"]',
                'button:text-matches(".*[Ll]og.*[Ii]n.*", "i")',
                'a:text-matches(".*[Ll]og.*[Ii]n.*", "i")'
            ]
        }

    async def _stealth_element_search(self, selectors: List[str]) -> Optional[Any]:
        """
        隐蔽性元素搜索 - 模拟人类搜索行为
        
        Args:
            selectors: 选择器列表
            
        Returns:
            找到的元素或None
        """
        import random
        
        # 随机打乱选择器顺序，避免固定模式
        shuffled_selectors = selectors.copy()
        random.shuffle(shuffled_selectors)
        
        for selector in shuffled_selectors:
            try:
                # 反检测随机延迟 (50-200ms)  
                delay = random.uniform(50, 200) / 1000
                await asyncio.sleep(delay)
                
                element = await self.page.wait_for_selector(selector, timeout=800)
                if element and await element.is_visible():
                    self.logger.debug(f"✅ 隐蔽性搜索找到元素: {selector}")
                    return element
                    
            except Exception:
                continue
                
        return None

    async def _check_login_button_exists(self) -> bool:
        """
        检测Log In按钮是否存在 - 整合隐蔽性优化版本

        Returns:
            bool: 是否存在Log In按钮
        """
        import random
        
        try:
            self.logger.info("🔍 开始隐蔽性登录按钮检测...")

            # 确保页面已设置
            await self._setup_page_if_needed()

            # 第一步：隐蔽性内容预检查
            has_login_content = await self._stealth_content_precheck()
            if not has_login_content:
                self.logger.info("⚡ 早期退出: 页面无登录内容")
                return False

            # 第二步：按优先级组进行隐蔽性搜索
            stealth_selectors = self._get_stealth_selectors()
            
            for priority in sorted(stealth_selectors.keys()):
                selectors = stealth_selectors[priority]
                self.logger.debug(f"🔄 搜索优先级 {priority} 组 ({len(selectors)}个选择器)")
                
                element = await self._stealth_element_search(selectors)
                if element:
                    self.logger.info(f"✅ 隐蔽性检测成功: 优先级{priority}")
                    return True
                
                # 优先级组间的隐蔽性延迟 (100-300ms)
                batch_delay = random.uniform(100, 300) / 1000
                await asyncio.sleep(batch_delay)
                self.logger.debug(f"🕐 组间隐蔽延迟: {batch_delay*1000:.0f}ms")

            # 第三步：如果隐蔽性方法失败，降级到原始方法
            self.logger.info("🔄 隐蔽性方法未找到，降级到传统方法...")
            return await self._check_login_button_exists_original()

        except Exception as e:
            self.logger.error(f"❌ 隐蔽性检测异常: {e}")
            # 异常时降级到原始方法
            return await self._check_login_button_exists_original()

    async def _check_login_button_exists_original(self) -> bool:
        """原有的检测方法（作为备用）"""
        try:
            self.logger.info("🔄 使用传统检测方法...")

            # 尝试多种Log In按钮选择器 - 统一 {HOST} >> 格式
            login_selectors = [
                # 动态宿主模板选择器 (优先级最高)
                '{HOST} >> button:has-text("Log In")',
                '{HOST} >> button:has-text("LOG IN")',
                '{HOST} >> a:has-text("Log In")',
                '{HOST} >> a:has-text("LOG IN")',
                '{HOST} >> [data-testid*="login"]',
                '{HOST} >> [data-testid*="signin"]',
                # Shadow DOM 精确选择器 (次优先级)
                '#hx-core-auth button:has-text("Log In")',
                '#hx-core-auth a:has-text("Log In")',
                '#hx-core-auth [data-testid*="login"]',
                '#hx-core-auth [data-testid*="signin"]',
                # 通用容器支持
                '[id*="hx-core-auth"] button:has-text("Log In")',
                '[class*="hx-core-auth"] button:has-text("Log In")',
                # 传统选择器 (兼容性降级)
                'button:has-text("Log In")',
                'a:has-text("Log In")',
                'text="Log In"',
                'text="LOG IN"',
                'text="Login"',
                '[data-testid*="login"]',
                '[data-testid*="signin"]',
                'button:text-matches(".*[Ll]og.*[Ii]n.*", "i")',
                'a:text-matches(".*[Ll]og.*[Ii]n.*", "i")'
            ]

            for selector in login_selectors:
                try:
                    element = await self.page.wait_for_selector(selector, timeout=3000)
                    if element and await element.is_visible():
                        self.logger.info(f"✅ 检测到Log In按钮: {selector}")
                        return True
                except:
                    continue

            self.logger.info("❌ 未检测到Log In按钮")
            return False

        except Exception as e:
            self.logger.error(f"❌ 传统检测方法出错: {e}")
            return False

    async def _setup_page_if_needed(self):
        """确保页面已设置（用于状态检查）"""
        if not self.page:
            await self._setup_cdp_connection()
            await self._apply_stealth_configuration()

        # 隐蔽性功能已直接整合到检测方法中

    async def _restore_login_state(self) -> bool:
        """
        恢复登录状态

        Returns:
            bool: 是否成功恢复登录状态
        """
        try:
            if not self.context:
                self.logger.error("❌ 浏览器上下文未初始化，无法恢复状态")
                return False

            self.logger.info("🔄 尝试恢复登录状态...")

            # 导航到Hulu主页检查登录状态
            await self.page.goto("https://www.hulu.com/", wait_until="networkidle")
            await asyncio.sleep(3)

            # 检查是否已经登录
            current_url = self.page.url
            if "login" not in current_url and "signin" not in current_url:
                self.logger.info("✅ 登录状态恢复成功")
                return True
            else:
                self.logger.info("⚠️ 登录状态已过期，需要重新登录")
                return False

        except Exception as e:
            self.logger.error(f"❌ 恢复登录状态失败: {e}")
            return False

    async def _save_login_state(self) -> bool:
        """
        保存登录状态

        Returns:
            bool: 是否成功保存登录状态
        """
        if not self.persistent_session or not self.state_manager or not self.context:
            return False

        try:
            self.logger.info("💾 保存登录状态...")
            success = await self.state_manager.save_state(self.storage_state_path, self.context)
            if success:
                self.logger.info("✅ 登录状态保存成功")
            else:
                self.logger.error("❌ 登录状态保存失败")
            return success
        except Exception as e:
            self.logger.error(f"❌ 保存登录状态时出错: {e}")
            return False

    async def execute_stealth_login(self) -> Dict[str, Any]:
        """
        执行隐身登录流程 - CDP模式

        Returns:
            Dict[str, Any]: 登录结果
        """
        self.logger.info("🚀 开始最大隐身模式登录流程...")

        try:
            # 1. 设置CDP连接
            await self._setup_cdp_connection()

            # 2. 应用反检测配置
            await self._apply_stealth_configuration()

            # 3. 智能检查登录状态
            login_state_valid = await self._check_existing_login_state()
            
            if login_state_valid:
                self.logger.info("✅ 检测到有效的登录状态，跳过登录流程")
                
                # 登录状态恢复后处理计费页面
                await self._handle_get_them_both_button()
                billing_result = await self._fill_billing_form()
                
                return {
                    "login_status": "restored",
                    "message": "登录状态恢复成功，计费页面处理完成",
                    "anti_detection_applied": True,
                    "stealth_mode": True,
                    "cdp_mode": True,
                    "url": self.page.url,
                    "billing_result": billing_result
                }
            
            # 4. 简化登录判断：基于Welcome页面状态直接决定是否需要登录
            self.logger.info("❌ 持久化登录状态失效，检查是否需要登录...")
            
            # 检查当前页面是否为welcome页面且未登录
            current_url = self.page.url
            if "welcome" in current_url:
                # Welcome页面且没有profile图标，直接执行登录流程
                self.logger.info("🚀 Welcome页面检测到需要登录，直接执行登录流程")
                login_result = await self._perform_login()
                
                if login_result.get("login_status") in ["success", "verification_completed"]:
                    # 登录成功后处理GET THEM BOTH按钮
                    await self._handle_get_them_both_button()
                    billing_result = await self._fill_billing_form()
                    
                    return {
                        "login_status": "success",
                        "message": "登录成功，计费页面处理完成",
                        "anti_detection_applied": True,
                        "stealth_mode": True,
                        "cdp_mode": True,
                        "url": self.page.url,
                        "billing_result": billing_result
                    }
                else:
                    return {
                        "login_status": "failed",
                        "message": "登录失败",
                        "anti_detection_applied": True,
                        "stealth_mode": True,
                        "cdp_mode": True,
                        "error": login_result.get("error")
                    }
            else:
                # 非welcome页面，尝试直接处理GET THEM BOTH按钮
                self.logger.info("✅ 非Welcome页面，尝试直接处理GET THEM BOTH按钮")
                await self._handle_get_them_both_button()
                billing_result = await self._fill_billing_form()
                
                return {
                    "login_status": "no_login_needed",
                    "message": "无需登录，计费页面处理完成",
                    "anti_detection_applied": True,
                    "stealth_mode": True,
                    "cdp_mode": True,
                    "url": self.page.url,
                    "billing_result": billing_result
                }
            
            # 5. 双重条件满足：持久化失效 + 检测到Log In按钮 → 清理cookies并执行登录
            self.logger.info("🔐 持久化失效且检测到Log In按钮，清理cookies并开始自动登录流程...")
            
            # 5.1 清理登录相关cookies，确保浏览器从干净状态开始
            # 这是解决cookies状态不同步问题的关键步骤
            await self._clear_login_cookies()
            
            # 5.2 从干净状态开始登录流程
            result = await self._perform_login()

            # 5. 如果登录成功，保存状态
            if result.get("login_status") in ["success", "verification_completed"]:
                await self._save_login_state()

            # 6. 如果登录成功，处理 GET THEM BOTH 按钮和计费页面
            billing_result = None
            if result.get("login_status") in ["success", "verification_completed"]:
                await self._handle_get_them_both_button()
                
                # 7. 自动处理计费页面
                billing_result = await self._fill_billing_form()

            # 8. 分析登录结果
            self._analyze_login_result(result)

            return {
                "login_status": result.get("login_status", "success"),
                "message": "隐身登录和计费处理执行完成",
                "anti_detection_applied": True,
                "stealth_mode": True,
                "cdp_mode": True,
                "url": result.get("url", ""),
                "billing_result": billing_result
            }

        except Exception as e:
            self.logger.error(f"❌ 隐身登录失败: {str(e)}")
            return {
                "login_status": "failed",
                "error": str(e),
                "message": "隐身登录失败",
                "anti_detection_applied": False,
                "stealth_mode": True,
                "cdp_mode": True
            }
        finally:
            # 不在这里清理资源，改为在main()函数最后统一清理
            # 这样可以保持浏览器会话，继续处理后续流程
            pass

    async def _setup_cdp_connection(self):
        """设置CDP连接"""
        self.logger.info("🔧 设置CDP连接...")

        # 如果启用自动启动，则先启动Chrome
        if self.auto_launch:
            try:
                self.logger.info("🚀 自动启动Chrome浏览器...")
                # 传递None让函数自动创建临时目录
                self.cdp_manager.auto_launch_chrome(user_data_dir=None)
                # 等待Chrome启动完成
                self.logger.info("⏳ 等待Chrome启动完成...")
                await asyncio.sleep(8)  # 增加等待时间
                self.logger.info("✅ Chrome启动完成，等待CDP连接...")
            except Exception as e:
                self.logger.error(f"❌ 自动启动Chrome失败: {e}")
                raise

        # 初始化Playwright
        self.playwright = await async_playwright().start()
        self.anti_detection_service = StealthAntiDetectionService(self.playwright)

        # 连接到Chrome浏览器
        max_retries = 3
        for attempt in range(max_retries):
            try:
                self.browser = await self.cdp_manager.connect_to_browser(self.playwright)
                break
            except Exception as e:
                if attempt == max_retries - 1:
                    self.logger.error(f"❌ 连接CDP失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                    raise
                else:
                    self.logger.warning(f"⚠️ 连接CDP失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                    await asyncio.sleep(2)

        # 获取或创建浏览器上下文，支持状态恢复
        contexts = self.browser.contexts
        if contexts:
            self.context = contexts[0]
            self.logger.info("✅ 使用现有浏览器上下文")
        else:
            # 检查是否有保存的状态文件
            if (self.persistent_session and
                os.path.exists(self.storage_state_path) and
                self.state_manager and
                self.state_manager.load_state(self.storage_state_path)):

                self.logger.info("🔄 使用保存的登录状态创建上下文...")
                self.context = await self.browser.new_context(storage_state=self.storage_state_path)
                self.logger.info("✅ 已加载保存的登录状态")
            else:
                self.context = await self.browser.new_context()
                self.logger.info("✅ 创建新的浏览器上下文")

    async def _apply_stealth_configuration(self):
        """应用反检测配置"""
        self.logger.info("🛡️ 应用反检测配置...")

        # 创建隐身页面
        self.page = await self.anti_detection_service.create_stealth_page(self.context)
        
        # 初始化智能等待管理器 (Phase 2 Enhancement)
        self.intelligent_waiting_manager = IntelligentWaitingManager(self.page, logger=self.logger)
        self.logger.info("⚡ IntelligentWaitingManager已初始化 - 智能等待策略启用")
        
        # 初始化新的反检测系统
        if self.anti_detection_manager:
            try:
                # 初始化会话
                session_info = self.anti_detection_manager.initialize_session()
                self.logger.info(f"🔥 增强反检测会话初始化: {session_info['session_id'][:8]}...")
                self.logger.info(f"📱 设备模板: {session_info['device_template']}")
                
                # 应用反检测配置到页面
                config_success = await self.anti_detection_manager.configure_page(self.page)
                if config_success:
                    self.logger.info("✅ 增强反检测配置已应用")
                else:
                    self.logger.warning("⚠️ 增强反检测配置失败，使用传统模式")
                    
            except Exception as e:
                self.logger.warning(f"⚠️ 增强反检测系统初始化失败: {e}，使用传统模式")
        
        # 初始化五层动态自适应架构组件
        # Layer 0: 动态宿主发现器 (新增)
        self.host_detector_config = HostDetectorConfig(
            iframe_detection_timeout=1000,  # 优化为1秒
            host_discovery_timeout=1000,    # 优化为1秒
            enable_monitoring=True,
            cache_discovery_results=True
        )
        self.dynamic_host_detector = DynamicHostDetector(self.page, self.host_detector_config)
        
        # 简化后的按钮处理 - 移除过度设计的四层架构初始化
        # 原Layer 1-4复杂架构已简化为直接的元素查找和点击逻辑
        
        self.logger.info("✅ 反检测配置已应用")
        self.logger.info("🎯 五层动态自适应架构组件已初始化")
        self.logger.info("🔍 Layer 0: 动态宿主发现器已就绪")
        self.logger.info("🔍 Layer 3: 渐进式DOM扫描器已就绪")
        self.logger.info("⚡ Layer 2: 智能并发控制器已就绪")
        self.logger.info("🏗️ Layer 1: 统一架构控制器已就绪")
    
    async def _detect_login_page_state(self) -> str:
        """
        检测当前登录页面的状态
        
        Returns:
            str: 页面状态 - 'email_page', 'password_page', 'verification_page', 'logged_in', 'unknown'
        """
        try:
            current_url = self.page.url
            self.logger.debug(f"🔍 检测页面状态，当前URL: {current_url}")
            
            # 检查是否已登录
            if "welcome" in current_url and not await self._check_login_button_exists():
                return "logged_in"
            
            # 检查是否在验证码页面
            if "enter-passcode" in current_url:
                return "verification_page"
            
            # 检查页面内容来判断状态
            page_content = await self.page.content()
            
            # 检查是否在密码输入页面
            if "Enter your password" in page_content or "MyDisney account using email:" in page_content:
                self.logger.debug("🔍 检测到密码输入页面特征")
                return "password_page"
            
            # 检查是否在邮箱输入页面
            email_indicators = [
                "Enter your email",
                "Email address", 
                "log in with your email",
                'name="email"',
                'type="email"'
            ]
            
            if any(indicator in page_content for indicator in email_indicators):
                self.logger.debug("🔍 检测到邮箱输入页面特征")
                return "email_page"
            
            # 检查是否有登录按钮但没有输入框（可能是首页）
            if await self._check_login_button_exists():
                return "home_page"
            
            self.logger.debug("🔍 无法确定页面状态")
            return "unknown"
            
        except Exception as e:
            self.logger.warning(f"⚠️ 页面状态检测异常: {e}")
            return "unknown"
    
    async def _clear_login_cookies(self):
        """
        清理所有登录相关的cookies，确保从干净状态开始登录
        
        这是解决cookies状态不同步问题的关键方法：
        - 当检测到cookies不完整时，程序决定重新登录
        - 但如果不清理现有cookies（如_hulu_email），浏览器会处于"半登录"状态
        - 这导致点击登录后直接跳到密码页面，而程序还在找邮箱输入框
        """
        try:
            self.logger.info("🧹 开始清理登录相关cookies...")
            
            # 定义需要清理的登录相关cookies
            login_cookies_to_clear = [
                "_hulu_session", "_hulu_uid", "_hulu_email", 
                "XSRF-TOKEN", "JSESSIONID", "guid"
            ]
            
            # 获取当前所有cookies
            all_cookies = await self.page.context.cookies()
            cookies_to_remove = []
            
            for cookie in all_cookies:
                cookie_name = cookie['name']
                # 检查是否是需要清理的登录cookies
                if any(login_cookie in cookie_name for login_cookie in login_cookies_to_clear):
                    cookies_to_remove.append(cookie)
                    self.logger.debug(f"   🗑️ 标记清理cookie: {cookie_name}")
            
            # 执行清理
            cleared_count = 0
            for cookie in cookies_to_remove:
                try:
                    # 删除cookie - 需要提供完整的cookie信息
                    await self.page.context.clear_cookies(
                        name=cookie['name'],
                        domain=cookie.get('domain', '.hulu.com')
                    )
                    cleared_count += 1
                    self.logger.debug(f"   ✅ 已清理cookie: {cookie['name']}")
                except Exception as e:
                    self.logger.warning(f"   ⚠️ 清理cookie失败 {cookie['name']}: {e}")
            
            self.logger.info(f"✅ cookies清理完成: 共清理 {cleared_count} 个登录相关cookies")
            
            # 验证清理结果
            remaining_cookies = await self.page.context.cookies()
            remaining_login_cookies = []
            for cookie in remaining_cookies:
                if any(login_cookie in cookie['name'] for login_cookie in login_cookies_to_clear):
                    remaining_login_cookies.append(cookie['name'])
            
            if remaining_login_cookies:
                self.logger.warning(f"⚠️ 仍有部分登录cookies未清理: {remaining_login_cookies}")
            else:
                self.logger.info("✅ 所有登录cookies已成功清理，浏览器处于干净状态")
                
        except Exception as e:
            self.logger.error(f"❌ 清理cookies过程异常: {e}")

    async def _perform_login(self) -> Dict[str, Any]:
        """执行登录流程"""
        self.logger.info("🔐 开始Hulu账户登录流程...")

        try:
            # 访问Hulu主页
            self.logger.info("访问Hulu welcome页面...")
            await self.page.goto("https://www.hulu.com")
            await self.wait_for_page_navigation("Hulu主页加载")

            # 使用JavaScript绕过ExitIntentModal拦截点击LOGIN按钮
            self.logger.info("点击右上角LOG IN按钮...")
            js_click_success = await self._click_login_with_js()
            
            if js_click_success:
                # 使用测试验证的页面稳定等待策略
                await self.wait_for_page_stability("LOGIN点击后页面稳定")
            else:
                # JavaScript点击失败，降级到原有方案
                self.logger.warning("⚠️ JavaScript点击失败，使用降级方案...")
                await self._handle_exit_intent_modal()  # 尝试处理ExitIntentModal
                if self.behavior_simulator:
                    await self.behavior_simulator.human_click(self.page, 'text="Log In"')
                else:
                    # 降级到直接点击
                    login_button = await self.page.wait_for_selector('text="Log In"')
                    await login_button.click()
                await self.wait_for_page_navigation("登录页面加载")

            # 智能页面观察等待替换固定延迟
            self.logger.info("⏳ 智能页面观察等待...")
            if self.intelligent_waiting_manager:
                await self.intelligent_waiting_manager.intelligent_wait(PAGE_OBSERVATION_CONTEXT)
            else:
                # 降级方案 - 原有的随机延迟
                observation_delay = random.randint(1800, 2200)  # 1.8-2.2秒
                self.logger.info(f"⏳ 页面加载完成，模拟用户观察页面 {observation_delay/1000:.1f}s...")
                await asyncio.sleep(observation_delay / 1000)

            # 检测页面状态并决定下一步操作
            page_state = await self._detect_login_page_state()
            self.logger.info(f"🔍 当前页面状态: {page_state}")
            
            if page_state == "password_page":
                self.logger.info("✅ 检测到已在密码输入页面，跳过邮箱填写，直接进入密码流程")
                # 直接进入密码输入流程
                password_result = await self._perform_realistic_password_login()
                if not password_result:
                    return {"login_status": "password_failed", "url": self.page.url}
                    
            elif page_state == "verification_page":
                self.logger.info("✅ 检测到已在验证码页面，直接进入验证码流程")
                # 直接进入验证码处理流程
                verification_result = await self._handle_verification_code()
                if verification_result:
                    return {"login_status": "verification_completed", "url": self.page.url}
                else:
                    return {"login_status": "verification_failed", "url": self.page.url}
                    
            elif page_state == "logged_in":
                self.logger.info("✅ 检测到已登录状态")
                return {"login_status": "success", "url": self.page.url}
                
            elif page_state in ["email_page", "home_page", "unknown"]:
                # 需要填写邮箱
                self.logger.info(f"📧 填写登录邮箱: {self.test_credentials['email']}")
                
                # 定义邮箱输入框选择器模板 - 支持动态宿主发现
                email_selector_templates = [
                    # 动态宿主组件选择器 (支持多模板)
                    '{HOST} >> input[type="email"]',
                    '{HOST} >> input[name="email"]',
                    '{HOST} >> input[autocomplete="username"]',
                    '{HOST} >> input[autocomplete="email"]',
                    '{HOST} >> #email-field',
                    '{HOST} >> [data-testid*="email"]',
                    # 无组件降级选择器 (兼容无宿主模板)
                    'input[type="email"]',
                    '#email-field', 
                    'input[name="email-field"]',
                    '[id*="email"]',
                    '[name*="email"]'
                ]
                
                # 使用Layer 2智能并发控制查找邮箱输入框
                email_element = await self._layer2_smart_concurrent_search(
                    email_selector_templates,
                    search_type="email_field",
                    timeout=5000
                )
                
                if not email_element:
                    self.logger.error("❌ 未找到邮箱输入框，尝试截图调试...")
                    screenshot_path = os.path.join(self.screenshots_dir, "email_field_not_found.png")
                    await self.page.screenshot(path=screenshot_path)
                    self.logger.info(f"📸 已保存调试截图: {screenshot_path}")
                    raise Exception("未找到邮箱输入框")
                
                # 🔍 详细调试：验证邮箱元素对象
                try:
                    self.logger.info(f"🔍 邮箱元素调试信息:")
                    self.logger.info(f"   📦 元素对象类型: {type(email_element)}")
                    self.logger.info(f"   📧 要填写的邮箱: {self.test_credentials['email']}")
                    
                    # 使用方法链模式进行邮箱填写
                    # 使用动态宿主发现器处理过的选择器而不是原始模板
                    if self.dynamic_host_detector:
                        login_context = await self.dynamic_host_detector.detect_login_context()
                        processed_selectors = self.dynamic_host_detector.expand_selectors(
                            email_selector_templates, login_context.host_tag
                        )
                    else:
                        processed_selectors = email_selector_templates
                    
                    await self._fill_email_with_methods_chain(processed_selectors)
                
                except Exception as e:
                    self.logger.error(f"❌ 邮箱填写过程异常: {e}")
                    # 保存调试截图
                    screenshot_path = os.path.join(self.screenshots_dir, "email_fill_error.png")
                    await self.page.screenshot(path=screenshot_path)
                    self.logger.info(f"📸 已保存错误截图: {screenshot_path}")
                    
                    # 尝试备用填写方法
                    self.logger.info("🔄 尝试备用邮箱填写方法...")
                    try:
                        await email_element.click()
                        await email_element.fill(self.test_credentials['email'])
                        self.logger.info("✅ 备用填写方法成功")
                    except Exception as backup_e:
                        self.logger.error(f"❌ 备用填写方法也失败: {backup_e}")
                        raise Exception(f"邮箱填写失败: {e}")
            

            # 点击Continue按钮 - Shadow DOM支持 + 安全点击函数
            self.logger.info("🔍 检查是否需要点击Continue按钮...")
            try:
                # Shadow DOM 优先选择器 - 使用>>递归符号
                continue_selectors = [
                    'hx-core-auth >> button:has-text("Continue")',
                    'hx-core-auth >> button:has-text("CONTINUE")',
                    'hx-core-auth >> [data-testid*="continue"]',
                    # 传统Shadow DOM选择器 (向后兼容)
                    '#hx-core-auth button:has-text("Continue")',
                    '[id*="hx-core-auth"] button:has-text("Continue")',
                    '[class*="hx-core-auth"] button:has-text("Continue")',
                    # 传统选择器 (兼容性降级)
                    'button:has-text("Continue")',
                    'button:has-text("CONTINUE")',
                    '[data-testid*="continue"]',
                    # 无前缀版本选择器 (提高完整性)
                    'button[type="submit"]',
                    'input[type="submit"]',
                    'button:contains("Continue")',
                    'button:contains("CONTINUE")',
                    '.continue-btn',
                    '.continue-button',
                    '#continue-btn',
                    '#continue-button'
                ]
                
                # 使用安全点击函数
                winner_selector = await self.safe_click(continue_selectors, timeout=1000)
                
                if winner_selector:
                    await self.wait_for_page_navigation("Continue按钮点击后页面加载")
                    self.logger.info(f"✅ Continue按钮点击成功: {winner_selector}")
                else:
                    raise Exception("No Continue button found")
            except:
                self.logger.info("ℹ️ 未找到Continue按钮，继续下一步")

            # 智能密码页面观察等待替换固定延迟
            self.logger.info("⏳ 智能密码页面观察等待...")
            if self.intelligent_waiting_manager:
                await self.intelligent_waiting_manager.intelligent_wait(PAGE_OBSERVATION_CONTEXT)
            else:
                # 降级方案 - 原有的随机延迟
                observation_delay = random.randint(1800, 2200)  # 1.8-2.2秒
                self.logger.info(f"⏳ 密码页面加载完成，模拟用户观察页面 {observation_delay/1000:.1f}s...")
                await asyncio.sleep(observation_delay / 1000)

            # 执行真实人类密码输入行为（包含错误尝试）
            login_success = await self._perform_realistic_password_login()
            if not login_success:
                self.logger.error("❌ 密码登录过程失败")
                return {"login_status": "password_failed", "url": self.page.url}

            # 智能等待页面响应 (替换5秒固定延迟) - 增强异常处理
            if hasattr(self, 'intelligent_waiting_manager') and self.intelligent_waiting_manager:
                try:
                    await self.intelligent_waiting_manager.smart_wait("form_submission")
                except Exception as e:
                    self.logger.warning(f"⚠️ 智能等待异常，可能连接中断: {e}")
                    # 检查页面连接状态
                    try:
                        page_title = await self.page.title()
                        self.logger.info(f"📄 页面连接正常，标题: {page_title}")
                        await asyncio.sleep(3)  # 降级等待
                    except Exception as page_error:
                        self.logger.error(f"❌ 页面连接已断开: {page_error}")
                        # 尝试保存调试信息
                        await self._save_debug_info_on_failure("connection_lost")
                        raise Exception("页面连接中断，可能被反自动化系统检测")
            else:
                await asyncio.sleep(3)  # 降级到更短的固定延迟

            # 检查登录结果
            current_url = self.page.url
            self.logger.info(f"🔍 登录后页面URL: {current_url}")

            if "enter-passcode" in current_url:
                self.logger.info("📧 检测到验证码页面，开始处理邮箱验证...")

                # 处理验证码
                verification_result = await self._handle_verification_code()
                if verification_result:
                    self.logger.info("✅ 验证码处理成功，检查最终登录状态...")
                    await asyncio.sleep(3)  # 等待页面跳转

                    final_url = self.page.url
                    if "welcome" not in final_url and "login" not in final_url and "enter-passcode" not in final_url:
                        self.logger.info("✅ 登录成功，已跳转到主页面")
                        return {"login_status": "success", "url": final_url}
                    else:
                        self.logger.warning("⚠️ 验证码处理后登录状态不明确")
                        return {"login_status": "verification_completed", "url": final_url}
                else:
                    return {"login_status": "verification_failed", "url": current_url}
            elif "welcome" not in current_url and "login" not in current_url:
                self.logger.info("✅ 登录成功，已跳转到主页面")
                return {"login_status": "success", "url": current_url}
            else:
                self.logger.warning("⚠️ 登录状态不明确")
                return {"login_status": "unknown", "url": current_url}

        except Exception as e:
            self.logger.error(f"❌ 登录流程失败: {e}")
            if self.page:
                screenshot_path = os.path.join(self.screenshots_dir, "login_error.png")
                await self.page.screenshot(path=screenshot_path)
                self.logger.info(f"📸 已保存错误截图: {screenshot_path}")
            raise

    async def _perform_realistic_password_login(self) -> bool:
        """
        执行直接密码输入行为 - 直接输入正确密码避免触发验证码

        Returns:
            bool: 登录过程是否成功
        """
        try:
            # 定义密码输入框选择器模板 - 支持动态宿主发现
            password_selector_templates = [
                # 动态宿主组件选择器 (支持多模板)
                '{HOST} >> input[type="password"]',
                '{HOST} >> input[name="password"]',
                '{HOST} >> input[autocomplete="current-password"]',
                '{HOST} >> #password-field',
                '{HOST} >> [data-testid*="password"]',
                # 无组件降级选择器 (兼容无宿主模板)
                'input[type="password"]',
                'input[name="password"]',
                '#password-field',
                '[id*="password"]',
                '[name*="password"]'
            ]

            # 使用Layer 2智能并发控制查找密码输入框
            password_element = await self._layer2_smart_concurrent_search(
                password_selector_templates,
                search_type="password_field",
                timeout=5000
            )

            if not password_element:
                self.logger.error("❌ 未找到密码输入框")
                return False

            self.logger.info("🔐 使用人类行为模拟输入密码...")
            
            # 使用方法链模式进行密码填写（优先人类行为模拟）
            # 使用动态宿主发现器处理过的选择器而不是原始模板
            if self.dynamic_host_detector:
                login_context = await self.dynamic_host_detector.detect_login_context()
                processed_password_selectors = self.dynamic_host_detector.expand_selectors(
                    password_selector_templates, login_context.host_tag
                )
            else:
                processed_password_selectors = password_selector_templates
            
            await self._fill_password_with_methods_chain(processed_password_selectors)

            # 等待表单稳定
            await asyncio.sleep(2)

            # 点击登录按钮 - 使用统一选择器模板
            self.logger.info("🎯 点击登录按钮...")
            login_button_templates = [
                '{HOST} >> button:has-text("Log In")',
                '{HOST} >> button:has-text("Submit")',
                '{HOST} >> button[type="submit"]',
                'button:has-text("Log In")',
                'button:has-text("Submit")',
                'button[type="submit"]'
            ]
            
            # 使用动态宿主发现展开选择器
            login_context = await self.dynamic_host_detector.detect_login_context()
            expanded_login_selectors = self.dynamic_host_detector.expand_selectors(
                login_button_templates, login_context.host_tag
            )
            
            # 尝试点击登录按钮
            login_clicked = False
            for selector in expanded_login_selectors:
                try:
                    if self.behavior_simulator:
                        await self.behavior_simulator.human_click(self.page, selector)
                    else:
                        element = await self.page.wait_for_selector(selector)
                        await element.click()
                    login_clicked = True
                    self.logger.info(f"✅ 登录按钮点击成功: {selector}")
                    break
                except Exception as e:
                    continue
            
            if not login_clicked:
                self.logger.warning("⚠️ 所有登录按钮选择器均失败，使用最简单的方式")
                if self.behavior_simulator:
                    await self.behavior_simulator.human_click(self.page, 'button:has-text("Log In")')
                else:
                    login_button = await self.page.wait_for_selector('button:has-text("Log In")')
                    await login_button.click()

            self.logger.info("✅ 密码输入行为完成")
            return True

        except Exception as e:
            self.logger.error(f"❌ 密码输入行为失败: {e}")
            return False

    async def _detect_password_error(self) -> bool:
        """
        检测密码错误提示

        Returns:
            bool: 是否检测到密码错误
        """
        try:
            # 常见的密码错误提示选择器 - Shadow DOM支持 + Layer 2并发优化目标
            error_selectors = [
                # Shadow DOM 精确选择器 (优先级最高)
                '#hx-core-auth [data-testid="error-message"]',
                '#hx-core-auth .error-message',
                '#hx-core-auth .alert-error',
                '#hx-core-auth [role="alert"]',
                '#hx-core-auth .notification-error',
                # 通用容器支持
                '[id*="hx-core-auth"] [role="alert"]',
                '[class*="hx-core-auth"] .error-message',
                # 传统选择器 (兼容性降级)
                '[data-testid="error-message"]',
                '.error-message',
                '.alert-error',
                '[role="alert"]',
                '.notification-error',
                'div:has-text("incorrect")',
                'div:has-text("invalid")',
                'div:has-text("wrong")',
                'div:has-text("error")'
            ]

            # 使用Layer 2智能并发控制检测错误提示
            error_element = await self._layer2_smart_concurrent_search(
                error_selectors,
                search_type="password_error",
                timeout=2000
            )

            if error_element:
                error_text = await error_element.text_content()
                if error_text and any(keyword in error_text.lower() for keyword in
                                    ['incorrect', 'invalid', 'wrong', 'error', 'failed']):
                    self.logger.info(f"🔍 检测到密码错误提示: {error_text}")
                    return True

            # 检查URL是否仍在登录页面（另一种错误检测方式）
            current_url = self.page.url
            if "login" in current_url or "signin" in current_url:
                self.logger.info("🔍 页面仍在登录页面，推测密码错误")
                return True

            return False

        except Exception as e:
            self.logger.debug(f"密码错误检测过程中出现异常: {e}")
            return True  # 保守起见，假设有错误

    async def _check_tempmail_api_health(self) -> bool:
        """
        检查TempMail API服务健康状态
        
        Returns:
            bool: API是否可用
        """
        try:
            self.logger.info("🏥 检查TempMail API服务状态...")
            
            import requests
            api_base = CONFIG.get('API_BASE', 'https://testkuroneko.xyz/api')
            health_url = f"{api_base}/health"
            
            response = await asyncio.to_thread(
                requests.get, 
                health_url, 
                timeout=10,
                verify=False
            )
            
            if response.status_code == 200:
                self.logger.info("✅ TempMail API服务正常")
                return True
            else:
                self.logger.warning(f"⚠️ TempMail API返回状态码: {response.status_code}")
                return False
                
        except Exception as e:
            self.logger.warning(f"⚠️ TempMail API健康检查失败: {e}")
            # 健康检查失败不影响继续尝试，可能只是健康检查端点不可用
            return True

    async def _get_verification_code_with_retry(self, temp_mail, email: str) -> Optional[str]:
        """
        带重试机制的稳定验证码获取
        
        Args:
            temp_mail: TempMailAPI实例
            email: 邮箱地址
        
        Returns:
            Optional[str]: 验证码或None
        """
        max_retries = 3
        base_timeout = 60  # 基础超时时间：60秒
        
        for attempt in range(max_retries):
            try:
                # 动态调整超时时间：第一次60s，第二次90s，第三次120s
                current_timeout = base_timeout + (attempt * 30)
                self.logger.info(f"🔄 第{attempt + 1}次尝试获取验证码，超时限制: {current_timeout}秒")
                
                # 分段式获取：先快速检查，再长时间等待
                if attempt == 0:
                    # 第一次尝试：快速检查是否已有验证码
                    verification_code = await asyncio.wait_for(
                        asyncio.to_thread(temp_mail.wait_for_verification_code, email),
                        timeout=current_timeout
                    )
                else:
                    # 后续尝试：使用更短的轮询间隔
                    self.logger.info(f"🔄 重试{attempt}：使用更频繁的轮询...")
                    verification_code = await self._poll_verification_code(temp_mail, email, current_timeout, poll_interval=3)
                
                if verification_code:
                    self.logger.info(f"✅ 第{attempt + 1}次尝试成功获取验证码: {verification_code}")
                    return verification_code
                else:
                    self.logger.warning(f"⚠️ 第{attempt + 1}次尝试返回空验证码")
                    
            except asyncio.TimeoutError:
                self.logger.warning(f"⏰ 第{attempt + 1}次尝试在{current_timeout}秒后超时")
                if attempt < max_retries - 1:
                    wait_time = 10 + (attempt * 5)  # 递增等待时间
                    self.logger.info(f"⏳ 等待{wait_time}秒后重试...")
                    await asyncio.sleep(wait_time)
                    
            except Exception as e:
                self.logger.error(f"❌ 第{attempt + 1}次尝试异常: {e}")
                if attempt < max_retries - 1:
                    self.logger.info("⏳ 等待10秒后重试...")
                    await asyncio.sleep(10)
        
        self.logger.error(f"❌ {max_retries}次重试均失败，无法获取验证码")
        return None

    async def _poll_verification_code(self, temp_mail, email: str, timeout: int, poll_interval: int = 5) -> Optional[str]:
        """
        轮询式验证码获取，提高成功率
        
        Args:
            temp_mail: TempMailAPI实例
            email: 邮箱地址
            timeout: 总超时时间
            poll_interval: 轮询间隔（秒）
        
        Returns:
            Optional[str]: 验证码或None
        """
        start_time = time.time()
        attempt_count = 0
        
        while time.time() - start_time < timeout:
            attempt_count += 1
            try:
                self.logger.info(f"🔄 轮询尝试 #{attempt_count}...")
                
                # 每次轮询使用较短的超时
                verification_code = await asyncio.wait_for(
                    asyncio.to_thread(temp_mail.wait_for_verification_code, email),
                    timeout=poll_interval + 5  # 轮询间隔 + 5秒缓冲
                )
                
                if verification_code:
                    self.logger.info(f"✅ 轮询成功，第{attempt_count}次尝试获取到验证码")
                    return verification_code
                    
            except asyncio.TimeoutError:
                self.logger.debug(f"轮询#{attempt_count}超时，继续下次尝试...")
            except Exception as e:
                self.logger.warning(f"轮询#{attempt_count}异常: {e}")
            
            # 等待下次轮询
            if time.time() - start_time < timeout - poll_interval:
                await asyncio.sleep(poll_interval)
        
        self.logger.warning(f"⏰ 轮询在{timeout}秒后结束，共尝试{attempt_count}次")
        return None

    async def _handle_verification_code(self) -> bool:
        """
        处理邮箱验证码 - 集成TempMailAPI和组合拳策略

        Returns:
            bool: 验证码处理是否成功
        """
        try:
            self.logger.info("📧 开始处理邮箱验证码...")

            # 等待10秒确保Hulu系统有足够时间发送邮件（优化：从20s缩短到10s）
            self.logger.info("⏳ 等待10秒确保邮件发送完成，避免获取到旧验证码...")
            self.logger.info("📋 预计总耗时：邮件等待10s + 验证码获取最长300s = 最长5.2分钟")
            self.logger.info("💡 如果命令行超时，程序会自动处理，请耐心等待...")
            
            # 显示倒计时进度
            for i in range(10, 0, -1):
                self.logger.info(f"   ⏰ 邮件发送等待倒计时: {i}秒")
                await asyncio.sleep(1)

            if not TEMPMAIL_AVAILABLE:
                self.logger.error("❌ TempMailAPI不可用，无法获取验证码")
                return False

            # 先检查API服务健康状态
            await self._check_tempmail_api_health()

            # 获取验证码，使用稳定的重试机制
            email = self.test_credentials['email']
            self.logger.info(f"📧 尝试从 {email} 获取验证码...")
            self.logger.info("🔄 使用多重重试机制确保获取成功...")

            temp_mail = TempMailAPI()
            
            # 使用改进的重试机制获取验证码
            verification_code = await self._get_verification_code_with_retry(temp_mail, email)
            
            if not verification_code:
                self.logger.error("❌ 所有重试尝试均失败，无法获取到验证码")
                self.logger.info("💡 建议检查:")
                self.logger.info("   1. 邮箱地址是否正确")
                self.logger.info("   2. 网络连接是否稳定")
                self.logger.info("   3. TempMail API服务是否正常")
                return False

            self.logger.info(f"✅ 获取到验证码: {verification_code}")

            # 使用组合拳策略填写验证码
            return await self._fill_verification_code(verification_code)

        except Exception as e:
            self.logger.error(f"❌ 验证码处理失败: {e}")
            return False

    async def _fill_verification_code(self, verification_code: str) -> bool:
        """
        使用改进的稳定填写策略填写验证码

        Args:
            verification_code: 6位验证码

        Returns:
            bool: 填写是否成功
        """
        max_attempts = 3  # 最多尝试3次
        
        for attempt in range(max_attempts):
            try:
                self.logger.info(f"🧪 第{attempt + 1}次尝试填写验证码: {verification_code}")
                
                # 确保页面已完全加载验证码输入框
                await self._wait_for_verification_input_ready()
                
                success = await self._attempt_fill_verification_code(verification_code, attempt)
                
                if success:
                    self.logger.info(f"✅ 第{attempt + 1}次尝试成功填写验证码")
                    return True
                else:
                    if attempt < max_attempts - 1:
                        self.logger.warning(f"⚠️ 第{attempt + 1}次尝试失败，{3-attempt}秒后重试...")
                        await asyncio.sleep(3)
                    else:
                        self.logger.error("❌ 所有填写尝试均失败")
                        
            except Exception as e:
                self.logger.error(f"❌ 第{attempt + 1}次填写尝试异常: {e}")
                if attempt < max_attempts - 1:
                    await asyncio.sleep(2)
        
        return False

    async def _wait_for_verification_input_ready(self) -> bool:
        """
        等待验证码输入框准备就绪
        
        Returns:
            bool: 输入框是否准备就绪
        """
        try:
            self.logger.info("⏳ 等待验证码输入框准备就绪...")
            
            # 等待隐藏的OTP输入框出现
            hidden_input_selector = "input[name='otp']"
            await self.page.wait_for_selector(hidden_input_selector, timeout=10000)
            
            # 等待可见的代码输入框
            visible_input_selector = '[data-testid="code-input"]'
            await self.page.wait_for_selector(visible_input_selector, timeout=5000)
            
            # 确保页面JS已加载完成
            await asyncio.sleep(1)
            
            self.logger.info("✅ 验证码输入框准备就绪")
            return True
            
        except Exception as e:
            self.logger.warning(f"⚠️ 等待输入框准备就绪时出错: {e}")
            return False

    async def _attempt_fill_verification_code(self, verification_code: str, attempt: int) -> bool:
        """
        单次验证码填写尝试
        
        Args:
            verification_code: 验证码
            attempt: 尝试次数
            
        Returns:
            bool: 是否成功
        """
        try:
            hidden_input_selector = "input[name='otp']"

            # 步骤 1: 清空现有内容（如果有）
            if attempt > 0:
                self.logger.info("🧹 清空现有验证码内容...")
                await self.page.evaluate(f'''
                    () => {{
                        const otpInput = document.querySelector("{hidden_input_selector}");
                        if (otpInput) {{
                            otpInput.value = '';
                            otpInput.dispatchEvent(new Event('input', {{ bubbles: true }}));
                        }}
                        // 清空可见输入框
                        const displayBoxes = document.querySelectorAll('[data-testid="code-input"]');
                        displayBoxes.forEach(box => box.textContent = '');
                    }}
                ''')
                await asyncio.sleep(0.5)

            # 步骤 2: 智能键盘输入
            self.logger.info("⌨️ 使用智能键盘输入验证码...")
            await asyncio.sleep(1)  # 确保页面焦点准备就绪
            
            # 点击第一个输入框确保焦点
            try:
                first_box = await self.page.wait_for_selector('[data-testid="code-input"]', timeout=3000)
                await first_box.click()
                await asyncio.sleep(0.3)
            except:
                pass
            
            # 逐字符输入，模拟真实用户
            for i, char in enumerate(verification_code):
                await self.page.keyboard.type(char, delay=150)  # 稍慢的输入速度
                await asyncio.sleep(0.1)  # 字符间小延迟
            
            self.logger.info("✅ 键盘输入完成")
            await asyncio.sleep(1)  # 等待页面JS响应

            # 步骤 3: 验证填写结果并同步UI
            self.logger.info("🔍 验证填写结果并同步UI...")
            
            validation_result = await self._validate_verification_code_input(verification_code)
            
            if not validation_result['success']:
                self.logger.warning(f"⚠️ 验证码验证失败: {validation_result['reason']}")
                return False

            self.logger.info("✅ 验证码填写验证成功")

            # 步骤 4: 处理提交按钮
            submit_success = await self._handle_verification_submit()
            
            return submit_success

        except Exception as e:
            self.logger.error(f"❌ 单次验证码填写异常: {e}")
            return False

    async def _validate_verification_code_input(self, verification_code: str) -> dict:
        """
        验证验证码填写结果
        
        Args:
            verification_code: 验证码
            
        Returns:
            dict: 验证结果
        """
        try:
            hidden_input_selector = "input[name='otp']"
            
            js_validation_script = f'''
            () => {{
                const otpInput = document.querySelector("{hidden_input_selector}");
                const displayBoxes = document.querySelectorAll('[data-testid="code-input"]');
                const code = '{verification_code}';

                let hiddenFieldValue = otpInput ? otpInput.value : '';
                let success = hiddenFieldValue === code;

                // 如果键盘输入未能更新隐藏字段，则强制更新 (保险措施)
                if (!success && otpInput) {{
                    otpInput.value = code;
                    otpInput.dispatchEvent(new Event('input', {{ bubbles: true }}));
                    otpInput.dispatchEvent(new Event('change', {{ bubbles: true }}));
                    success = true;
                    hiddenFieldValue = code;
                }}

                // 同步可见的显示框
                if (displayBoxes.length === 6 && code.length === 6) {{
                    for (let i = 0; i < 6; i++) {{
                        const box = displayBoxes[i];
                        const char = code[i];
                        if (box.textContent !== char) {{
                            box.textContent = char;
                        }}
                    }}
                }}

                return {{
                    success: success,
                    hiddenFieldValue: hiddenFieldValue,
                    displayBoxCount: displayBoxes.length,
                    hiddenInputExists: !!otpInput
                }};
            }}
            '''
            
            result = await self.page.evaluate(js_validation_script)
            self.logger.info(f"验证结果: {result}")

            if result['success']:
                return {'success': True, 'reason': 'validation_passed'}
            else:
                return {
                    'success': False, 
                    'reason': f"hidden_field_mismatch: expected={verification_code}, actual={result.get('hiddenFieldValue')}"
                }
                
        except Exception as e:
            return {'success': False, 'reason': f'validation_error: {e}'}

    async def _handle_verification_submit(self) -> bool:
        """
        处理验证码提交按钮
        
        Returns:
            bool: 提交是否成功
        """
        try:
            self.logger.info("🔍 处理验证码提交...")
            
            # 等待页面处理验证码
            await asyncio.sleep(2)

            # 优化的Continue按钮选择器
            continue_selectors = [
                'button:has-text("Continue")',
                'button[type="submit"]',
                'input[type="submit"]',
                '[data-testid*="continue"]',
                '.continue-btn',
                '.continue-button',
                'button:contains("Continue")'
            ]
            
            for selector in continue_selectors:
                try:
                    continue_button = await self.page.wait_for_selector(selector, timeout=2000)
                    if continue_button and await continue_button.is_visible():
                        self.logger.info(f"🔍 找到Continue按钮: {selector}")
                        
                        # 使用人类行为模拟点击
                        if self.behavior_simulator:
                            await self.behavior_simulator.human_click(self.page, selector)
                        else:
                            await continue_button.click()
                        
                        self.logger.info("✅ Continue按钮点击成功")
                        await asyncio.sleep(2)  # 等待页面响应
                        return True
                        
                except Exception as continue_error:
                    self.logger.debug(f"尝试选择器 {selector} 失败: {continue_error}")
                    continue
            
            # 如果没有找到Continue按钮，可能是自动提交
            self.logger.info("ℹ️ 未找到Continue按钮，验证码可能已自动提交")
            await asyncio.sleep(3)  # 等待自动提交处理
            return True

        except Exception as e:
            self.logger.error(f"❌ 处理验证码提交失败: {e}")
            return False

    async def _cleanup(self):
        """清理资源"""
        try:
            if self.playwright:
                await self.playwright.stop()

            if self.auto_launch:
                self.cdp_manager.cleanup()

            self.logger.info("✅ 资源清理完成")
        except Exception as e:
            self.logger.warning(f"⚠️ 清理资源时出错: {e}")

    async def _handle_get_them_both_button(self):
        """
        处理 GET THEM BOTH 按钮点击 - 增强版
        """
        try:
            self.logger.info("🔍 查找 GET THEM BOTH 按钮...")
            
            # 检查是否在welcome页面，使用快速路径
            current_url = self.page.url
            if "welcome" in current_url:
                self.logger.info("🚀 检测到welcome页面，使用快速路径")
                # Welcome页面直接进入按钮搜索，跳过复杂等待和扫描
                await asyncio.sleep(1)  # 简单等待页面稳定
            else:
                # 其他页面使用完整的智能等待
                self.logger.info("⏳ 智能等待页面完全稳定...")
                if self.intelligent_waiting_manager:
                    await self.intelligent_waiting_manager.intelligent_wait(BUTTON_SEARCH_CONTEXT)
                else:
                    await asyncio.sleep(5)  # 降级方案
                await self.wait_for_page_navigation("密码页面观察等待后")
                
                # 首先检查页面内容，看看是否有相关文本
                self.logger.info("🔍 扫描页面内容...")
                page_content = await self.page.content()
                
                # 检查是否有包含"both"的文本
                if "both" in page_content.lower() or "bundle" in page_content.lower():
                    self.logger.info("✅ 页面包含相关内容，继续查找按钮")
                else:
                    self.logger.warning("⚠️ 页面未包含预期内容，可能不在正确页面")
            
            # 高优先级选择器 - 基于Shadow DOM检测结果优化（验证Hulu不使用Shadow DOM）
            priority_selectors = [
                # 第1层: 最精确的目标选择器（Shadow DOM检测验证有效）
                'button:has-text("GET THEM BOTH")',  # 最精确，只找到1个目标元素
                'button.button--cta.DualCtaMasthead__input-cta.--green-with-black-text',  # 完整CSS类名
                
                # 第2层: 高可靠性类选择器（Shadow DOM检测验证有效）
                'button.button--cta.DualCtaMasthead__input-cta',  # 找到2个元素，包含目标
                'button[class*="DualCtaMasthead__input-cta"]',     # 找到2个元素，包含目标
                
                # 第3层: 通用降级选择器（基于调试结果）
                '.DualCtaMasthead__input-cta',  # 找到2个元素，类选择器降级
                'button.button--cta:has-text("GET THEM BOTH")',  # 双重验证
                'button.button--cta'  # 最后的类选择器降级
            ]
            
            # 备用选择器 - 基于检测结果优化（移除无效Shadow DOM选择器）
            fallback_selectors = [
                # 文本部分匹配选择器
                'button:has-text("BOTH")',  # 部分文本匹配
                'button:has-text("GET")',   # 部分文本匹配
                
                # 属性选择器组合
                '[class*="button"]:has-text("GET THEM BOTH")',
                '[class*="cta"]:has-text("GET THEM BOTH")',
                
                # 兼容其他可能的订阅按钮（基于实际检测）
                'button:has-text("START YOUR FREE TRIAL")',
                'button:has-text("GET ALL THREE")',
                
                # 大小写变体（容错处理）
                'button:has-text("Get them both")',
                'button:has-text("get them both")',
                'a:has-text("GET THEM BOTH")',  # 链接元素降级
                
                # 最后的通用降级
                '[role="button"]:has-text("GET THEM BOTH")',
                '*:has-text("GET THEM BOTH")'
            ]
            
            button_element = None
            search_context = {
                "operation": "button_search",
                "page_type": "welcome",
                "timestamp": time.time()
            }
            
            # 🛡️ Stage 3: 执行reCAPTCHA v3风险评估 (welcome页面跳过)
            if "welcome" not in current_url:
                risk_assessment = await self.assess_recaptcha_v3_risk("button_search")
                # 应用风险缓解策略
                await self.apply_risk_mitigation_strategies(risk_assessment)
            else:
                self.logger.info("🚀 Welcome页面跳过reCAPTCHA风险评估，直接进入按钮搜索")
            
            # Welcome页面使用简化搜索策略
            if "welcome" in current_url:
                self.logger.info("🚀 Welcome页面使用简化直接搜索策略")
                # 直接使用传统串行搜索，跳过复杂的智能搜索
                button_element = await self._traditional_button_search(
                    priority_selectors + fallback_selectors
                )
            else:
                # 其他页面使用完整的智能搜索流程
                # 🆕 阶段1：并行搜索高优先级选择器（缓存加速）
                if self.caching_system:
                    self.logger.info("🚀 并行搜索高优先级按钮选择器（缓存加速）...")
                    priority_results = await self.caching_system.intelligent_element_search(
                        self.page,
                        priority_selectors,
                        context=search_context,
                        parallel=True,
                        cache_ttl=self.cache_config["button_search_ttl"]
                    )
                    
                    # 简化搜索策略：直接使用简单按钮搜索
                    self.logger.info("🔍 执行简化按钮搜索...")
                    button_found = await self._simple_button_search()
                    
                    if priority_results and priority_results.get("element"):
                        button_element = priority_results["element"]
                        self.logger.info(f"🎯 人类行为并行搜索成功: {priority_results.get('selector')} (批次 {priority_results.get('batch_index', 0) + 1})")
                    else:
                        self.logger.warning("⚠️ 人类行为并行搜索未找到按钮")
                
                # 🆕 阶段4：最后降级方案 - 使用传统串行搜索
                if not button_element:
                    self.logger.warning("⚠️ 所有智能搜索失败，使用传统串行搜索...")
                    button_element = await self._traditional_button_search(
                        priority_selectors + fallback_selectors
                    )
            
            # 执行按钮点击
            if button_element:
                await self._execute_button_click(button_element)
            else:
                await self._handle_button_not_found()
                
        except Exception as e:
            self.logger.error(f"❌ 优化按钮处理失败: {e}")
            await self._handle_button_search_error(e)

    async def _validate_button_element(self, element) -> bool:
        """验证按钮元素的有效性"""
        try:
            if not element:
                return False
                
            # 检查元素是否附加到DOM (Playwright兼容性处理)
            try:
                is_attached = await element.is_attached()
                if not is_attached:
                    return False
            except AttributeError:
                # 较旧版本的Playwright可能没有is_attached方法，跳过检查
                pass
            
            # 检查元素是否可见
            is_visible = await element.is_visible()
            if not is_visible:
                return False
            
            # 检查元素是否可点击
            is_enabled = await element.is_enabled()
            if not is_enabled:
                return False
            
            # 检查元素文本内容（如果是文本匹配的选择器）
            text_content = await element.text_content()
            if text_content and "GET THEM BOTH" in text_content.upper():
                return True
            
            # 检查元素类名（如果是CSS类选择器）
            class_name = await element.get_attribute("class")
            if class_name and any(cls in class_name for cls in ["DualCtaMasthead", "button--cta"]):
                return True
                
            return True  # 基础验证通过
            
        except Exception as e:
            self.logger.debug(f"按钮验证异常: {e}")
            return False

    async def _execute_button_click(self, button_element):
        """执行按钮点击操作"""
        try:
            # Welcome页面跳过滚动，其他页面正常滚动
            current_url = self.page.url
            if "welcome" not in current_url:
                # 滚动到按钮位置
                await button_element.scroll_into_view_if_needed()
                await asyncio.sleep(0.5)
            else:
                self.logger.info("🚀 Welcome页面跳过滚动，直接点击")
            
            # 获取按钮信息用于日志
            button_text = await button_element.text_content()
            button_class = await button_element.get_attribute("class")
            
            self.logger.info(f"🎯 准备点击按钮: '{button_text}' (class: {button_class})")
            
            # 简化的点击逻辑 - 修复过度复杂的嵌套结构
            try:
                # Welcome页面优先使用JavaScript点击避免滚动
                if "welcome" in current_url:
                    try:
                        selector = await self._get_element_selector(button_element)
                        await self.page.evaluate(f"""
                            const element = document.querySelector('{selector}');
                            if (element) {{
                                element.click();
                                console.log('Welcome页面JavaScript点击执行');
                            }}
                        """)
                        self.logger.info("✅ Welcome页面JavaScript点击成功（避免滚动且触发事件）")
                    except Exception as js_error:
                        self.logger.warning(f"⚠️ JavaScript点击失败: {js_error}，回退到普通点击")
                        await button_element.click()
                        self.logger.info("✅ Welcome页面回退到普通点击")
                        
                # 其他页面使用行为模拟或直接点击
                elif self.behavior_simulator:
                    selector = await self._get_element_selector(button_element)
                    await self.behavior_simulator.human_click(self.page, selector)
                    self.logger.info("✅ 人类行为模拟点击成功")
                    
                # 降级到直接点击
                else:
                    await button_element.click()
                    self.logger.info("✅ 直接点击成功")
            except Exception as click_error:
                self.logger.warning(f"⚠️ 主要点击方法失败: {click_error}，尝试回退方案")
                # Welcome页面回退也使用坐标点击
                if "welcome" in current_url:
                    try:
                        box = await button_element.bounding_box()
                        if box:
                            click_x = box['x'] + box['width'] / 2
                            click_y = box['y'] + box['height'] / 2
                            await self.page.mouse.click(click_x, click_y)
                            self.logger.info("✅ Welcome页面回退坐标点击成功")
                        else:
                            await button_element.click()
                            self.logger.info("✅ Welcome页面最终回退到普通点击")
                    except Exception as coord_error:
                        self.logger.warning(f"⚠️ 坐标点击也失败: {coord_error}，使用普通点击")
                        await button_element.click()
                        self.logger.info("✅ Welcome页面最终普通点击成功")
                else:
                    await button_element.click()
                    self.logger.info("✅ 回退直接点击成功")
            
            # 等待页面响应
            await asyncio.sleep(2)
            await self.wait_for_page_navigation("登录提交后页面加载", timeout=5000)
            
            # 检查页面变化
            new_url = self.page.url
            self.logger.info(f"🌐 点击后页面URL: {new_url}")
            
            # 🆕 更新缓存状态
            if self.caching_system:
                await self.caching_system.setup_page_caching(self.page)
            
        except Exception as e:
            self.logger.error(f"❌ 按钮点击执行失败: {e}")
            raise

    async def _get_element_selector(self, element) -> str:
        """获取元素的CSS选择器"""
        try:
            selector = await element.evaluate("""
                element => {
                    // 优先使用ID
                    if (element.id) {
                        return '#' + element.id;
                    }
                    
                    // 其次使用类名
                    if (element.className) {
                        const classes = element.className.split(' ').filter(c => c.length > 0);
                        if (classes.length > 0) {
                            return '.' + classes.join('.');
                        }
                    }
                    
                    // 最后使用标签名
                    return element.tagName.toLowerCase();
                }
            """)
            return selector or "button"
        except:
            return "button"

    async def _traditional_button_search(self, selectors):
        """传统的串行按钮搜索（降级方案）"""
        for selector in selectors:
            try:
                self.logger.debug(f"尝试选择器: {selector}")
                element = await self.page.wait_for_selector(selector, timeout=3000)
                if element and await self._validate_button_element(element):
                    self.logger.info(f"✅ 传统搜索找到按钮: {selector}")
                    return element
            except Exception as e:
                self.logger.debug(f"选择器 {selector} 失败: {e}")
                continue
        return None

    async def _handle_button_not_found(self):
        """处理按钮未找到的情况"""
        self.logger.warning("⚠️ 所有方法都未找到 GET THEM BOTH 按钮")
        
        # 尝试坐标点击备选方案
        try:
            self.logger.info("🎯 尝试坐标点击备选方案...")
            # 基于调试结果的准确坐标：x:297, y:338.8203125, width:335, height:40
            button_x, button_y = 297 + 167.5, 338.8203125 + 20  # 中心点坐标
            
            self.logger.info(f"🎯 使用调试验证的按钮坐标: ({button_x}, {button_y})")
            await self.page.mouse.move(button_x, button_y)
            await asyncio.sleep(0.5)
            await self.page.mouse.click(button_x, button_y)
            
            await asyncio.sleep(3)
            new_url = self.page.url
            
            if "signup" in new_url or new_url != "https://www.hulu.com/welcome":
                self.logger.info("✅ 坐标点击成功")
                return
        except Exception as coord_error:
            self.logger.error(f"❌ 坐标点击失败: {coord_error}")
        
        # 保存调试信息
        await self._save_debug_info("button_not_found")

    async def _handle_button_search_error(self, error: Exception):
        """处理按钮搜索错误"""
        self.logger.error(f"❌ 按钮搜索过程出错: {error}")
        await self._save_debug_info("button_search_error")

    async def _save_debug_info(self, debug_type: str):
        """保存调试信息"""
        try:
            timestamp = int(time.time())
            
            # 保存截图
            screenshot_path = os.path.join(
                self.screenshots_dir, 
                f"{debug_type}_{timestamp}.png"
            )
            await self.page.screenshot(path=screenshot_path, full_page=True)
            
            # 保存页面HTML
            html_path = os.path.join(
                self.screenshots_dir, 
                f"{debug_type}_{timestamp}.html"
            )
            html_content = await self.page.content()
            with open(html_path, "w", encoding="utf-8") as f:
                f.write(html_content)
            
            # 🆕 保存缓存状态
            if self.caching_system:
                cache_stats = self.caching_system.get_comprehensive_stats()
                stats_path = os.path.join(
                    self.screenshots_dir, 
                    f"{debug_type}_cache_stats_{timestamp}.json"
                )
                import json
                with open(stats_path, "w", encoding="utf-8") as f:
                    json.dump(cache_stats, f, indent=2, default=str)
                
                self.logger.info(f"🗄️ 缓存统计已保存: {stats_path}")
            
            self.logger.info(f"📸 调试信息已保存: {screenshot_path}, {html_path}")
            
        except Exception as save_error:
            self.logger.warning(f"⚠️ 保存调试信息失败: {save_error}")

    # 性能监控和缓存管理方法
    async def get_caching_performance_report(self) -> Dict[str, Any]:
        """获取缓存性能报告"""
        if not self.caching_system:
            return {"status": "caching_system_not_initialized"}
        
        stats = self.caching_system.get_comprehensive_stats()
        
        # 计算性能提升估算
        element_cache_stats = stats.get("element_cache", {})
        hit_rate = element_cache_stats.get("hit_rate", 0)
        
        # 估算性能提升（基于缓存命中率）
        estimated_time_savings = hit_rate * 0.7  # 缓存命中平均节省70%的时间
        
        return {
            "cache_statistics": stats,
            "performance_estimation": {
                "hit_rate": hit_rate,
                "estimated_time_savings_percent": estimated_time_savings * 100,
                "cache_effectiveness": "高" if hit_rate > 0.6 else "中" if hit_rate > 0.3 else "低"
            },
            "recommendations": self._generate_cache_recommendations(stats)
        }

    def _generate_cache_recommendations(self, stats: Dict[str, Any]) -> List[str]:
        """生成缓存优化建议"""
        recommendations = []
        
        element_stats = stats.get("element_cache", {})
        hit_rate = element_stats.get("hit_rate", 0)
        utilization = element_stats.get("utilization", 0)
        
        if hit_rate < 0.3:
            recommendations.append("缓存命中率较低，建议增加TTL时间或优化缓存策略")
        
        if utilization > 0.9:
            recommendations.append("缓存利用率过高，建议增加缓存容量")
        
        if utilization < 0.3:
            recommendations.append("缓存利用率较低，可以考虑减少缓存容量")
        
        if not recommendations:
            recommendations.append("缓存系统运行良好，无需调整")
        
        return recommendations
    
    async def assess_recaptcha_v3_risk(self, operation_context: str = "button_search") -> Dict[str, Any]:
        """
        reCAPTCHA v3检测风险评估 - Stage 3核心功能
        
        分析当前操作的reCAPTCHA v3检测风险并提供缓解建议
        
        Args:
            operation_context: 操作上下文
            
        Returns:
            包含风险评估和建议的字典
        """
        try:
            self.logger.info(f"🛡️ 开始reCAPTCHA v3风险评估 - 上下文: {operation_context}")
            
            risk_factors = {
                "dom_query_pattern": 0,      # DOM查询模式风险
                "timing_pattern": 0,         # 时序模式风险  
                "behavior_signature": 0,     # 行为特征风险
                "network_pattern": 0         # 网络模式风险
            }
            
            # 🔍 分析DOM查询模式
            if hasattr(self, '_recent_dom_queries') and len(self._recent_dom_queries) > 5:
                # 检查是否有快速连续的DOM查询
                query_intervals = []
                for i in range(1, len(self._recent_dom_queries)):
                    interval = self._recent_dom_queries[i] - self._recent_dom_queries[i-1]
                    query_intervals.append(interval)
                
                avg_interval = sum(query_intervals) / len(query_intervals) if query_intervals else 1000
                if avg_interval < 100:  # 小于100ms的查询间隔可能被检测
                    risk_factors["dom_query_pattern"] = min(8, len([i for i in query_intervals if i < 100]))
            
            # ⏱️ 分析时序模式
            current_time = time.time()
            if hasattr(self, '_operation_start_time'):
                operation_duration = current_time - self._operation_start_time
                if operation_duration < 2:  # 过快的操作可能被检测
                    risk_factors["timing_pattern"] = 6
                elif operation_duration < 5:
                    risk_factors["timing_pattern"] = 3
            
            # 🎭 分析行为特征
            # 移除了parallel_search_manager的风险评估 - 已删除的未使用组件
            
            if not self.intelligent_waiting_manager:
                risk_factors["behavior_signature"] += 3  # 使用固定等待增加风险
            
            # 🌐 分析网络模式
            if hasattr(self, '_network_requests') and len(self._network_requests) > 10:
                risk_factors["network_pattern"] = 2  # 过多网络请求可能增加风险
            
            # 📊 计算总体风险评分
            total_risk = sum(risk_factors.values())
            
            # 🎯 风险等级评估
            if total_risk <= 3:
                risk_level = "低"
                risk_color = "🟢"
            elif total_risk <= 7:
                risk_level = "中"
                risk_color = "🟡"
            else:
                risk_level = "高"
                risk_color = "🔴"
            
            # 💡 生成缓解建议
            mitigation_strategies = []
            
            if risk_factors["dom_query_pattern"] > 5:
                mitigation_strategies.append("启用人类行为并行搜索以分散DOM查询")
            
            if risk_factors["timing_pattern"] > 3:
                mitigation_strategies.append("增加操作前的智能等待时间")
            
            if risk_factors["behavior_signature"] > 5:
                mitigation_strategies.append("启用完整的人类行为模拟系统")
            
            if not mitigation_strategies:
                mitigation_strategies.append("当前操作风险较低，保持现有策略")
            
            assessment = {
                "risk_score": total_risk,
                "risk_level": risk_level,
                "risk_color": risk_color,
                "risk_factors": risk_factors,
                "mitigation_strategies": mitigation_strategies,
                "operation_context": operation_context,
                "assessment_time": current_time,
                "recommended_delay": max(2, total_risk * 0.3)  # 推荐延迟时间
            }
            
            self.logger.info(f"{risk_color} reCAPTCHA v3风险评估: {risk_level} (评分: {total_risk}/40)")
            
            return assessment
            
        except Exception as e:
            self.logger.error(f"❌ reCAPTCHA v3风险评估失败: {e}")
            return {
                "risk_score": 10,
                "risk_level": "高",
                "risk_color": "🔴",
                "error": str(e),
                "mitigation_strategies": ["启用所有反检测功能"],
                "operation_context": operation_context
            }
    
    async def apply_risk_mitigation_strategies(self, risk_assessment: Dict[str, Any]) -> bool:
        """
        应用风险缓解策略
        
        Args:
            risk_assessment: 风险评估结果
            
        Returns:
            是否成功应用缓解策略
        """
        try:
            strategies = risk_assessment.get("mitigation_strategies", [])
            risk_score = risk_assessment.get("risk_score", 0)
            
            self.logger.info(f"🛡️ 应用风险缓解策略 (风险评分: {risk_score})")
            
            applied_strategies = []
            
            for strategy in strategies:
                # 移除了人类行为并行搜索策略 - parallel_search_manager已删除
                
                if "智能等待" in strategy and self.intelligent_waiting_manager:
                    # 增加额外的等待时间
                    additional_delay = risk_assessment.get("recommended_delay", 2)
                    await asyncio.sleep(additional_delay)
                    applied_strategies.append(f"✅ 增加智能等待 {additional_delay:.1f}秒")
                
                elif "人类行为模拟" in strategy and self.behavior_simulator:
                    # 应用额外的行为模拟
                    await asyncio.sleep(random.uniform(0.5, 1.5))
                    applied_strategies.append("✅ 应用额外人类行为模拟")
                
                else:
                    applied_strategies.append(f"⚠️ {strategy} (需要手动配置)")
            
            self.logger.info("📋 缓解策略应用结果:")
            for strategy in applied_strategies:
                self.logger.info(f"   {strategy}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 应用风险缓解策略失败: {e}")
            return False


# 已移除的Layer 4紧急坐标点击方法 - 过度设计，依赖已删除的组件

    async def _simple_button_search(self) -> bool:
        """
        简化的按钮搜索 - 替代原Layer 3复杂扫描
        
        直接使用CSS选择器查找按钮，替代过度设计的渐进式DOM扫描
        
        Returns:
            bool: 搜索是否成功
        """
        try:
            self.logger.info("🔍 简化按钮搜索启动")
            
            # 简单直接的按钮查找选择器 - 基于Shadow DOM检测结果优化
            simple_selectors = [
                'button:has-text("GET THEM BOTH")',  # 最精确的目标选择器
                'button:has-text("BOTH")',
                'button[class*="DualCtaMasthead__input-cta"]',  # 验证有效的CSS类选择器
                'button.button--cta'
            ]
            
            for selector in simple_selectors:
                try:
                    # 直接查找按钮 - 无需复杂的分区域扫描
                    button_element = await self.page.wait_for_selector(selector, timeout=10000)
                    if button_element:
                        button_text = await button_element.text_content()
                        self.logger.info(f"✅ 找到按钮: '{button_text}' (选择器: {selector})")
                        
                        # 执行点击 - 保留Welcome页面优化逻辑
                        await self._execute_button_click(button_element)
                        self.logger.info("✅ 简化按钮点击成功")
                        
                        # 等待页面响应
                        await asyncio.sleep(2)
                        await self.wait_for_page_navigation("按钮点击后")
                        
                        return True
                        
                except Exception as selector_error:
                    self.logger.debug(f"选择器 {selector} 未找到按钮: {selector_error}")
                    continue
            
            self.logger.warning("⚠️ 简化按钮搜索未找到合适的按钮")
            return False
                
        except Exception as e:
            self.logger.error(f"❌ 简化按钮搜索失败: {e}")
            return False
    
    # 移除了复杂的Layer 3调试方法 - 简化架构不再需要复杂的扫描结果保存

    async def _simple_element_search(self, selectors: list, search_context: str, coordinates_hint: Optional[Tuple[float, float]] = None):
        """
        简化的元素搜索 - 保留Layer 0动态宿主发现，移除复杂架构
        
        使用简化的元素搜索逻辑，集成Layer 0动态宿主发现的价值功能
        
        Args:
            selectors: 选择器模板列表 (支持{HOST}占位符)
            search_context: 搜索上下文 (如 "email_field", "password_field", "login_button")
            coordinates_hint: 坐标提示 (可选)
            
        Returns:
            found_element: 找到的元素，如果没找到则返回None
        """
        # 添加监控钩子
        start_time = time.time()
        success = False
        timeout_param = 5000  # 默认超时
        
        try:
            result = await self._simple_element_search_core(selectors, search_context, coordinates_hint)
            success = result is not None
            return result
            
        except Exception as e:
            success = False
            raise
            
        finally:
            # 记录监控数据到Layer 0检测器 (如果存在)
            if hasattr(self, 'dynamic_host_detector') and self.dynamic_host_detector and hasattr(self.dynamic_host_detector, 'monitor'):
                duration_ms = (time.time() - start_time) * 1000
                params = {
                    'timeout': timeout_param, 
                    'search_context': search_context,
                    'selector_count': len(selectors)
                }
                self.dynamic_host_detector.monitor.record("element_location", duration_ms, success, params)

    async def _simple_element_search_core(self, selectors: list, search_context: str, coordinates_hint: Optional[Tuple[float, float]] = None):
        """
        简化的元素搜索核心逻辑 - 保留Layer 0动态宿主发现，移除复杂架构
        """
        try:
            self.logger.info(f"🔍 简化元素搜索启动 ({search_context})")
            
            # Layer 0: 保留动态宿主发现 - 这是有价值的功能
            if self.dynamic_host_detector:
                self.logger.info("🔍 Layer 0: 开始动态宿主发现...")
                
                # 检测登录上下文
                login_context = await self.dynamic_host_detector.detect_login_context()
                
                # 展开选择器模板
                expanded_selectors = self.dynamic_host_detector.expand_selectors(
                    selectors, login_context.host_tag
                )
                
                self.logger.info(f"📋 选择器展开: {len(selectors)}个模板 → {len(expanded_selectors)}个选择器")
                self.logger.info(f"🏷️ 发现宿主: {login_context.host_tag or 'None'} ({login_context.template_type})")
                
                # 如果发现iframe，使用iframe的frame进行搜索
                search_frame = login_context.frame if login_context.has_iframe else self.page
                if login_context.has_iframe and login_context.frame != self.page:
                    self.logger.info("🖼️ 使用iframe上下文进行搜索")
                
                actual_selectors = expanded_selectors
            else:
                self.logger.warning("⚠️ Layer 0动态宿主发现器未初始化，使用原始选择器")
                actual_selectors = selectors
                search_frame = self.page
            
            # 简化的直接搜索 - 替代复杂的Layer 1-4架构
            for selector in actual_selectors:
                try:
                    element = await search_frame.wait_for_selector(selector, timeout=5000)
                    if element:
                        self.logger.info(f"✅ 找到元素: {selector}")
                        return element
                except Exception as selector_error:
                    self.logger.debug(f"选择器 {selector} 未找到: {selector_error}")
                    continue
            
            return None
                
        except Exception as e:
            self.logger.error(f"❌ 简化元素搜索异常: {e}")
            return None

    async def _layer2_smart_concurrent_search(self, selectors: list, search_type: str, timeout: int = 5000):
        """
        Layer 2: 智能并发控制搜索 (兼容性保留)
        
        使用分批并发 + 自适应调整 + 人类思考延迟
        解决reCAPTCHA v3并发检测问题
        
        建议使用: _simple_element_search() 获得更好的性能
        
        Args:
            selectors: 选择器列表
            search_type: 搜索类型描述
            timeout: 超时时间(ms)
            
        Returns:
            found_element: 找到的元素，如果没找到则返回None
        """
        # 使用Layer 1统一搜索，但映射到Layer 2的接口
        return await self._simple_element_search(selectors, search_type)

    async def get_performance_report(self) -> Dict[str, Any]:
        """
        获取简化的性能报告
        
        Returns:
            Dict: 包含基本性能统计
        """
        try:
            return {
                "timestamp": time.time(),
                "status": "简化架构运行中",
                "layer0_host_detector": "已启用" if self.dynamic_host_detector else "未启用",
                "anti_detection": "已启用" if self.anti_detection_manager else "未启用",
                "behavior_simulator": "已启用" if self.behavior_simulator else "未启用"
            }
            
        except Exception as e:
            self.logger.error(f"❌ 获取性能报告失败: {e}")
            return {"error": str(e)}

    # 移除了复杂的架构模式切换 - 简化架构使用固定的优化模式

    def _verify_config_applied_legacy(self, bot) -> bool:
        """
        验证隐身配置是否正确应用
        
        Args:
            bot: HuluBotFacade实例
            
        Returns:
            bool: 配置是否正确应用
        """
        try:
            # 检查关键配置项
            checks = []
            
            # 检查反检测服务
            if hasattr(bot, 'anti_detection_service'):
                checks.append(True)
                self.logger.debug("✓ 反检测服务存在")
            
            # 检查人类行为模拟器
            if hasattr(bot, 'human_behavior'):
                checks.append(True)
                self.logger.debug("✓ 人类行为模拟器存在")
            
            # 检查生物特征模拟器
            if hasattr(bot, 'biometric_sim'):
                checks.append(True)
                self.logger.debug("✓ 生物特征模拟器存在")
            
            success_rate = sum(checks) / len(checks) if checks else 0
            self.logger.info(f"📊 配置验证成功率: {success_rate:.2%}")
            
            return success_rate >= 0.8  # 80%以上成功率
            
        except Exception as e:
            self.logger.error(f"❌ 配置验证时出错: {str(e)}")
            return False
    
    def _analyze_login_result(self, result: Dict[str, Any]):
        """
        分析登录结果并输出详细信息
        
        Args:
            result: 登录结果字典
        """
        self.logger.info("📊 登录结果分析:")
        self.logger.info("=" * 50)
        
        # 基本状态
        login_status = result.get("login_status", "unknown")
        self.logger.info(f"🔐 登录状态: {login_status}")
        
        if login_status == "success":
            self.logger.info("✅ 登录成功!")
            
            # 显示账户信息
            if "email" in result:
                self.logger.info(f"📧 账户邮箱: {result['email']}")
            
            # 显示会话信息
            if "session_info" in result:
                session = result["session_info"]
                self.logger.info(f"🕐 会话时长: {session.get('duration', 'N/A')}")
                self.logger.info(f"🌐 当前URL: {session.get('current_url', 'N/A')}")
        
        elif login_status == "failed":
            self.logger.warning("⚠️ 登录失败")
            if "message" in result:
                self.logger.warning(f"📝 失败原因: {result['message']}")
        
        elif login_status == "error":
            self.logger.error("❌ 登录过程中发生错误")
            if "message" in result:
                self.logger.error(f"📝 错误信息: {result['message']}")
        
        # 隐身模式特有信息
        if result.get("stealth_mode"):
            self.logger.info("🛡️ 隐身模式: 已启用")
        
        # 行为模拟统计
        if "behavior_stats" in result:
            stats = result["behavior_stats"]
            self.logger.info(f"⌨️ 平均打字速度: {stats.get('avg_wpm', 'N/A')} WPM")
            self.logger.info(f"🎯 鼠标轨迹点数: {stats.get('mouse_points', 'N/A')}")
            self.logger.info(f"🔄 错误修正次数: {stats.get('error_corrections', 'N/A')}")
        
        self.logger.info("=" * 50)
    
    def get_system_info(self) -> Dict[str, Any]:
        """
        获取系统信息和配置详情
        
        Returns:
            Dict[str, Any]: 系统信息
        """
        return {
            "system_version": "1.0.0",
            "architecture": "4层分层架构",
            "anti_detection": {
                "engine": "MediaCrawler",
                "stealth_script": "stealth.min.js (180KB)",
                "cdp_mode": self.config.enable_cdp_mode,
                "fingerprint_masking": self.config.enable_fingerprint_masking,
                "behavior_simulation": self.config.enable_behavior_simulation,
                "typing_wpm_range": self.config.typing_wpm_range,
                "typing_error_rate": self.config.typing_error_rate
            },
            "browser_config": {
                "cdp_mode": True,
                "auto_launch": self.auto_launch,
                "viewport": f"{self.config.viewport_width}x{self.config.viewport_height}",
                "timeout": self.config.default_timeout,
                "user_agents": len(self.config.desktop_user_agents)
            }
        }

    async def _parse_billing_data(self, data_index: int = 0) -> Tuple[Dict[str, str], int]:
        """
        解析计费数据文件 data/data_input/fake_data.txt
        
        数据格式: 卡号 过期月 过期年 CVC 邮编 姓名 地址 城市 州 其他
        例如: 377234925403018  08  27  1799  10023  Joel B Rudin  201 West 70th Street Apartment 34K  New York   NY  H1
        
        Args:
            data_index (int): 要解析的数据行索引（从0开始）
        
        Returns:
            Tuple[Dict[str, str], int]: (解析后的计费数据, 总数据行数)
        """
        try:
            fake_data_path = os.path.join(os.getcwd(), "data", "data_input", "fake_data.txt")
            self.logger.info(f"🔍 读取计费数据文件: {fake_data_path}")
            
            if not os.path.exists(fake_data_path):
                raise FileNotFoundError(f"计费数据文件不存在: {fake_data_path}")
            
            with open(fake_data_path, 'r', encoding='utf-8') as f:
                lines = [line.strip() for line in f.readlines() if line.strip()]  # 过滤空行
            
            if not lines:
                raise ValueError("计费数据文件为空")
                
            total_lines = len(lines)
            self.logger.info(f"📊 数据文件包含 {total_lines} 行数据")
            
            # 检查索引是否有效
            if data_index < 0 or data_index >= total_lines:
                raise IndexError(f"数据索引 {data_index} 超出范围，可用范围: 0-{total_lines-1}")
            
            # 使用指定索引的数据
            target_line = lines[data_index]
            self.logger.info(f"📄 解析数据行 {data_index}: {target_line}")
            
            # 按空格分割数据
            parts = target_line.split()
            
            if len(parts) < 10:
                raise ValueError(f"数据格式不正确，期望至少10个字段，实际获得{len(parts)}个")
            
            # 解析各个字段
            card_number = parts[0]
            exp_month = parts[1]
            exp_year = parts[2]  
            cvv = parts[3]
            zip_code = parts[4]
            
            # 姓名可能包含多个部分
            name_parts = []
            address_parts = []
            
            # 从索引5开始，寻找姓名结束位置（通常是地址开始的数字）
            name_end_index = 5
            for i in range(5, len(parts)):
                if parts[i].isdigit():  # 地址通常以数字开头
                    break
                name_parts.append(parts[i])
                name_end_index = i + 1
            
            cardholder_name = " ".join(name_parts)
            
            # 剩余部分是地址信息
            address_info = " ".join(parts[name_end_index:])
            
            billing_data = {
                "card_number": card_number,
                "expiration_month": exp_month,
                "expiration_year": exp_year,
                "cvv": cvv,
                "zip_code": zip_code,
                "cardholder_name": cardholder_name,
                "address": address_info
            }
            
            self.logger.info("✅ 计费数据解析成功:")
            self.logger.info(f"   🔢 索引: {data_index}/{total_lines-1}")
            self.logger.info(f"   💳 卡号: {card_number[:4]}****{card_number[-4:]}")
            self.logger.info(f"   📅 过期: {exp_month}/{exp_year}")
            self.logger.info(f"   🔐 CVV: ***")
            self.logger.info(f"   📮 邮编: {zip_code}")
            self.logger.info(f"   👤 姓名: {cardholder_name}")
            
            return billing_data, total_lines
            
        except Exception as e:
            self.logger.error(f"❌ 计费数据解析失败: {e}")
            raise

    async def _clear_form_fields(self, field_selectors: Dict[str, List[str]]):
        """
        清空所有表单字段
        
        Args:
            field_selectors: 表单字段选择器映射
        """
        try:
            self.logger.info("🧹 清空表单字段...")
            cleared_count = 0
            
            for field_name, selectors in field_selectors.items():
                for selector in selectors:
                    try:
                        element = await self.page.wait_for_selector(selector, timeout=3000)
                        if element and await element.is_visible():
                            await element.click()
                            await element.fill("")  # 清空字段
                            self.logger.debug(f"🧹 已清空字段: {field_name}")
                            cleared_count += 1
                            break
                    except:
                        continue
                        
            self.logger.info(f"✅ 成功清空 {cleared_count} 个表单字段")
            await asyncio.sleep(1)  # 等待DOM更新
            
        except Exception as e:
            self.logger.warning(f"⚠️ 清空表单字段时出错: {e}")

    async def _fill_form_with_data(self, billing_data: Dict[str, str], field_selectors: Dict[str, List[str]]) -> Tuple[List[str], List[str]]:
        """
        使用指定数据填充表单
        
        Args:
            billing_data: 要填充的计费数据
            field_selectors: 表单字段选择器映射
            
        Returns:
            Tuple[List[str], List[str]]: (成功填充的字段列表, 失败的字段列表)
        """
        filled_fields = []
        failed_fields = []
        
        for field_name, selectors in field_selectors.items():
            try:
                self.logger.info(f"🔍 查找字段: {field_name}")
                
                element = None
                successful_selector = None
                
                # 尝试每个选择器
                for selector in selectors:
                    try:
                        element = await self.page.wait_for_selector(selector, timeout=5000)
                        if element and await element.is_visible():
                            successful_selector = selector
                            self.logger.info(f"✅ 找到字段 {field_name}: {selector}")
                            break
                    except:
                        continue
                
                if not element:
                    self.logger.warning(f"⚠️ 未找到字段: {field_name}")
                    failed_fields.append(field_name)
                    continue
                
                # 获取要填充的值
                if field_name == "cardholder_name":
                    value = billing_data["cardholder_name"]
                elif field_name == "card_number":
                    value = billing_data["card_number"]
                elif field_name == "expiration":
                    # 合并到期月份和年份为 "MM / YY" 格式
                    month = billing_data["expiration_month"].zfill(2)  # 确保是2位数
                    year = billing_data["expiration_year"][-2:]  # 取年份后2位
                    value = f"{month} / {year}"
                elif field_name == "cvv":
                    value = billing_data["cvv"]
                elif field_name == "zip_code":
                    value = billing_data["zip_code"]
                else:
                    continue
                
                # 快速填充字段（不使用人类行为模拟）
                await element.click()
                await element.fill("")  # 清空字段
                await element.fill(value)
                
                # 验证填充是否成功
                filled_value = await element.input_value()
                if filled_value == value:
                    self.logger.info(f"✅ 成功填充 {field_name}: {value if field_name != 'cvv' else '***'}")
                    filled_fields.append(field_name)
                else:
                    self.logger.warning(f"⚠️ 填充验证失败 {field_name}: 期望 '{value}', 实际 '{filled_value}'")
                    failed_fields.append(field_name)
                
            except Exception as field_error:
                self.logger.error(f"❌ 填充字段 {field_name} 失败: {field_error}")
                failed_fields.append(field_name)
                
        return filled_fields, failed_fields

    async def _is_submit_button_enabled(self, submit_selectors: List[str]) -> bool:
        """
        检查提交按钮是否启用
        
        Args:
            submit_selectors: 提交按钮选择器列表
            
        Returns:
            bool: 是否有可用的提交按钮
        """
        for selector in submit_selectors:
            try:
                submit_button = await self.page.wait_for_selector(selector, timeout=3000)
                if submit_button and await submit_button.is_visible():
                    is_disabled = await submit_button.is_disabled()
                    if not is_disabled:
                        self.logger.info(f"✅ 找到可用的提交按钮: {selector}")
                        return True
                    else:
                        self.logger.debug(f"⚠️ 提交按钮禁用: {selector}")
            except:
                continue
        
        return False

    async def _wait_for_validation(self, max_wait_seconds: int = 5):
        """
        等待表单验证完成
        
        Args:
            max_wait_seconds: 最大等待时间（秒）
        """
        self.logger.info("⏳ 等待表单验证完成...")
        await asyncio.sleep(max_wait_seconds)

    async def _submit_form(self, data_index: int, submit_selectors: List[str]) -> Dict[str, Any]:
        """
        提交表单
        
        Args:
            data_index: 成功的数据索引
            submit_selectors: 提交按钮选择器列表
            
        Returns:
            Dict[str, Any]: 提交结果
        """
        for selector in submit_selectors:
            try:
                submit_button = await self.page.wait_for_selector(selector, timeout=5000)
                if submit_button and await submit_button.is_visible():
                    # 检查按钮是否启用
                    is_disabled = await submit_button.is_disabled()
                    if is_disabled:
                        self.logger.info(f"⏳ 按钮当前禁用，等待启用: {selector}")
                        # 等待按钮启用（最多等待10秒）
                        for _ in range(20):  # 10秒，每0.5秒检查一次
                            await asyncio.sleep(0.5)
                            is_disabled = await submit_button.is_disabled()
                            if not is_disabled:
                                break
                    
                    if not await submit_button.is_disabled():
                        self.logger.info(f"🎯 点击提交按钮: {selector}")
                        await submit_button.click()
                        
                        # 等待页面响应
                        await asyncio.sleep(3)
                        
                        return {
                            "status": "success",
                            "message": f"计费表单使用数据索引 {data_index} 提交成功",
                            "data_index": data_index,
                            "final_url": self.page.url
                        }
                    else:
                        self.logger.warning(f"⚠️ 按钮仍然禁用: {selector}")
            except Exception as e:
                self.logger.debug(f"提交按钮选择器失败 {selector}: {e}")
                continue
        
        return {
            "status": "failed",
            "message": "所有提交按钮都无法点击或禁用",
            "data_index": data_index
        }

    async def _debug_billing_page_structure(self):
        """
        调试计费页面DOM结构，分析表单字段和选择器
        """
        try:
            import json
            import time
            
            self.logger.info("🔍 开始分析计费页面DOM结构...")
            
            # 1. 保存页面HTML内容
            page_html = await self.page.content()
            timestamp = int(time.time())
            html_file = f"debug/billing_page_structure_{timestamp}.html"
            
            with open(html_file, 'w', encoding='utf-8') as f:
                f.write(page_html)
            self.logger.info(f"📄 页面HTML已保存: {html_file}")
            
            # 2. 截取页面截图
            screenshot_file = f"debug/billing_page_screenshot_{timestamp}.png"
            await self.page.screenshot(path=screenshot_file, full_page=True)
            self.logger.info(f"📸 页面截图已保存: {screenshot_file}")
            
            # 3. 使用JavaScript分析表单结构
            form_analysis_js = """
            () => {
                const result = {
                    forms: [],
                    inputs: [],
                    buttons: []
                };
                
                // 分析所有表单
                document.querySelectorAll('form').forEach((form, index) => {
                    result.forms.push({
                        index: index,
                        action: form.action || '',
                        method: form.method || 'GET',
                        id: form.id || '',
                        className: form.className || '',
                        innerHTML: form.innerHTML.substring(0, 200) + '...'
                    });
                });
                
                // 分析所有输入字段
                document.querySelectorAll('input').forEach((input, index) => {
                    result.inputs.push({
                        index: index,
                        type: input.type || 'text',
                        name: input.name || '',
                        id: input.id || '',
                        className: input.className || '',
                        placeholder: input.placeholder || '',
                        value: input.value || '',
                        required: input.required || false,
                        autocomplete: input.autocomplete || '',
                        tagName: input.tagName,
                        outerHTML: input.outerHTML
                    });
                });
                
                // 分析所有按钮
                document.querySelectorAll('button, input[type="submit"], input[type="button"]').forEach((button, index) => {
                    result.buttons.push({
                        index: index,
                        type: button.type || '',
                        name: button.name || '',
                        id: button.id || '',
                        className: button.className || '',
                        textContent: button.textContent || '',
                        value: button.value || '',
                        innerHTML: button.innerHTML || '',
                        outerHTML: button.outerHTML
                    });
                });
                
                return result;
            }
            """
            
            # 执行JavaScript分析
            form_data = await self.page.evaluate(form_analysis_js)
            
            # 4. 保存分析结果
            analysis_file = f"debug/billing_page_analysis_{timestamp}.json"
            with open(analysis_file, 'w', encoding='utf-8') as f:
                json.dump(form_data, f, indent=2, ensure_ascii=False)
            self.logger.info(f"📊 DOM分析结果已保存: {analysis_file}")
            
            # 5. 输出关键信息到日志
            self.logger.info("📋 DOM结构分析结果:")
            self.logger.info(f"   📝 表单数量: {len(form_data['forms'])}")
            self.logger.info(f"   🎯 输入字段数量: {len(form_data['inputs'])}")
            self.logger.info(f"   🔘 按钮数量: {len(form_data['buttons'])}")
            
            # 输出输入字段详情
            self.logger.info("📋 输入字段详情:")
            for i, input_field in enumerate(form_data['inputs']):
                field_info = f"   [{i}] {input_field['type']} - name='{input_field['name']}' id='{input_field['id']}' class='{input_field['className'][:50]}'"
                if input_field['placeholder']:
                    field_info += f" placeholder='{input_field['placeholder']}'"
                self.logger.info(field_info)
            
            # 输出按钮详情
            self.logger.info("📋 按钮详情:")
            for i, button in enumerate(form_data['buttons']):
                button_info = f"   [{i}] {button['type']} - name='{button['name']}' id='{button['id']}' text='{button['textContent'][:30]}'"
                self.logger.info(button_info)
            
            # 6. 生成推荐选择器
            self.logger.info("🎯 推荐的表单字段选择器:")
            for input_field in form_data['inputs']:
                if input_field['type'] in ['text', 'email', 'tel', 'number']:
                    selectors = []
                    if input_field['name']:
                        selectors.append(f"input[name='{input_field['name']}']")
                    if input_field['id']:
                        selectors.append(f"input[id='{input_field['id']}']")
                    if input_field['placeholder']:
                        selectors.append(f"input[placeholder*='{input_field['placeholder'][:20]}']")
                    
                    if selectors:
                        field_desc = input_field['placeholder'] or input_field['name'] or input_field['id'] or f"type-{input_field['type']}"
                        self.logger.info(f"   {field_desc}: {selectors[:3]}")  # 只显示前3个选择器
            
            self.logger.info("✅ 计费页面DOM结构分析完成")
            
        except Exception as e:
            self.logger.error(f"❌ DOM结构分析失败: {e}")
            # 继续执行，不中断流程

    async def _fill_billing_form(self) -> Dict[str, Any]:
        """
        自动填充计费页面表单 - 支持数据轮换
        
        检测 https://signup.hulu.com/billing 页面并自动填充表单字段
        实现智能数据轮换：当提交按钮禁用时，自动尝试fake_data.txt中的下一条数据
        直到找到有效的信用卡数据或所有数据都尝试完毕
        
        核心流程:
        1. 读取所有可用的信用卡数据 (fake_data.txt)
        2. 依次尝试每条数据：
           - 清空表单字段 (第二次开始)
           - 填充新的数据
           - 等待表单验证
           - 检查提交按钮状态
        3. 找到有效数据时立即提交表单
        4. 所有数据无效时返回失败结果
        
        Returns:
            Dict[str, Any]: 填充和提交结果，包含以下字段:
                - status: "success", "failed", "error"
                - message: 详细结果说明
                - data_index: 成功时使用的数据索引
                - tried_data_count: 尝试的数据条数
                - tried_data_indexes: 尝试过的数据索引列表
                - filled_fields: 成功填充的字段列表
                - failed_fields: 失败的字段列表
                - final_url: 最终页面URL
        """
        try:
            start_time = asyncio.get_event_loop().time()
            self.logger.info("💳 开始自动计费页面处理（支持数据轮换）...")
            
            # 等待页面跳转到计费页面
            self.logger.info("⏳ 等待页面跳转到计费页面...")
            max_wait_time = 15  # 15秒超时
            start_time = asyncio.get_event_loop().time()
            
            while True:
                current_url = self.page.url
                elapsed_time = asyncio.get_event_loop().time() - start_time
                
                if "signup.hulu.com/billing" in current_url:
                    self.logger.info(f"✅ 检测到计费页面: {current_url}")
                    break
                    
                if elapsed_time > max_wait_time:
                    self.logger.warning(f"⚠️ 等待计费页面超时 ({max_wait_time}s), 当前页面: {current_url}")
                    # 继续尝试处理，可能页面结构不同
                    break
                    
                await asyncio.sleep(0.5)
            
            # 使用优化的页面导航等待机制
            await self.wait_for_page_navigation("表单页面加载", timeout=5000)
            
            await asyncio.sleep(3)  # 额外等待确保表单加载完成
            
            # ===========================================
            # DOM分析调试功能已移除 - 使用已知的选择器结构
            # ===========================================
            # await self._debug_billing_page_structure()  # 调试时取消注释
            
            # 解析计费数据（第一步：获取数据总数）
            billing_data, total_lines = await self._parse_billing_data(0)
            
            # 定义表单字段选择器映射 - 基于真实DOM结构
            field_selectors = {
                "cardholder_name": [
                    '#ownerFullName',  # 主选择器 - ID
                    '[data-testid="ownerFullName"]',  # 备用选择器 - data-testid
                    'input[name="payment.ownerFullName"]',  # name属性
                    'input[placeholder*="Name on card"]'  # 通用选择器
                ],
                "card_number": [
                    '#creditCard',  # 主选择器 - ID
                    '[data-testid="creditCard"]',  # 备用选择器 - data-testid
                    'input[name="payment.creditCard"]',  # name属性
                    'input[type="tel"][inputmode="numeric"]'  # 类型和模式
                ],
                "expiration": [
                    '#expiry',  # 主选择器 - ID (统一的到期日期字段)
                    '[data-testid="expiry"]',  # 备用选择器 - data-testid
                    'input[name="payment.expiry"]',  # name属性
                    'input[placeholder="MM / YY"]'  # 占位符
                ],
                "cvv": [
                    '#cvc',  # 主选择器 - ID
                    '[data-testid="cvc"]',  # 备用选择器 - data-testid
                    'input[name="payment.cvc"]',  # name属性
                    'input[maxlength="4"][inputmode="numeric"]'  # 特征属性
                ],
                "zip_code": [
                    '#zip',  # 主选择器 - ID
                    '[data-testid="zip"]',  # 备用选择器 - data-testid
                    'input[name="payment.zip"]',  # name属性
                    'input[maxlength="5"][inputmode="numeric"]'  # 特征属性
                ]
            }
            
            # ===========================================
            # 数据轮换逻辑 - 自动尝试不同的信用卡数据
            # ===========================================
            max_attempts = min(total_lines, 5)  # 最大尝试次数限制，避免无限尝试
            self.logger.info(f"🔄 开始数据轮换逻辑，共有 {total_lines} 条数据，最多尝试 {max_attempts} 次")
            
            # 提交按钮选择器定义 - Shadow DOM支持（在轮换循环外定义一次）
            submit_selectors = [
                # Shadow DOM 精确选择器 (优先级最高)
                '#hx-core-auth button[type="submit"]',
                '#hx-core-auth button.button.capitalize',
                '#hx-core-auth button:has-text("Agree & Subscribe")',
                '#hx-core-auth .button:has-text("Agree & Subscribe")',
                '#hx-core-auth form.form.billing button[type="submit"]',
                # 通用容器支持
                '[id*="hx-core-auth"] button[type="submit"]',
                '[class*="hx-core-auth"] button[type="submit"]',
                # 传统选择器 (兼容性降级)
                'button[type="submit"]',  # 主选择器 - 类型属性
                'button.button.capitalize',  # 类选择器
                'button:has-text("Agree & Subscribe")',  # 文本内容
                '.button:has-text("Agree & Subscribe")',  # 类+文本
                'form.form.billing button[type="submit"]'  # 表单内的提交按钮
            ]
            
            # 数据轮换循环（限制最大尝试次数）
            for data_index in range(max_attempts):
                try:
                    self.logger.info(f"🔄 尝试数据索引 {data_index}/{total_lines-1} (第 {data_index + 1}/{max_attempts} 次尝试)")
                    
                    # 1. 解析指定索引的数据
                    billing_data, _ = await self._parse_billing_data(data_index)
                    
                    # 2. 清空并填充表单（如果不是第一次尝试）
                    if data_index > 0:
                        await self._clear_form_fields(field_selectors)
                    
                    filled_fields, failed_fields = await self._fill_form_with_data(billing_data, field_selectors)
                    
                    # 3. 检查表单填充是否成功
                    if len(filled_fields) < 4:  # 至少需要4个必要字段
                        self.logger.warning(f"❌ 数据索引 {data_index} 表单填充失败，仅成功 {len(filled_fields)} 个字段")
                        continue
                    
                    # 4. 等待验证和检查按钮状态
                    await self._wait_for_validation(3)  # 等待3秒
                    
                    # 5. 检查提交按钮是否可用
                    if await self._is_submit_button_enabled(submit_selectors):
                        # 成功找到有效数据，提交表单
                        self.logger.info(f"✅ 数据索引 {data_index} 有效，按钮已启用，准备提交表单")
                        result = await self._submit_form(data_index, submit_selectors)
                        
                        # 添加额外信息和性能统计到结果
                        total_time = asyncio.get_event_loop().time() - start_time
                        result["filled_fields"] = filled_fields
                        result["failed_fields"] = failed_fields
                        result["tried_data_indexes"] = list(range(data_index + 1))
                        result["performance"] = {
                            "total_time_seconds": round(total_time, 2),
                            "successful_data_index": data_index,
                            "attempts_needed": data_index + 1
                        }
                        
                        self.logger.info(f"🎉 数据轮换成功完成！用时: {total_time:.2f}秒, 尝试次数: {data_index + 1}")
                        return result
                    else:
                        # 当前数据无效，尝试下一条
                        self.logger.warning(f"❌ 数据索引 {data_index} 无效，按钮仍禁用，尝试下一条...")
                        
                        # 添加适当延迟，避免过于频繁的请求
                        await asyncio.sleep(2)
                        
                except Exception as data_error:
                    self.logger.error(f"❌ 数据索引 {data_index} 处理失败: {data_error}")
                    continue
            
            # 所有尝试都失败
            total_time = asyncio.get_event_loop().time() - start_time
            self.logger.error(f"❌ 已尝试 {max_attempts} 条数据都无效，无法找到可用的信用卡信息")
            self.logger.error(f"💸 数据轮换失败总结 - 用时: {total_time:.2f}秒, 尝试次数: {max_attempts}")
            
            remaining_data = total_lines - max_attempts
            additional_info = f"，还有 {remaining_data} 条数据未尝试" if remaining_data > 0 else ""
            
            return {
                "status": "failed",
                "message": f"已尝试 {max_attempts} 条数据都无效，所有提交按钮都保持禁用状态{additional_info}",
                "tried_data_count": max_attempts,
                "total_data_count": total_lines,
                "tried_data_indexes": list(range(max_attempts)),
                "performance": {
                    "total_time_seconds": round(total_time, 2),
                    "attempts_made": max_attempts,
                    "success_rate": 0.0
                },
                "final_url": self.page.url
            }
                
        except Exception as e:
            self.logger.error(f"❌ 计费页面处理失败: {e}")
            return {
                "status": "error",
                "message": f"计费页面处理异常: {str(e)}",
                "filled_fields": [],
                "failed_fields": [],
                "final_url": self.page.url if hasattr(self, 'page') else ""
            }

    async def _click_login_with_js(self):
        """
        使用JavaScript直接点击LOGIN按钮，绕过ExitIntentModal拦截
        基于测试验证的最直接有效方案
        """
        try:
            self.logger.info("🎯 使用JavaScript绕过ExitIntentModal拦截...")
            
            # 使用测试验证的最佳策略：DOM直接点击
            click_result = await self.page.evaluate("""
                () => {
                    const selectors = [
                        'a[href*="login"]',           // 测试验证的最佳选择器
                        'text="LOG IN"',              // 备用选择器
                        'text="Log In"',              // 备用选择器
                        'a:has-text("Log In")',       // 备用选择器
                        'button:has-text("LOG IN")'   // 备用选择器
                    ];
                    
                    for (const selector of selectors) {
                        try {
                            let element = null;
                            
                            // 处理text选择器和CSS选择器
                            if (selector.startsWith('text=')) {
                                const text = selector.replace('text=', '').replace(/"/g, '');
                                const elements = Array.from(document.querySelectorAll('a, button'));
                                element = elements.find(el => 
                                    el.textContent.trim() === text && 
                                    el.offsetParent !== null
                                );
                            } else {
                                element = document.querySelector(selector);
                            }
                            
                            if (element && element.offsetParent !== null) {
                                // DOM直接点击 - 测试验证最有效
                                element.click();
                                return { 
                                    success: true, 
                                    selector: selector,
                                    method: 'DOM直接点击',
                                    element_tag: element.tagName,
                                    element_text: element.textContent.trim().substring(0, 20)
                                };
                            }
                        } catch (e) {
                            continue;
                        }
                    }
                    
                    return { success: false, error: 'No clickable login element found' };
                }
            """)
            
            if click_result.get('success'):
                self.logger.info(f"✅ JavaScript点击成功: {click_result['method']}")
                self.logger.info(f"   - 选择器: {click_result['selector']}")
                self.logger.info(f"   - 元素: {click_result['element_tag']} - {click_result['element_text']}")
                return True
            else:
                self.logger.warning(f"❌ JavaScript点击失败: {click_result.get('error', 'Unknown error')}")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ JavaScript点击异常: {e}")
            return False

    async def _handle_exit_intent_modal(self):
        """
        废弃的ExitIntentModal处理方法 - 现在使用JavaScript绕过
        保留用于向后兼容，但不再主动调用
        """
        try:
            # 检测ExitIntentModal状态（包括--show类）
            modal_info = await self.page.evaluate("""
                () => {
                    const modal = document.querySelector('.ExitIntentModal__wrapper');
                    if (!modal) return { exists: false };
                    
                    return {
                        exists: true,
                        has_show_class: modal.classList.contains('--show'),
                        visible: modal.offsetParent !== null,
                        classes: modal.className,
                        display: getComputedStyle(modal).display,
                        intercepts_clicks: false // 已通过JavaScript绕过
                    };
                }
            """)
            
            if modal_info['exists']:
                if modal_info['has_show_class']:
                    self.logger.info("ℹ️ 检测到ExitIntentModal --show类，但已通过JavaScript绕过")
                else:
                    self.logger.info("ℹ️ ExitIntentModal存在但未激活，无需处理")
            else:
                self.logger.info("ℹ️ ExitIntentModal不存在")
                
        except Exception as e:
            self.logger.debug(f"ExitIntentModal检测异常: {e}")  # 降级为debug级别

    async def debug_exit_intent_modal(self):
        """
        使用Playwright调试ExitIntentModal的最佳处理方法
        测试多种消除方法并收集性能数据
        """
        try:
            import time
            self.logger.info("🔍 开始ExitIntentModal Playwright调试...")
            
            # 确保页面已初始化
            await self._setup_page_if_needed()
            
            # 设置DOM变化监听器
            await self.page.evaluate("""
                window.modalDebugInfo = {
                    detected: false,
                    timestamp: null,
                    structure: null,
                    detectionLog: []
                };
                
                // 监听DOM变化
                const observer = new MutationObserver((mutations) => {
                    mutations.forEach((mutation) => {
                        mutation.addedNodes.forEach((node) => {
                            if (node.nodeType === 1) {
                                const className = node.className || '';
                                if (className.includes('ExitIntentModal') || 
                                    node.id === 'exit-intent-modal') {
                                    window.modalDebugInfo.detected = true;
                                    window.modalDebugInfo.timestamp = Date.now();
                                    window.modalDebugInfo.structure = node.outerHTML;
                                    window.modalDebugInfo.detectionLog.push({
                                        time: Date.now(),
                                        element: node.tagName,
                                        className: className,
                                        id: node.id
                                    });
                                    console.log('📋 ExitIntentModal detected:', node);
                                }
                            }
                        });
                    });
                });
                observer.observe(document.body, { childList: true, subtree: true });
            """)
            
            self.logger.info("✅ DOM监听器设置完成")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 调试函数初始化失败: {e}")
            return False

    async def test_modal_elimination_methods(self):
        """
        测试多种ExitIntentModal消除方法并收集性能数据
        """
        try:
            self.logger.info("🎯 开始测试模态框消除方法...")
            
            # 测试方法列表
            test_methods = [
                ("页面左上角点击", "click", {'position': {'x': 10, 'y': 10}}),
                ("页面右上角点击", "click", {'position': {'x': -10, 'y': 10}}),
                ("页面左下角点击", "click", {'position': {'x': 10, 'y': -10}}),
                ("页面右下角点击", "click", {'position': {'x': -10, 'y': -10}}),
                ("页面中心偏左点击", "click", {'position': {'x': 100, 'y': 200}}),
                ("ESC键方法", "keyboard", {'key': 'Escape'}),
                ("点击body背景", "click", {'position': {'x': 50, 'y': 50}}),
            ]
            
            results = []
            
            for i, (method_name, action_type, params) in enumerate(test_methods, 1):
                self.logger.info(f"🧪 测试方法 {i}/{len(test_methods)}: {method_name}")
                
                try:
                    # 重新加载页面以触发模态框
                    await self.page.goto("https://www.hulu.com")
                    await self.page.wait_for_load_state('domcontentloaded')
                    
                    # 尝试触发ExitIntentModal的多种方法
                    await self.page.evaluate("""
                        () => {
                            // 模拟鼠标移动到页面顶部边缘（常见的退出意图触发）
                            const event = new MouseEvent('mousemove', {
                                clientX: 50,
                                clientY: 0,
                                bubbles: true
                            });
                            document.dispatchEvent(event);
                        }
                    """)
                    
                    await asyncio.sleep(1)
                    
                    # 模拟用户试图离开页面
                    await self.page.evaluate("""
                        () => {
                            const event = new MouseEvent('mouseout', {
                                clientX: 0,
                                clientY: 0,
                                bubbles: true
                            });
                            document.dispatchEvent(event);
                        }
                    """)
                    
                    await asyncio.sleep(3)  # 增加等待时间
                    
                    # 检查模态框是否存在
                    modal_exists = await self.page.evaluate("""
                        () => {
                            const modal = document.querySelector('.ExitIntentModal__wrapper') || 
                                         document.querySelector('#exit-intent-modal');
                            return modal && modal.offsetParent !== null;
                        }
                    """)
                    
                    if not modal_exists:
                        # 调试：检查页面上所有可能的模态框元素
                        all_modals = await self.page.evaluate("""
                            () => {
                                const modalSelectors = [
                                    '.ExitIntentModal__wrapper',
                                    '#exit-intent-modal',
                                    '[class*="modal"]',
                                    '[class*="Modal"]',
                                    '[id*="modal"]',
                                    '[aria-label*="modal"]'
                                ];
                                
                                const found = [];
                                modalSelectors.forEach(selector => {
                                    const elements = document.querySelectorAll(selector);
                                    elements.forEach(el => {
                                        found.push({
                                            selector: selector,
                                            className: el.className,
                                            id: el.id,
                                            visible: el.offsetParent !== null,
                                            innerHTML: el.innerHTML.substring(0, 100)
                                        });
                                    });
                                });
                                return found;
                            }
                        """)
                        
                        if all_modals:
                            self.logger.info(f"🔍 发现其他模态框元素: {len(all_modals)}个")
                            for modal in all_modals[:3]:  # 只显示前3个
                                self.logger.info(f"   - {modal['selector']}: {modal['className']} (可见: {modal['visible']})")
                        else:
                            self.logger.warning(f"⚠️ {method_name}: 页面上没有任何模态框元素，跳过测试")
                        
                        continue
                    
                    start_time = time.time()
                    
                    # 执行消除方法
                    if action_type == "click":
                        await self.page.click('body', timeout=3000, **params)
                    elif action_type == "keyboard":
                        await self.page.keyboard.press(params['key'])
                    
                    await asyncio.sleep(0.5)  # 等待效果生效
                    
                    # 检查是否成功消除
                    modal_gone = await self.page.evaluate("""
                        () => {
                            const modal = document.querySelector('.ExitIntentModal__wrapper') || 
                                         document.querySelector('#exit-intent-modal');
                            return !modal || modal.offsetParent === null;
                        }
                    """)
                    
                    end_time = time.time()
                    duration = (end_time - start_time) * 1000
                    
                    result = {
                        'method': method_name,
                        'success': modal_gone,
                        'duration_ms': round(duration, 1),
                        'action_type': action_type,
                        'params': params
                    }
                    results.append(result)
                    
                    status = "✅ 成功" if modal_gone else "❌ 失败"
                    self.logger.info(f"   {status} - 耗时: {duration:.1f}ms")
                    
                except Exception as e:
                    self.logger.warning(f"⚠️ {method_name} 测试异常: {e}")
                    results.append({
                        'method': method_name,
                        'success': False,
                        'duration_ms': -1,
                        'error': str(e)
                    })
                
                # 短暂休息避免请求过频
                await asyncio.sleep(1)
            
            # 分析结果
            self.logger.info("📊 测试结果分析:")
            successful_methods = [r for r in results if r['success']]
            
            if successful_methods:
                # 按速度排序
                successful_methods.sort(key=lambda x: x['duration_ms'])
                self.logger.info(f"✅ 成功方法数量: {len(successful_methods)}/{len(results)}")
                self.logger.info("🏆 最佳方法排序:")
                
                for i, result in enumerate(successful_methods[:3], 1):
                    self.logger.info(f"   {i}. {result['method']} - {result['duration_ms']}ms")
                
                return successful_methods[0]  # 返回最佳方法
            else:
                self.logger.warning("❌ 所有测试方法都失败了")
                return None
                
        except Exception as e:
            self.logger.error(f"❌ 测试方法执行失败: {e}")
            return None

    async def _save_debug_info_on_failure(self, failure_type: str = "unknown"):
        """在失败时保存调试信息
        
        Args:
            failure_type: 失败类型描述
        """
        try:
            debug_dir = "debug"
            os.makedirs(debug_dir, exist_ok=True)
            
            timestamp = int(time.time())
            
            # 尝试保存页面截图
            try:
                screenshot_path = os.path.join(debug_dir, f"{failure_type}_{timestamp}.png")
                await self.page.screenshot(path=screenshot_path, full_page=True)
                self.logger.info(f"📸 调试截图已保存: {screenshot_path}")
            except Exception as e:
                self.logger.warning(f"⚠️ 保存截图失败: {e}")
            
            # 尝试保存页面HTML
            try:
                html_path = os.path.join(debug_dir, f"{failure_type}_{timestamp}.html")
                html_content = await self.page.content()
                with open(html_path, 'w', encoding='utf-8') as f:
                    f.write(html_content)
                self.logger.info(f"📄 页面HTML已保存: {html_path}")
            except Exception as e:
                self.logger.warning(f"⚠️ 保存HTML失败: {e}")
            
            # 保存基本状态信息
            try:
                state_path = os.path.join(debug_dir, f"{failure_type}_{timestamp}_state.txt")
                with open(state_path, 'w', encoding='utf-8') as f:
                    f.write(f"失败类型: {failure_type}\n")
                    f.write(f"时间戳: {timestamp}\n")
                    f.write(f"页面URL: {self.page.url}\n")
                    try:
                        f.write(f"页面标题: {await self.page.title()}\n")
                    except:
                        f.write("页面标题: 无法获取\n")
                    f.write(f"浏览器连接状态: 已检查\n")
                self.logger.info(f"📊 状态信息已保存: {state_path}")
            except Exception as e:
                self.logger.warning(f"⚠️ 保存状态信息失败: {e}")
                
        except Exception as e:
            self.logger.error(f"❌ 保存调试信息完全失败: {e}")

    async def cleanup(self):
        """公共清理方法，调用内部_cleanup"""
        try:
            await self._cleanup()
        except Exception as e:
            self.logger.warning(f"⚠️ 清理资源时出错: {e}")

    async def _fill_email_with_methods_chain(self, email_selector_templates):
        """优化的邮箱填写逻辑 - 方法链模式"""
        
        email_methods = [
            ("人类行为模拟", self._try_human_typing_email),  # 优先使用真实人类行为
            ("安全打字", self._try_safe_type_email),
            ("安全填写", self._try_safe_fill_email), 
            ("直接填写", self._try_direct_fill_email)
        ]
        
        for method_name, method_func in email_methods:
            try:
                self.logger.info(f"🎯 尝试{method_name}方法...")
                success = await method_func(email_selector_templates)
                if success:
                    self.logger.info(f"✅ {method_name}成功")
                    return success
            except Exception as e:
                self.logger.warning(f"⚠️ {method_name}失败: {e}")
                continue
        
        raise Exception("所有邮箱填写方法均失败")
    
    async def _try_safe_type_email(self, email_selector_templates):
        """尝试安全打字方法填写邮箱"""
        winner_selector = await self.safe_type(
            email_selector_templates, 
            self.test_credentials['email'],
            delay=random.randint(80, 120)
        )
        
        if winner_selector:
            # 验证填写结果
            try:
                current_value = await self.page.locator(winner_selector).first().input_value()
                if current_value and self.test_credentials['email'] in current_value:
                    self.logger.info(f"✅ 邮箱填写验证成功: '{current_value}'")
                    return winner_selector
                else:
                    self.logger.warning(f"⚠️ 邮箱填写验证失败，期望包含: {self.test_credentials['email']}")
            except Exception as verify_error:
                self.logger.debug(f"⚠️ 邮箱填写验证异常: {verify_error}")
            
            return winner_selector
        return None
    
    async def _try_safe_fill_email(self, email_selector_templates):
        """尝试安全填写方法填写邮箱"""
        winner_selector = await self.safe_fill(email_selector_templates, self.test_credentials['email'])
        return winner_selector if winner_selector else None
    
    async def _try_human_typing_email(self, email_selector_templates):
        """尝试人类行为模拟填写邮箱 - 45-50 WPM + 真实打字行为"""
        
        self.logger.info("🔍 开始人类行为模拟邮箱填写诊断...")
        
        # 尝试每个选择器模板
        for i, selector in enumerate(email_selector_templates):
            try:
                self.logger.info(f"   [{i+1}/{len(email_selector_templates)}] 测试选择器: {selector}")
                
                # 检查元素是否存在且可编辑
                locator = self.page.locator(selector).first
                element_count = await locator.count()
                self.logger.info(f"   📊 元素数量: {element_count}")
                
                if element_count > 0:
                    is_editable = await locator.is_editable()
                    self.logger.info(f"   ✏️ 可编辑性: {is_editable}")
                    
                    if is_editable:
                        self.logger.info(f"🎭 使用人类行为模拟填写邮箱: {selector}")
                        self.logger.info(f"   📧 邮箱: {self.test_credentials['email']}")
                        self.logger.info(f"   ⌨️ 打字速度: 45-50 WPM + QWERTY感知")
                        
                        # 使用专业人类行为模拟器
                        await self.human_type(
                            self.page,
                            selector,
                            self.test_credentials['email']
                        )
                        
                        # 验证填写结果
                        try:
                            current_value = await locator.input_value()
                            if current_value and self.test_credentials['email'] in current_value:
                                self.logger.info(f"✅ 人类行为模拟邮箱填写成功: '{current_value}'")
                                return selector
                            else:
                                self.logger.warning(f"⚠️ 人类行为模拟填写验证失败，期望: {self.test_credentials['email']}, 实际: '{current_value}'")
                        except Exception as verify_error:
                            self.logger.warning(f"⚠️ 人类行为模拟填写验证异常: {verify_error}")
                        
                        return selector
                    else:
                        self.logger.info(f"   ❌ 元素不可编辑，跳过选择器: {selector}")
                else:
                    self.logger.info(f"   ❌ 未找到元素，跳过选择器: {selector}")
                    
            except Exception as e:
                self.logger.warning(f"⚠️ 选择器 {selector} 人类行为模拟失败: {e}")
                import traceback
                self.logger.warning(f"   详细错误: {traceback.format_exc()}")
                continue
        
        self.logger.warning("❌ 所有选择器的人类行为模拟都失败了")
        return None
    
    async def _try_direct_fill_email(self, email_selector_templates):
        """直接填写方法作为最后的回退"""
        for selector in email_selector_templates:
            try:
                element = self.page.locator(selector).first()
                await element.click()
                await element.fill(self.test_credentials['email'])
                self.logger.info("✅ 直接填写方法成功")
                return selector
            except Exception:
                continue
        return None

    async def _fill_password_with_methods_chain(self, password_selector_templates):
        """优化的密码填写逻辑 - 方法链模式，优先人类行为模拟"""
        
        password_methods = [
            ("人类行为模拟", self._try_human_typing_password),  # 优先使用真实人类行为
            ("安全打字", self._try_safe_type_password),
            ("安全填写", self._try_safe_fill_password), 
            ("直接填写", self._try_direct_fill_password)
        ]
        
        for method_name, method_func in password_methods:
            try:
                self.logger.info(f"🎯 尝试{method_name}方法...")
                success = await method_func(password_selector_templates)
                if success:
                    self.logger.info(f"✅ {method_name}成功")
                    return success
            except Exception as e:
                self.logger.warning(f"⚠️ {method_name}失败: {e}")
                continue
        
        raise Exception("所有密码填写方法均失败")
    
    async def _try_human_typing_password(self, password_selector_templates):
        """尝试人类行为模拟填写密码 - 45-50 WPM + 真实打字行为"""
        
        self.logger.info("🔍 开始人类行为模拟密码填写诊断...")
        
        # 尝试每个选择器模板
        for i, selector in enumerate(password_selector_templates):
            try:
                self.logger.info(f"   [{i+1}/{len(password_selector_templates)}] 测试选择器: {selector}")
                
                # 检查元素是否存在且可编辑
                locator = self.page.locator(selector).first
                element_count = await locator.count()
                self.logger.info(f"   📊 元素数量: {element_count}")
                
                if element_count > 0:
                    is_editable = await locator.is_editable()
                    self.logger.info(f"   ✏️ 可编辑性: {is_editable}")
                    
                    if is_editable:
                        self.logger.info(f"🎭 使用人类行为模拟填写密码: {selector}")
                        self.logger.info(f"   🔐 密码长度: {len(self.test_credentials['password'])}字符")
                        self.logger.info(f"   ⌨️ 打字速度: 45-50 WPM + QWERTY感知")
                        self.logger.info(f"   🧠 包含键盘布局感知和手指映射")
                        
                        # 使用专业人类行为模拟器
                        await self.human_type(
                            self.page,
                            selector,
                            self.test_credentials['password']
                        )
                        
                        # 验证填写结果
                        try:
                            current_value = await locator.input_value()
                            if current_value and len(current_value) == len(self.test_credentials['password']):
                                self.logger.info(f"✅ 人类行为模拟密码填写成功 (长度: {len(current_value)})")
                                return selector
                            else:
                                self.logger.warning(f"⚠️ 人类行为模拟密码填写验证失败，期望长度: {len(self.test_credentials['password'])}, 实际长度: {len(current_value) if current_value else 0}")
                        except Exception as verify_error:
                            self.logger.warning(f"⚠️ 人类行为模拟密码填写验证异常: {verify_error}")
                        
                        return selector
                    else:
                        self.logger.info(f"   ❌ 元素不可编辑，跳过选择器: {selector}")
                else:
                    self.logger.info(f"   ❌ 未找到元素，跳过选择器: {selector}")
                    
            except Exception as e:
                self.logger.warning(f"⚠️ 选择器 {selector} 人类行为模拟失败: {e}")
                import traceback
                self.logger.warning(f"   详细错误: {traceback.format_exc()}")
                continue
        
        self.logger.warning("❌ 所有选择器的密码人类行为模拟都失败了")
        return None
    
    async def _try_safe_type_password(self, password_selector_templates):
        """尝试安全打字方法填写密码"""
        winner_selector = await self.safe_type(
            password_selector_templates, 
            self.test_credentials['password'],
            delay=random.randint(80, 120)
        )
        return winner_selector if winner_selector else None
    
    async def _try_safe_fill_password(self, password_selector_templates):
        """尝试安全填写方法填写密码"""
        winner_selector = await self.safe_fill(password_selector_templates, self.test_credentials['password'])
        return winner_selector if winner_selector else None
    
    async def _try_direct_fill_password(self, password_selector_templates):
        """直接填写密码方法作为最后的回退"""
        for selector in password_selector_templates:
            try:
                element = self.page.locator(selector).first()
                await element.click()
                await element.fill(self.test_credentials['password'])
                self.logger.info("✅ 直接填写密码方法成功")
                return selector
            except Exception:
                continue
        return None


async def main():
    """主程序入口"""
    print("🛡️ Hulu智能隐身自动化系统启动中...")
    print("📚 基于CDP + MediaCrawler反检测技术")
    print("=" * 60)

    stealth_system = None
    try:
        # 初始化隐身自动化系统（已整合隐蔽性功能）
        # 默认使用第一个账户（索引0）
        stealth_system = HuluStealthAutomation(account_index=0)

        # 显示系统信息
        system_info = stealth_system.get_system_info()
        print(f"🏗️ 系统架构: {system_info['architecture']}")
        print(f"🛡️ 反检测引擎: {system_info['anti_detection']['engine']}")
        print(f"⌨️ 打字速度: {system_info['anti_detection']['typing_wpm_range']} WPM")
        print(f"🎯 错误率: {system_info['anti_detection']['typing_error_rate']:.1%}")
        print()
        print("🆕 核心功能:")
        print("   🔍 智能Cookies检测 - 自动判断登录状态")
        print("   🤖 自动登录 - 无有效会话时自动执行登录")
        print("   🎯 GET THEM BOTH按钮 - 登录后自动点击")
        print("   💳 计费页面自动化 - 自动填充表单和提交")
        print("   📄 数据源集成 - 读取fake_data.txt文件")
        print("=" * 60)

        # 执行隐身登录
        result = await stealth_system.execute_stealth_login()

        # 分析登录结果
        stealth_system._analyze_login_result(result)

        # 输出最终结果（隐蔽性功能已整合，无需单独报告）
        print("\n🎯 执行完成!")
        login_status = result.get("login_status")
        billing_result = result.get("billing_result")

        # 显示计费处理结果
        if billing_result:
            billing_status = billing_result.get("status")
            print(f"\n💳 计费页面处理结果: {billing_status}")
            if billing_status == "success":
                print("✅ 计费表单自动填充和提交成功!")
                filled_fields = billing_result.get("filled_fields", [])
                print(f"📋 成功填充字段: {', '.join(filled_fields)}")
            elif billing_status == "partial_success":
                print("⚠️ 计费表单填充成功，但未能自动提交")
                filled_fields = billing_result.get("filled_fields", [])
                print(f"📋 成功填充字段: {', '.join(filled_fields)}")
            elif billing_status in ["failed", "error"]:
                print("❌ 计费页面处理失败")
                failed_fields = billing_result.get("failed_fields", [])
                if failed_fields:
                    print(f"❌ 失败字段: {', '.join(failed_fields)}")
                print(f"📄 错误信息: {billing_result.get('message', 'Unknown error')}")
            
            print(f"🔗 最终页面URL: {billing_result.get('final_url')}")

        # 确定最终返回码
        if login_status == "success":
            print("✅ 隐身登录成功 - 完美规避检测!")
            exit_code = 0 if not billing_result or billing_result.get("status") in ["success", "partial_success"] else 1
        elif login_status == "restored":
            print("✅ 检测到有效登录状态 - 已跳过登录流程!")
            print("🎯 已自动点击 GET THEM BOTH 按钮")
            print("💳 计费页面自动化处理完成")
            exit_code = 0 if not billing_result or billing_result.get("status") in ["success", "partial_success"] else 1
        elif login_status == "no_login_needed":
            print("✅ 无需登录 - 未检测到Log In按钮!")
            print("🎯 已自动点击 GET THEM BOTH 按钮")
            print("💳 计费页面自动化处理完成")
            exit_code = 0 if not billing_result or billing_result.get("status") in ["success", "partial_success"] else 1
        elif login_status == "verification_completed":
            print("📧 验证码处理完成 - 登录流程已完成")
            exit_code = 0 if not billing_result or billing_result.get("status") in ["success", "partial_success"] else 1
        elif login_status == "verification_failed":
            print("❌ 验证码处理失败 - 请检查邮箱API或网络连接")
            exit_code = 1
        else:
            print("❌ 隐身登录失败 - 请检查日志")
            exit_code = 1
        
        # 在返回前清理资源
        if stealth_system:
            try:
                print("\n🧹 清理系统资源...")
                await stealth_system._cleanup()
                print("✅ 资源清理完成")
            except Exception as cleanup_error:
                print(f"⚠️ 资源清理时出现警告: {cleanup_error}")
        
        return exit_code

    except KeyboardInterrupt:
        print("\n⏹️ 用户中断程序执行")
        # 清理资源
        if stealth_system:
            try:
                print("🧹 用户中断，清理资源...")
                await stealth_system._cleanup()
                print("✅ 资源清理完成")
            except Exception as cleanup_error:
                print(f"⚠️ 资源清理时出现警告: {cleanup_error}")
        return 1

    except Exception as e:
        print(f"\n❌ 程序执行失败: {str(e)}")
        # 清理资源
        if stealth_system:
            try:
                print("🧹 异常退出，清理资源...")
                await stealth_system._cleanup()
                print("✅ 资源清理完成")
            except Exception as cleanup_error:
                print(f"⚠️ 资源清理时出现警告: {cleanup_error}")
        return 1


if __name__ == "__main__":
    # 运行异步主程序
    exit_code = asyncio.run(main())
    sys.exit(exit_code)