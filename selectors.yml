# Hulu 页面选择器配置文件
# 基于现有 HuluPageSelectors 类优化，支持动态宿主模板和降级策略

# 登录流程选择器
login_flow:
  # Welcome页面登录按钮 - 支持动态宿主模板 {HOST}
  welcome_login_buttons:
    primary:  # 动态宿主模板选择器 (优先级最高)
      - '{HOST} >> button:has-text("Log In")'
      - '{HOST} >> button:has-text("LOG IN")'
      - '{HOST} >> a:has-text("Log In")'
      - '{HOST} >> a:has-text("LOG IN")'
    
    fallback:  # 传统选择器 (降级兼容)
      - 'button:has-text("Log In")'
      - 'button:has-text("LOG IN")'
      - 'a:has-text("Log In")'
      - 'a:has-text("LOG IN")'
      - 'text="Log In"'
      - 'text="LOG IN"'
      - 'nav a[href*="login"]'
      - 'header a[href*="login"]'
      - '[href*="login"]'

  # 邮箱输入字段
  email_fields:
    primary:
      - '#email'               # 最常见的邮箱字段ID (提升优先级)
      - 'input[type="email"]'   # 最通用的邮箱字段类型
      - 'input[name="email"]'   # 标准name属性
      - 'input[autocomplete="email"]'  # 现代浏览器标准
    
    verified:  # 基于真实测试验证的选择器
      - '#email-field'          # 特定的邮箱字段ID
      - 'input[name="email-field"]'  # 特定的name属性
      - '#email_id'             # 备用ID变体
      - 'input[id="email_id"]'
    
    fallback:
      - 'input[placeholder="Email"]'
      - 'input[placeholder*="email"]'
      - 'input[data-testid*="email"]'
      - 'input[placeholder*="Email"]'
      - 'input[class*="email"]'
      - '[data-automation-id*="email"]'

  # Continue按钮 (分步登录)
  continue_buttons:
    primary:  # 动态宿主模板选择器
      - '{HOST} >> button:has-text("Continue")'
      - '{HOST} >> button:has-text("继续")'
      - '{HOST} >> button[type="submit"]:has-text("Continue")'
      - '{HOST} >> input[type="submit"][value="Continue"]'
    
    fallback:  # 传统选择器
      - 'button:has-text("Continue")'
      - 'button:has-text("继续")'
      - 'input[type="submit"][value="Continue"]'
      - 'button[type="submit"]:has-text("Continue")'
      - 'button[type="submit"]'
      - 'input[type="submit"]'
      - 'button:contains("Continue")'
      - '[data-testid*="continue"]'
      - '.continue-btn'
      - '.continue-button'
      - '#continue-btn'
      - '#continue-button'

  # 密码输入字段
  password_fields:
    primary:
      - "#password"                    # 正确的密码字段ID
      - 'input[name="password"]'       # 正确的密码字段name
      - 'input[type="password"]'       # 最通用的选择器
    
    fallback:
      - 'input[placeholder*="password"]'  # 占位符包含password
      - 'input[placeholder*="Password"]'  # 占位符包含Password
      - 'input[data-testid*="password"]'   # 测试ID包含password

  # 登录提交按钮
  login_submit_buttons:
    primary:  # 动态宿主模板选择器
      - '{HOST} >> button:has-text("Log In")'           # 主要登录按钮
      - '{HOST} >> button:has-text("LOGIN")'            # 大写版本
      - '{HOST} >> button:has-text("Submit")'           # 提交按钮
      - '{HOST} >> button[type="submit"]:has-text("Log In")'  # 精确匹配
      - '{HOST} >> button[type="submit"]'               # 通用提交按钮
    
    verified:  # 基于真实DOM测试结果优化
      - 'button:has-text("Log In")'                     # 调试脚本验证成功的选择器（最高优先级）
      - 'button[type="submit"]:has-text("Log In")'      # 精确：type=submit + 文本匹配
      - 'form button[type="submit"]'                    # 表单中的提交按钮（真实DOM确认）
      - 'button[type="submit"]'                         # 提交按钮类型
    
    css_classes:  # 基于真实DOM的CSS类选择器
      - 'button.cursor-pointer[type="submit"]'          # 带cursor-pointer类的提交按钮
      - 'button.border-solid[type="submit"]'            # 带border-solid类的提交按钮
      - 'button.transition-colors[type="submit"]'       # 带transition-colors类的提交按钮
      - 'button.rounded-xl[type="submit"]'              # 带rounded-xl类的提交按钮
      - 'button.flex.items-center[type="submit"]'       # 带flex和items-center类的提交按钮
    
    fallback:  # 备用选择器（按优先级排序）
      - 'button:has-text("LOGIN")'                      # 大写文本匹配
      - 'input[type="submit"]'                          # 输入提交
      - 'button:has-text("Sign In")'                    # Sign In文本
      - 'button[data-testid*="login"]'                 # 测试ID包含login
      - 'button[data-testid*="signin"]'                # 测试ID包含signin
      - '.login-button'                                 # CSS类名
      - '.signin-button'                                # CSS类名
      - 'button[class*="login"]'                        # class包含login
      - 'button[class*="submit"]'                       # class包含submit
      - 'button[value="Log In"]'                        # 按钮值
      - 'button[value="LOGIN"]'                         # 按钮值大写
      - 'button[aria-label*="log in"]'                  # aria-label属性
      - 'button[aria-label*="login"]'                   # aria-label属性
      - '[role="button"]:has-text("Log In")'            # 角色为按钮的元素
      - '[role="button"]:has-text("LOGIN")'             # 角色为按钮的元素
      - 'a[role="button"]:has-text("Log In")'           # 链接作为按钮
      - 'div[role="button"]:has-text("Log In")'          # div作为按钮

# 验证流程选择器
verification_flow:
  # 验证码输入字段
  verification_code_fields:
    primary:
      - 'input[name="verificationCode"]'
      - 'input[name="verification_code"]'
      - 'input[name="code"]'
      - 'input[name="otp"]'           # Hulu的OTP字段
      - 'input[name="authCode"]'
    
    fallback:
      - 'input[placeholder*="code"]'
      - 'input[placeholder*="verification"]'
      - 'input[placeholder*="verify"]'
      - 'input[data-testid*="verification"]'
      - 'input[data-testid*="code"]'
      - 'input[maxlength="6"]'        # 6位验证码字段
      - 'input[type="text"]'          # 通用文本输入

  # 验证提交按钮
  verification_submit_buttons:
    primary:  # 动态宿主模板选择器
      - '{HOST} >> button:has-text("Verify")'
      - '{HOST} >> button:has-text("Submit")'
      - '{HOST} >> button:has-text("Continue")'
      - '{HOST} >> button:has-text("Confirm")'
      - '{HOST} >> button[type="submit"]'
    
    fallback:  # 传统选择器
      - 'button:has-text("Verify")'
      - 'button:has-text("Submit")'
      - 'button:has-text("Continue")'
      - 'button:has-text("Confirm")'
      - 'button:has-text("Next")'
      - 'button[type="submit"]'
      - 'button[data-testid*="submit"]'
      - 'button[data-testid*="verify"]'
      - 'button[data-testid*="continue"]'
      - 'input[type="submit"]'

# 状态检查选择器
status_checks:
  # 登录成功指示元素
  login_success_elements:
    - '[data-testid*="profile"]'
    - '[data-testid*="account"]'
    - '[data-testid*="user"]'
    - '.profile-menu'
    - '.user-menu'
    - '.account-menu'
    - 'nav[data-testid*="navigation"]'
    - '[aria-label*="Profile"]'
    - '[aria-label*="Account"]'
    - 'button[aria-label*="Profile"]'
    - '[data-testid="user-menu"]'    # 具体的用户菜单
    - '.header-user-menu'            # 头部用户菜单
    - '[class*="user-avatar"]'        # 用户头像

  # 登录错误指示元素
  login_error_elements:
    - '[data-testid*="error"]'
    - '.error-message'
    - '.alert-error'
    - '[role="alert"]'
    - '.notification-error'

# 注册流程选择器
registration_flow:
  # 注册按钮
  register_buttons:
    - 'button:has-text("Sign Up")'
    - 'button:has-text("Get Started")'
    - 'button:has-text("Create Account")'
    - 'a:has-text("Sign Up")'
    - '[href*="signup"]'
    - '[href*="register"]'

  # 用户名字段
  name_fields:
    - 'input[name="first_name"]'
    - 'input[name="firstName"]'
    - 'input[name="name"]'
    - 'input[placeholder*="name"]'
    - 'input[placeholder*="Name"]'
    - 'input[data-testid*="name"]'

# 健康检查专用配置
healthcheck:
  # 页面检查顺序（线性流程）
  flow_sequence:
    - phase: "welcome_page"
      selectors: "login_flow.welcome_login_buttons"
      description: "Welcome页面LOG IN按钮检测"
    
    - phase: "login_page"
      selectors: "login_flow.email_fields"
      description: "登录页面邮箱字段检测"
    
    - phase: "email_continue"
      selectors: "login_flow.continue_buttons"
      description: "邮箱输入后Continue按钮检测"
    
    - phase: "password_page"
      selectors: "login_flow.password_fields"
      description: "密码页面字段检测"
    
    - phase: "login_submit"
      selectors: "login_flow.login_submit_buttons"
      description: "最终登录提交按钮检测"
    
    - phase: "verification_page"
      selectors: "verification_flow.verification_code_fields"
      description: "验证码页面字段检测（可选）"

  # 网络请求拦截规则
  network_stubs:
    - pattern: "**/accounts/**"
      response: 
        status: 200
        body: '{"success": true, "message": "healthcheck_stub"}'
    
    - pattern: "**/login/**"
      response:
        status: 200
        body: '{"status": "ok", "redirect": "/home"}'

  # reCAPTCHA Mock配置
  recaptcha_mock:
    script: |
      window.grecaptcha = {
        execute: () => Promise.resolve('dummy_token_for_healthcheck'),
        ready: (callback) => callback(),
        render: () => 'dummy_widget_id'
      };

# 配置元信息
metadata:
  version: "1.0.0"
  description: "Hulu页面选择器配置 - Quick-Wins优化版本"
  last_updated: "2025-07"
  compatibility: "playwright>=1.40.0"