version: '3.8'

services:
  cvv-frontend:
    build:
      context: ../../web_interface/frontend
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_API_URL=http://cvv-backend:5000
    depends_on:
      - cvv-backend
    restart: unless-stopped
    
  cvv-backend:
    build:
      context: ../../web_interface/backend
      dockerfile: Dockerfile
    ports:
      - "5000:5000"
    environment:
      - NODE_ENV=production
      - PORT=5000
      - CORS_ORIGIN=http://localhost:3000
    restart: unless-stopped
    
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - cvv-frontend
      - cvv-backend
    restart: unless-stopped

networks:
  default:
    name: cvv-web-network
