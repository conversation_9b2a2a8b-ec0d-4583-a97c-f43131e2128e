#!/usr/bin/env python3
"""
Hulu自动化系统手动清理工具
清理浏览器状态、cookies和缓存数据
"""

import os
import shutil
import sys
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def get_cleanup_targets():
    """获取需要清理的目标文件和目录"""
    base_dir = Path(__file__).parent
    
    targets = {
        "持久化用户数据": base_dir / "hulu_session_data",
        "调试截图": base_dir / "screenshots",
        "调试数据": base_dir / "debug",
        "日志文件": base_dir / "logs",
        "临时文件": base_dir / "tmp",
        "Chrome缓存": base_dir / "chrome_cache",
        "反检测数据": base_dir / "anti_detection" / "analytics" / "detection_attempts.jsonl",
        "监控数据": base_dir / "logs" / "monitoring_data.csv",
    }
    
    return targets

def check_target_size(path):
    """检查目标的大小"""
    if not path.exists():
        return 0, "不存在"
    
    try:
        if path.is_file():
            size = path.stat().st_size
            return size, f"{size / 1024:.1f} KB"
        elif path.is_dir():
            total_size = sum(f.stat().st_size for f in path.rglob('*') if f.is_file())
            file_count = len(list(path.rglob('*')))
            return total_size, f"{total_size / (1024*1024):.1f} MB ({file_count} 文件)"
    except (OSError, PermissionError) as e:
        return 0, f"无法访问: {e}"
    
    return 0, "未知"

def remove_target(path, name):
    """安全删除目标文件或目录"""
    try:
        if not path.exists():
            logger.info(f"   ℹ️ {name}: 不存在，跳过")
            return True
            
        if path.is_file():
            path.unlink()
            logger.info(f"   ✅ {name}: 文件已删除")
        elif path.is_dir():
            shutil.rmtree(path)
            logger.info(f"   ✅ {name}: 目录已删除")
        
        return True
        
    except PermissionError:
        logger.error(f"   ❌ {name}: 权限不足，无法删除")
        return False
    except Exception as e:
        logger.error(f"   ❌ {name}: 删除失败 - {e}")
        return False

def interactive_cleanup():
    """交互式清理"""
    print("🧹 Hulu自动化系统手动清理工具")
    print("=" * 50)
    
    targets = get_cleanup_targets()
    
    print("\n📊 当前系统状态:")
    total_size = 0
    existing_targets = {}
    
    for name, path in targets.items():
        size_bytes, size_str = check_target_size(path)
        status = "✅ 存在" if path.exists() else "❌ 不存在"
        print(f"   {status} {name}: {size_str}")
        
        if path.exists():
            existing_targets[name] = path
            total_size += size_bytes
    
    if not existing_targets:
        print("\n✨ 系统已经很干净，无需清理！")
        return True
    
    print(f"\n📈 总占用空间: {total_size / (1024*1024):.1f} MB")
    
    print("\n🎯 清理选项:")
    print("1. 🔥 完全清理 - 删除所有数据（推荐）")
    print("2. 🎯 选择性清理 - 选择要删除的项目")
    print("3. 🚪 退出")
    
    while True:
        try:
            choice = input("\n请选择操作 (1-3): ").strip()
            
            if choice == "1":
                return full_cleanup(existing_targets)
            elif choice == "2":
                return selective_cleanup(existing_targets)
            elif choice == "3":
                print("👋 已取消清理")
                return False
            else:
                print("❌ 无效选项，请重新选择")
                
        except KeyboardInterrupt:
            print("\n⏹️ 用户中断")
            return False

def full_cleanup(targets):
    """完全清理"""
    print("\n🔥 开始完全清理...")
    
    # 二次确认
    confirm = input("⚠️ 这将删除所有浏览器状态和缓存数据，确定继续吗？(y/N): ").strip().lower()
    if confirm != 'y':
        print("❌ 已取消清理")
        return False
    
    success_count = 0
    total_count = len(targets)
    
    for name, path in targets.items():
        if remove_target(path, name):
            success_count += 1
    
    print(f"\n📊 清理结果: {success_count}/{total_count} 项清理成功")
    
    if success_count == total_count:
        print("✅ 完全清理成功！系统已恢复到初始状态")
        print("💡 现在可以运行: uv run python hulu_automation_stealth.py")
        return True
    else:
        print("⚠️ 部分清理失败，请检查权限或手动删除")
        return False

def selective_cleanup(targets):
    """选择性清理"""
    print("\n🎯 选择性清理模式")
    print("请选择要清理的项目（输入编号，用空格分隔，如: 1 3 5）:")
    
    target_list = list(targets.items())
    for i, (name, path) in enumerate(target_list, 1):
        size_bytes, size_str = check_target_size(path)
        print(f"   {i}. {name}: {size_str}")
    
    try:
        selection = input("\n请输入要清理的编号: ").strip()
        if not selection:
            print("❌ 未选择任何项目")
            return False
        
        indices = [int(x) - 1 for x in selection.split()]
        selected_targets = {target_list[i][0]: target_list[i][1] for i in indices if 0 <= i < len(target_list)}
        
        if not selected_targets:
            print("❌ 无效选择")
            return False
        
        print(f"\n🎯 将清理 {len(selected_targets)} 个项目:")
        for name in selected_targets.keys():
            print(f"   - {name}")
        
        confirm = input("\n确定清理这些项目吗？(y/N): ").strip().lower()
        if confirm != 'y':
            print("❌ 已取消清理")
            return False
        
        success_count = 0
        for name, path in selected_targets.items():
            if remove_target(path, name):
                success_count += 1
        
        print(f"\n📊 清理结果: {success_count}/{len(selected_targets)} 项清理成功")
        return success_count > 0
        
    except (ValueError, IndexError):
        print("❌ 输入格式错误")
        return False

def silent_cleanup():
    """静默清理（命令行参数模式）"""
    targets = get_cleanup_targets()
    existing_targets = {name: path for name, path in targets.items() if path.exists()}
    
    if not existing_targets:
        print("✨ 系统已经很干净！")
        return True
    
    print("🧹 开始静默清理...")
    success_count = 0
    
    for name, path in existing_targets.items():
        if remove_target(path, name):
            success_count += 1
    
    print(f"📊 清理完成: {success_count}/{len(existing_targets)} 项成功")
    return success_count == len(existing_targets)

def main():
    """主函数"""
    if len(sys.argv) > 1 and sys.argv[1] in ['--silent', '-s']:
        # 静默模式
        success = silent_cleanup()
    else:
        # 交互模式
        success = interactive_cleanup()
    
    if success:
        print("\n🎉 清理完成！")
        print("💡 提示：清理后首次运行可能需要重新登录")
    
    return 0 if success else 1

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n⏹️ 用户中断")
        sys.exit(1)
    except Exception as e:
        logger.error(f"❌ 程序异常: {e}")
        sys.exit(1)