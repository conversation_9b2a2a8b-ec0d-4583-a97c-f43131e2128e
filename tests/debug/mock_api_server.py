#!/usr/bin/env python3
"""
模拟临时邮箱API服务器
用于测试Hulu Account C<PERSON>的功能
"""

from flask import Flask, request, jsonify
import time
import random
import string
import threading

app = Flask(__name__)

# 模拟数据存储
emails_storage = {}
verification_codes = {}

def generate_random_string(length=8):
    """生成随机字符串"""
    return ''.join(random.choices(string.ascii_lowercase + string.digits, k=length))

def generate_verification_code():
    """生成6位验证码"""
    return ''.join(random.choices(string.digits, k=6))

@app.route('/health', methods=['GET'])
def health_check():
    """健康检查端点"""
    return jsonify({
        "status": "healthy",
        "timestamp": time.strftime("%Y-%m-%dT%H:%M:%S.000000"),
        "uptime_seconds": 3600,
        "environment": "mock",
        "version": "1.0"
    })

@app.route('/api/automation/generate-email', methods=['POST'])
def generate_email():
    """生成临时邮箱"""
    data = request.get_json() or {}
    
    prefix = data.get('custom_prefix', 'test')
    domain = data.get('domain_name', 'testkuroneko.xyz')
    expires_hours = data.get('expires_hours', 2)
    session_id = data.get('automation_session_id', f'auto_{int(time.time())}')
    
    # 生成唯一的邮箱地址
    random_suffix = generate_random_string(6)
    email_address = f"{prefix}_{random_suffix}@{domain}"
    
    # 存储邮箱信息
    expires_at = time.time() + (expires_hours * 3600)
    emails_storage[email_address] = {
        'created_at': time.time(),
        'expires_at': expires_at,
        'session_id': session_id,
        'emails': []
    }
    
    return jsonify({
        "success": True,
        "data": {
            "email_address": email_address,
            "expires_at": time.strftime("%Y-%m-%dT%H:%M:%S.000000+00:00", time.gmtime(expires_at)),
            "automation_session_id": session_id
        },
        "error": None
    })

@app.route('/api/automation/wait-for-verification', methods=['POST'])
def wait_for_verification():
    """等待验证码邮件"""
    data = request.get_json() or {}
    
    email_address = data.get('email_address')
    timeout_seconds = data.get('timeout_seconds', 300)
    poll_interval = data.get('poll_interval', 5)
    
    if not email_address:
        return jsonify({
            "success": False,
            "error": "缺少邮箱地址参数",
            "error_code": "MISSING_EMAIL_ADDRESS"
        }), 400
    
    if email_address not in emails_storage:
        return jsonify({
            "success": False,
            "error": "邮箱不存在",
            "error_code": "EMAIL_NOT_FOUND"
        }), 404
    
    # 模拟延迟2-5秒后收到验证码邮件
    def simulate_email_arrival():
        time.sleep(random.uniform(2, 5))
        
        # 生成模拟验证码邮件
        verification_code = generate_verification_code()
        email_content = {
            "id": len(emails_storage[email_address]['emails']) + 1,
            "sender": "<EMAIL>",
            "subject": "Verify Your Email Address",
            "received_at": time.strftime("%Y-%m-%dT%H:%M:%S.000000"),
            "body_text": f"Your verification code is: {verification_code}. Please enter this code to complete your registration.",
            "body_html": f"<p>Your verification code is: <strong>{verification_code}</strong></p><p>Please enter this code to complete your registration.</p>"
        }
        
        emails_storage[email_address]['emails'].append(email_content)
        verification_codes[email_address] = verification_code
    
    # 启动模拟邮件到达的线程
    threading.Thread(target=simulate_email_arrival, daemon=True).start()
    
    # 轮询等待验证码
    start_time = time.time()
    while time.time() - start_time < timeout_seconds:
        if email_address in verification_codes:
            code = verification_codes[email_address]
            latest_email = emails_storage[email_address]['emails'][-1] if emails_storage[email_address]['emails'] else None
            
            return jsonify({
                "success": True,
                "data": {
                    "verification_codes": [code],
                    "email_found": True,
                    "wait_time_seconds": int(time.time() - start_time),
                    "email_details": latest_email
                }
            })
        
        time.sleep(poll_interval)
    
    # 超时未找到验证码
    return jsonify({
        "success": True,
        "data": {
            "verification_codes": [],
            "email_found": False,
            "wait_time_seconds": timeout_seconds,
            "email_details": None
        }
    })

@app.route('/api/automation/email-summary', methods=['GET'])
def email_summary():
    """获取邮箱摘要"""
    email_address = request.args.get('address')
    
    if not email_address:
        return jsonify({
            "success": False,
            "error": "缺少邮箱地址参数",
            "error_code": "MISSING_EMAIL_ADDRESS"
        }), 400
    
    if email_address not in emails_storage:
        return jsonify({
            "success": False,
            "error": "邮箱不存在",
            "error_code": "EMAIL_NOT_FOUND"
        }), 404
    
    email_data = emails_storage[email_address]
    latest_email = email_data['emails'][-1] if email_data['emails'] else None
    codes_found = [verification_codes[email_address]] if email_address in verification_codes else []
    
    return jsonify({
        "success": True,
        "data": {
            "address": email_address,
            "total_emails": len(email_data['emails']),
            "latest_email": latest_email,
            "verification_codes_found": codes_found,
            "status": "active"
        }
    })

@app.route('/api/csrf-token', methods=['GET'])
def csrf_token():
    """获取CSRF令牌（模拟）"""
    return jsonify({
        "success": True,
        "csrf_token": "mock_csrf_token_12345"
    })

if __name__ == '__main__':
    print("🚀 启动模拟临时邮箱API服务器...")
    print("📧 API Base URL: http://localhost:5000")
    print("🔍 健康检查: http://localhost:5000/health")
    print("📝 支持的端点:")
    print("   - POST /api/automation/generate-email")
    print("   - POST /api/automation/wait-for-verification")
    print("   - GET /api/automation/email-summary")
    print()
    
    app.run(host='0.0.0.0', port=5000, debug=True)