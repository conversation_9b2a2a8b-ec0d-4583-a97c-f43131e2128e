#!/usr/bin/env python3
"""
工具函数集合 - 简化版Hulu Account Creator
集成临时邮箱API、CSV写入和日志配置功能
"""

import requests
import time
import csv
import logging
import os
import random
import string
import re
from typing import Optional, Dict, List
import urllib3
import glob
from datetime import datetime, timedelta

# 禁用SSL警告（开发环境）
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 加载环境变量
def load_env_config():
    """
    加载环境变量配置
    
    Returns:
        Dict[str, str]: 配置字典
    """
    # 尝试加载 .env 文件
    env_file = os.path.join(os.path.dirname(__file__), '.env')
    if os.path.exists(env_file):
        with open(env_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    os.environ[key.strip()] = value.strip()
    
    return {
        'API_BASE': os.getenv('API_BASE', 'https://testkuroneko.xyz/api'),
        'API_KEY': os.getenv('API_KEY'),
        'DEFAULT_EMAIL_DOMAIN': os.getenv('DEFAULT_EMAIL_DOMAIN', 'testkuroneko.xyz'),
        'API_TIMEOUT': int(os.getenv('API_TIMEOUT', '30')),
        'BROWSER_TIMEOUT': int(os.getenv('BROWSER_TIMEOUT', '30000')),
        'DEFAULT_HEADLESS': os.getenv('DEFAULT_HEADLESS', 'true').lower() == 'true',
        'PAGE_LOAD_TIMEOUT': int(os.getenv('PAGE_LOAD_TIMEOUT', '30000')),
        'DEFAULT_OUTPUT_FILE': os.getenv('DEFAULT_OUTPUT_FILE', 'data/accounts.csv'),
        'LOG_FILE': os.getenv('LOG_FILE', 'logs/hulu_creator.log'),
        'LOG_LEVEL': os.getenv('LOG_LEVEL', 'INFO'),
        'APP_MODE': os.getenv('APP_MODE', 'development'),
        'DEBUG': os.getenv('DEBUG', 'false').lower() == 'true',
        'VERIFICATION_TIMEOUT': int(os.getenv('VERIFICATION_TIMEOUT', '300')),
        'VERIFICATION_POLL_INTERVAL': int(os.getenv('VERIFICATION_POLL_INTERVAL', '5')),
        
        # reCAPTCHA 求解服务配置
        'TWOCAPTCHA_API_KEY': os.getenv('TWOCAPTCHA_API_KEY'),
        'DBC_USERNAME': os.getenv('DBC_USERNAME'),
        'DBC_PASSWORD': os.getenv('DBC_PASSWORD'),
        'RECAPTCHA_TIMEOUT': int(os.getenv('RECAPTCHA_TIMEOUT', '120')),
        'RECAPTCHA_MIN_SCORE': float(os.getenv('RECAPTCHA_MIN_SCORE', '0.3')),
        
        # 截图配置
        'SCREENSHOT_QUALITY': int(os.getenv('SCREENSHOT_QUALITY', '90')),
        'SCREENSHOT_INCLUDE_ADDRESS_BAR': os.getenv('SCREENSHOT_INCLUDE_ADDRESS_BAR', 'true').lower() == 'true',
        'SCREENSHOT_DIRECTORY': os.getenv('SCREENSHOT_DIRECTORY', 'screenshots'),
        'SCREENSHOT_CLEANUP_DAYS': int(os.getenv('SCREENSHOT_CLEANUP_DAYS', '7')),
        'SCREENSHOT_BROWSER_KEYWORDS': os.getenv('SCREENSHOT_BROWSER_KEYWORDS', 'Chrome,Chromium,Firefox,Safari,Edge,Hulu').split(','),
        'ENABLE_BROWSER_SCREENSHOT': os.getenv('ENABLE_BROWSER_SCREENSHOT', 'true').lower() == 'true'
    }

# 加载配置
CONFIG = load_env_config()

# 配置常量（向后兼容）
API_BASE = CONFIG['API_BASE']
TIMEOUT_SECONDS = CONFIG['VERIFICATION_TIMEOUT'] 
POLL_INTERVAL = CONFIG['VERIFICATION_POLL_INTERVAL']

class TempMailAPI:
    """简化的临时邮箱API客户端"""
    
    def __init__(self, api_key: Optional[str] = None):
        """
        初始化临时邮箱API客户端
        
        Args:
            api_key: 可选的API密钥，用于认证（如果未提供，将尝试从环境变量获取）
        """
        # 如果没有提供API密钥，尝试从环境变量获取
        self.api_key = api_key or CONFIG.get('API_KEY')
        self.api_base = CONFIG['API_BASE']
        self.timeout = CONFIG['API_TIMEOUT']
        self.headers = {"Content-Type": "application/json"}
        if self.api_key:
            self.headers["Authorization"] = f"Bearer {self.api_key}"
    
    def get_csrf_token(self) -> str:
        """
        获取CSRF令牌
        
        Returns:
            str: CSRF令牌
            
        Raises:
            Exception: 获取失败时抛出异常
        """
        try:
            response = requests.get(
                f"{self.api_base}/csrf-token",
                timeout=self.timeout,
                verify=False
            )
            response.raise_for_status()
            data = response.json()
            
            if data["success"]:
                return data["csrf_token"]
            else:
                raise Exception(f"获取CSRF令牌失败: {data.get('error', '未知错误')}")
                
        except requests.RequestException as e:
            raise Exception(f"网络请求失败: {str(e)}")
    
    def generate_email(self, prefix: str = "hulu") -> str:
        """
        生成临时邮箱地址 - 使用匿名访问模式
        
        Args:
            prefix: 邮箱前缀
            
        Returns:
            str: 生成的邮箱地址
            
        Raises:
            Exception: API调用失败时抛出异常
        """
        try:
            # 添加随机字符串确保邮箱地址唯一性
            import random
            import string
            random_suffix = ''.join(random.choices(string.ascii_lowercase + string.digits, k=8))
            unique_prefix = f"{prefix}_{random_suffix}"
            
            # 使用自动化API的匿名访问模式
            response = requests.post(
                f"{self.api_base}/automation/generate-email",
                json={
                    "custom_prefix": unique_prefix, 
                    "domain_name": CONFIG['DEFAULT_EMAIL_DOMAIN'],
                    "expires_hours": 2,
                    "automation_session_id": f"hulu_{int(time.time())}_{random_suffix}"
                },
                headers=self.headers,
                timeout=self.timeout,
                verify=False
            )
            response.raise_for_status()
            data = response.json()
            
            if data["success"]:
                return data["data"]["email_address"]
            else:
                raise Exception(f"API错误: {data.get('error', '未知错误')}")
                
        except requests.RequestException as e:
            raise Exception(f"网络请求失败: {str(e)}")
    
    def wait_for_verification_code(self, email_address: str) -> Optional[str]:
        """
        等待并获取验证码 - 使用自动化API
        
        Args:
            email_address: 邮箱地址
            
        Returns:
            Optional[str]: 验证码，如果未找到则返回None
        """
        try:
            logging.info(f"🔍 开始等待邮件验证码: {email_address}")
            logging.info(f"⏱️ 超时时间: {CONFIG['VERIFICATION_TIMEOUT']}秒")
            
            # 使用自动化API的等待验证码功能（匿名访问模式）
            request_data = {
                "email_address": email_address,
                "timeout_seconds": CONFIG['VERIFICATION_TIMEOUT'],
                "poll_interval": CONFIG['VERIFICATION_POLL_INTERVAL'],
                "verification_patterns": [
                    "验证码[：:]\\s*(\\d{4,8})",
                    "code[：:]\\s*(\\d{4,8})",
                    "verification code[：:]\\s*(\\d{4,8})",
                    "Your verification code is[：:]\\s*(\\d{4,8})",
                    "Enter this code[：:]\\s*(\\d{4,8})",
                    "Please use this code[：:]\\s*(\\d{4,8})",
                    "\\b(\\d{4,8})\\b"  # 通用数字匹配
                ]
            }
            
            # 构建完整的API URL
            api_url = f"{self.api_base}/automation/wait-for-verification"
            logging.info(f"📤 发送API请求到: {api_url}")
            logging.info(f"📝 请求数据: {request_data}")
            
            response = requests.post(
                api_url,
                json=request_data,
                headers=self.headers,
                timeout=CONFIG['VERIFICATION_TIMEOUT'] + 30,  # 给API额外30秒处理时间
                verify=False
            )
            
            logging.info(f"📥 API响应状态码: {response.status_code}")
            
            response.raise_for_status()
            
            # 检查响应内容
            response_text = response.text.strip()
            logging.info(f"📥 API响应原始内容: {response_text[:200]}...")  # 只显示前200字符
            
            if not response_text:
                logging.error("❌ API返回空响应")
                return None
                
            try:
                data = response.json()
            except ValueError as e:
                logging.error(f"❌ JSON解析失败: {e}")
                logging.error(f"📥 完整响应内容: {response_text}")
                return None
            
            logging.info(f"📥 API响应数据: {data}")
            
            if data["success"] and data["data"]["verification_codes"]:
                verification_code = data["data"]["verification_codes"][0]
                logging.info(f"✅ 成功获取验证码: {verification_code}")
                
                # 记录邮件详情
                if "email_details" in data["data"]:
                    email_details = data["data"]["email_details"]
                    logging.info(f"📧 邮件发送者: {email_details.get('sender', 'N/A')}")
                    logging.info(f"📧 邮件主题: {email_details.get('subject', 'N/A')}")
                    logging.info(f"📧 接收时间: {email_details.get('received_at', 'N/A')}")
                
                # 记录等待时间
                wait_time = data["data"].get("wait_time_seconds", 0)
                logging.info(f"⏱️ 等待时间: {wait_time}秒")
                
                return verification_code
            else:
                logging.warning("⚠️ API响应成功但未找到验证码")
                if "error" in data:
                    logging.warning(f"❌ API错误信息: {data['error']}")
                return None
            
        except requests.RequestException as e:
            logging.error(f"❌ 获取验证码网络请求失败: {str(e)}")
            if hasattr(e, 'response') and e.response is not None:
                try:
                    error_data = e.response.json()
                    logging.error(f"❌ API错误响应: {error_data}")
                except:
                    logging.error(f"❌ HTTP错误响应: {e.response.text}")
            return None
        except Exception as e:
            logging.error(f"❌ 获取验证码时发生异常: {str(e)}")
            return None
    
    def wait_for_hulu_verification_code(self, email_address: str, timeout: int = 120) -> Optional[str]:
        """
        专门为Hulu邮件验证等待验证码
        
        Args:
            email_address: 邮箱地址
            timeout: 超时时间（秒）
            
        Returns:
            Optional[str]: Hulu验证码，如果未找到则返回None
        """
        try:
            logging.info(f"🎯 开始等待Hulu邮件验证码: {email_address}")
            logging.info(f"⏱️ 超时时间: {timeout}秒")
            
            # Hulu特定的验证码模式
            hulu_patterns = [
                # Hulu常用的验证码格式
                "verification code.*?(\\d{6})",
                "code.*?(\\d{6})", 
                "verify.*?(\\d{6})",
                "security code.*?(\\d{6})",
                "confirm.*?(\\d{6})",
                "(\\d{6}).*?hulu",
                "(\\d{6}).*?verify",
                "(\\d{6}).*?confirm",
                # 通用6位数字模式
                "\\b(\\d{6})\\b",
                # 其他可能的格式
                "验证码.*?(\\d{6})",
                "Your code.*?(\\d{6})",
                "Enter.*?(\\d{6})",
                "Use.*?(\\d{6})"
            ]
            
            request_data = {
                "email_address": email_address,
                "timeout_seconds": timeout,
                "poll_interval": 3,  # Hulu验证码通常很快到达，3秒轮询间隔
                "verification_patterns": hulu_patterns
            }
            
            api_url = f"{self.api_base}/automation/wait-for-verification"
            logging.info(f"📤 发送Hulu验证码请求到: {api_url}")
            logging.info(f"📝 请求参数: {request_data}")
            
            response = requests.post(
                api_url,
                json=request_data,
                headers=self.headers,
                timeout=timeout + 30,
                verify=False
            )
            
            logging.info(f"📥 API响应状态码: {response.status_code}")
            
            response.raise_for_status()
            
            # 检查响应内容
            response_text = response.text.strip()
            logging.info(f"📥 API响应原始内容: {response_text[:200]}...")  # 只显示前200字符
            
            if not response_text:
                logging.error("❌ API返回空响应")
                return None
                
            try:
                data = response.json()
            except ValueError as e:
                logging.error(f"❌ JSON解析失败: {e}")
                logging.error(f"📥 完整响应内容: {response_text}")
                return None
            
            logging.info(f"📥 API响应数据: {data}")
            
            if data["success"] and data["data"]["verification_codes"]:
                verification_code = data["data"]["verification_codes"][0]
                logging.info(f"✅ 成功获取Hulu验证码: {verification_code}")
                
                # 记录邮件详情
                if "email_details" in data["data"]:
                    email_details = data["data"]["email_details"]
                    logging.info(f"📧 Hulu邮件发送者: {email_details.get('sender', 'N/A')}")
                    logging.info(f"📧 邮件主题: {email_details.get('subject', 'N/A')}")
                
                wait_time = data["data"].get("wait_time_seconds", 0)
                logging.info(f"⏱️ Hulu验证码等待时间: {wait_time}秒")
                
                return verification_code
            else:
                logging.warning("⚠️ 未找到Hulu验证码")
                if "error" in data:
                    logging.warning(f"❌ API错误信息: {data['error']}")
                return None
            
        except requests.RequestException as e:
            logging.error(f"❌ 获取Hulu验证码网络请求失败: {str(e)}")
            if hasattr(e, 'response') and e.response is not None:
                try:
                    error_data = e.response.json()
                    logging.error(f"❌ API错误响应: {error_data}")
                except:
                    logging.error(f"❌ HTTP错误响应: {e.response.text}")
            return None
        except Exception as e:
            logging.error(f"❌ 获取Hulu验证码时发生异常: {str(e)}")
            logging.error(f"🔍 异常类型: {type(e).__name__}")
            return None

def save_account_to_csv(account_info: Dict[str, str], filename: str = "accounts.csv"):
    """
    保存账户信息到CSV文件
    
    Args:
        account_info: 包含账户信息的字典
        filename: CSV文件名
    """
    import os
    
    # 检查文件是否存在且非空
    file_exists_and_not_empty = os.path.exists(filename) and os.path.getsize(filename) > 0
    
    with open(filename, 'a', newline='', encoding='utf-8') as file:
        writer = csv.writer(file)
        
        # 如果文件不存在或为空，写入表头
        if not file_exists_and_not_empty:
            writer.writerow(['email', 'password', 'name', 'created_at'])
        
        # 写入账户数据
        writer.writerow([
            account_info.get('email', ''),
            account_info.get('password', ''),
            account_info.get('name', ''),
            account_info.get('created_at', '')
        ])

def setup_logging() -> logging.Logger:
    """
    设置简单的日志记录
    
    Returns:
        logging.Logger: 配置好的日志记录器
    """
    # 获取日志级别
    log_level = getattr(logging, CONFIG['LOG_LEVEL'].upper(), logging.INFO)
    
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler(CONFIG['LOG_FILE'], encoding='utf-8')
        ]
    )
    return logging.getLogger(__name__)

def generate_random_user_info() -> Dict[str, str]:
    """
    生成随机用户信息
    
    Returns:
        Dict[str, str]: 包含姓名和密码的字典
    """
    import random
    import string
    
    # 简单的姓名列表
    first_names = ["Alex", "Jordan", "Taylor", "Casey", "Morgan", "Riley", "Avery", "Quinn"]
    last_names = ["Smith", "Johnson", "Brown", "Davis", "Wilson", "Miller", "Moore", "Taylor"]
    
    # 生成随机密码
    password_chars = string.ascii_letters + string.digits + "!@#$%"
    password = ''.join(random.choice(password_chars) for _ in range(12))
    
    return {
        "name": f"{random.choice(first_names)} {random.choice(last_names)}",
        "password": password
    }

def validate_email(email: str) -> bool:
    """
    简单的邮箱格式验证
    
    Args:
        email: 邮箱地址
        
    Returns:
        bool: 是否为有效邮箱格式
    """
    import re
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None

def get_api_status(api_base: str = API_BASE) -> Dict:
    """
    检查API服务状态
    
    Args:
        api_base: API基础URL
        
    Returns:
        Dict: API状态信息
    """
    try:
        response = requests.get(f"{api_base}/health", timeout=5)
        response.raise_for_status()
        return response.json()
    except requests.RequestException as e:
        return {"status": "error", "message": str(e)}

def cleanup_old_screenshots(screenshot_dir: str = None, days_to_keep: int = None) -> Dict:
    """
    清理过期的截图文件
    
    Args:
        screenshot_dir: 截图目录路径，默认从配置获取
        days_to_keep: 保留天数，默认从配置获取
        
    Returns:
        Dict: 清理结果统计
    """
    if screenshot_dir is None:
        screenshot_dir = CONFIG['SCREENSHOT_DIRECTORY']
    if days_to_keep is None:
        days_to_keep = CONFIG['SCREENSHOT_CLEANUP_DAYS']
    
    result = {
        'total_files': 0,
        'deleted_files': 0,
        'errors': [],
        'freed_space': 0
    }
    
    try:
        # 确保截图目录存在
        if not os.path.exists(screenshot_dir):
            return result
        
        # 计算截止时间
        cutoff_time = datetime.now() - timedelta(days=days_to_keep)
        
        # 查找所有截图文件
        screenshot_patterns = [
            os.path.join(screenshot_dir, "*.png"),
            os.path.join(screenshot_dir, "*.jpg"),
            os.path.join(screenshot_dir, "*.jpeg")
        ]
        
        for pattern in screenshot_patterns:
            for file_path in glob.glob(pattern):
                result['total_files'] += 1
                
                try:
                    # 获取文件修改时间
                    file_mtime = datetime.fromtimestamp(os.path.getmtime(file_path))
                    
                    # 如果文件超过保留时间，删除它
                    if file_mtime < cutoff_time:
                        file_size = os.path.getsize(file_path)
                        os.remove(file_path)
                        result['deleted_files'] += 1
                        result['freed_space'] += file_size
                        
                except Exception as e:
                    result['errors'].append(f"删除文件失败 {file_path}: {str(e)}")
        
        # 记录清理结果
        logger = logging.getLogger(__name__)
        if result['deleted_files'] > 0:
            freed_mb = result['freed_space'] / (1024 * 1024)
            logger.info(f"🧹 截图清理完成: 删除 {result['deleted_files']} 个文件，释放 {freed_mb:.2f} MB 空间")
        
        return result
        
    except Exception as e:
        result['errors'].append(f"截图清理失败: {str(e)}")
        return result

def ensure_screenshot_directory() -> bool:
    """
    确保截图目录存在
    
    Returns:
        bool: 是否成功创建或目录已存在
    """
    try:
        screenshot_dir = CONFIG['SCREENSHOT_DIRECTORY']
        os.makedirs(screenshot_dir, exist_ok=True)
        return True
    except Exception as e:
        logger = logging.getLogger(__name__)
        logger.error(f"❌ 创建截图目录失败: {str(e)}")
        return False

# 导出的公共接口
__all__ = [
    'TempMailAPI',
    'save_account_to_csv', 
    'setup_logging',
    'generate_random_user_info',
    'validate_email',
    'get_api_status',
    'cleanup_old_screenshots',
    'ensure_screenshot_directory'
]
