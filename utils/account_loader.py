#!/usr/bin/env python3
"""
AccountLoader - 账户加载工具
简单的CSV账户文件加载器，支持多账户管理
"""

import csv
import os
import logging
from typing import List, Dict, Optional
from dataclasses import dataclass


@dataclass
class AccountInfo:
    """账户信息数据类"""
    email: str
    password: str
    cookies_status: str = "fresh"  # Cookies状态: fresh, active, expired, invalid
    index: int = 0  # 在CSV中的索引位置


class AccountLoader:
    """
    简单的账户加载器
    从CSV文件加载多个账户信息，支持按索引或email查找
    """
    
    def __init__(self, csv_file: str = "data/accounts.csv"):
        """
        初始化账户加载器
        
        Args:
            csv_file: CSV账户文件路径
        """
        self.csv_file = csv_file
        self.logger = logging.getLogger(__name__)
        self._accounts_cache: List[AccountInfo] = []
        self._load_accounts()
    
    def _load_accounts(self) -> None:
        """从CSV文件加载账户信息"""
        try:
            if not os.path.exists(self.csv_file):
                self.logger.warning(f"账户文件不存在: {self.csv_file}")
                return
            
            self._accounts_cache = []
            
            with open(self.csv_file, 'r', encoding='utf-8') as file:
                # 检查文件是否为空
                content = file.read().strip()
                if not content:
                    self.logger.warning(f"账户文件为空: {self.csv_file}")
                    return
                
                # 重置文件指针
                file.seek(0)
                
                csv_reader = csv.DictReader(file)
                
                for index, row in enumerate(csv_reader):
                    # 处理可能的空格和大小写问题
                    email = row.get('email', '').strip()
                    password = row.get('password', '').strip()
                    cookies_status = row.get('cookies_status', 'fresh').strip()
                    
                    # 如果没有email列，尝试其他可能的列名
                    if not email:
                        for key in row.keys():
                            if 'email' in key.lower() or 'mail' in key.lower():
                                email = row[key].strip()
                                break
                    
                    # 如果没有password列，尝试其他可能的列名
                    if not password:
                        for key in row.keys():
                            if 'password' in key.lower() or 'pass' in key.lower() or 'pwd' in key.lower():
                                password = row[key].strip()
                                break
                    
                    # 如果没有cookies_status列，尝试其他可能的列名
                    if not cookies_status or cookies_status == 'fresh':
                        for key in row.keys():
                            if 'cookies_status' in key.lower() or 'status' in key.lower():
                                cookies_status = row[key].strip() or 'fresh'
                                break
                        if not cookies_status:
                            cookies_status = 'fresh'  # 默认值
                    
                    if email and password:
                        account_info = AccountInfo(
                            email=email,
                            password=password,
                            cookies_status=cookies_status,
                            index=index
                        )
                        self._accounts_cache.append(account_info)
                        self.logger.debug(f"加载账户 {index}: {email} (状态: {cookies_status})")
                    else:
                        self.logger.warning(f"跳过无效账户记录 {index}: email='{email}', password='{password}'")
            
            self.logger.info(f"成功加载 {len(self._accounts_cache)} 个账户")
            
        except Exception as e:
            self.logger.error(f"加载账户文件失败: {e}")
            self._accounts_cache = []
    
    def load_all_accounts(self) -> List[AccountInfo]:
        """
        获取所有账户信息
        
        Returns:
            List[AccountInfo]: 所有账户信息列表
        """
        return self._accounts_cache.copy()
    
    def get_account_by_index(self, index: int) -> Optional[AccountInfo]:
        """
        根据索引获取账户信息
        
        Args:
            index: 账户索引（从0开始）
            
        Returns:
            Optional[AccountInfo]: 账户信息，如果不存在返回None
        """
        if 0 <= index < len(self._accounts_cache):
            return self._accounts_cache[index]
        else:
            self.logger.warning(f"账户索引超出范围: {index}, 总数: {len(self._accounts_cache)}")
            return None
    
    def get_account_by_email(self, email: str) -> Optional[AccountInfo]:
        """
        根据email获取账户信息
        
        Args:
            email: 邮箱地址
            
        Returns:
            Optional[AccountInfo]: 账户信息，如果不存在返回None
        """
        for account in self._accounts_cache:
            if account.email.lower() == email.lower():
                return account
        
        self.logger.warning(f"未找到email为 {email} 的账户")
        return None
    
    def get_account_count(self) -> int:
        """
        获取账户数量
        
        Returns:
            int: 账户总数
        """
        return len(self._accounts_cache)
    
    def reload_accounts(self) -> None:
        """重新加载账户文件"""
        self.logger.info("重新加载账户文件...")
        self._load_accounts()
    
    def update_cookies_status(self, email: str, status: str) -> bool:
        """
        更新指定账户的cookies状态
        
        Args:
            email: 账户邮箱地址
            status: 新的状态值 (fresh, active, expired, invalid)
            
        Returns:
            bool: 是否成功更新
        """
        try:
            # 验证状态值
            valid_statuses = ['fresh', 'active', 'expired', 'invalid']
            if status not in valid_statuses:
                self.logger.error(f"无效的状态值: {status}，有效值: {valid_statuses}")
                return False
            
            # 查找并更新账户
            for account in self._accounts_cache:
                if account.email.lower() == email.lower():
                    old_status = account.cookies_status
                    account.cookies_status = status
                    self.logger.info(f"账户 {email} 状态更新: {old_status} → {status}")
                    
                    # 保存到CSV文件
                    return self.save_accounts()
            
            self.logger.warning(f"未找到email为 {email} 的账户")
            return False
            
        except Exception as e:
            self.logger.error(f"更新账户状态时出错: {e}")
            return False
    
    def save_accounts(self) -> bool:
        """
        将账户信息保存回CSV文件
        
        Returns:
            bool: 是否成功保存
        """
        try:
            import csv
            
            # 创建备份（可选）
            backup_path = f"{self.csv_file}.backup"
            
            with open(self.csv_file, 'w', newline='', encoding='utf-8') as file:
                fieldnames = ['email', 'password', 'cookies_status']
                writer = csv.DictWriter(file, fieldnames=fieldnames)
                
                # 写入表头
                writer.writeheader()
                
                # 写入账户数据
                for account in self._accounts_cache:
                    writer.writerow({
                        'email': account.email,
                        'password': account.password,
                        'cookies_status': account.cookies_status
                    })
            
            self.logger.info(f"账户信息已保存到 {self.csv_file}")
            return True
            
        except Exception as e:
            self.logger.error(f"保存账户信息失败: {e}")
            return False


def get_session_path_for_account(email: str, sessions_dir: str = "data/sessions") -> str:
    """
    为指定账户生成session文件路径
    
    Args:
        email: 账户邮箱
        sessions_dir: session文件存储目录
        
    Returns:
        str: session文件路径
    """
    # 将email转换为安全的文件名
    safe_email = email.replace('@', '_').replace('.', '_').replace('/', '_').replace('\\', '_')
    
    # 确保目录存在
    os.makedirs(sessions_dir, exist_ok=True)
    
    return os.path.join(sessions_dir, f"{safe_email}_session.json")


# 便捷函数
def load_account_by_index(index: int, csv_file: str = "data/accounts.csv") -> Optional[AccountInfo]:
    """
    便捷函数：根据索引加载账户
    
    Args:
        index: 账户索引
        csv_file: CSV文件路径
        
    Returns:
        Optional[AccountInfo]: 账户信息
    """
    loader = AccountLoader(csv_file)
    return loader.get_account_by_index(index)


def load_account_by_email(email: str, csv_file: str = "data/accounts.csv") -> Optional[AccountInfo]:
    """
    便捷函数：根据email加载账户
    
    Args:
        email: 邮箱地址
        csv_file: CSV文件路径
        
    Returns:
        Optional[AccountInfo]: 账户信息
    """
    loader = AccountLoader(csv_file)
    return loader.get_account_by_email(email)