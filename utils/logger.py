#!/usr/bin/env python3
"""
结构化日志系统 - Quick-Wins优化版本
基于 structlog 的统一日志管理，与现有 SimpleMonitor 系统集成

核心功能:
1. 结构化日志输出 - 统一字段格式
2. 多种输出格式 - 开发环境友好 / 生产环境JSON
3. 监控系统集成 - 与 SimpleMonitor 无缝集成  
4. 性能优化 - 低开销的异步日志
5. 向后兼容 - 保持现有代码正常工作

设计原则:
- 统一字段: task_id, phase, selector, status, duration_ms, proxy_id
- 开发友好: 彩色输出，易读格式
- 生产就绪: JSON格式，结构化查询
- 低开销: <5ms/操作的日志开销
"""

import logging
import os
import sys
import time
from pathlib import Path
from typing import Any, Dict, Optional, Union
import json
import uuid

try:
    import structlog
    STRUCTLOG_AVAILABLE = True
except ImportError:
    STRUCTLOG_AVAILABLE = False
    print("⚠️ structlog未安装，将使用标准logging")


class StructuredLogger:
    """
    结构化日志管理器
    
    特性:
    - structlog 集成，支持结构化数据
    - 多种输出格式：开发环境彩色输出 / 生产环境JSON
    - 统一字段规范：task_id, phase, selector等
    - 与现有 SimpleMonitor 系统集成
    """
    
    def __init__(self, 
                 name: str = "hulu_automation",
                 log_level: str = "INFO",
                 output_format: str = "auto",  # auto, dev, prod, json
                 log_file: Optional[str] = None):
        """
        初始化结构化日志器
        
        Args:
            name: 日志器名称
            log_level: 日志级别 (DEBUG, INFO, WARNING, ERROR)
            output_format: 输出格式 (auto, dev, prod, json)
            log_file: 日志文件路径，None表示不写文件
        """
        self.name = name
        self.log_level = getattr(logging, log_level.upper(), logging.INFO)
        self.output_format = self._determine_format(output_format)
        self.log_file = log_file
        
        # 设置基本字段
        self.base_context = {
            "service": "hulu-automation",
            "version": "2.1.0"
        }
        
        # 初始化logger
        if STRUCTLOG_AVAILABLE:
            self.logger = self._setup_structlog()
        else:
            self.logger = self._setup_standard_logging()
        
        if STRUCTLOG_AVAILABLE:
            self.logger.info("📊 结构化日志系统已初始化", 
                            format=self.output_format, 
                            structlog_available=STRUCTLOG_AVAILABLE)
        else:
            self.logger.info(f"📊 结构化日志系统已初始化 (format={self.output_format}, structlog={STRUCTLOG_AVAILABLE})")
    
    def _determine_format(self, format_setting: str) -> str:
        """确定输出格式"""
        if format_setting == "auto":
            # 自动检测：开发环境 vs 生产环境
            if os.getenv("ENVIRONMENT") == "production":
                return "prod"
            elif sys.stdout.isatty():  # 终端环境
                return "dev"
            else:  # 重定向/管道环境
                return "json"
        return format_setting
    
    def _setup_structlog(self):
        """设置 structlog 配置"""
        
        # 处理器链配置
        processors = [
            # 添加时间戳
            structlog.processors.TimeStamper(fmt="iso"),
            # 添加日志级别
            structlog.stdlib.add_log_level,
            # 添加调用者信息 (仅DEBUG级别)
            structlog.processors.CallsiteParameterAdder(
                parameters=[structlog.processors.CallsiteParameter.FILENAME,
                           structlog.processors.CallsiteParameter.LINENO]
            ) if self.log_level <= logging.DEBUG else lambda logger, method_name, event_dict: event_dict,
        ]
        
        # 根据输出格式选择渲染器
        if self.output_format == "dev":
            # 开发环境：彩色输出，易读格式
            processors.append(
                structlog.dev.ConsoleRenderer(colors=True)
            )
        elif self.output_format == "json" or self.output_format == "prod":
            # 生产环境：JSON格式
            processors.append(
                structlog.processors.JSONRenderer()
            )
        else:
            # 默认格式
            processors.append(
                structlog.dev.ConsoleRenderer(colors=False)
            )
        
        # 配置 structlog
        structlog.configure(
            processors=processors,
            wrapper_class=structlog.stdlib.BoundLogger,
            logger_factory=structlog.stdlib.LoggerFactory(),
            cache_logger_on_first_use=True,
        )
        
        # 创建logger实例
        logger = structlog.get_logger(self.name)
        
        # 绑定基础上下文
        return logger.bind(**self.base_context)
    
    def _setup_standard_logging(self):
        """设置标准 logging (fallback)"""
        logger = logging.getLogger(self.name)
        logger.setLevel(self.log_level)
        
        # 清除现有处理器
        if logger.handlers:
            logger.handlers.clear()
        
        # 控制台处理器
        console_handler = logging.StreamHandler()
        
        if self.output_format == "json":
            # JSON格式处理器
            formatter = JsonFormatter()
        else:
            # 标准格式处理器
            formatter = logging.Formatter(
                '%(asctime)s | %(levelname)8s | %(name)s | %(message)s',
                datefmt='%H:%M:%S'
            )
        
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
        
        # 文件处理器 (如果指定)
        if self.log_file:
            file_handler = logging.FileHandler(self.log_file, encoding='utf-8')
            file_handler.setFormatter(JsonFormatter())  # 文件始终使用JSON格式
            logger.addHandler(file_handler)
        
        return logger
    
    def bind(self, **context) -> 'StructuredLogger':
        """
        绑定上下文信息，返回新的logger实例
        
        Args:
            **context: 要绑定的上下文字段
            
        Returns:
            StructuredLogger: 绑定了上下文的新logger实例
        """
        if STRUCTLOG_AVAILABLE:
            new_logger = self.logger.bind(**context)
            # 创建新的StructuredLogger实例
            instance = self.__class__.__new__(self.__class__)
            instance.__dict__.update(self.__dict__)
            instance.logger = new_logger
            return instance
        else:
            # 标准logging不支持bind，返回当前实例
            return self
    
    def info(self, message: str, **kwargs):
        """记录INFO级别日志"""
        if STRUCTLOG_AVAILABLE:
            self.logger.info(message, **kwargs)
        else:
            extra_info = f" | {json.dumps(kwargs, ensure_ascii=False)}" if kwargs else ""
            self.logger.info(f"{message}{extra_info}")
    
    def debug(self, message: str, **kwargs):
        """记录DEBUG级别日志"""
        if STRUCTLOG_AVAILABLE:
            self.logger.debug(message, **kwargs)
        else:
            extra_info = f" | {json.dumps(kwargs, ensure_ascii=False)}" if kwargs else ""
            self.logger.debug(f"{message}{extra_info}")
    
    def warning(self, message: str, **kwargs):
        """记录WARNING级别日志"""
        if STRUCTLOG_AVAILABLE:
            self.logger.warning(message, **kwargs)
        else:
            extra_info = f" | {json.dumps(kwargs, ensure_ascii=False)}" if kwargs else ""
            self.logger.warning(f"{message}{extra_info}")
    
    def error(self, message: str, **kwargs):
        """记录ERROR级别日志"""
        if STRUCTLOG_AVAILABLE:
            self.logger.error(message, **kwargs)
        else:
            extra_info = f" | {json.dumps(kwargs, ensure_ascii=False)}" if kwargs else ""
            self.logger.error(f"{message}{extra_info}")
    
    def exception(self, message: str, **kwargs):
        """记录异常信息"""
        if STRUCTLOG_AVAILABLE:
            self.logger.exception(message, **kwargs)
        else:
            extra_info = f" | {json.dumps(kwargs, ensure_ascii=False)}" if kwargs else ""
            self.logger.exception(f"{message}{extra_info}")


class JsonFormatter(logging.Formatter):
    """JSON格式的日志格式化器 (标准logging fallback用)"""
    
    def format(self, record):
        log_entry = {
            "timestamp": self.formatTime(record, "%Y-%m-%dT%H:%M:%S.%fZ"),
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno
        }
        
        # 添加异常信息
        if record.exc_info:
            log_entry["exception"] = self.formatException(record.exc_info)
        
        return json.dumps(log_entry, ensure_ascii=False)


class TaskLogger:
    """
    任务级别的日志器 - 与 SimpleMonitor 集成
    
    提供统一的字段格式：task_id, phase, selector, status, duration_ms
    """
    
    def __init__(self, 
                 base_logger: Optional[StructuredLogger] = None,
                 task_id: Optional[str] = None):
        """
        初始化任务日志器
        
        Args:
            base_logger: 基础日志器，None表示创建新实例
            task_id: 任务ID，None表示自动生成
        """
        self.base_logger = base_logger or get_logger()
        self.task_id = task_id or str(uuid.uuid4())[:8]
        
        # 绑定任务上下文
        self.logger = self.base_logger.bind(task_id=self.task_id)
    
    def log_phase_start(self, phase: str, **context):
        """记录阶段开始"""
        self.logger.info(f"🔄 {phase} 开始", 
                        phase=phase, 
                        status="started",
                        **context)
    
    def log_phase_success(self, phase: str, duration_ms: float, 
                         selector: Optional[str] = None, **context):
        """记录阶段成功"""
        self.logger.info(f"✅ {phase} 成功", 
                        phase=phase,
                        status="success",
                        duration_ms=round(duration_ms, 2),
                        selector=selector,
                        **context)
    
    def log_phase_failure(self, phase: str, duration_ms: float,
                         error_message: str, selector: Optional[str] = None, 
                         **context):
        """记录阶段失败"""
        self.logger.error(f"❌ {phase} 失败: {error_message}",
                         phase=phase,
                         status="failure", 
                         duration_ms=round(duration_ms, 2),
                         selector=selector,
                         error_message=error_message,
                         **context)
    
    def log_selector_found(self, selector: str, phase: str, 
                          duration_ms: float, **context):
        """记录选择器找到"""
        self.logger.info(f"🔍 选择器找到: {selector}",
                        phase=phase,
                        selector=selector,
                        status="selector_found",
                        duration_ms=round(duration_ms, 2),
                        **context)
    
    def log_selector_failed(self, selectors: list, phase: str,
                           duration_ms: float, **context):
        """记录选择器失败"""
        self.logger.warning(f"⚠️ 选择器未找到: 尝试了 {len(selectors)} 个",
                           phase=phase,
                           selector_count=len(selectors),
                           status="selector_failed", 
                           duration_ms=round(duration_ms, 2),
                           **context)
    
    def log_monitoring_data(self, operation: str, duration_ms: float,
                           success: bool, **params):
        """记录监控数据 (与 SimpleMonitor 集成)"""
        self.logger.info(f"📊 监控数据: {operation}",
                        operation=operation,
                        duration_ms=round(duration_ms, 2),
                        success=success,
                        monitoring=True,
                        **params)


# 全局日志实例管理
_global_logger: Optional[StructuredLogger] = None
_logger_config = {
    "log_level": "INFO",
    "output_format": "auto"
}

def configure_logging(log_level: str = "INFO", 
                     output_format: str = "auto",
                     log_file: Optional[str] = None):
    """
    配置全局日志设置
    
    Args:
        log_level: 日志级别
        output_format: 输出格式 (auto, dev, prod, json)
        log_file: 日志文件路径
    """
    global _logger_config
    _logger_config.update({
        "log_level": log_level,
        "output_format": output_format,
        "log_file": log_file
    })
    
    # 重置全局logger
    global _global_logger
    _global_logger = None

def get_logger(name: str = "hulu_automation") -> StructuredLogger:
    """
    获取全局日志器实例
    
    Args:
        name: 日志器名称
        
    Returns:
        StructuredLogger: 日志器实例
    """
    global _global_logger
    if _global_logger is None:
        _global_logger = StructuredLogger(name=name, **_logger_config)
    return _global_logger

def get_task_logger(task_id: Optional[str] = None) -> TaskLogger:
    """
    获取任务日志器实例
    
    Args:
        task_id: 任务ID
        
    Returns:
        TaskLogger: 任务日志器实例
    """
    return TaskLogger(base_logger=get_logger(), task_id=task_id)


# 便捷函数 - 向后兼容
def setup_logging(log_level=logging.INFO, log_file: Optional[str] = None):
    """
    向后兼容的日志设置函数
    
    Args:
        log_level: 日志级别 (logging常量或字符串)
        log_file: 日志文件路径
    """
    # 转换日志级别
    if isinstance(log_level, int):
        level_name = logging.getLevelName(log_level)
    else:
        level_name = str(log_level).upper()
    
    configure_logging(log_level=level_name, log_file=log_file)


if __name__ == "__main__":
    # 测试代码
    print("🧪 测试结构化日志系统...")
    
    # 测试基本功能
    logger = get_logger("test")
    logger.info("测试基本日志功能", test_field="test_value")
    
    # 测试任务日志器
    task_logger = get_task_logger("test_task")
    task_logger.log_phase_start("email_detection")
    
    task_logger.log_selector_found(
        selector="#email-field",
        phase="email_detection", 
        duration_ms=123.45
    )
    
    task_logger.log_phase_success(
        phase="email_detection",
        duration_ms=123.45,
        selector="#email-field"
    )
    
    # 测试不同格式
    print("\n🎨 测试JSON格式...")
    configure_logging(output_format="json")
    json_logger = get_logger("json_test")
    json_logger.info("JSON格式测试", format="json", success=True)
    
    print("✅ 结构化日志系统测试完成！")