#!/usr/bin/env python3
"""
统一选择器管理器 - Quick-Wins优化版本
基于现有 HuluPageSelectors 类优化，支持 YAML 配置和动态宿主模板

核心功能:
1. YAML 配置文件加载
2. 动态宿主模板替换 ({HOST} >> selector)
3. 优先级策略 (primary > verified > fallback)
4. 向后兼容性保证
5. 健康检查专用接口
"""

import yaml
from pathlib import Path
from typing import Dict, List, Any, Optional, Union
import logging


class SelectorManager:
    """
    统一选择器管理器
    
    特性:
    - 基于 YAML 配置的选择器管理
    - 支持动态宿主模板 {HOST} 替换
    - 多级优先级策略 (primary > verified > fallback)
    - 向后兼容现有 HuluPageSelectors
    """
    
    def __init__(self, config_path: Optional[str] = None):
        """
        初始化选择器管理器
        
        Args:
            config_path: YAML配置文件路径，默认使用项目根目录下的 selectors.yml
        """
        self.logger = logging.getLogger('selector_manager')
        
        # 确定配置文件路径
        if config_path is None:
            # 项目根目录的 selectors.yml
            project_root = Path(__file__).parent.parent
            config_path = project_root / "selectors.yml"
        
        self.config_path = Path(config_path)
        self.config = self._load_config()
        
        self.logger.debug(f"📋 SelectorManager已加载配置: {self.config_path}")
    
    def _load_config(self) -> Dict[str, Any]:
        """
        加载 YAML 配置文件
        
        Returns:
            Dict[str, Any]: 配置字典
            
        Raises:
            FileNotFoundError: 配置文件不存在
            yaml.YAMLError: YAML解析错误
        """
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            self.logger.info(f"✅ 选择器配置加载成功: {len(config)} 个主要部分")
            return config
            
        except FileNotFoundError:
            self.logger.error(f"❌ 配置文件未找到: {self.config_path}")
            raise
        except yaml.YAMLError as e:
            self.logger.error(f"❌ YAML解析错误: {e}")
            raise
    
    def get_selectors(self, 
                     path: str, 
                     host: Optional[str] = None,
                     priority: Optional[str] = None) -> List[str]:
        """
        获取指定路径的选择器列表
        
        Args:
            path: 选择器路径，如 'login_flow.email_fields'
            host: 动态宿主，用于替换 {HOST} 模板
            priority: 优先级过滤 ('primary', 'verified', 'fallback', None表示所有)
            
        Returns:
            List[str]: 选择器列表
            
        Examples:
            >>> sm = SelectorManager()
            >>> sm.get_selectors('login_flow.email_fields')
            ['#email-field', 'input[name="email-field"]', ...]
            
            >>> sm.get_selectors('login_flow.welcome_login_buttons', 
            ...                  host='iframe[src*="hulu.com"]',
            ...                  priority='primary')
            ['iframe[src*="hulu.com"] >> button:has-text("Log In")', ...]
        """
        # 解析路径
        parts = path.split('.')
        config_section = self.config
        
        try:
            for part in parts:
                config_section = config_section[part]
        except KeyError:
            self.logger.warning(f"⚠️ 选择器路径未找到: {path}")
            return []
        
        # 收集选择器
        selectors = []
        
        if isinstance(config_section, dict):
            # 有优先级结构的配置
            priority_order = ['primary', 'verified', 'css_classes', 'fallback']
            
            if priority:
                # 只获取指定优先级
                if priority in config_section:
                    selectors.extend(config_section[priority])
            else:
                # 按优先级顺序获取所有
                for prio in priority_order:
                    if prio in config_section:
                        selectors.extend(config_section[prio])
                        
        elif isinstance(config_section, list):
            # 简单列表结构
            selectors = config_section.copy()
        
        # 应用动态宿主模板替换
        if host:
            selectors = [selector.replace('{HOST}', host) for selector in selectors]
        
        self.logger.debug(f"🔍 获取选择器 {path}: {len(selectors)} 个 (host={host}, priority={priority})")
        return selectors
    
    def get_login_selectors(self, host: Optional[str] = None) -> Dict[str, List[str]]:
        """
        获取所有登录相关选择器 (向后兼容接口)
        
        Args:
            host: 动态宿主模板
            
        Returns:
            Dict[str, List[str]]: 登录选择器字典
        """
        return {
            'welcome_login_buttons': self.get_selectors('login_flow.welcome_login_buttons', host),
            'email_fields': self.get_selectors('login_flow.email_fields', host),
            'continue_buttons': self.get_selectors('login_flow.continue_buttons', host),
            'password_fields': self.get_selectors('login_flow.password_fields', host),
            'login_submit_buttons': self.get_selectors('login_flow.login_submit_buttons', host)
        }
    
    def get_verification_selectors(self, host: Optional[str] = None) -> Dict[str, List[str]]:
        """
        获取所有验证相关选择器 (向后兼容接口)
        
        Args:
            host: 动态宿主模板
            
        Returns:
            Dict[str, List[str]]: 验证选择器字典
        """
        return {
            'verification_code_fields': self.get_selectors('verification_flow.verification_code_fields', host),
            'verification_submit_buttons': self.get_selectors('verification_flow.verification_submit_buttons', host)
        }
    
    def get_status_check_selectors(self) -> Dict[str, List[str]]:
        """
        获取所有状态检查相关选择器 (向后兼容接口)
        
        Returns:
            Dict[str, List[str]]: 状态检查选择器字典
        """
        return {
            'login_success_elements': self.get_selectors('status_checks.login_success_elements'),
            'login_error_elements': self.get_selectors('status_checks.login_error_elements')
        }
    
    def get_healthcheck_flow(self) -> List[Dict[str, str]]:
        """
        获取健康检查流程配置
        
        Returns:
            List[Dict[str, str]]: 健康检查流程步骤
        """
        try:
            return self.config['healthcheck']['flow_sequence']
        except KeyError:
            self.logger.warning("⚠️ 健康检查流程配置未找到")
            return []
    
    def get_network_stubs(self) -> List[Dict[str, Any]]:
        """
        获取网络请求拦截配置
        
        Returns:
            List[Dict[str, Any]]: 网络拦截规则
        """
        try:
            return self.config['healthcheck']['network_stubs']
        except KeyError:
            self.logger.warning("⚠️ 网络拦截配置未找到")
            return []
    
    def get_recaptcha_mock_script(self) -> str:
        """
        获取 reCAPTCHA Mock 脚本
        
        Returns:
            str: Mock 脚本内容
        """
        try:
            return self.config['healthcheck']['recaptcha_mock']['script']
        except KeyError:
            self.logger.warning("⚠️ reCAPTCHA Mock配置未找到")
            return ""
    
    def reload_config(self):
        """重新加载配置文件"""
        self.config = self._load_config()
        self.logger.info("🔄 配置文件已重新加载")


class LegacySelectors:
    """
    向后兼容的选择器接口
    保持与原有 HuluPageSelectors 和 Selectors 类的兼容性
    """
    
    def __init__(self, manager: Optional[SelectorManager] = None):
        """
        初始化兼容接口
        
        Args:
            manager: SelectorManager实例，默认创建新实例
        """
        self.manager = manager or SelectorManager()
    
    # 直接访问常用选择器 (向后兼容)
    @property
    def LOGIN_BUTTONS(self) -> List[str]:
        return self.manager.get_selectors('login_flow.login_submit_buttons')
    
    @property
    def EMAIL_INPUTS(self) -> List[str]:
        return self.manager.get_selectors('login_flow.email_fields')
    
    @property
    def PASSWORD_INPUTS(self) -> List[str]:
        return self.manager.get_selectors('login_flow.password_fields')
    
    @property
    def CONTINUE_BUTTONS(self) -> List[str]:
        return self.manager.get_selectors('login_flow.continue_buttons')
    
    def login(self, host: Optional[str] = None) -> Dict[str, List[str]]:
        """获取登录相关选择器 (向后兼容)"""
        return self.manager.get_login_selectors(host)
    
    def verification(self, host: Optional[str] = None) -> Dict[str, List[str]]:
        """获取验证相关选择器 (向后兼容)"""
        return self.manager.get_verification_selectors(host)
    
    def status(self) -> Dict[str, List[str]]:
        """获取状态检查相关选择器 (向后兼容)"""
        return self.manager.get_status_check_selectors()


# 全局实例 (向后兼容)
_global_manager = None
_global_selectors = None

def get_selector_manager() -> SelectorManager:
    """获取全局选择器管理器实例"""
    global _global_manager
    if _global_manager is None:
        _global_manager = SelectorManager()
    return _global_manager

def get_selectors() -> LegacySelectors:
    """获取向后兼容的选择器接口"""
    global _global_selectors
    if _global_selectors is None:
        _global_selectors = LegacySelectors(get_selector_manager())
    return _global_selectors


# 便捷函数
def get_login_selectors(host: Optional[str] = None) -> Dict[str, List[str]]:
    """便捷函数：获取登录选择器"""
    return get_selector_manager().get_login_selectors(host)

def get_verification_selectors(host: Optional[str] = None) -> Dict[str, List[str]]:
    """便捷函数：获取验证选择器"""
    return get_selector_manager().get_verification_selectors(host)

def get_status_check_selectors() -> Dict[str, List[str]]:
    """便捷函数：获取状态检查选择器"""
    return get_selector_manager().get_status_check_selectors()


# 兼容性别名
Selectors = LegacySelectors

if __name__ == "__main__":
    # 测试代码
    import sys
    logging.basicConfig(level=logging.DEBUG)
    
    print("🧪 测试 SelectorManager...")
    
    try:
        manager = SelectorManager()
        
        # 测试基本功能
        email_selectors = manager.get_selectors('login_flow.email_fields')
        print(f"📧 邮箱选择器数量: {len(email_selectors)}")
        print(f"首个选择器: {email_selectors[0]}")
        
        # 测试动态宿主
        login_buttons = manager.get_selectors(
            'login_flow.welcome_login_buttons', 
            host='iframe[src*="hulu.com"]',
            priority='primary'
        )
        print(f"🔘 登录按钮 (动态宿主): {login_buttons[0]}")
        
        # 测试健康检查流程
        flow = manager.get_healthcheck_flow()
        print(f"🏥 健康检查流程步骤: {len(flow)} 步")
        
        # 测试向后兼容性
        legacy = LegacySelectors(manager)
        old_login = legacy.login()
        print(f"🔄 向后兼容测试: {len(old_login)} 个登录选择器组")
        
        print("✅ 所有测试通过！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        sys.exit(1)