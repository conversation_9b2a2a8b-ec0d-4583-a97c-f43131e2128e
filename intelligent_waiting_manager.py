#!/usr/bin/env python3
"""
智能等待管理器 - Intelligent Waiting Manager
用于替换hulu_automation_stealth.py中的固定等待，提供基于DOM稳定性和网络活动的智能等待策略
与现有HumanBehaviorSimulator完全兼容
"""

import asyncio
import logging
import random
import time
from typing import Dict, List, Optional, Any, Callable, Union
from dataclasses import dataclass
from playwright.async_api import Page


@dataclass
class WaitingContext:
    """等待上下文配置"""
    operation_type: str                 # 操作类型 ("login_check", "button_search", "page_observation")
    base_timeout: float = 30.0         # 基础超时时间（秒）
    dom_stability_threshold: int = 300 # DOM稳定性阈值（毫秒）
    network_idle_threshold: int = 500  # 网络空闲阈值（毫秒）
    adaptive_factor: float = 1.0       # 自适应因子
    human_behavior_enabled: bool = True # 启用人类行为模拟


class NetworkActivityMonitor:
    """网络活动监控器"""
    
    def __init__(self, page: Page):
        self.page = page
        self.logger = logging.getLogger(__name__)
        self.active_requests = set()
        self.last_request_time = 0
        
    async def start_monitoring(self):
        """开始监控网络活动"""
        self.page.on("request", self._on_request)
        self.page.on("response", self._on_response)
        self.page.on("requestfinished", self._on_request_finished)
        self.page.on("requestfailed", self._on_request_failed)
        
    def _on_request(self, request):
        """请求开始事件"""
        self.active_requests.add(request.url)
        self.last_request_time = time.time() * 1000
        
    def _on_response(self, response):
        """响应事件"""
        self.last_request_time = time.time() * 1000
        
    def _on_request_finished(self, request):
        """请求完成事件"""
        self.active_requests.discard(request.url)
        self.last_request_time = time.time() * 1000
        
    def _on_request_failed(self, request):
        """请求失败事件"""
        self.active_requests.discard(request.url)
        self.last_request_time = time.time() * 1000
        
    async def wait_for_network_idle(self, idle_time: int = 500) -> bool:
        """等待网络空闲"""
        start_time = time.time() * 1000
        
        while True:
            current_time = time.time() * 1000
            
            # 检查是否有活跃请求
            if not self.active_requests:
                # 检查距离最后一个请求的时间
                if current_time - self.last_request_time >= idle_time:
                    self.logger.debug(f"✅ 网络空闲检测完成: {idle_time}ms")
                    return True
            
            # 超时检查（最多等待30秒）
            if current_time - start_time > 30000:
                self.logger.warning("⚠️ 网络空闲等待超时")
                return False
                
            await asyncio.sleep(0.1)
    
    def get_network_stats(self) -> Dict[str, Any]:
        """获取网络状态统计"""
        return {
            "active_requests": len(self.active_requests),
            "last_request_time": self.last_request_time,
            "is_idle": len(self.active_requests) == 0
        }


class DOMStabilityDetector:
    """DOM稳定性检测器"""
    
    def __init__(self, page: Page):
        self.page = page
        self.logger = logging.getLogger(__name__)
        
    async def wait_for_dom_stability(
        self, 
        stability_time: int = 300,
        max_wait: int = 30000
    ) -> bool:
        """
        等待DOM稳定
        
        Args:
            stability_time: DOM稳定时间阈值（毫秒）
            max_wait: 最大等待时间（毫秒）
            
        Returns:
            bool: 是否成功检测到DOM稳定
        """
        
        # 注入DOM监控脚本
        monitor_script = f"""
        window.domStabilityMonitor = {{
            lastChangeTime: Date.now(),
            isStable: false,
            stabilityThreshold: {stability_time},
            
            init: function() {{
                const observer = new MutationObserver((mutations) => {{
                    // 过滤掉不重要的变化
                    const significantChanges = mutations.filter(mutation => {{
                        // 忽略style、class等非结构性变化
                        if (mutation.type === 'attributes') {{
                            const ignoredAttrs = ['style', 'class', 'data-testid'];
                            return !ignoredAttrs.includes(mutation.attributeName);
                        }}
                        return true;
                    }});
                    
                    if (significantChanges.length > 0) {{
                        this.lastChangeTime = Date.now();
                        this.isStable = false;
                    }}
                }});
                
                observer.observe(document.body, {{
                    childList: true,
                    subtree: true,
                    attributes: true,
                    attributeOldValue: true
                }});
                
                // 检查加载状态
                if (document.readyState === 'complete') {{
                    this.checkStability();
                }}
                
                document.addEventListener('DOMContentLoaded', () => {{
                    this.checkStability();
                }});
                
                window.addEventListener('load', () => {{
                    this.checkStability();
                }});
            }},
            
            checkStability: function() {{
                const now = Date.now();
                if (now - this.lastChangeTime >= this.stabilityThreshold) {{
                    this.isStable = true;
                }}
            }},
            
            getStatus: function() {{
                this.checkStability();
                return {{
                    isStable: this.isStable,
                    lastChangeTime: this.lastChangeTime,
                    timeSinceChange: Date.now() - this.lastChangeTime
                }};
            }}
        }};
        
        window.domStabilityMonitor.init();
        """
        
        try:
            await self.page.add_init_script(monitor_script)
            await self.page.evaluate(monitor_script)
        except:
            # 如果脚本注入失败，使用基础等待
            self.logger.warning("DOM监控脚本注入失败，使用基础等待策略")
            await asyncio.sleep(stability_time / 1000)
            return True
        
        # 等待DOM稳定
        start_time = time.time() * 1000
        
        while True:
            current_time = time.time() * 1000
            
            try:
                status = await self.page.evaluate("window.domStabilityMonitor.getStatus()")
                
                if status.get('isStable', False):
                    self.logger.debug(f"✅ DOM稳定性检测完成: {status}")
                    return True
                    
                # 超时检查
                if current_time - start_time > max_wait:
                    self.logger.warning("⚠️ DOM稳定性等待超时")
                    return False
                    
            except Exception as e:
                self.logger.debug(f"DOM状态检查异常: {e}")
                
            await asyncio.sleep(0.1)
    
    async def wait_for_elements_stable(self, selectors: List[str]) -> bool:
        """等待指定元素稳定"""
        
        check_script = f"""
        () => {{
            const selectors = {selectors};
            let allStable = true;
            const results = {{}};
            
            for (const selector of selectors) {{
                const element = document.querySelector(selector);
                results[selector] = {{
                    found: !!element,
                    visible: element ? element.offsetParent !== null : false,
                    stable: element ? element.getBoundingClientRect().width > 0 : false
                }};
                
                if (!results[selector].stable) {{
                    allStable = false;
                }}
            }}
            
            return {{ allStable, results }};
        }}
        """
        
        max_attempts = 100
        for attempt in range(max_attempts):
            try:
                result = await self.page.evaluate(check_script)
                
                if result.get('allStable', False):
                    self.logger.debug("✅ 指定元素稳定性检测完成")
                    return True
                    
            except Exception as e:
                self.logger.debug(f"元素稳定性检查异常: {e}")
                
            await asyncio.sleep(0.1)
        
        return False


class AdaptiveTimeoutCalculator:
    """自适应超时计算器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.performance_history = []
        
    async def calculate_adaptive_timeout(
        self, 
        page: Page, 
        base_timeout: float,
        operation_type: str
    ) -> float:
        """
        计算自适应超时时间
        
        Args:
            page: Playwright页面实例
            base_timeout: 基础超时时间
            operation_type: 操作类型
            
        Returns:
            float: 调整后的超时时间
        """
        
        try:
            # 获取网络性能指标
            performance_metrics = await self._get_performance_metrics(page)
            
            # 获取页面复杂度
            page_complexity = await self._assess_page_complexity(page)
            
            # 计算自适应因子
            adaptive_factor = self._calculate_adaptive_factor(
                performance_metrics, 
                page_complexity, 
                operation_type
            )
            
            # 应用自适应调整
            adjusted_timeout = base_timeout * adaptive_factor
            
            # 限制在合理范围内
            min_timeout = base_timeout * 0.5
            max_timeout = base_timeout * 3.0
            adjusted_timeout = max(min_timeout, min(adjusted_timeout, max_timeout))
            
            self.logger.debug(
                f"自适应超时计算: {base_timeout}s → {adjusted_timeout:.2f}s "
                f"(因子: {adaptive_factor:.2f})"
            )
            
            return adjusted_timeout
            
        except Exception as e:
            self.logger.warning(f"自适应超时计算失败: {e}, 使用基础超时")
            return base_timeout
    
    async def _get_performance_metrics(self, page: Page) -> Dict[str, float]:
        """获取性能指标"""
        
        try:
            metrics = await page.evaluate("""
                () => {
                    if (!window.performance) return {};
                    
                    const timing = performance.timing;
                    const navigation = performance.getEntriesByType('navigation')[0];
                    
                    return {
                        pageLoadTime: timing.loadEventEnd - timing.navigationStart,
                        domReadyTime: timing.domContentLoadedEventEnd - timing.navigationStart,
                        networkLatency: navigation ? navigation.responseStart - navigation.requestStart : 0,
                        resourceCount: performance.getEntriesByType('resource').length
                    };
                }
            """)
            
            return metrics
            
        except Exception as e:
            self.logger.debug(f"性能指标获取失败: {e}")
            return {}
    
    async def _assess_page_complexity(self, page: Page) -> Dict[str, int]:
        """评估页面复杂度"""
        
        try:
            complexity = await page.evaluate("""
                () => {
                    return {
                        elementCount: document.querySelectorAll('*').length,
                        scriptCount: document.scripts.length,
                        imageCount: document.images.length,
                        formCount: document.forms.length,
                        iframeCount: document.querySelectorAll('iframe').length
                    };
                }
            """)
            
            return complexity
            
        except Exception as e:
            self.logger.debug(f"页面复杂度评估失败: {e}")
            return {}
    
    def _calculate_adaptive_factor(
        self, 
        performance: Dict[str, float], 
        complexity: Dict[str, int],
        operation_type: str
    ) -> float:
        """计算自适应因子"""
        
        factor = 1.0
        
        # 基于页面加载时间调整
        page_load_time = performance.get('pageLoadTime', 3000)
        if page_load_time > 5000:  # 5秒以上
            factor *= 1.5
        elif page_load_time > 3000:  # 3-5秒
            factor *= 1.2
        elif page_load_time < 1000:  # 1秒以下
            factor *= 0.8
        
        # 基于页面复杂度调整
        element_count = complexity.get('elementCount', 100)
        if element_count > 1000:
            factor *= 1.3
        elif element_count > 500:
            factor *= 1.1
        elif element_count < 100:
            factor *= 0.9
        
        # 基于操作类型调整
        operation_factors = {
            'login_check': 1.0,
            'button_search': 1.2,  # 按钮搜索可能需要更多时间
            'page_observation': 0.8  # 页面观察可以相对快一些
        }
        
        factor *= operation_factors.get(operation_type, 1.0)
        
        return factor


class IntelligentWaitingManager:
    """智能等待管理器主类"""
    
    def __init__(self, page: Page, human_behavior_simulator=None):
        """
        初始化智能等待管理器
        
        Args:
            page: Playwright页面实例
            human_behavior_simulator: 现有的人类行为模拟器实例
        """
        self.page = page
        self.human_behavior_simulator = human_behavior_simulator
        self.logger = logging.getLogger(__name__)
        
        # 初始化子组件
        self.network_monitor = NetworkActivityMonitor(page)
        self.dom_detector = DOMStabilityDetector(page)
        self.timeout_calculator = AdaptiveTimeoutCalculator()
        
        # 启动网络监控
        asyncio.create_task(self.network_monitor.start_monitoring())
    
    async def intelligent_wait(
        self, 
        context: WaitingContext,
        additional_conditions: Optional[List[Callable]] = None
    ) -> bool:
        """
        智能等待主方法 - 替换固定等待的核心函数
        
        Args:
            context: 等待上下文配置
            additional_conditions: 额外的等待条件函数列表
            
        Returns:
            bool: 是否成功完成等待
        """
        
        self.logger.info(f"🔍 开始智能等待: {context.operation_type}")
        start_time = time.time()
        
        # 1. 计算自适应超时时间
        adaptive_timeout = await self.timeout_calculator.calculate_adaptive_timeout(
            self.page, context.base_timeout, context.operation_type
        )
        
        # 2. 人类行为模拟前置动作
        if context.human_behavior_enabled and self.human_behavior_simulator:
            await self._simulate_pre_wait_behavior(context)
        
        # 3. 并行等待多个条件
        wait_tasks = []
        
        # DOM稳定性等待
        wait_tasks.append(
            self.dom_detector.wait_for_dom_stability(
                context.dom_stability_threshold,
                int(adaptive_timeout * 1000)
            )
        )
        
        # 网络空闲等待
        wait_tasks.append(
            self.network_monitor.wait_for_network_idle(
                context.network_idle_threshold
            )
        )
        
        # 额外条件等待
        if additional_conditions:
            for condition in additional_conditions:
                wait_tasks.append(condition())
        
        # 4. 等待任一条件满足或超时
        try:
            # 使用wait_for实现总体超时控制
            await asyncio.wait_for(
                self._wait_for_any_condition(wait_tasks),
                timeout=adaptive_timeout
            )
            
            success = True
            
        except asyncio.TimeoutError:
            self.logger.warning(f"⚠️ 智能等待超时: {context.operation_type}")
            success = False
        
        # 5. 人类行为模拟后置动作
        if context.human_behavior_enabled and self.human_behavior_simulator:
            await self._simulate_post_wait_behavior(context, success)
        
        elapsed_time = time.time() - start_time
        self.logger.info(
            f"✅ 智能等待完成: {context.operation_type}, "
            f"耗时: {elapsed_time:.2f}s, 成功: {success}"
        )
        
        return success
    
    async def _wait_for_any_condition(self, tasks: List[asyncio.Task]) -> None:
        """等待任一条件满足"""
        done, pending = await asyncio.wait(
            tasks, 
            return_when=asyncio.FIRST_COMPLETED
        )
        
        # 取消未完成的任务
        for task in pending:
            task.cancel()
        
        # 检查是否有任务成功
        for task in done:
            try:
                result = await task
                if result:  # 如果有任务返回True，认为条件满足
                    return
            except Exception as e:
                self.logger.debug(f"等待条件检查异常: {e}")
    
    async def _simulate_pre_wait_behavior(self, context: WaitingContext):
        """等待前的人类行为模拟"""
        
        if context.operation_type == "page_observation":
            # 页面观察：模拟视觉扫描
            await self._simulate_visual_scanning()
            
        elif context.operation_type == "login_check":
            # 登录检查：模拟短暂的认知处理
            await self._simulate_cognitive_processing(duration_range=(0.5, 1.5))
            
        elif context.operation_type == "button_search":
            # 按钮搜索：模拟寻找目标的视觉行为
            await self._simulate_target_searching()
    
    async def _simulate_post_wait_behavior(self, context: WaitingContext, success: bool):
        """等待后的人类行为模拟"""
        
        if success:
            # 成功：短暂确认
            await self._simulate_cognitive_processing(duration_range=(0.2, 0.8))
        else:
            # 失败：模拟困惑和重新思考
            await self._simulate_confusion_behavior()
    
    async def _simulate_visual_scanning(self):
        """模拟视觉扫描行为"""
        scan_points = random.randint(3, 6)
        
        for _ in range(scan_points):
            x = random.randint(200, 1200)
            y = random.randint(150, 700)
            
            await self.page.mouse.move(x, y)
            await asyncio.sleep(random.uniform(0.3, 0.8))
    
    async def _simulate_cognitive_processing(self, duration_range: tuple = (0.5, 2.0)):
        """模拟认知处理时间"""
        duration = random.uniform(*duration_range)
        await asyncio.sleep(duration)
    
    async def _simulate_target_searching(self):
        """模拟寻找目标的行为"""
        # 随机的小范围鼠标移动
        for _ in range(random.randint(2, 4)):
            # 获取当前视口大小
            viewport = self.page.viewport_size
            if viewport:
                x = random.randint(int(viewport['width'] * 0.2), int(viewport['width'] * 0.8))
                y = random.randint(int(viewport['height'] * 0.2), int(viewport['height'] * 0.8))
            else:
                x = random.randint(200, 1000)
                y = random.randint(200, 600)
            
            await self.page.mouse.move(x, y)
            await asyncio.sleep(random.uniform(0.4, 1.0))
    
    async def _simulate_confusion_behavior(self):
        """模拟困惑行为"""
        # 稍长的暂停
        await asyncio.sleep(random.uniform(1.0, 2.5))
        
        # 轻微的鼠标移动表示重新思考
        for _ in range(random.randint(1, 3)):
            offset_x = random.randint(-100, 100)
            offset_y = random.randint(-50, 50)
            
            await self.page.mouse.move(
                random.randint(400, 800) + offset_x,
                random.randint(300, 500) + offset_y
            )
            await asyncio.sleep(random.uniform(0.2, 0.6))


# 预定义的等待上下文配置，用于替换hulu_automation_stealth.py中的固定等待

# 替换第468行: await asyncio.sleep(2) - 登录状态检查后的等待
LOGIN_STATUS_CHECK_CONTEXT = WaitingContext(
    operation_type="login_check",
    base_timeout=5.0,  # 比原来的2秒稍长，但更智能
    dom_stability_threshold=300,
    network_idle_threshold=500,
    adaptive_factor=1.0,
    human_behavior_enabled=True
)

# 替换第1189行: await asyncio.sleep(5) - GET THEM BOTH按钮处理前的等待
BUTTON_SEARCH_CONTEXT = WaitingContext(
    operation_type="button_search", 
    base_timeout=10.0,  # 比原来的5秒长，因为按钮搜索可能需要更多时间
    dom_stability_threshold=500,   # 更长的DOM稳定时间
    network_idle_threshold=800,    # 更长的网络空闲时间
    adaptive_factor=1.2,
    human_behavior_enabled=True
)

# 替换第818-819行: observation_delay = random.randint(1800, 2200) - 页面观察延迟
PAGE_OBSERVATION_CONTEXT = WaitingContext(
    operation_type="page_observation",
    base_timeout=3.0,  # 基于原来的1.8-2.2秒
    dom_stability_threshold=200,   # 更短的稳定时间，因为只是观察
    network_idle_threshold=300,    # 更短的网络空闲时间
    adaptive_factor=0.8,
    human_behavior_enabled=True
)


# 与现有HumanBehaviorSimulator的集成工厂函数

# 与现有HumanBehaviorSimulator的集成工厂函数


def create_intelligent_waiting_manager(page: Page, human_behavior_simulator=None) -> IntelligentWaitingManager:
    """
    创建智能等待管理器实例，与现有系统集成
    
    Args:
        page: Playwright页面实例
        human_behavior_simulator: 现有的HumanBehaviorSimulator实例
        
    Returns:
        IntelligentWaitingManager: 配置好的智能等待管理器
    """
    return IntelligentWaitingManager(page, human_behavior_simulator)
