import request from 'supertest'
import express from 'express'
import cors from 'cors'
import detectionRoutes from '../../routes/detection'

// Create test app
const createTestApp = () => {
  const app = express()
  app.use(cors())
  app.use(express.json())
  app.use('/api/detection', detectionRoutes)
  return app
}

describe('Detection Routes', () => {
  let app: express.Application

  beforeEach(() => {
    app = createTestApp()
  })

  describe('POST /api/detection/validate', () => {
    it('should validate a valid Visa card', async () => {
      const validCard = {
        cardNumber: '****************',
        cvv: '123',
        expDate: '12/25'
      }

      const response = await request(app)
        .post('/api/detection/validate')
        .send(validCard)
        .expect(200)

      expect(response.body).toHaveProperty('valid')
      expect(response.body).toHaveProperty('cardType')
      expect(response.body).toHaveProperty('message')
      expect(response.body.cardType).toBe('visa')
    })

    it('should validate a valid Mastercard', async () => {
      const validCard = {
        cardNumber: '****************',
        cvv: '456',
        expDate: '06/26'
      }

      const response = await request(app)
        .post('/api/detection/validate')
        .send(validCard)
        .expect(200)

      expect(response.body.cardType).toBe('mastercard')
    })

    it('should reject invalid card number', async () => {
      const invalidCard = {
        cardNumber: '1234567890123456',
        cvv: '123',
        expDate: '12/25'
      }

      const response = await request(app)
        .post('/api/detection/validate')
        .send(invalidCard)
        .expect(400)

      expect(response.body).toHaveProperty('error')
      expect(response.body.error).toContain('Invalid card number')
    })

    it('should reject invalid CVV format', async () => {
      const invalidCard = {
        cardNumber: '****************',
        cvv: '12', // Too short
        expDate: '12/25'
      }

      const response = await request(app)
        .post('/api/detection/validate')
        .send(invalidCard)
        .expect(400)

      expect(response.body).toHaveProperty('error')
      expect(response.body.error).toContain('Invalid CVV')
    })

    it('should reject invalid expiry date format', async () => {
      const invalidCard = {
        cardNumber: '****************',
        cvv: '123',
        expDate: '13/25' // Invalid month
      }

      const response = await request(app)
        .post('/api/detection/validate')
        .send(invalidCard)
        .expect(400)

      expect(response.body).toHaveProperty('error')
      expect(response.body.error).toContain('Invalid expiry date')
    })

    it('should reject missing required fields', async () => {
      const incompleteCard = {
        cardNumber: '****************'
        // Missing cvv and expDate
      }

      const response = await request(app)
        .post('/api/detection/validate')
        .send(incompleteCard)
        .expect(400)

      expect(response.body).toHaveProperty('error')
      expect(response.body.error).toContain('Missing required fields')
    })

    it('should handle expired cards', async () => {
      const expiredCard = {
        cardNumber: '****************',
        cvv: '123',
        expDate: '01/20' // Expired date
      }

      const response = await request(app)
        .post('/api/detection/validate')
        .send(expiredCard)
        .expect(400)

      expect(response.body).toHaveProperty('error')
      expect(response.body.error).toContain('Card has expired')
    })

    it('should handle American Express cards with 4-digit CVV', async () => {
      const amexCard = {
        cardNumber: '***************',
        cvv: '1234', // 4 digits for Amex
        expDate: '12/25'
      }

      const response = await request(app)
        .post('/api/detection/validate')
        .send(amexCard)
        .expect(200)

      expect(response.body.cardType).toBe('amex')
    })

    it('should reject American Express cards with 3-digit CVV', async () => {
      const amexCard = {
        cardNumber: '***************',
        cvv: '123', // Should be 4 digits for Amex
        expDate: '12/25'
      }

      const response = await request(app)
        .post('/api/detection/validate')
        .send(amexCard)
        .expect(400)

      expect(response.body).toHaveProperty('error')
      expect(response.body.error).toContain('American Express cards require 4-digit CVV')
    })

    it('should include request ID in response', async () => {
      const validCard = {
        cardNumber: '****************',
        cvv: '123',
        expDate: '12/25'
      }

      const response = await request(app)
        .post('/api/detection/validate')
        .send(validCard)
        .expect(200)

      expect(response.body).toHaveProperty('requestId')
      expect(typeof response.body.requestId).toBe('string')
    })
  })
})
