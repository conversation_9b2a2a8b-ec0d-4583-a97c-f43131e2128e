import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import dotenv from 'dotenv';

// Import routes
import detectionRoutes from './routes/detection';
import userRoutes from './routes/user';

// Import configuration
import { config } from './config';

// Load environment variables
dotenv.config();

const app = express();

// Middleware
app.use(helmet());
app.use(cors({
  origin: config.corsOrigin,
  credentials: true
}));
app.use(morgan(config.nodeEnv === 'production' ? 'combined' : 'dev'));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Routes
app.use('/api/detection', detectionRoutes);
app.use('/api/user', userRoutes);

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Not Found',
    message: 'The requested resource was not found on this server.'
  });
});

// Error handler
app.use((err: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  console.error(err.stack);
  res.status(500).json({
    error: 'Internal Server Error',
    message: 'Something went wrong on our end.'
  });
});

// Start server
app.listen(config.port, () => {
  console.log(`🚀 CVV检测后端服务启动成功`);
  console.log(`📊 环境: ${config.nodeEnv}`);
  console.log(`🌐 端口: ${config.port}`);
  console.log(`📱 前端地址: ${config.corsOrigin}`);
  console.log(`🔗 健康检查: http://localhost:${config.port}/api/health`);
  console.log(`🎯 CVV API: ${config.cvvApiUrl}`);

  if (config.nodeEnv === 'development') {
    console.log(`🔧 开发模式已启用`);
    console.log(`📝 日志级别: ${config.logLevel}`);
  }
});

export default app;
