import express from 'express';
import { config } from '../config';

const router = express.Router();

// Card type detection
enum CardType {
  VISA = 'visa',
  MASTERCARD = 'mastercard',
  AMEX = 'amex',
  DISCOVER = 'discover',
  UNKNOWN = 'unknown'
}

// Detect card type from card number
function detectCardType(cardNumber: string): CardType {
  const cleaned = cardNumber.replace(/\s/g, '');

  if (cleaned.startsWith('4')) {
    return CardType.VISA;
  } else if (cleaned.startsWith('5') || (parseInt(cleaned.substring(0, 4)) >= 2221 && parseInt(cleaned.substring(0, 4)) <= 2720)) {
    return CardType.MASTERCARD;
  } else if (cleaned.startsWith('34') || cleaned.startsWith('37')) {
    return CardType.AMEX;
  } else if (cleaned.startsWith('6')) {
    return CardType.DISCOVER;
  }

  return CardType.UNKNOWN;
}

// Validate card number using <PERSON><PERSON> algorithm
function validateCardNumber(cardNumber: string): boolean {
  const cleaned = cardNumber.replace(/\s/g, '');

  if (!/^\d+$/.test(cleaned) || cleaned.length < 13 || cleaned.length > 19) {
    return false;
  }

  let sum = 0;
  let isEven = false;

  for (let i = cleaned.length - 1; i >= 0; i--) {
    let digit = parseInt(cleaned[i]);

    if (isEven) {
      digit *= 2;
      if (digit > 9) {
        digit -= 9;
      }
    }

    sum += digit;
    isEven = !isEven;
  }

  return sum % 10 === 0;
}

// Validate CVV format
function validateCVV(cvv: string, cardType: CardType): boolean {
  if (cardType === CardType.AMEX) {
    return /^\d{4}$/.test(cvv);
  } else {
    return /^\d{3}$/.test(cvv);
  }
}

// Validate expiry date
function validateExpiryDate(expDate: string): boolean {
  const match = expDate.match(/^(0[1-9]|1[0-2])\/(\d{2})$/);
  if (!match) return false;

  const month = parseInt(match[1]);
  const year = parseInt(match[2]) + 2000;
  const now = new Date();
  const expiry = new Date(year, month - 1);

  return expiry > now;
}

// Single card validation endpoint
router.post('/validate', async (req, res) => {
  try {
    const { cardNumber, cvv, expDate } = req.body;

    // Validate required fields
    if (!cardNumber || !cvv || !expDate) {
      return res.status(400).json({
        error: 'Missing required fields',
        message: 'cardNumber, cvv, and expDate are required'
      });
    }

    // Detect card type
    const cardType = detectCardType(cardNumber);

    // Validate card number
    if (!validateCardNumber(cardNumber)) {
      return res.status(400).json({
        error: 'Invalid card number',
        message: 'Card number failed validation'
      });
    }

    // Validate CVV format
    if (!validateCVV(cvv, cardType)) {
      const expectedLength = cardType === CardType.AMEX ? 4 : 3;
      return res.status(400).json({
        error: 'Invalid CVV',
        message: `${cardType === CardType.AMEX ? 'American Express cards require 4-digit CVV' : 'CVV must be 3 digits'}`
      });
    }

    // Validate expiry date
    if (!validateExpiryDate(expDate)) {
      return res.status(400).json({
        error: 'Invalid expiry date',
        message: 'Card has expired or invalid date format (MM/YY)'
      });
    }

    // Generate request ID
    const requestId = `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // Simulate CVV validation (in real implementation, this would call the CVV API)
    const isValid = Math.random() > 0.3; // 70% success rate for demo

    const response = {
      valid: isValid,
      cardType: cardType,
      message: isValid ? 'CVV validation successful' : 'CVV validation failed',
      requestId: requestId,
      timestamp: new Date().toISOString()
    };

    return res.json(response);

  } catch (error) {
    console.error('CVV validation error:', error);
    return res.status(500).json({
      error: 'Internal server error',
      message: 'An error occurred during CVV validation'
    });
  }
});

// CVV Detection endpoint (placeholder)
router.post('/cvv', async (req, res) => {
  try {
    const { cards } = req.body;

    // Validate input
    if (!cards || !Array.isArray(cards)) {
      return res.status(400).json({
        error: 'Invalid input',
        message: 'Cards array is required'
      });
    }

    // Placeholder response structure
    const results = cards.map((card: string, index: number) => ({
      id: index + 1,
      cardNumber: card,
      status: 'pending', // In real implementation: 'live', 'dead', 'unknown'
      detectedAt: new Date().toISOString(),
      processingTime: Math.random() * 1000 // Random processing time in ms
    }));

    const summary = {
      total: cards.length,
      live: 0,
      dead: 0,
      unknown: 0,
      pending: cards.length
    };

    return res.json({
      success: true,
      results,
      summary,
      message: 'Detection completed successfully'
    });

  } catch (error) {
    console.error('Detection error:', error);
    return res.status(500).json({
      error: 'Detection failed',
      message: 'An error occurred during CVV detection'
    });
  }
});

// Get detection history (placeholder)
router.get('/history', async (req, res) => {
  try {
    const { page = 1, limit = 50 } = req.query;

    // Placeholder response
    return res.json({
      success: true,
      data: [],
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total: 0,
        pages: 0
      }
    });

  } catch (error) {
    console.error('History fetch error:', error);
    return res.status(500).json({
      error: 'Failed to fetch history',
      message: 'An error occurred while fetching detection history'
    });
  }
});

// Get detection statistics (placeholder)
router.get('/stats', async (req, res) => {
  try {
    // Placeholder statistics
    return res.json({
      success: true,
      stats: {
        totalDetections: 0,
        todayDetections: 0,
        successRate: 0,
        averageProcessingTime: 0
      }
    });

  } catch (error) {
    console.error('Stats fetch error:', error);
    return res.status(500).json({
      error: 'Failed to fetch statistics',
      message: 'An error occurred while fetching detection statistics'
    });
  }
});

export default router;
