export const config = {
  // 服务器配置
  port: parseInt(process.env.PORT || '5000', 10),
  nodeEnv: process.env.NODE_ENV || 'development',
  corsOrigin: process.env.CORS_ORIGIN || 'http://localhost:3000',

  // CVV检测API集成
  cvvApiUrl: process.env.CVV_API_URL || 'http://localhost:8000/api/v1/cvv',
  cvvApiKey: process.env.CVV_API_KEY || '',
  cvvApiTimeout: parseInt(process.env.CVV_API_TIMEOUT || '5000', 10),

  // 数据库配置
  databaseUrl: process.env.DATABASE_URL || 'postgresql://postgres:password@localhost:5432/cvv_dev_db',
  redisUrl: process.env.REDIS_URL || 'redis://localhost:6379',
  dbPoolSize: parseInt(process.env.DB_POOL_SIZE || '10', 10),

  // 安全配置
  jwtSecret: process.env.JWT_SECRET || 'dev_jwt_secret_not_for_production',
  encryptionKey: process.env.ENCRYPTION_KEY || 'dev_encryption_key_not_for_production',
  sessionSecret: process.env.SESSION_SECRET || 'dev_session_secret_not_for_production',

  // 频率限制配置
  rateLimitWindowMs: parseInt(process.env.RATE_LIMIT_WINDOW || '900000', 10), // 15分钟
  rateLimitMax: parseInt(process.env.RATE_LIMIT_MAX || '100', 10), // 每个IP最多请求数
  requestTimeout: parseInt(process.env.REQUEST_TIMEOUT || '30000', 10),

  // 日志配置
  logLevel: process.env.LOG_LEVEL || 'info',
  logFilePath: process.env.LOG_FILE_PATH || './logs/app.log',
  logConsole: process.env.LOG_CONSOLE === 'true',

  // 开发工具配置
  hotReload: process.env.HOT_RELOAD === 'true',
  sourceMaps: process.env.SOURCE_MAPS === 'true',
  performanceProfiling: process.env.PERFORMANCE_PROFILING === 'true',
}
