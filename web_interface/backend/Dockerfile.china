# 使用阿里云镜像源
FROM registry.cn-hangzhou.aliyuncs.com/library/node:18-alpine

# 设置npm镜像源
RUN npm config set registry https://registry.npmmirror.com

# Install dumb-init for proper signal handling
RUN apk add --no-cache dumb-init

# Create app directory
WORKDIR /app

# Create non-root user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nodejs -u 1001

# Copy package files
COPY package*.json ./
COPY tsconfig.json ./

# Install dependencies
RUN npm ci --only=production && npm cache clean --force

# Copy source code
COPY src ./src
COPY healthcheck.js ./

# Change ownership to nodejs user
RUN chown -R nodejs:nodejs /app
USER nodejs

# Expose port
EXPOSE 5000

# Start application with dumb-init
ENTRYPOINT ["dumb-init", "--"]
CMD ["npm", "run", "dev"]
