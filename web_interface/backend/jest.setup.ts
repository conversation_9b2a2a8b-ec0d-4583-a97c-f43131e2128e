import dotenv from 'dotenv'

// Load test environment variables
dotenv.config({ path: '.env.test' })

// Set test environment variables
process.env.NODE_ENV = 'test'
process.env.PORT = '5001'
process.env.CVV_API_URL = 'http://localhost:8001/api/v1/cvv'
process.env.DATABASE_URL = 'postgresql://postgres:test@localhost:5432/cvv_test_db'
process.env.REDIS_URL = 'redis://localhost:6380'
process.env.JWT_SECRET = 'test_jwt_secret'
process.env.ENCRYPTION_KEY = 'test_encryption_key'
process.env.LOG_LEVEL = 'error' // Reduce log noise during tests

// Mock external services
jest.mock('axios')

// Global test utilities
global.console = {
  ...console,
  // Suppress console.log during tests unless explicitly needed
  log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
  warn: console.warn,
  error: console.error,
}
