# CVV检测Web界面 - 开发环境配置

# 前端配置
NEXT_PUBLIC_API_URL=http://localhost:5000
NEXT_PUBLIC_APP_ENV=development
NEXT_PUBLIC_APP_VERSION=1.0.0
NEXT_PUBLIC_DEBUG_MODE=true

# 后端配置
PORT=5000
NODE_ENV=development
CORS_ORIGIN=http://localhost:3000

# CVV检测API集成
CVV_API_URL=http://localhost:8000/api/v1/cvv
CVV_API_KEY=dev_api_key_placeholder
CVV_API_TIMEOUT=5000

# 数据库配置 (开发环境)
DATABASE_URL=postgresql://postgres:dev_password@localhost:5432/cvv_dev_db
REDIS_URL=redis://localhost:6379
DB_POOL_SIZE=5

# 安全配置 (开发环境 - 使用弱密钥)
JWT_SECRET=dev_jwt_secret_key_not_for_production
ENCRYPTION_KEY=dev_encryption_key_not_for_production
SESSION_SECRET=dev_session_secret_not_for_production

# 日志配置
LOG_LEVEL=debug
LOG_FILE_PATH=./logs/dev.log
LOG_CONSOLE=true

# 性能配置 (开发环境宽松限制)
RATE_LIMIT_MAX=1000
RATE_LIMIT_WINDOW=900000
REQUEST_TIMEOUT=30000

# 开发工具
HOT_RELOAD=true
SOURCE_MAPS=true
PERFORMANCE_PROFILING=true
