import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import CVVDetectionPanel from '@/components/CVVDetectionPanel'

// Mock fetch
global.fetch = jest.fn()

describe('CVVDetectionPanel', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders CVV detection form', () => {
    render(<CVVDetectionPanel />)
    
    expect(screen.getByLabelText(/card number/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/cvv/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/expiry date/i)).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /check cvv/i })).toBeInTheDocument()
  })

  it('validates card number input', async () => {
    const user = userEvent.setup()
    render(<CVVDetectionPanel />)
    
    const cardNumberInput = screen.getByLabelText(/card number/i)
    
    // Test invalid card number
    await user.type(cardNumberInput, '1234')
    await user.tab() // Trigger blur event
    
    await waitFor(() => {
      expect(screen.getByText(/invalid card number/i)).toBeInTheDocument()
    })
  })

  it('validates CVV input', async () => {
    const user = userEvent.setup()
    render(<CVVDetectionPanel />)
    
    const cvvInput = screen.getByLabelText(/cvv/i)
    
    // Test invalid CVV
    await user.type(cvvInput, '12')
    await user.tab()
    
    await waitFor(() => {
      expect(screen.getByText(/cvv must be 3 or 4 digits/i)).toBeInTheDocument()
    })
  })

  it('submits form with valid data', async () => {
    const user = userEvent.setup()
    const mockResponse = {
      valid: true,
      cardType: 'visa',
      message: 'Valid CVV'
    }
    
    ;(global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => mockResponse
    })
    
    render(<CVVDetectionPanel />)
    
    // Fill form with valid data
    await user.type(screen.getByLabelText(/card number/i), '****************')
    await user.type(screen.getByLabelText(/cvv/i), '123')
    await user.type(screen.getByLabelText(/expiry date/i), '12/25')
    
    // Submit form
    await user.click(screen.getByRole('button', { name: /check cvv/i }))
    
    // Verify API call
    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining('/api/detection/validate'),
        expect.objectContaining({
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            cardNumber: '****************',
            cvv: '123',
            expDate: '12/25'
          })
        })
      )
    })
    
    // Verify success message
    await waitFor(() => {
      expect(screen.getByText(/valid cvv/i)).toBeInTheDocument()
    })
  })

  it('handles API error gracefully', async () => {
    const user = userEvent.setup()
    
    ;(global.fetch as jest.Mock).mockRejectedValueOnce(new Error('Network error'))
    
    render(<CVVDetectionPanel />)
    
    // Fill and submit form
    await user.type(screen.getByLabelText(/card number/i), '****************')
    await user.type(screen.getByLabelText(/cvv/i), '123')
    await user.type(screen.getByLabelText(/expiry date/i), '12/25')
    await user.click(screen.getByRole('button', { name: /check cvv/i }))
    
    // Verify error handling
    await waitFor(() => {
      expect(screen.getByText(/error occurred/i)).toBeInTheDocument()
    })
  })

  it('shows loading state during submission', async () => {
    const user = userEvent.setup()
    
    // Mock delayed response
    ;(global.fetch as jest.Mock).mockImplementationOnce(
      () => new Promise(resolve => setTimeout(resolve, 1000))
    )
    
    render(<CVVDetectionPanel />)
    
    // Fill and submit form
    await user.type(screen.getByLabelText(/card number/i), '****************')
    await user.type(screen.getByLabelText(/cvv/i), '123')
    await user.type(screen.getByLabelText(/expiry date/i), '12/25')
    await user.click(screen.getByRole('button', { name: /check cvv/i }))
    
    // Verify loading state
    expect(screen.getByText(/checking/i)).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /checking/i })).toBeDisabled()
  })
})
