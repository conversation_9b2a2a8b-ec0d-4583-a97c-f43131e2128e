{"name": "cvv-detector-frontend", "version": "1.0.0", "description": "Frontend for CVV Detector MVP - xcheck.top recreation", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"@hookform/resolvers": "^3.3.2", "@tanstack/react-query": "^5.14.2", "@types/node": "^20.10.5", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "autoprefixer": "^10.4.16", "axios": "^1.6.2", "clsx": "^2.0.0", "lucide-react": "^0.303.0", "next": "^14.0.4", "postcss": "^8.4.32", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "tailwind-merge": "^2.2.0", "tailwindcss": "^3.3.6", "typescript": "^5.3.3", "zod": "^3.22.4"}, "devDependencies": {"@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "eslint": "^8.56.0", "eslint-config-next": "^14.0.4", "@testing-library/react": "^14.0.0", "@testing-library/jest-dom": "^6.0.0", "@testing-library/user-event": "^14.5.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "@types/jest": "^29.5.0"}}