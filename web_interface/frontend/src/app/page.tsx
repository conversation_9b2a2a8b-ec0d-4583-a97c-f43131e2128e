'use client';

import React from 'react';
import { CheckCircle, XCircle, HelpCircle } from 'lucide-react';
import Sidebar from '@/components/Sidebar';
import Header from '@/components/Header';
import StatCard from '@/components/StatCard';
import CVVDetectionPanel from '@/components/CVVDetectionPanel';

export default function HomePage() {
  // Mock data for stats
  const stats = {
    live: 0,
    dead: 0,
    unknown: 0,
  };

  return (
    <div className="flex h-screen bg-background-primary">
      {/* Sidebar */}
      <Sidebar />

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Header */}
        <Header />

        {/* Main Content Area */}
        <main className="flex-1 overflow-auto p-4 sm:p-6">
          <div className="max-w-7xl mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 items-stretch">
              {/* Left Column - CVV Detection Panel */}
              <div className="lg:col-span-2 order-2 lg:order-1">
                <CVVDetectionPanel />
              </div>

              {/* Right Column - Stats */}
              <div className="flex flex-col sm:flex-row lg:flex-col gap-6 order-1 lg:order-2">
                <StatCard
                  title="Live"
                  count={stats.live}
                  icon={CheckCircle}
                  variant="live"
                  className="flex-1"
                />

                <StatCard
                  title="Dead"
                  count={stats.dead}
                  icon={XCircle}
                  variant="dead"
                  className="flex-1"
                />

                <StatCard
                  title="Unknown"
                  count={stats.unknown}
                  icon={HelpCircle}
                  variant="unknown"
                  className="flex-1"
                />
              </div>
            </div>


          </div>
        </main>
      </div>
    </div>
  );
}
