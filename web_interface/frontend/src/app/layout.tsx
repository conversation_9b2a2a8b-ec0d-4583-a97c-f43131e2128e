import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'CVV Detector | X CHECK',
  description: 'Best CVV checker on the market, offering unmatched speed, reliability, and accuracy for all your verification needs.',
  keywords: ['CVV', 'checker', 'detector', 'verification', 'card', 'validation'],
  authors: [{ name: 'X CHECK' }],
}

export const viewport = {
  width: 'device-width',
  initialScale: 1,
  themeColor: '#0a0a0a',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className="dark">
      <body className={`${inter.className} antialiased`}>
        <div className="min-h-screen bg-background-primary">
          {children}
        </div>
      </body>
    </html>
  )
}
