@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@layer base {
  body {
    @apply bg-background-primary text-text-primary font-sans;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  html {
    @apply scroll-smooth;
  }
}

@layer components {
  .card {
    @apply bg-card-primary border border-border-primary rounded-3xl shadow-xcheck;
    /* 确保所有卡片有相同的边框和对齐基准 */
    box-sizing: border-box;
  }

  .card-hover {
    @apply transition-all duration-200 hover:bg-card-hover hover:shadow-md;
  }

  .btn-primary {
    @apply bg-accent-primary hover:bg-accent-secondary text-white font-medium px-4 py-2 rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed;
  }

  .btn-secondary {
    @apply bg-card-secondary hover:bg-card-hover text-text-primary border border-border-primary font-medium px-4 py-2 rounded-lg transition-colors duration-200;
  }

  .input-primary {
    @apply bg-card-secondary border border-border-primary text-text-primary placeholder-text-muted rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-accent-primary focus:border-transparent transition-colors duration-200;
  }

  .textarea-primary {
    @apply bg-card-secondary border border-border-primary text-text-primary placeholder-text-muted rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-accent-primary focus:border-transparent transition-colors duration-200 resize-none;
  }

  .nav-item {
    @apply flex items-center gap-3 px-4 py-3 text-text-secondary hover:text-text-primary hover:bg-card-hover rounded-lg transition-colors duration-200 cursor-pointer;
  }

  .nav-item.active {
    @apply text-text-primary bg-card-hover;
  }
}