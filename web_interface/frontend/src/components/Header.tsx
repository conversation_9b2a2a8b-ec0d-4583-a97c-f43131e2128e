'use client';

import React from 'react';
import { Bell, MessageCircle, User, ChevronDown } from 'lucide-react';

export default function Header() {
  return (
    <header className="bg-card-primary">
      <div className="flex items-center justify-between px-6 py-4 border-b border-border-primary">
        {/* Left side - could add breadcrumbs or page title here */}
        <div className="flex items-center">
          {/* Mobile menu button would go here */}
        </div>

        {/* Right side - User info and actions */}
        <div className="flex items-center gap-4">
          {/* Contact us button */}
          <button className="flex items-center gap-2 px-3 py-2 text-text-secondary hover:text-text-primary hover:bg-card-hover rounded-lg transition-colors duration-200">
            <MessageCircle className="w-4 h-4" />
            <span className="text-sm font-medium">Contact us</span>
          </button>

          {/* Notifications */}
          <button className="relative p-2 text-text-secondary hover:text-text-primary hover:bg-card-hover rounded-lg transition-colors duration-200">
            <Bell className="w-5 h-5" />
            {/* Notification badge */}
            <span className="absolute -top-1 -right-1 w-3 h-3 bg-accent-danger rounded-full"></span>
          </button>

          {/* User menu */}
          <div className="flex items-center gap-3 px-3 py-2 hover:bg-card-hover rounded-lg cursor-pointer transition-colors duration-200">
            <div className="flex items-center justify-center w-8 h-8 bg-accent-primary/20 rounded-full">
              <User className="w-4 h-4 text-accent-primary" />
            </div>
            <div className="flex flex-col">
              <span className="text-sm font-medium text-text-primary">Demo User</span>
              <span className="text-xs text-text-secondary"><EMAIL></span>
            </div>
            <ChevronDown className="w-4 h-4 text-text-secondary" />
          </div>
        </div>
      </div>
    </header>
  );
}
