'use client';

import React, { useState, useEffect } from 'react';
import { Play, CheckCircle, XCircle, AlertCircle } from 'lucide-react';
import { cn } from '@/lib/utils';
import { validateCardList } from '@/lib/luhn';

interface CVVDetectionPanelProps {
  className?: string;
}

interface ValidationStats {
  total: number;
  valid: number;
  invalid: number;
  validationRate: number;
}

export default function CVVDetectionPanel({ className }: CVVDetectionPanelProps) {
  const [inputValue, setInputValue] = useState('');
  const [isDetecting, setIsDetecting] = useState(false);
  const [validationStats, setValidationStats] = useState<ValidationStats>({
    total: 0,
    valid: 0,
    invalid: 0,
    validationRate: 0
  });

  // Real-time validation effect
  useEffect(() => {
    if (inputValue.trim()) {
      const results = validateCardList(inputValue);
      setValidationStats(results);
    } else {
      setValidationStats({
        total: 0,
        valid: 0,
        invalid: 0,
        validationRate: 0
      });
    }
  }, [inputValue]);

  const handleDetection = async () => {
    if (!inputValue.trim()) return;

    // First, validate cards using Luhn algorithm
    const validation = validateCardList(inputValue);
    
    if (validation.valid === 0) {
      alert('No valid card numbers found. Please check your input.');
      return;
    }

    setIsDetecting(true);
    
    // Only send valid cards for detection
    const validCards = validation.validCards;
    console.log(`Starting detection for ${validCards.length} valid cards...`);
    
    // Simulate detection process
    setTimeout(() => {
      setIsDetecting(false);
      console.log(`Detection completed for ${validCards.length} valid cards!`);
    }, 2000);
  };

  return (
    <div className={cn('card flex flex-col h-full', className)} style={{ minHeight: '600px' }}>
      {/* Header */}
      <div className="p-6 pb-4">
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-semibold text-white">CVV Detection</h2>
          
          {/* Validation Stats */}
          {validationStats.total > 0 && (
            <div className="flex items-center gap-4 text-sm">
              <div className="flex items-center gap-1">
                <AlertCircle className="w-4 h-4 text-gray-400" />
                <span className="text-gray-300">Total: {validationStats.total}</span>
              </div>
              <div className="flex items-center gap-1">
                <CheckCircle className="w-4 h-4 text-green-400" />
                <span className="text-green-300">Valid: {validationStats.valid}</span>
              </div>
              <div className="flex items-center gap-1">
                <XCircle className="w-4 h-4 text-red-400" />
                <span className="text-red-300">Invalid: {validationStats.invalid}</span>
              </div>
              <div className="px-2 py-1 bg-blue-500/20 rounded text-blue-300 text-xs">
                {validationStats.validationRate}% Valid
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Input Section - Takes up remaining space */}
      <div className="flex-1 px-6 pb-4">
        <textarea
          value={inputValue}
          onChange={(e) => setInputValue(e.target.value)}
          placeholder="Please enter card information, one per line..."
          className="textarea-primary w-full h-full min-h-[400px] text-sm resize-none"
          disabled={isDetecting}
        />
      </div>

      {/* Detection Button */}
      <div className="px-6 pb-6">
        <button
          onClick={handleDetection}
          disabled={!inputValue.trim() || isDetecting || validationStats.valid === 0}
          className={cn(
            'w-full flex items-center justify-center gap-2 px-6 py-3 rounded-lg font-medium transition-all duration-200',
            isDetecting
              ? 'bg-yellow-600/20 text-yellow-400 cursor-not-allowed'
              : validationStats.valid === 0 && inputValue.trim()
              ? 'bg-red-600/20 text-red-400 cursor-not-allowed'
              : 'btn-primary hover:scale-105'
          )}
        >
          {isDetecting ? (
            <>
              <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
              <span>Detecting...</span>
            </>
          ) : validationStats.valid === 0 && inputValue.trim() ? (
            <>
              <XCircle className="w-4 h-4" />
              <span>No valid cards found</span>
            </>
          ) : (
            <>
              <Play className="w-4 h-4" />
              <span>
                Start detection
                {validationStats.valid > 0 && ` (${validationStats.valid} cards)`}
              </span>
            </>
          )}
        </button>
      </div>
    </div>
  );
}
