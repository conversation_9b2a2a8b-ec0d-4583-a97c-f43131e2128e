'use client';

import React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { 
  Shield, 
  Search, 
  Layers, 
  Zap, 
  Settings,
  CreditCard
} from 'lucide-react';
import { cn } from '@/lib/utils';

const navigation = [
  {
    name: 'CVV Detection',
    href: '/',
    icon: Shield,
    current: true,
  },
  {
    name: 'BIN Query',
    href: '/bin-query',
    icon: Search,
    current: false,
  },
  {
    name: 'Card Classification',
    href: '/card-classification',
    icon: Layers,
    current: false,
  },
  {
    name: 'Card Predetection',
    href: '/pre-detection',
    icon: Zap,
    current: false,
  },
  {
    name: 'Settings',
    href: '/settings',
    icon: Settings,
    current: false,
  },
];

export default function Sidebar() {
  const pathname = usePathname();

  return (
    <div className="flex flex-col w-64 bg-card-primary border-r border-border-primary">
      {/* Logo */}
      <div className="flex items-center gap-3 px-6 py-4 border-b border-border-primary min-h-[85px]">
        <div className="flex items-center justify-center w-8 h-8 bg-accent-primary rounded-lg">
          <CreditCard className="w-5 h-5 text-white" />
        </div>
        <span className="text-xl font-semibold text-text-primary">X CHECK</span>
      </div>

      {/* Navigation */}
      <nav className="flex-1 px-4 py-6 space-y-2">
        {navigation.map((item) => {
          const isActive = pathname === item.href;
          return (
            <Link
              key={item.name}
              href={item.href}
              className={cn(
                'nav-item',
                isActive && 'active'
              )}
            >
              <item.icon className="w-5 h-5" />
              <span className="font-medium">{item.name}</span>
            </Link>
          );
        })}
      </nav>

      {/* Points Section */}
      <div className="px-4 py-6 border-t border-border-primary">
        <div className="card p-4">
          <div className="text-sm text-text-secondary mb-2">Available points</div>
          <div className="flex items-center gap-2 mb-3">
            <div className="flex items-center justify-center w-6 h-6 bg-accent-primary/20 rounded">
              <CreditCard className="w-3 h-3 text-accent-primary" />
            </div>
            <span className="text-lg font-semibold text-text-primary">0 PT</span>
          </div>
          <Link
            href="/recharge"
            className="block w-full text-center bg-accent-primary hover:bg-accent-secondary text-white text-sm font-medium py-2 px-4 rounded-lg transition-colors duration-200"
          >
            Recharge
          </Link>
        </div>
      </div>
    </div>
  );
}
