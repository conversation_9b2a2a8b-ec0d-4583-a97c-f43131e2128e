'use client';

import React from 'react';
import { LucideIcon, Copy } from 'lucide-react';
import { cn } from '@/lib/utils';

interface StatCardProps {
  title: string;
  count: number;
  icon: LucideIcon;
  variant: 'live' | 'dead' | 'unknown';
  className?: string;
}

const variantStyles = {
  live: {
    bg: 'bg-status-live',
    text: 'text-status-live',
    icon: 'text-status-live',
    iconBg: 'bg-status-live/10',
    border: 'border-status-live/20',
    countText: 'text-status-live',
    titleText: 'text-status-live',
  },
  dead: {
    bg: 'bg-status-dead',
    text: 'text-status-dead',
    icon: 'text-status-dead',
    iconBg: 'bg-status-dead/10',
    border: 'border-status-dead/20',
    countText: 'text-status-dead',
    titleText: 'text-status-dead',
  },
  unknown: {
    bg: 'bg-status-unknown',
    text: 'text-status-unknown',
    icon: 'text-status-unknown',
    iconBg: 'bg-status-unknown/10',
    border: 'border-status-unknown/20',
    countText: 'text-status-unknown',
    titleText: 'text-status-unknown',
  },
};

export default function StatCard({ 
  title, 
  count, 
  icon: Icon, 
  variant, 
  className 
}: StatCardProps) {
  const styles = variantStyles[variant];

  return (
    <div className={cn(
      'bg-card-primary border border-border-primary rounded-3xl shadow-xcheck flex-1',
      className
    )}>
      {/* 顶部区域 - 水平布局 */}
      <div className="flex items-center justify-between px-4 py-2">
        <div className="flex items-center gap-2">
          <Icon className={cn('w-5 h-5', styles.icon)} />
          <h6 className={cn('text-base font-semibold', styles.titleText)}>{title}</h6>
          <p className={cn('text-sm font-semibold', styles.countText)}>{count.toLocaleString()}</p>
        </div>
        <button className="p-2 text-gray-500 hover:text-gray-300 hover:bg-gray-700/50 rounded-lg transition-colors duration-200">
          <Copy className="w-5 h-5" />
        </button>
      </div>

      {/* 剩余空间区域 */}
      <div className="flex-1 px-4 pb-4">
        {/* 保持空白，匹配 xcheck.top 的设计 */}
      </div>
    </div>
  );
}