# CVV检测Web界面 - 生产环境配置模板
# 注意: 生产环境中应该通过环境变量或密钥管理系统设置这些值

# 前端配置
NEXT_PUBLIC_API_URL=https://api.yourdomain.com
NEXT_PUBLIC_APP_ENV=production
NEXT_PUBLIC_APP_VERSION=1.0.0
NEXT_PUBLIC_DEBUG_MODE=false

# 后端配置
PORT=5000
NODE_ENV=production
CORS_ORIGIN=https://yourdomain.com

# CVV检测API集成
CVV_API_URL=https://cvv-api.yourdomain.com/api/v1/cvv
CVV_API_KEY=REPLACE_WITH_ACTUAL_PRODUCTION_KEY
CVV_API_TIMEOUT=5000

# 数据库配置 (生产环境)
DATABASE_URL=REPLACE_WITH_ACTUAL_PRODUCTION_DATABASE_URL
REDIS_URL=REPLACE_WITH_ACTUAL_PRODUCTION_REDIS_URL
DB_POOL_SIZE=20

# 安全配置 (生产环境 - 必须使用强密钥)
JWT_SECRET=REPLACE_WITH_STRONG_JWT_SECRET
ENCRYPTION_KEY=REPLACE_WITH_STRONG_ENCRYPTION_KEY
SESSION_SECRET=REPLACE_WITH_STRONG_SESSION_SECRET

# 日志配置
LOG_LEVEL=warn
LOG_FILE_PATH=/var/log/cvv-web/app.log
LOG_CONSOLE=false

# 性能配置 (生产环境严格限制)
RATE_LIMIT_MAX=100
RATE_LIMIT_WINDOW=900000
REQUEST_TIMEOUT=10000

# 生产优化
HOT_RELOAD=false
SOURCE_MAPS=false
PERFORMANCE_PROFILING=false
