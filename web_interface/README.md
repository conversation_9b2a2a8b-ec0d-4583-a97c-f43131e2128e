# CVV检测Web界面

基于Next.js和Express.js构建的CVV检测MVP Web界面，高保真还原xcheck.top的设计和功能。

## 🎯 项目概述

本模块是Account Registrar项目的Web界面部分，提供：
- 现代化的CVV检测用户界面
- 响应式设计支持桌面和移动端
- 与后端CVV检测API的无缝集成

## 🛠️ 技术栈

**前端 (frontend/)**
- Next.js 14 with App Router
- TypeScript
- Tailwind CSS
- React Hook Form + Zod
- Lucide React Icons

**后端 (backend/)**
- Node.js + Express.js
- TypeScript
- CORS + Helmet安全中间件
- RESTful API设计

## 🚀 快速开始

### 开发环境启动
```bash
# 启动完整开发环境
docker-compose up -d

# 或分别启动前后端
cd frontend && npm run dev
cd backend && npm run dev
```

### 生产环境部署
```bash
# 使用生产配置
docker-compose -f ../deployment/docker/docker-compose.web.yml up -d
```

## 📁 目录结构

```
web_interface/
├── frontend/           # Next.js前端应用
├── backend/            # Express.js后端API
├── docker-compose.yml  # 开发环境配置
└── README.md          # 本文档
```

## 🔗 集成说明

本Web界面与主项目的CVV检测核心模块(`cvv_detection/`)集成，提供用户友好的操作界面。

## 📋 开发指南

### 前端开发
```bash
cd frontend
npm install
npm run dev
```

### 后端开发
```bash
cd backend
npm install
npm run dev
```

### Docker开发
```bash
# 构建并启动所有服务
docker-compose up --build

# 查看日志
docker-compose logs -f
```

## 🧪 测试

### 前端测试
```bash
cd frontend
npm run lint
npm run type-check
```

### 后端测试
```bash
cd backend
npm run lint
npm run build
```

## 🔧 配置

### 环境变量
- `NODE_ENV`: 运行环境 (development/production)
- `NEXT_PUBLIC_API_URL`: 前端API地址
- `PORT`: 后端服务端口
- `CORS_ORIGIN`: CORS允许的源

### API端点
- `GET /api/health` - 健康检查
- `POST /api/detection/validate` - CVV验证
- `GET /api/user/profile` - 用户信息

## 📝 更新日志

### v1.0.0 (2025-01-18)
- 初始版本发布
- 完整的前后端分离架构
- Docker容器化支持
- 响应式UI设计

---

**维护团队**: 全栈工程师团队  
**最后更新**: 2025-01-18
