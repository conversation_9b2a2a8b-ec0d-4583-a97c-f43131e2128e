# CVV检测Web界面环境变量配置模板
# 复制此文件为.env.local并填入实际值

# ===========================================
# 前端配置 (NEXT_PUBLIC_ 前缀的变量会暴露给浏览器)
# ===========================================

# API服务地址
NEXT_PUBLIC_API_URL=http://localhost:5000

# 应用环境
NEXT_PUBLIC_APP_ENV=development

# 应用版本
NEXT_PUBLIC_APP_VERSION=1.0.0

# 是否启用调试模式
NEXT_PUBLIC_DEBUG_MODE=true

# ===========================================
# 后端配置
# ===========================================

# 服务端口
PORT=5000

# 运行环境
NODE_ENV=development

# CORS允许的源
CORS_ORIGIN=http://localhost:3000

# ===========================================
# CVV检测API集成
# ===========================================

# CVV检测核心API地址
CVV_API_URL=http://localhost:8000/api/v1/cvv

# CVV API认证密钥 (生产环境必须设置)
CVV_API_KEY=your_cvv_api_key_here

# CVV API超时时间 (毫秒)
CVV_API_TIMEOUT=5000

# ===========================================
# 数据库配置
# ===========================================

# PostgreSQL数据库连接
DATABASE_URL=postgresql://postgres:password@localhost:5432/cvv_db

# Redis缓存连接
REDIS_URL=redis://localhost:6379

# 数据库连接池大小
DB_POOL_SIZE=10

# ===========================================
# 安全配置
# ===========================================

# JWT密钥 (生产环境必须使用强密钥)
JWT_SECRET=your_jwt_secret_key_here

# 数据加密密钥 (生产环境必须使用强密钥)
ENCRYPTION_KEY=your_encryption_key_here

# 会话密钥
SESSION_SECRET=your_session_secret_here

# ===========================================
# 日志配置
# ===========================================

# 日志级别 (error, warn, info, debug)
LOG_LEVEL=info

# 日志文件路径
LOG_FILE_PATH=./logs/app.log

# 是否启用控制台日志
LOG_CONSOLE=true

# ===========================================
# 性能配置
# ===========================================

# 请求频率限制 (每15分钟最大请求数)
RATE_LIMIT_MAX=100

# 频率限制时间窗口 (毫秒)
RATE_LIMIT_WINDOW=900000

# 请求超时时间 (毫秒)
REQUEST_TIMEOUT=30000

# ===========================================
# 开发工具配置
# ===========================================

# 是否启用热重载
HOT_RELOAD=true

# 是否启用源码映射
SOURCE_MAPS=true

# 是否启用性能分析
PERFORMANCE_PROFILING=false
