# Account Registrar 项目文档

> **项目**: Account Registrar - 智能账户注册和CVV检测系统  
> **维护团队**: 全栈工程师团队  
> **最后更新**: 2025-01-18

## 📚 文档目录

### 🎯 核心开发指南

#### [CVV检测MVP集成开发指南](./CVV_DETECTOR_MVP_INTEGRATION_GUIDE.md)
**完整的CVV检测功能集成技术方案**

- **分支管理策略**: Git Flow工作流程和分支保护规则
- **代码组织建议**: 模块化架构设计和项目结构重组
- **开发测试策略**: TDD驱动开发和全面测试覆盖
- **合并部署计划**: 分阶段部署和风险控制策略
- **具体执行建议**: 立即行动项和质量保证检查清单

**适用场景**: CVV检测MVP功能开发和集成
**技术栈**: Python, FastAPI, Next.js, Express.js, Docker
**预计工期**: 9周开发周期

### 🏗️ 架构设计文档

#### [CVV集成架构设计](./cvv_integration_architecture.md)
**CVV检测功能的详细架构设计**

- **项目结构**: 推荐的目录组织和模块划分
- **核心模块设计**: CVV验证器、加密服务、API层设计
- **安全考虑**: 数据保护策略和安全模块实现
- **系统集成策略**: Hulu系统集成桥接方案
- **监控日志**: 统一日志系统和性能监控
- **测试策略**: 测试层次结构和数据管理

## 🚀 快速开始

### 开发环境设置

```bash
# 1. 克隆项目
git clone <repository-url>
cd Account\ Registrar

# 2. 创建CVV功能分支
git checkout develop
git checkout -b feature/cvv-detector-mvp

# 3. 安装依赖
pip install -e .[cvv,test]

# 4. 运行测试
pytest cvv_detection/tests/ -v
```

### 项目结构概览

```
Account Registrar/
├── docs/                           # 📚 项目文档
│   ├── README.md                   # 文档索引
│   ├── CVV_DETECTOR_MVP_INTEGRATION_GUIDE.md
│   └── cvv_integration_architecture.md
│
├── hulu_automation/                # 🎭 Hulu自动化系统
├── cvv_detection/                  # 💳 CVV检测模块
├── shared/                         # 🔧 共享组件
├── web_interface/                  # 🌐 Web界面
├── integration/                    # 🔗 系统集成
└── deployment/                     # 🚀 部署配置
```

## 📋 开发工作流

### 分支策略
- `main` - 生产稳定版本
- `develop` - 开发主分支
- `feature/cvv-detector-mvp` - CVV功能主分支
- `feature/cvv-*` - CVV子功能分支

### 代码提交规范
```
feat(cvv): 添加CVV验证核心算法
fix(api): 修复频率限制bug
docs(guide): 更新集成开发指南
test(security): 添加安全组件测试
```

### 质量检查清单
- [ ] 代码审查通过（≥2人）
- [ ] 单元测试覆盖率 ≥ 80%
- [ ] 安全扫描无高危漏洞
- [ ] 性能测试满足要求
- [ ] 文档更新完整

## 🛡️ 安全和合规

### 数据保护
- **敏感数据加密**: 所有CVV和卡号数据加密存储
- **内存安全清理**: 处理完成后立即清理敏感数据
- **审计日志**: 完整的操作审计记录
- **访问控制**: 基于角色的权限管理

### 合规标准
- PCI DSS合规要求
- OWASP安全标准
- 数据隐私保护法规
- 行业最佳实践

## 📊 监控和运维

### 关键指标
- **API性能**: 响应时间 < 200ms, 错误率 < 1%
- **系统资源**: CPU < 70%, 内存 < 80%
- **业务指标**: 验证成功率 > 95%, 误报率 < 5%

### 告警配置
- 高错误率告警
- 性能异常告警
- 安全事件告警
- 资源使用告警

## 🤝 贡献指南

### 开发流程
1. 从`develop`分支创建功能分支
2. 实现功能并编写测试
3. 提交Pull Request
4. 代码审查和CI检查
5. 合并到目标分支

### 代码规范
- 遵循PEP 8编码规范
- 完整的类型注解
- 详细的文档字符串
- 全面的单元测试

## 📞 支持和联系

### 技术支持
- **项目Issue**: 在GitHub Issues中提出技术问题
- **开发团队**: 联系全栈工程师团队
- **紧急联系**: 生产环境问题请立即联系运维团队

### 文档维护
- 功能开发时同步更新文档
- 定期审查文档准确性
- 收集用户反馈改进文档

---

**最后更新**: 2025-01-18  
**文档版本**: v1.0  
**维护团队**: 全栈工程师团队
