# CVV检测文件中转架构实施指南

> **项目**: Account Registrar - CVV检测实施指南  
> **创建时间**: 2025-01-19  
> **版本**: v1.0  
> **依赖文档**: CVV_FILE_TRANSFER_ARCHITECTURE.md

## 🚀 快速开始

### 前置条件检查

```bash
# 1. 检查现有项目结构
ls -la /Users/<USER>/Desktop/workflow/Account\ Registrar/
├── hulu_automation_stealth.py          # ✅ 现有Hulu自动化脚本
├── credit_card_validator/              # ✅ 现有加密模块
├── web_interface/                      # ✅ 现有前端后端
└── data/                              # 需要创建CVV目录

# 2. 检查Python环境
python3 --version  # 需要Python 3.8+
pip list | grep playwright  # 检查Playwright依赖

# 3. 检查Node.js环境
node --version  # 需要Node.js 16+
npm --version
```

### 第一步：创建目录结构

```bash
# 进入项目根目录
cd "/Users/<USER>/Desktop/workflow/Account Registrar"

# 创建CVV检测相关目录
mkdir -p data/{cvv_requests,cvv_results,cvv_processing}
mkdir -p cvv_detection/{services,utils}
mkdir -p logs

# 设置目录权限（PCI DSS要求）
chmod 700 data/cvv_*
chmod 755 cvv_detection
chmod 755 logs

# 验证目录结构
tree data/ cvv_detection/
```

### 第二步：安装依赖

```bash
# 后端依赖
cd web_interface/backend
npm install express cors helmet crypto

# 前端依赖
cd ../frontend
npm install

# Python依赖（如果需要额外的）
pip install asyncio pathlib
```

### 第三步：环境配置

```bash
# 创建环境变量文件
cat > .env << EOF
# CVV检测配置
CVV_ENCRYPTION_KEY=cvv_secure_key_2025_change_in_production
CVV_REQUEST_DIR=data/cvv_requests
CVV_RESULT_DIR=data/cvv_results
CVV_PROCESSING_DIR=data/cvv_processing

# API配置
API_PORT=3001
API_HOST=localhost

# 安全配置
RATE_LIMIT_WINDOW=900000  # 15分钟
RATE_LIMIT_MAX=100        # 最多100次请求

# 日志配置
LOG_LEVEL=info
LOG_FILE=logs/cvv_detection.log
EOF

# 设置环境变量文件权限
chmod 600 .env
```

## 📁 文件创建清单

### 前端文件

**1. Luhn验证器**
```bash
# 创建文件：web_interface/frontend/src/utils/luhnValidator.js
# 内容：参考架构文档中的LuhnValidator类实现
```

**2. CVV检测组件**
```bash
# 创建文件：web_interface/frontend/src/components/CVVDetection.jsx
# 内容：参考架构文档中的CVVDetection组件实现
```

**3. 文件服务**
```bash
# 创建文件：web_interface/frontend/src/services/cvvFileService.js
# 内容：参考架构文档中的CVVFileService类实现
```

**4. 样式文件**
```bash
# 创建文件：web_interface/frontend/src/styles/cvvDetection.css
cat > web_interface/frontend/src/styles/cvvDetection.css << EOF
.cvv-detection-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.input-section {
  margin-bottom: 30px;
}

.input-section textarea {
  width: 100%;
  min-height: 200px;
  padding: 15px;
  border: 2px solid #ddd;
  border-radius: 8px;
  font-family: monospace;
  font-size: 14px;
}

.input-section button {
  margin-top: 15px;
  padding: 12px 30px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 16px;
}

.input-section button:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.results-section {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 30px;
}

.statistics {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #dee2e6;
}

.card-results {
  display: grid;
  gap: 10px;
}

.card-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 6px;
  background: white;
}

.card-item.live {
  border-color: #28a745;
  background: #d4edda;
}

.card-item.dead {
  border-color: #dc3545;
  background: #f8d7da;
}

.card-item.unknown {
  border-color: #ffc107;
  background: #fff3cd;
}

.card-info {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.card-number {
  font-family: monospace;
  font-weight: bold;
}

.card-type {
  font-size: 12px;
  color: #6c757d;
  text-transform: uppercase;
}

.status-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 5px;
}

.status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
  text-transform: uppercase;
}

.status.pending {
  background: #e9ecef;
  color: #495057;
}

.status.processing {
  background: #cce5ff;
  color: #0066cc;
}

.status.completed {
  background: #d4edda;
  color: #155724;
}

.status.luhn_failed {
  background: #f8d7da;
  color: #721c24;
}

.result {
  font-weight: bold;
  text-transform: uppercase;
}

.result.live {
  color: #28a745;
}

.result.dead {
  color: #dc3545;
}

.result.unknown {
  color: #ffc107;
}
EOF
```

### 后端文件

**1. API路由**
```bash
# 创建文件：web_interface/backend/src/routes/cvv-file-transfer.js
# 内容：参考架构文档中的路由实现
```

**2. 文件管理服务**
```bash
# 创建文件：web_interface/backend/src/services/cvvFileManager.js
# 内容：参考架构文档中的CVVFileManager类实现
```

**3. 安全文件处理器**
```bash
# 创建文件：web_interface/backend/src/services/secureFileHandler.js
# 内容：参考架构文档中的SecureFileHandler类实现
```

**4. 安全中间件**
```bash
# 创建文件：web_interface/backend/src/middleware/security.js
cat > web_interface/backend/src/middleware/security.js << EOF
const rateLimit = require('express-rate-limit');
const helmet = require('helmet');

class SecurityMiddleware {
  static validateRequest = (req, res, next) => {
    // 基础请求验证
    if (!req.body) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Request body is required'
      });
    }
    
    // Content-Type验证
    if (req.method === 'POST' && !req.is('application/json')) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Content-Type must be application/json'
      });
    }
    
    next();
  };
  
  static rateLimiting = rateLimit({
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW) || 900000, // 15分钟
    max: parseInt(process.env.RATE_LIMIT_MAX) || 100, // 最多100次请求
    message: {
      error: 'Too Many Requests',
      message: 'Rate limit exceeded, please try again later'
    },
    standardHeaders: true,
    legacyHeaders: false,
  });
  
  static helmet = helmet({
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'"],
        scriptSrc: ["'self'"],
        imgSrc: ["'self'", "data:", "https:"],
      },
    },
    hsts: {
      maxAge: 31536000,
      includeSubDomains: true,
      preload: true
    }
  });
}

module.exports = { SecurityMiddleware };
EOF
```

### Python验证服务

**1. CVV验证服务**
```bash
# 创建文件：cvv_detection/services/hulu_cvv_validator.py
# 内容：参考架构文档中的HuluCVVValidator类实现
```

**2. 服务启动脚本**
```bash
# 创建文件：cvv_detection/start_cvv_service.py
cat > cvv_detection/start_cvv_service.py << EOF
#!/usr/bin/env python3
"""
CVV检测服务启动脚本
"""

import asyncio
import logging
import signal
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from cvv_detection.services.hulu_cvv_validator import HuluCVVValidator

class CVVServiceManager:
    def __init__(self):
        self.validator = None
        self.running = False
        
    async def start_service(self):
        """启动CVV验证服务"""
        print("🚀 启动CVV检测服务...")
        
        try:
            self.validator = HuluCVVValidator()
            self.running = True
            
            # 设置信号处理
            signal.signal(signal.SIGINT, self._signal_handler)
            signal.signal(signal.SIGTERM, self._signal_handler)
            
            await self.validator.start_validation_service()
            
        except Exception as e:
            print(f"❌ 服务启动失败: {e}")
            sys.exit(1)
    
    def _signal_handler(self, signum, frame):
        """信号处理器"""
        print(f"\n📡 接收到信号 {signum}，正在关闭服务...")
        self.running = False
        
    async def stop_service(self):
        """停止服务"""
        if self.validator:
            print("⏹️ 正在停止CVV验证服务...")
            # 这里可以添加清理逻辑
            
def main():
    """主函数"""
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('logs/cvv_service.log')
        ]
    )
    
    # 创建服务管理器
    service_manager = CVVServiceManager()
    
    try:
        asyncio.run(service_manager.start_service())
    except KeyboardInterrupt:
        print("\n⏹️ 用户中断服务")
    except Exception as e:
        print(f"❌ 服务运行失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
EOF

chmod +x cvv_detection/start_cvv_service.py
```

## 🔧 集成配置

### 后端路由集成

```bash
# 编辑：web_interface/backend/src/app.js
# 添加CVV路由
cat >> web_interface/backend/src/app.js << EOF

// CVV检测路由
const cvvRoutes = require('./routes/cvv-file-transfer');
app.use('/api/cvv', cvvRoutes);
EOF
```

### 前端路由集成

```bash
# 编辑：web_interface/frontend/src/App.jsx
# 添加CVV检测页面路由
```

## 🧪 测试验证

### 单元测试

```bash
# 创建测试文件：tests/cvv_detection_test.js
mkdir -p tests
cat > tests/cvv_detection_test.js << EOF
const { LuhnValidator } = require('../web_interface/frontend/src/utils/luhnValidator');

// Luhn算法测试
console.log('🧪 测试Luhn算法验证...');

const testCards = [
  { number: '****************', expected: true, type: 'visa' },
  { number: '****************', expected: true, type: 'mastercard' },
  { number: '1234567890123456', expected: false, type: 'unknown' },
];

testCards.forEach(test => {
  const isValid = LuhnValidator.validate(test.number);
  const cardType = LuhnValidator.detectCardType(test.number);
  
  console.log(`卡号: ${test.number}`);
  console.log(`预期: ${test.expected}, 实际: ${isValid} ${isValid === test.expected ? '✅' : '❌'}`);
  console.log(`类型: ${cardType} ${cardType === test.type ? '✅' : '❌'}`);
  console.log('---');
});
EOF

node tests/cvv_detection_test.js
```

### 集成测试

```bash
# 测试API端点
curl -X POST http://localhost:3001/api/cvv/submit-batch \
  -H "Content-Type: application/json" \
  -d '{
    "cards": [
      {
        "name": "Test User",
        "cardNumber": "****************",
        "expiration": "12/25",
        "cvc": "123",
        "zipCode": "12345",
        "cardType": "visa"
      }
    ]
  }'
```

## 📋 部署检查清单

- [ ] 目录结构创建完成
- [ ] 环境变量配置完成
- [ ] 前端组件文件创建
- [ ] 后端API文件创建
- [ ] Python验证服务创建
- [ ] 安全中间件配置
- [ ] 文件权限设置正确
- [ ] 依赖包安装完成
- [ ] 测试验证通过
- [ ] 日志目录创建
- [ ] 加密密钥配置

## 🚨 安全注意事项

1. **生产环境密钥**: 必须更改默认加密密钥
2. **文件权限**: 确保CVV相关目录权限为700
3. **网络安全**: 生产环境使用HTTPS
4. **日志安全**: 不在日志中记录敏感信息
5. **定期清理**: 设置自动清理过期文件的定时任务

---

**下一步**: 完成文件创建后，按照启动顺序测试整个系统。
