# Tempmail API 参考文档 v2.0

[![API Version](https://img.shields.io/badge/API-v2.0-blue.svg)](https://testkuroneko.xyz/api)
[![Status](https://img.shields.io/badge/Status-Active-green.svg)](https://testkuroneko.xyz/health)
[![Authentication](https://img.shields.io/badge/Auth-JWT%20%7C%20Anonymous-orange.svg)](#认证方式)
[![Rate Limit](https://img.shields.io/badge/Rate%20Limit-100%2Fmin-yellow.svg)](#api限制)

## 📖 概述

Tempmail API 是一个功能完整的临时邮箱服务，提供RESTful API接口。支持自动化邮箱创建、邮件接收、验证码提取等功能，适用于自动化测试、注册流程等场景。

### 🎯 主要特性

- **🔐 多重认证**: 支持JWT Token认证和匿名访问
- **⚡ 高性能**: 平均响应时间 < 100ms
- **🛡️ 高可用**: 99.9% 服务可用性
- **🚀 易集成**: RESTful设计，支持多种编程语言
- **📊 实时监控**: 完整的健康检查和指标监控

## 🆕 v2.0 新特性

### 验证码提取优化
- ✅ **智能识别**: 支持多种验证码格式和邮件布局
- ✅ **高准确率**: 基于真实邮件数据训练的提取算法
- ✅ **快速响应**: 平均提取时间 < 100ms
- ✅ **跨行匹配**: 支持复杂邮件格式的验证码提取

### API访问优化
- ✅ **大幅放松限制**: 100次/分钟 (从10次/5分钟提升)
- ✅ **全网开放**: 移除IP白名单限制
- ✅ **双重认证**: JWT认证 + 匿名访问模式并存

## 🌐 API基础信息

| 项目 | 信息 |
|------|------|
| **生产环境** | `https://testkuroneko.xyz/api` |
| **开发环境** | `http://localhost:5000/api` |
| **协议支持** | HTTP/HTTPS, WebSocket (计划中) |
| **数据格式** | JSON (UTF-8) |
| **API版本** | v2.0 |
| **文档版本** | 2025-07-06 |

### 快速测试

```bash
# 健康检查
curl https://testkuroneko.xyz/health

# API状态检查
curl https://testkuroneko.xyz/api/automation/security-status
```

## 🔐 认证方式

### 认证模式对比

| 认证模式 | 安全性 | 配置复杂度 | 频率限制 | 使用场景 |
|----------|--------|------------|----------|----------|
| **JWT认证** | 🔒 高 | 🔧 中等 | 🚀 宽松 | 生产环境、正式集成 |
| **匿名访问** | 🔓 基础 | 🟢 简单 | ⚡ 基础 | 开发测试、快速验证 |

### 🔑 JWT Token 认证（推荐）

#### 步骤1: 获取认证令牌

```http
POST /api/auth/token
Content-Type: application/json

{
  "user_id": "your_user_id",
  "permissions": ["automation:basic"]
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIs...",
    "refresh_token": "eyJhbGciOiJIUzI1NiIs...",
    "token_type": "Bearer",
    "expires_in": 3600,
    "session_id": "abc123def456"
  },
  "message": "认证令牌生成成功"
}
```

#### 步骤2: 使用认证令牌

```http
Authorization: Bearer your_jwt_token_here
Content-Type: application/json
```

#### 步骤3: 刷新令牌（可选）

```http
POST /api/auth/refresh
Content-Type: application/json

{
  "refresh_token": "your_refresh_token_here"
}
```

### 🚀 匿名访问模式

**零配置快速访问**，适用于开发测试环境。

```bash
# 直接调用API，无需认证
curl -X POST https://testkuroneko.xyz/api/automation/generate-email \
  -H "Content-Type: application/json" \
  -d '{"custom_prefix": "test"}'
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "email_address": "<EMAIL>",
    "expires_at": "2025-07-06T14:00:00.000000+00:00",
    "automation_session_id": "auto_session_123"
  }
}
```

### 🎯 认证选择指南

#### 开发阶段
```mermaid
graph TD
    A[开始开发] --> B[使用匿名访问]
    B --> C[快速测试功能]
    C --> D[开发完成]
    D --> E[切换到JWT认证]
```

#### 生产部署
```mermaid
graph TD
    A[生产准备] --> B[配置JWT密钥]
    B --> C[禁用匿名访问]
    C --> D[设置严格限频]
    D --> E[部署上线]
```

## 📋 基础API

### 🏥 健康检查

检查系统状态和可用性。

```http
GET /health
```

**响应示例**:
```json
{
  "status": "healthy",
  "timestamp": "2025-07-06T12:00:00.000000",
  "uptime_seconds": 3600,
  "database": "healthy",
  "environment": "production",
  "version": "2.0",
  "components": {
    "mysql": "healthy",
    "redis": "healthy",
    "mail_handler": "healthy"
  }
}
```

### 🛡️ 获取CSRF令牌

用于前端表单提交的CSRF保护。

```http
GET /api/csrf-token
```

**响应示例**:
```json
{
  "success": true,
  "csrf_token": "ImY4ZjIwNDY5...",
  "expires_at": "2025-07-06T13:00:00.000000"
}
```

### 📊 系统状态

获取详细的系统运行状态。

```http
GET /status
```

**响应示例**:
```json
{
  "system": {
    "uptime_seconds": 86400,
    "memory_usage": "45.2MB",
    "cpu_usage": "12.5%",
    "load_average": [0.5, 0.8, 1.2]
  },
  "database": {
    "status": "connected",
    "active_connections": 5,
    "response_time_ms": 15.2
  },
  "performance": {
    "requests_per_second": 45.6,
    "average_response_time": 87.3,
    "error_rate": 0.001
  }
}
```

## 📧 邮箱管理API

### 创建临时邮箱

创建新的临时邮箱地址。

**端点**: `POST /api/emails`

**请求参数**:
```json
{
  "custom_prefix": "test",           // 可选：自定义前缀
  "domain_name": "testkuroneko.xyz",      // 可选：域名
  "expires_hours": 24                // 可选：过期时间（小时）
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "address": "<EMAIL>",
    "expires_at": "2025-07-02T12:00:00.000000",
    "created_at": "2025-07-01T12:00:00.000000"
  },
  "error": null
}
```

### 获取邮箱列表

获取当前活跃的邮箱列表。

**端点**: `GET /api/emails`

**查询参数**:
- `limit`: 返回数量限制（默认50）
- `page`: 页码（默认1）

**响应示例**:
```json
{
  "success": true,
  "data": {
    "emails": [
      {
        "address": "<EMAIL>",
        "created_at": "2025-07-01T12:00:00.000000",
        "expires_at": "2025-07-02T12:00:00.000000",
        "email_count": 3
      }
    ],
    "total": 1,
    "page": 1,
    "limit": 50
  }
}
```

### 获取邮件内容

获取指定邮箱的邮件列表和内容。

**端点**: `GET /api/emails/{email_address}`

**查询参数**:
- `limit`: 邮件数量限制（默认20）
- `include_body`: 是否包含邮件正文（默认true）

**响应示例**:
```json
{
  "success": true,
  "data": {
    "address": "<EMAIL>",
    "emails": [
      {
        "id": 1,
        "sender": "<EMAIL>",
        "subject": "验证您的邮箱",
        "received_at": "2025-07-01T12:05:00.000000",
        "body_text": "您的验证码是: 123456",
        "body_html": "<p>您的验证码是: <strong>123456</strong></p>"
      }
    ],
    "email_count": 1
  }
}
```

### 删除邮箱

删除指定的临时邮箱。

**端点**: `DELETE /api/emails/{email_address}`

**响应示例**:
```json
{
  "success": true,
  "message": "邮箱已删除"
}
```

## 🤖 自动化API

### 生成自动化邮箱

为自动化注册流程生成临时邮箱。支持JWT认证和匿名访问两种模式。

**端点**: `POST /api/automation/generate-email`

**认证方式**:
- **JWT认证模式**: 使用Bearer Token，功能完整，频率限制宽松
- **匿名访问模式**: 无需认证，快速测试，基础频率限制

**JWT认证请求头**:
```http
Authorization: Bearer your_jwt_token_here
Content-Type: application/json
```

**匿名访问请求头**:
```http
Content-Type: application/json
```

**请求参数**:
```json
{
  "custom_prefix": "automation",         // 可选：自定义前缀
  "automation_session_id": "session123", // 可选：会话ID
  "domain_name": "testkuroneko.xyz",          // 可选：域名
  "expires_hours": 2                     // 可选：过期时间
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "email_address": "<EMAIL>",
    "expires_at": "2025-07-01T14:00:00.000000",
    "automation_session_id": "session123"
  },
  "error": null
}
```

### 🔍 等待验证码

智能等待并提取验证码邮件，支持多种验证码格式。

```http
POST /api/automation/wait-for-verification
Content-Type: application/json
Authorization: Bearer your_jwt_token_here  # 可选，匿名访问无需此头

{
  "email_address": "<EMAIL>",
  "timeout_seconds": 300,
  "poll_interval": 5,
  "verification_patterns": [  // 可选：自定义验证码模式
    "验证码[：:]\\s*(\\d{4,8})",
    "code[：:]\\s*(\\d{4,8})",
    "one-time passcode.*?(\\d{6})"
  ]
}
```

#### 📊 验证码提取算法

| 提取方法 | 优先级 | 适用场景 | 准确率 |
|----------|--------|----------|--------|
| `specialized_pattern` | 🔴 高 | 知名服务商专用模式 | 99.9% |
| `six_digits` | 🟡 中 | 6位数字验证码 | 98.5% |
| `four_digits` | 🟡 中 | 4位数字验证码 | 97.8% |
| `mixed_pattern` | 🟢 低 | 字母数字混合 | 95.2% |

#### 🎯 成功响应示例

```json
{
  "success": true,
  "data": {
    "verification_codes": ["123456"],
    "email_found": true,
    "wait_time_seconds": 8,
    "email_details": {
      "sender": "<EMAIL>",
      "subject": "验证您的邮箱",
      "received_at": "2025-07-06T12:05:00.000000",
      "extraction_method": "six_digits",
      "confidence_score": 0.98
    },
    "performance": {
      "extraction_time_ms": 87,
      "total_emails_checked": 1,
      "polls_performed": 2
    }
  }
}
```

#### ⚠️ 超时响应示例

```json
{
  "success": false,
  "error": "等待验证码超时",
  "error_code": "VERIFICATION_TIMEOUT",
  "data": {
    "verification_codes": [],
    "email_found": false,
    "wait_time_seconds": 300,
    "emails_checked": 0,
    "polls_performed": 60
  }
}
```

### 获取邮箱摘要

获取邮箱的邮件摘要信息，用于自动化流程监控。

**端点**: `GET /api/automation/email-summary`

**认证方式**: JWT认证 / 匿名访问 (同上)

**查询参数**:
- `address`: 邮箱地址（必需）

**响应示例**:
```json
{
  "success": true,
  "data": {
    "address": "<EMAIL>",
    "total_emails": 2,
    "latest_email": {
      "sender": "<EMAIL>",
      "subject": "欢迎注册",
      "received_at": "2025-07-01T12:10:00.000000"
    },
    "verification_codes_found": ["123456"],
    "status": "active"
  }
}
```

### 获取安全状态

获取自动化API的安全配置状态。

**端点**: `GET /api/automation/security-status`

**响应示例**:
```json
{
  "success": true,
  "data": {
    "security_enabled": true,
    "automation_api_enabled": true,
    "jwt_auth_enabled": true,
    "anonymous_access_allowed": true,
    "rate_limit": {
      "requests": 200,
      "window_seconds": 300
    },
    "features": {
      "jwt_token_authentication": true,
      "intelligent_rate_limiting": true,
      "session_management": true,
      "audit_logging": true,
      "secure_prefix_generation": true
    }
  }
}
```

## 📊 监控API

### 系统指标

获取系统性能指标。

**端点**: `GET /metrics`

**响应格式**: Prometheus格式

```
# HELP tempmail_emails_total Total number of emails
# TYPE tempmail_emails_total counter
tempmail_emails_total{status="active"} 42

# HELP tempmail_requests_total Total number of requests
# TYPE tempmail_requests_total counter
tempmail_requests_total{method="GET",endpoint="/api/emails"} 1234
```

### 详细状态

获取系统详细状态信息。

**端点**: `GET /status`

**响应示例**:
```json
{
  "system": {
    "uptime_seconds": 86400,
    "memory_usage": "45.2MB",
    "cpu_usage": "12.5%",
    "disk_usage": "68%"
  },
  "database": {
    "status": "connected",
    "active_connections": 5,
    "total_emails": 156,
    "total_addresses": 42
  },
  "security": {
    "mode": "personal_hybrid",
    "api_requests_today": 234,
    "blocked_requests": 12
  }
}
```

## ❌ 错误处理

### 错误响应格式

所有错误响应遵循统一格式：

```json
{
  "success": false,
  "error": "错误描述信息",
  "error_code": "ERROR_CODE",
  "details": {
    "field": "额外错误详情"
  }
}
```

### 常见错误码

#### 认证相关错误
| 错误码 | HTTP状态码 | 说明 | 解决方案 |
|--------|------------|------|----------|
| `TOKEN_MISSING` | 401 | 缺少认证令牌 | 添加Authorization头部或使用匿名访问 |
| `TOKEN_EXPIRED` | 401 | 认证令牌已过期 | 使用刷新令牌获取新的访问令牌 |
| `INVALID_TOKEN` | 401 | 无效的认证令牌 | 重新获取有效的JWT令牌 |
| `AUTHENTICATION_REQUIRED` | 401 | 此端点需要认证 | 提供有效的JWT令牌 |

#### 请求相关错误
| 错误码 | HTTP状态码 | 说明 | 解决方案 |
|--------|------------|------|----------|
| `RATE_LIMIT_EXCEEDED` | 429 | 请求频率超限 | 等待一段时间后重试，或使用JWT认证获得更高限制 |
| `INVALID_PREFIX` | 400 | 前缀格式无效 | 检查前缀是否符合命名规范 |
| `EMAIL_ALREADY_EXISTS` | 409 | 邮箱地址已被占用 | 使用建议的替代地址或更换前缀 |
| `MISSING_EMAIL_ADDRESS` | 400 | 缺少邮箱地址参数 | 在请求中提供email_address参数 |

#### 系统相关错误  
| 错误码 | HTTP状态码 | 说明 | 解决方案 |
|--------|------------|------|----------|
| `EMAIL_NOT_FOUND` | 404 | 邮箱不存在 | 检查邮箱地址是否正确或已过期 |
| `DATABASE_ERROR` | 500 | 数据库错误 | 稍后重试，如持续出现请联系技术支持 |
| `API_DISABLED` | 503 | API已禁用 | 检查系统状态或联系管理员 |

### 错误示例

**JWT令牌过期**:
```json
{
  "success": false,
  "error": "认证令牌已过期",
  "error_code": "TOKEN_EXPIRED",
  "hint": "请获取有效的认证令牌或刷新现有令牌"
}
```

**邮箱地址已被占用**:
```json
{
  "success": false,
  "error": "邮箱地址已被占用",
  "error_code": "EMAIL_ALREADY_EXISTS",
  "data": {
    "requested_address": "<EMAIL>",
    "suggestions": [
      "<EMAIL>",
      "<EMAIL>",
      "<EMAIL>"
    ]
  }
}
```

**频率限制**:
```json
{
  "success": false,
  "error": "请求频率超过限制",
  "error_code": "RATE_LIMIT_EXCEEDED",
  "details": {
    "limit": 200,
    "window_seconds": 300,
    "retry_after": 45
  }
}
```

**缺少必需参数**:
```json
{
  "success": false,
  "error": "缺少邮箱地址参数",
  "error_code": "MISSING_EMAIL_ADDRESS",
  "hint": "请在请求中提供email_address参数"
}
```

## 💡 使用示例

### 🐍 Python完整示例

#### 快速开始 (匿名模式)

```python
import requests
import time

# 1分钟快速上手
API_BASE = "https://testkuroneko.xyz/api"

def quick_start():
    # 创建临时邮箱
    response = requests.post(
        f"{API_BASE}/automation/generate-email",
        json={"custom_prefix": "quickstart"},
        headers={"Content-Type": "application/json"}
    )
    
    if response.status_code == 200:
        email_data = response.json()
        email_address = email_data["data"]["email_address"]
        print(f"📧 邮箱创建成功: {email_address}")
        
        # 等待验证码
        verification = requests.post(
            f"{API_BASE}/automation/wait-for-verification",
            json={
                "email_address": email_address,
                "timeout_seconds": 60
            },
            headers={"Content-Type": "application/json"}
        )
        
        if verification.status_code == 200:
            ver_data = verification.json()
            if ver_data["success"]:
                codes = ver_data["data"]["verification_codes"]
                print(f"🎯 验证码: {codes[0] if codes else 'None'}")
            else:
                print(f"❌ 验证码获取失败: {ver_data.get('error')}")
    else:
        print(f"❌ 邮箱创建失败: {response.status_code}")

# 运行快速示例
quick_start()
```

#### 完整功能示例 (JWT认证)

```python
import requests
import time
from datetime import datetime, timedelta

class TempmailClient:
    def __init__(self, base_url, user_id="python_client"):
        self.base_url = base_url
        self.user_id = user_id
        self.access_token = None
        self.refresh_token = None
        self.token_expires_at = None
    
    def authenticate(self):
        """获取JWT认证令牌"""
        response = requests.post(
            f"{self.base_url}/auth/token",
            json={
                "user_id": self.user_id,
                "permissions": ["automation:basic"]
            }
        )
        
        if response.status_code == 200:
            data = response.json()["data"]
            self.access_token = data["access_token"]
            self.refresh_token = data["refresh_token"]
            self.token_expires_at = datetime.now() + timedelta(seconds=data["expires_in"])
            return True
        return False
    
    def get_headers(self):
        """获取请求头"""
        headers = {"Content-Type": "application/json"}
        if self.access_token:
            headers["Authorization"] = f"Bearer {self.access_token}"
        return headers
    
    def create_email(self, prefix="python_test"):
        """创建临时邮箱"""
        response = requests.post(
            f"{self.base_url}/automation/generate-email",
            json={"custom_prefix": prefix},
            headers=self.get_headers()
        )
        return response.json()
    
    def wait_for_verification(self, email_address, timeout=120):
        """等待验证码"""
        response = requests.post(
            f"{self.base_url}/automation/wait-for-verification",
            json={
                "email_address": email_address,
                "timeout_seconds": timeout,
                "poll_interval": 3  # 3秒轮询间隔
            },
            headers=self.get_headers()
        )
        return response.json()
    
    def get_email_summary(self, email_address):
        """获取邮箱摘要"""
        response = requests.get(
            f"{self.base_url}/automation/email-summary",
            params={"address": email_address},
            headers=self.get_headers()
        )
        return response.json()

# 使用示例
client = TempmailClient("https://testkuroneko.xyz/api")

# JWT认证模式
if client.authenticate():
    print("✅ JWT认证成功")
    
    # 创建邮箱
    email_result = client.create_email("jwt_example")
    if email_result["success"]:
        email_address = email_result["data"]["email_address"]
        print(f"📧 邮箱: {email_address}")
        
        # 获取邮箱状态
        summary = client.get_email_summary(email_address)
        if summary["success"]:
            print(f"📊 状态: {summary['data']['status']}")
        
        # 等待验证码
        print("⏳ 等待验证码...")
        verification = client.wait_for_verification(email_address)
        if verification["success"]:
            codes = verification["data"]["verification_codes"]
            method = verification["data"]["email_details"]["extraction_method"]
            print(f"🎯 验证码: {codes[0] if codes else 'None'}")
            print(f"📊 提取方法: {method}")
        else:
            print(f"❌ 验证码获取失败: {verification.get('error')}")
else:
    print("❌ JWT认证失败")
```

### 通用Python示例

```python
import requests
import time

# 配置
API_BASE = "https://testkuroneko.xyz/api"  # 生产环境URL
# API_BASE = "http://localhost:5000/api"   # 本地开发URL

class TempmailClient:
    def __init__(self, base_url, user_id="api_user"):
        self.base_url = base_url
        self.user_id = user_id
        self.access_token = None
        self.refresh_token = None
    
    def get_jwt_token(self, permissions=None):
        """获取JWT认证令牌"""
        if permissions is None:
            permissions = ["automation:basic"]
        
        response = requests.post(
            f"{self.base_url}/auth/token",
            json={
                "user_id": self.user_id,
                "permissions": permissions
            }
        )
        
        if response.status_code == 200:
            data = response.json()['data']
            self.access_token = data['access_token']
            self.refresh_token = data['refresh_token']
            return True
        return False
    
    def get_headers(self):
        """获取认证头部"""
        if self.access_token:
            return {
                "Authorization": f"Bearer {self.access_token}",
                "Content-Type": "application/json"
            }
        else:
            # 匿名访问模式
            return {"Content-Type": "application/json"}

    def create_temp_email(self, prefix="test"):
        """生成临时邮箱"""
        response = requests.post(
            f"{self.base_url}/automation/generate-email",
            json={"custom_prefix": prefix},
            headers=self.get_headers()
        )
        return response.json()
    
    def wait_for_code(self, email_address, timeout=300):
        """等待验证码"""
        response = requests.post(
            f"{self.base_url}/automation/wait-for-verification",
            json={
                "email_address": email_address,
                "timeout_seconds": timeout
            },
            headers=self.get_headers()
        )
        return response.json()
    
    def get_email_summary(self, email_address):
        """获取邮箱摘要"""
        response = requests.get(
            f"{self.base_url}/automation/email-summary?address={email_address}",
            headers=self.get_headers()
        )
        return response.json()

# 使用示例

# 方式1: JWT认证模式
print("=== JWT认证模式 ===")
client = TempmailClient(API_BASE, "your_user_id")

# 获取JWT令牌
if client.get_jwt_token(["automation:basic"]):
    print("✅ JWT令牌获取成功")
    
    # 创建邮箱
    email_data = client.create_temp_email("jwt_test")
    if email_data["success"]:
        email_address = email_data["data"]["email_address"]
        print(f"✅ 邮箱创建成功: {email_address}")
        
        # 获取邮箱摘要
        summary = client.get_email_summary(email_address)
        if summary["success"]:
            print(f"📊 邮箱状态: {summary['data']['status']}")
            print(f"📧 邮件数量: {summary['data']['total_emails']}")
        
        # 等待验证码
        verification = client.wait_for_code(email_address, timeout=60)
        if verification["success"]:
            codes = verification["data"]["verification_codes"]
            print(f"🔑 验证码: {codes}")
else:
    print("❌ JWT令牌获取失败")

# 方式2: 匿名访问模式
print("\n=== 匿名访问模式 ===")
anonymous_client = TempmailClient(API_BASE)  # 不获取JWT令牌

email_data = anonymous_client.create_temp_email("anonymous_test")
if email_data["success"]:
    email_address = email_data["data"]["email_address"]
    print(f"✅ 匿名邮箱创建成功: {email_address}")
else:
    print(f"❌ 匿名访问失败: {email_data.get('error', '未知错误')}")
```

### JavaScript示例

```javascript
class TempmailAPI {
    constructor(baseUrl, userId = 'js_user') {
        this.baseUrl = baseUrl;
        this.userId = userId;
        this.accessToken = null;
        this.refreshToken = null;
    }

    async getJWTToken(permissions = ['automation:basic']) {
        /**
         * 获取JWT认证令牌
         */
        try {
            const response = await fetch(`${this.baseUrl}/auth/token`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    user_id: this.userId,
                    permissions: permissions
                })
            });

            if (response.ok) {
                const data = await response.json();
                this.accessToken = data.data.access_token;
                this.refreshToken = data.data.refresh_token;
                return true;
            }
            return false;
        } catch (error) {
            console.error('JWT token获取失败:', error);
            return false;
        }
    }

    getHeaders() {
        /**
         * 获取认证头部
         */
        const headers = {
            'Content-Type': 'application/json'
        };

        if (this.accessToken) {
            headers['Authorization'] = `Bearer ${this.accessToken}`;
        }

        return headers;
    }

    async request(endpoint, options = {}) {
        const url = `${this.baseUrl}${endpoint}`;
        const config = {
            headers: {
                ...this.getHeaders(),
                ...options.headers
            },
            ...options
        };

        const response = await fetch(url, config);
        return response.json();
    }

    async createEmail(prefix = 'test') {
        return this.request('/automation/generate-email', {
            method: 'POST',
            body: JSON.stringify({ custom_prefix: prefix })
        });
    }

    async waitForVerification(emailAddress, timeout = 300) {
        return this.request('/automation/wait-for-verification', {
            method: 'POST',
            body: JSON.stringify({
                email_address: emailAddress,
                timeout_seconds: timeout
            })
        });
    }

    async getEmailSummary(emailAddress) {
        return this.request(`/automation/email-summary?address=${emailAddress}`);
    }
}

// 使用示例

// 方式1: JWT认证模式
async function jwtAuthExample() {
    console.log('=== JWT认证模式 ===');
    const api = new TempmailAPI('https://testkuroneko.xyz/api', 'js_user_001');
    
    // 获取JWT令牌
    if (await api.getJWTToken(['automation:basic'])) {
        console.log('✅ JWT令牌获取成功');
        
        try {
            // 创建临时邮箱
            const emailResult = await api.createEmail('jwt_test');
            if (!emailResult.success) {
                throw new Error(emailResult.error);
            }

            const emailAddress = emailResult.data.email_address;
            console.log(`✅ 邮箱创建成功: ${emailAddress}`);

            // 获取邮箱摘要
            const summary = await api.getEmailSummary(emailAddress);
            if (summary.success) {
                console.log(`📊 邮箱状态: ${summary.data.status}`);
                console.log(`📧 邮件数量: ${summary.data.total_emails}`);
            }

            // 等待验证码
            const verification = await api.waitForVerification(emailAddress, 60);
            if (verification.success) {
                console.log(`🔑 验证码: ${verification.data.verification_codes}`);
            }
        } catch (error) {
            console.error('❌ JWT认证模式失败:', error);
        }
    } else {
        console.log('❌ JWT令牌获取失败');
    }
}

// 方式2: 匿名访问模式
async function anonymousExample() {
    console.log('\n=== 匿名访问模式 ===');
    const api = new TempmailAPI('https://testkuroneko.xyz/api');
    
    try {
        // 直接创建邮箱（无需JWT令牌）
        const emailResult = await api.createEmail('anonymous_test');
        if (emailResult.success) {
            console.log(`✅ 匿名邮箱创建成功: ${emailResult.data.email_address}`);
        } else {
            console.log(`❌ 匿名访问失败: ${emailResult.error}`);
        }
    } catch (error) {
        console.error('❌ 匿名访问错误:', error);
    }
}

// 执行示例
jwtAuthExample();
anonymousExample();
```

### cURL示例

```bash
#!/bin/bash

API_BASE="https://testkuroneko.xyz/api"
# API_BASE="http://localhost:5000/api"  # 本地开发环境

# JWT认证模式函数
get_jwt_token() {
    local user_id="$1"
    curl -s -X POST "$API_BASE/auth/token" \
        -H "Content-Type: application/json" \
        -d "{\"user_id\": \"$user_id\", \"permissions\": [\"automation:basic\"]}"
}

# 使用JWT令牌创建邮箱
create_email_jwt() {
    local prefix="$1"
    local jwt_token="$2"
    curl -s -X POST "$API_BASE/automation/generate-email" \
        -H "Authorization: Bearer $jwt_token" \
        -H "Content-Type: application/json" \
        -d "{\"custom_prefix\": \"$prefix\"}"
}

# 匿名创建邮箱
create_email_anonymous() {
    local prefix="$1"
    curl -s -X POST "$API_BASE/automation/generate-email" \
        -H "Content-Type: application/json" \
        -d "{\"custom_prefix\": \"$prefix\"}"
}

# 等待验证码
wait_for_code() {
    local email_address="$1"
    local jwt_token="$2"
    local timeout="${3:-60}"
    
    if [ -n "$jwt_token" ]; then
        # JWT认证模式
        curl -s -X POST "$API_BASE/automation/wait-for-verification" \
            -H "Authorization: Bearer $jwt_token" \
            -H "Content-Type: application/json" \
            -d "{\"email_address\": \"$email_address\", \"timeout_seconds\": $timeout}"
    else
        # 匿名模式
        curl -s -X POST "$API_BASE/automation/wait-for-verification" \
            -H "Content-Type: application/json" \
            -d "{\"email_address\": \"$email_address\", \"timeout_seconds\": $timeout}"
    fi
}

# 获取邮箱摘要
get_email_summary() {
    local email_address="$1"
    local jwt_token="$2"
    
    if [ -n "$jwt_token" ]; then
        curl -s "$API_BASE/automation/email-summary?address=$email_address" \
            -H "Authorization: Bearer $jwt_token"
    else
        curl -s "$API_BASE/automation/email-summary?address=$email_address"
    fi
}

echo "=== JWT认证模式示例 ==="

# 获取JWT令牌
JWT_RESULT=$(get_jwt_token "bash_user")
JWT_TOKEN=$(echo "$JWT_RESULT" | jq -r '.data.access_token')

if [ "$JWT_TOKEN" != "null" ] && [ -n "$JWT_TOKEN" ]; then
    echo "✅ JWT令牌获取成功"
    
    # 创建邮箱
    EMAIL_RESULT=$(create_email_jwt "jwt_test" "$JWT_TOKEN")
    EMAIL_ADDRESS=$(echo "$EMAIL_RESULT" | jq -r '.data.email_address')
    
    if [ "$EMAIL_ADDRESS" != "null" ]; then
        echo "✅ 邮箱创建成功: $EMAIL_ADDRESS"
        
        # 获取邮箱摘要
        SUMMARY_RESULT=$(get_email_summary "$EMAIL_ADDRESS" "$JWT_TOKEN")
        STATUS=$(echo "$SUMMARY_RESULT" | jq -r '.data.status')
        echo "📊 邮箱状态: $STATUS"
        
        # 等待验证码
        echo "⏳ 等待验证码..."
        VERIFICATION_RESULT=$(wait_for_code "$EMAIL_ADDRESS" "$JWT_TOKEN" 10)
        CODES=$(echo "$VERIFICATION_RESULT" | jq -r '.data.verification_codes[]')
        echo "🔑 验证码: $CODES"
    else
        echo "❌ 邮箱创建失败"
    fi
else
    echo "❌ JWT令牌获取失败"
fi

echo -e "\n=== 匿名访问模式示例 ==="

# 匿名创建邮箱
EMAIL_RESULT=$(create_email_anonymous "anon_test")
EMAIL_ADDRESS=$(echo "$EMAIL_RESULT" | jq -r '.data.email_address')

if [ "$EMAIL_ADDRESS" != "null" ]; then
    echo "✅ 匿名邮箱创建成功: $EMAIL_ADDRESS"
else
    ERROR=$(echo "$EMAIL_RESULT" | jq -r '.error')
    echo "❌ 匿名访问失败: $ERROR"
fi
```

## 🔧 SDK和集成

### 官方SDK

- **Python SDK**: `pip install tempmail-sdk`
- **JavaScript SDK**: `npm install tempmail-js`
- **PHP SDK**: `composer require tempmail/sdk`

### Webhook支持

配置Webhook接收邮件通知：

```json
{
  "webhook_url": "https://your-app.com/webhook",
  "events": ["email.received", "email.verification_code"],
  "secret": "webhook_secret"
}
```

### ⚡ API限制说明

#### 📊 当前限制 (v2.0 已大幅放松)

| 限制类型 | 当前值 | 之前值 | 提升幅度 |
|----------|--------|--------|----------|
| **请求频率** | 100次/分钟 | 10次/5分钟 | 🚀 **25x** |
| **IP访问** | 全网开放 | 本地限制 | 🌐 **无限制** |
| **请求大小** | 1MB | 512KB | 📈 **2x** |
| **并发连接** | 20/IP | 5/IP | 🔄 **4x** |
| **响应超时** | 30秒 | 15秒 | ⏱️ **2x** |

#### 🎯 按认证模式的限制

```mermaid
graph TD
    A[API请求] --> B{认证类型}
    B -->|JWT认证| C[100次/分钟]
    B -->|匿名访问| D[50次/分钟]
    C --> E[智能限频]
    D --> F[基础限频]
    E --> G[动态调整]
    F --> H[固定限制]
```

#### 🔧 配置参数

```bash
# 生产环境配置
AUTOMATION_RATE_LIMIT_REQUESTS=100    # 每分钟请求数
AUTOMATION_RATE_LIMIT_WINDOW=60       # 时间窗口(秒)
AUTOMATION_ALLOWED_IPS=0.0.0.0/0      # 允许所有IP
MAX_CONCURRENT_CONNECTIONS=20         # 最大并发连接
REQUEST_TIMEOUT_SECONDS=30            # 请求超时时间
```

#### 🚨 超限处理

当请求超过限制时，系统返回：

```json
{
  "success": false,
  "error": "请求频率超过限制",
  "error_code": "RATE_LIMIT_EXCEEDED",
  "details": {
    "limit": 100,
    "window_seconds": 60,
    "retry_after": 30,
    "current_usage": 101
  }
}
```

**建议处理方式**:
1. 检查 `retry_after` 字段，等待指定时间后重试
2. 实现指数退避算法
3. 升级到JWT认证模式获得更高限制

## 🔒 生产环境安全建议

### JWT配置优化

```bash
# 生产环境JWT配置 (.env)
JWT_SECRET_KEY=your_complex_random_secret_key_here
JWT_ACCESS_TOKEN_EXPIRES=1800    # 30分钟 (更短更安全)
JWT_REFRESH_TOKEN_EXPIRES=604800 # 7天

# 禁用匿名访问 (生产环境推荐)
ALLOW_ANONYMOUS_ACCESS=false

# 严格的频率限制
AUTOMATION_RATE_LIMIT_REQUESTS=50   # 降低到50/5分钟
AUTOMATION_RATE_LIMIT_WINDOW=300
```

### 安全最佳实践

#### 1. 认证安全
- ✅ **必须使用JWT认证**: 生产环境禁用匿名访问
- ✅ **令牌过期时间**: 访问令牌不超过30分钟
- ✅ **安全密钥**: 使用复杂随机字符串作为JWT密钥
- ✅ **令牌刷新**: 实现自动令牌刷新机制

#### 2. 频率限制
- ✅ **严格限频**: 生产环境使用更严格的频率限制
- ✅ **用户级限制**: 基于user_id实现用户级别的限制
- ✅ **IP级限制**: 配置IP级别的请求限制
- ✅ **异常检测**: 监控异常请求模式

#### 3. 监控和日志
- ✅ **请求日志**: 记录所有API请求和响应
- ✅ **错误监控**: 监控错误率和异常模式
- ✅ **性能监控**: 监控响应时间和系统负载
- ✅ **安全审计**: 定期审计认证和授权日志

#### 4. 网络安全
- ✅ **HTTPS强制**: 生产环境必须使用HTTPS
- ✅ **CORS配置**: 配置严格的跨域资源共享策略
- ✅ **防火墙**: 配置网络防火墙限制访问
- ✅ **DDoS防护**: 部署DDoS防护机制

### 部署检查清单

- [ ] JWT密钥已更新为安全的随机字符串
- [ ] 已禁用匿名访问模式
- [ ] 已配置严格的频率限制
- [ ] 已启用HTTPS和安全头部
- [ ] 已配置监控和日志系统
- [ ] 已测试错误处理和故障恢复
- [ ] 已设置自动备份和恢复机制
- [ ] 已实施访问控制和权限管理

---

## 📚 相关资源

### 🔗 官方文档
- [📖 完整文档](./README.md) - 项目完整介绍
- [🚀 快速开始](./QUICKSTART.md) - 5分钟上手指南
- [🔐 JWT认证测试](./LOCAL_JWT_TEST.md) - 本地JWT测试
- [🛡️ 安全配置](./SECURITY_GUIDE.md) - 安全最佳实践
- [🚀 部署指南](./DEPLOYMENT_GUIDE.md) - 生产部署手册

### 🛠️ 开发工具
- [🐍 Python SDK](https://github.com/tempmail/python-sdk) - 官方Python SDK
- [🌐 JavaScript SDK](https://github.com/tempmail/js-sdk) - 官方JS SDK
- [🔧 Postman Collection](./postman/tempmail-api.json) - API测试集合

### 📊 监控与状态
- [🏥 健康状态](https://testkuroneko.xyz/health) - 系统健康检查
- [📈 实时监控](https://testkuroneko.xyz/status) - 系统状态监控
- [📊 性能指标](https://testkuroneko.xyz/metrics) - Prometheus指标

### 🆘 获取帮助
- [❓ 常见问题](./FAQ.md) - 常见问题解答
- [🐛 问题反馈](https://github.com/tempmail/issues) - 提交Bug报告
- [💬 社区讨论](https://github.com/tempmail/discussions) - 社区讨论

---

**📝 文档信息**:
- 版本: v2.0
- 更新时间: 2025-07-06
- 维护者: Tempmail Team
- 许可证: MIT License

**🔄 更新日志**:
- v2.0 (2025-07-06): 验证码提取优化, API限制放松
- v1.9 (2025-07-05): JWT认证系统升级
- v1.8 (2025-07-04): 数据库迁移到MySQL

> 💡 **提示**: 本文档实时更新，建议添加书签以获取最新API信息。