# CVV检测功能集成架构设计

## 📁 推荐的项目结构

```
Account Registrar/
├── hulu_automation/                    # 现有Hulu自动化系统
│   ├── business/
│   ├── data/
│   ├── infrastructure/
│   └── presentation/
│
├── cvv_detection/                      # 新增CVV检测模块
│   ├── __init__.py
│   ├── core/                          # 核心验证逻辑
│   │   ├── __init__.py
│   │   ├── validator.py               # CVV验证算法
│   │   ├── encryption.py              # 加密处理
│   │   └── card_types.py              # 卡类型识别
│   │
│   ├── api/                           # API层
│   │   ├── __init__.py
│   │   ├── routes.py                  # API路由
│   │   ├── middleware.py              # 中间件
│   │   └── schemas.py                 # 数据模式
│   │
│   ├── security/                      # 安全模块
│   │   ├── __init__.py
│   │   ├── rate_limiter.py            # 频率限制
│   │   ├── input_sanitizer.py         # 输入清理
│   │   └── audit_logger.py            # 审计日志
│   │
│   ├── utils/                         # 工具函数
│   │   ├── __init__.py
│   │   ├── formatters.py              # 格式化工具
│   │   └── validators.py              # 通用验证
│   │
│   └── tests/                         # 测试文件
│       ├── __init__.py
│       ├── test_core.py
│       ├── test_api.py
│       └── test_security.py
│
├── shared/                            # 共享组件
│   ├── __init__.py
│   ├── config/                        # 配置管理
│   │   ├── __init__.py
│   │   ├── settings.py                # 应用设置
│   │   └── environment.py             # 环境配置
│   │
│   ├── database/                      # 数据库层
│   │   ├── __init__.py
│   │   ├── models.py                  # 数据模型
│   │   └── migrations/                # 数据库迁移
│   │
│   └── logging/                       # 日志系统
│       ├── __init__.py
│       └── logger.py                  # 统一日志配置
│
├── web_interface/                     # Web界面（从cvv-detector-mvp迁移）
│   ├── frontend/                      # Next.js前端
│   │   ├── src/
│   │   │   ├── app/
│   │   │   ├── components/
│   │   │   └── lib/
│   │   └── package.json
│   │
│   └── backend/                       # Express.js后端
│       ├── src/
│       │   ├── routes/
│       │   └── middleware/
│       └── package.json
│
├── integration/                       # 系统集成
│   ├── __init__.py
│   ├── hulu_cvv_bridge.py            # Hulu与CVV系统桥接
│   └── unified_api.py                 # 统一API入口
│
└── deployment/                        # 部署配置
    ├── docker/
    │   ├── Dockerfile.hulu
    │   ├── Dockerfile.cvv
    │   └── docker-compose.yml
    │
    └── kubernetes/                    # K8s配置（可选）
        ├── hulu-deployment.yaml
        └── cvv-deployment.yaml
```

## 🔧 核心模块设计

### CVV验证核心模块 (cvv_detection/core/)

```python
# validator.py - 主验证逻辑
class CVVValidator:
    def __init__(self, encryption_service, audit_logger):
        self.encryption = encryption_service
        self.audit = audit_logger
    
    async def validate_cvv(self, card_number: str, cvv: str, exp_date: str) -> ValidationResult:
        """CVV验证主方法"""
        pass
    
    def _detect_card_type(self, card_number: str) -> CardType:
        """检测卡片类型"""
        pass
    
    def _validate_cvv_format(self, cvv: str, card_type: CardType) -> bool:
        """验证CVV格式"""
        pass

# encryption.py - 加密处理
class EncryptionService:
    def encrypt_sensitive_data(self, data: str) -> str:
        """加密敏感数据"""
        pass
    
    def decrypt_sensitive_data(self, encrypted_data: str) -> str:
        """解密敏感数据"""
        pass
```

### API层设计 (cvv_detection/api/)

```python
# routes.py - API路由
from fastapi import APIRouter, Depends
from .schemas import CVVValidationRequest, CVVValidationResponse

router = APIRouter(prefix="/api/v1/cvv")

@router.post("/validate", response_model=CVVValidationResponse)
async def validate_cvv(
    request: CVVValidationRequest,
    validator: CVVValidator = Depends(get_cvv_validator)
):
    """CVV验证API端点"""
    pass

@router.get("/health")
async def health_check():
    """健康检查端点"""
    return {"status": "healthy", "service": "cvv-detection"}
```

## 🔐 安全考虑

### 数据保护策略
1. **敏感数据加密**：所有CVV和卡号数据必须加密存储
2. **内存清理**：处理完成后立即清理内存中的敏感数据
3. **审计日志**：记录所有验证请求（不包含敏感数据）
4. **访问控制**：实现基于角色的访问控制

### 安全模块实现
```python
# security/rate_limiter.py
class RateLimiter:
    def __init__(self, max_requests: int = 100, window_seconds: int = 3600):
        self.max_requests = max_requests
        self.window_seconds = window_seconds
    
    async def check_rate_limit(self, client_id: str) -> bool:
        """检查请求频率限制"""
        pass

# security/input_sanitizer.py
class InputSanitizer:
    @staticmethod
    def sanitize_card_number(card_number: str) -> str:
        """清理和验证卡号输入"""
        pass
    
    @staticmethod
    def sanitize_cvv(cvv: str) -> str:
        """清理和验证CVV输入"""
        pass
```

## 🔄 系统集成策略

### Hulu系统集成桥接
```python
# integration/hulu_cvv_bridge.py
class HuluCVVBridge:
    def __init__(self, hulu_service, cvv_service):
        self.hulu = hulu_service
        self.cvv = cvv_service
    
    async def validate_payment_method(self, account_data: dict) -> bool:
        """集成支付方式验证到Hulu流程"""
        pass
    
    async def enhanced_account_creation(self, user_data: dict) -> dict:
        """增强的账户创建流程"""
        pass
```

## 📊 监控和日志

### 统一日志系统
```python
# shared/logging/logger.py
import logging
from typing import Dict, Any

class UnifiedLogger:
    def __init__(self, service_name: str):
        self.service_name = service_name
        self.logger = self._setup_logger()
    
    def log_cvv_validation(self, request_id: str, result: str, metadata: Dict[str, Any]):
        """记录CVV验证日志（不包含敏感数据）"""
        pass
    
    def log_security_event(self, event_type: str, details: Dict[str, Any]):
        """记录安全事件"""
        pass
```

## 🧪 测试策略

### 测试层次结构
1. **单元测试**：核心验证逻辑测试
2. **集成测试**：API端点测试
3. **安全测试**：渗透测试和漏洞扫描
4. **性能测试**：负载和压力测试
5. **端到端测试**：完整用户流程测试

### 测试数据管理
```python
# cvv_detection/tests/fixtures.py
class TestDataManager:
    @staticmethod
    def get_valid_test_cards() -> List[Dict]:
        """获取有效的测试卡数据"""
        return [
            {"number": "****************", "cvv": "123", "type": "visa"},
            {"number": "****************", "cvv": "456", "type": "mastercard"}
        ]
    
    @staticmethod
    def get_invalid_test_cards() -> List[Dict]:
        """获取无效的测试卡数据"""
        pass
```
