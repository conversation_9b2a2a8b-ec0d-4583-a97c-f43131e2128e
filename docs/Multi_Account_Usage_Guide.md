# 多账号多Cookies管理使用指南

## 🎯 功能概述

本系统现已支持极简版的多账号和多Cookies管理功能，核心特性：

- ✅ **从CSV文件加载多个账户**
- ✅ **每个账户使用独立的session/cookies文件**  
- ✅ **支持按索引或email指定账户**
- ✅ **完全向后兼容现有代码**

## 📁 目录结构

```
data/
├── accounts.csv              # 多账户信息文件
└── sessions/                 # 各账户独立session存储
    ├── 3c63b55bf45e_testkuroneko_xyz_session.json
    ├── 6250e8982ad1_testkuroneko_xyz_session.json
    └── 9aa89c347d2c_testkuroneko_xyz_session.json

utils/
└── account_loader.py         # 账户加载工具
```

## 🚀 使用方法

### 1. 基础账户管理

```python
from utils.account_loader import AccountLoader

# 加载所有账户
loader = AccountLoader()
accounts = loader.load_all_accounts()
print(f"共有 {len(accounts)} 个账户")

# 按索引获取账户
account = loader.get_account_by_index(0)
print(f"第一个账户: {account.email}")

# 按email获取账户  
account = loader.get_account_by_email("<EMAIL>")
print(f"指定账户: {account.email}")
```

### 2. 多账户登录

#### 方式1：使用账户索引
```python
from hulu_automation.presentation.hulu_bot_facade import HuluBotFacade

# 使用第一个账户（索引0）
with HuluBotFacade.with_account_index(0, headless=False) as bot:
    result = bot.login_account()  # 自动使用该账户的email/password
    print(f"登录结果: {result}")
```

#### 方式2：使用指定email
```python
# 使用指定email的账户
with HuluBotFacade.with_account_email('<EMAIL>', headless=False) as bot:
    result = bot.login_account()  # 自动使用该账户的email/password  
    print(f"登录结果: {result}")
```

#### 方式3：构造函数参数
```python
# 直接在构造函数中指定
bot = HuluBotFacade(account_index=0, headless=False)
bot = HuluBotFacade(account_email='<EMAIL>', headless=False)
```

### 3. Session文件自动管理

每个账户会自动使用独立的session文件：

```python
with HuluBotFacade.with_account_index(0) as bot:
    print(f"Session文件: {bot.storage_state_path}")
    # 输出: data/sessions/3c63b55bf45e_testkuroneko_xyz_session.json

with HuluBotFacade.with_account_index(1) as bot:  
    print(f"Session文件: {bot.storage_state_path}")
    # 输出: data/sessions/6250e8982ad1_testkuroneko_xyz_session.json
```

### 4. 向后兼容使用

现有代码无需任何修改：

```python
# 传统方式仍然完全有效
with HuluBotFacade(headless=False) as bot:
    result = bot.login_account(
        email="<EMAIL>", 
        password="testpass"
    )
    # 使用默认session文件: hulu_auth_state.json
```

## 📊 验证功能

运行验证脚本确认功能正常：

```bash
python3 verify_multi_account.py
```

预期输出：
```
🎯 多账户功能完整验证
============================================================
▶️ 验证: 账户加载功能
✅ 账户加载功能 验证通过

▶️ 验证: Session隔离效果  
✅ Session隔离效果 验证通过

📊 验证完成: 4/4 通过
🎉 所有验证通过！多账户功能实现成功
```

## 🔧 配置说明

### accounts.csv格式

```csv
email, password
<EMAIL>,Fiona127,.
<EMAIL>,Fiona127,.
<EMAIL>,Fiona127,.
```

- 支持email/password两列（必需）
- 支持列名的大小写变化和空格
- 自动跳过无效行

### Session文件命名规则

```
原始email: <EMAIL>
安全文件名: test_example_com_session.json
完整路径: data/sessions/test_example_com_session.json
```

特殊字符转换：
- `@` → `_`
- `.` → `_`  
- `/` → `_`
- `\` → `_`

## ⚡ 核心优势

1. **极简设计** - 只实现必需功能，代码量最小
2. **完全兼容** - 现有代码无需任何修改
3. **自动隔离** - 每个账户自动使用独立cookies/session
4. **即用即得** - 通过索引或email直接使用，无需额外配置

## 🔄 实际使用场景

### 批量账户测试
```python
from utils.account_loader import AccountLoader

loader = AccountLoader()
accounts = loader.load_all_accounts()

for i, account in enumerate(accounts):
    print(f"\n测试账户 {i}: {account.email}")
    
    with HuluBotFacade.with_account_index(i, headless=True) as bot:
        result = bot.login_account()
        if result.get('login_status') == 'success':
            print("✅ 登录成功")
        else:
            print("❌ 登录失败")
```

### 账户轮换
```python
# 简单的轮换逻辑
account_index = 0
max_accounts = AccountLoader().get_account_count()

def get_next_bot():
    global account_index
    bot = HuluBotFacade.with_account_index(account_index % max_accounts, headless=True)
    account_index += 1
    return bot

# 使用
with get_next_bot() as bot:
    result = bot.login_account()
```

这个极简版实现专注于核心需求，避免了复杂的管理功能，确保简单易用且高度可靠。