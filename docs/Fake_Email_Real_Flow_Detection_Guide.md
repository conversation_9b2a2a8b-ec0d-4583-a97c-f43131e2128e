# 假邮箱真流程检测系统 - 使用指南

> **系统功能**: 假邮箱 + 真流程检测 + 视觉模板比较  
> **版本**: v2.0.0  
> **状态**: 🚀 生产就绪

---

## 📋 系统概览

**假邮箱真流程检测系统**是一个企业级的UI健康检查解决方案，通过执行真实的登录流程（使用假凭据）来验证关键选择器的有效性，同时集成视觉模板比较功能进行UI漂移监控。

### 🎯 核心特性

- **🔄 假邮箱真流程**: 使用假凭据执行真实5阶段登录流程
- **👁️ 视觉模板比较**: SSIM相似度计算，支持多级依赖降级
- **🚫 智能网络拦截**: 8条规则动态拦截敏感API请求
- **📸 元素级截图**: 精准截图减少视觉噪声
- **🎛️ 多级依赖降级**: scikit-image → opencv → PIL+numpy
- **📊 三级漂移分类**: normal(>0.8) / warning(0.6-0.8) / critical(<0.6)

---

## 🚀 快速开始

### 1. 环境准备

#### 必需依赖
```bash
# 基础运行环境
python >= 3.8
playwright >= 1.40.0
PyYAML

# 安装Playwright浏览器
python -m playwright install chromium
```

#### 可选依赖（图像处理增强）
```bash
# 优先级1: 最佳性能和准确性
pip install scikit-image

# 优先级2: 良好性能
pip install opencv-python

# 优先级3: 基础功能（已内置PIL+numpy支持）
# 无需额外安装
```

 📅 日常使用建议

  推荐的日常检查模式：

  # 标准日常检查 (推荐)
  python ci/selector_healthcheck.py

  为什么选择基础模式：
  1. ✅ 性能最优：51.6秒完成全流程，适合定时任务
  2. ✅ 功能完整：包含假邮箱流程+视觉比较+网络拦截
  3. ✅ 输出详细：JSON结果文件 + 视觉漂移报告
  4. ✅ 无干扰：无头模式不占用屏幕资源

  问题排查流程：

  # 1. 日常检查发现问题
  python ci/selector_healthcheck.py

  # 2. 使用可视化模式调试
  python ci/selector_healthcheck.py --headless false --verbose

  # 3. 如果是合理的UI变更，更新基线
  python ci/selector_healthcheck.py --create-baselines

  定时任务配置 (推荐)：

  # 每天早上9点运行健康检查
  0 9 * * * cd /path/to/project && python ci/selector_healthcheck.py >> logs/daily_health.log 2>&1

### 2. 基础使用

#### 标准健康检查
```bash
# 基础检查（无头模式）
python ci/selector_healthcheck.py

# 可视化模式（调试用）
python ci/selector_healthcheck.py --headless false

# 启用视觉比较
python ci/selector_healthcheck.py --enable-visual-comparison

# 创建基线模板
python ci/selector_healthcheck.py --create-baselines
```

#### 高级参数
```bash
# 完整参数示例
python ci/selector_healthcheck.py \
  --headless false \
  --enable-visual-comparison \
  --visual-threshold 0.8 \
  --element-screenshots \
  --create-baselines \
  --url https://www.hulu.com/welcome \
  --output-format json \
  --log-level DEBUG
```

---

## 📊 核心功能详解

### 假邮箱真流程检测

系统执行真实的5阶段登录流程，使用安全的假凭据：

#### 阶段配置
```python
FAKE_CREDENTIALS = {
    "email": "<EMAIL>",
    "password": "FakePassword123!"
}
```

#### 流程阶段
1. **Welcome页面** → 检测并点击"Log In"按钮
2. **邮箱输入** → 填入假邮箱地址
3. **Continue按钮** → 点击继续到密码页面
4. **密码输入** → 填入假密码
5. **Login按钮检测** → 检测最终登录按钮（安全退出）

### 视觉模板比较

#### 相似度计算策略
```python
# 多级依赖降级
def _calculate_similarity(current_img, baseline_img):
    if IMAGE_LIBS.skimage:
        return _ssim_skimage()     # 最佳：SSIM算法
    elif IMAGE_LIBS.cv2:
        return _ssim_opencv()      # 良好：OpenCV实现
    elif IMAGE_LIBS.pil:
        return _basic_similarity_pil()  # 基础：PIL实现
    else:
        return None, "no_dependency"
```

#### 漂移状态分类
- **🟢 Normal** (>0.8): UI稳定，无需关注
- **🟡 Warning** (0.6-0.8): 轻微变化，建议检查
- **🔴 Critical** (<0.6): 重大变化，需要立即处理

### 智能网络拦截

#### 拦截规则（8条）
```python
NETWORK_INTERCEPTION_RULES = [
    "**/accounts/**",      # 账户相关API
    "**/auth/**",          # 认证相关API
    "**/login/**",         # 登录相关API
    "**/signin/**",        # 登录相关API
    "**/recaptcha/**",     # reCAPTCHA相关
    "**/captcha/**",       # 验证码相关
    "**/2fa/**",           # 双因子认证
    "**/verify/**"         # 验证相关
]
```

#### 动态端点发现
系统自动检测和拦截新的敏感API端点，防止真实登录操作。

---

## 🎛️ 配置参数详解

### 命令行参数

| 参数 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| `--headless` | bool | true | 是否使用无头模式 |
| `--enable-visual-comparison` | bool | false | 启用视觉模板比较 |
| `--visual-threshold` | float | 0.8 | 相似度阈值 |
| `--create-baselines` | bool | false | 创建基线模板 |
| `--element-screenshots` | bool | false | 启用元素级截图 |
| `--url` | string | hulu.com/welcome | 起始URL |
| `--output-format` | string | text | 输出格式(text/json) |
| `--log-level` | string | INFO | 日志级别 |

### 配置文件

#### selectors.yml 配置
```yaml
# 假邮箱真流程专用配置
healthcheck:
  flow_sequence:
    - phase: "welcome_page"
      selectors: "login_flow.welcome_login_buttons"
    - phase: "email_input"
      selectors: "login_flow.email_fields"
    # ... 更多阶段配置
```

#### 图像处理配置
```python
# 系统自动检测可用的图像处理库
IMAGE_LIBS = detect_image_libraries()

# 支持的相似度阈值范围
SIMILARITY_THRESHOLDS = {
    "normal": 0.8,     # 正常状态
    "warning": 0.6,    # 警告状态
    "critical": 0.0    # 临界状态
}
```

---

## 📸 视觉模板管理

### 基线模板创建

#### 首次创建基线
```bash
# 创建所有阶段的基线模板
python ci/selector_healthcheck.py --create-baselines

# 检查生成的基线文件
ls -la screenshots/baselines/
# baseline_welcome_page.png
# baseline_email_input.png
# baseline_continue_button.png
# baseline_password_input.png
# baseline_login_button_detection.png
```

#### 基线模板更新
```bash
# 更新特定阶段的基线（当UI有合理变更时）
python ci/selector_healthcheck.py \
  --create-baselines \
  --phases welcome_page,email_input
```

### 截图文件管理

#### 目录结构
```
screenshots/
├── baselines/           # 基线模板
│   ├── baseline_welcome_page.png
│   ├── baseline_email_input.png
│   └── ...
├── current/            # 当前截图
│   ├── current_welcome_page.png
│   └── ...
└── comparisons/        # 比较结果
    ├── comparison_welcome_page.png
    └── ...
```

#### 文件命名规范
- **基线**: `baseline_{phase_name}.png`
- **当前**: `current_{phase_name}.png`
- **比较**: `comparison_{phase_name}.png`

---

## 🔧 故障排查指南

### 常见问题及解决方案

#### 1. 图像处理库缺失
```bash
问题: "no image processing libraries available"
解决: 
  pip install scikit-image  # 推荐
  # 或
  pip install opencv-python
```

#### 2. 基线模板不存在
```bash
问题: "baseline not found for phase: welcome_page"
解决:
  python ci/selector_healthcheck.py --create-baselines
```

#### 3. 网络拦截失效
```bash
问题: 检测到真实API调用
检查: 
  - 查看日志中的网络请求
  - 更新网络拦截规则
  - 检查page.route()设置
```

#### 4. 相似度计算异常
```bash
问题: 相似度始终为None
检查:
  - PIL库是否可用: python -c "from PIL import Image; print('OK')"
  - 图像文件是否损坏
  - 文件路径是否正确
```

#### 5. 选择器失效
```bash
问题: 某个阶段选择器找不到元素
解决:
  - 使用可视化模式调试: --headless false
  - 检查selectors.yml配置
  - 查看页面DOM结构变化
```

### 调试技巧

#### 启用详细日志
```bash
python ci/selector_healthcheck.py --log-level DEBUG
```

#### 使用可视化模式
```bash
# 慢速执行，便于观察
python ci/selector_healthcheck.py \
  --headless false \
  --log-level DEBUG
```

#### 检查网络请求
```python
# 在脚本中添加网络监控
async def log_network_requests(route, request):
    print(f"Network: {request.method} {request.url}")
    await route.continue_()

page.route("**/*", log_network_requests)
```

---

## 📊 输出格式说明

### 文本格式输出
```
🧪 假邮箱真流程检测开始
========================================

✅ 阶段1 - Welcome页面: 成功
   选择器: button:has-text("Log In")
   执行时间: 234ms
   视觉相似度: 0.95 (normal)

⚠️ 阶段2 - 邮箱输入: 警告
   选择器: #email
   执行时间: 456ms
   视觉相似度: 0.75 (warning)

📊 总体结果:
   成功率: 80% (4/5)
   总耗时: 2.3秒
   视觉漂移: 1个警告
```

### JSON格式输出
```json
{
  "summary": {
    "overall_success": true,
    "total_phases": 5,
    "success_count": 4,
    "success_rate": 0.8,
    "total_duration_seconds": 2.3,
    "visual_drift_warnings": 1
  },
  "phases": [
    {
      "phase": "welcome_page",
      "success": true,
      "duration_ms": 234,
      "selector": "button:has-text(\"Log In\")",
      "visual_similarity": 0.95,
      "drift_status": "normal",
      "screenshot_path": "/path/to/current_welcome_page.png"
    }
  ],
  "system_info": {
    "image_processing_capability": "PIL+numpy",
    "network_interception_rules": 8,
    "fake_credentials_used": true
  }
}
```

---

## 🔄 最佳实践

### 日常使用建议

#### 1. 定期健康检查
```bash
# 建议频率：每日一次
# 添加到crontab
0 9 * * * cd /path/to/project && python ci/selector_healthcheck.py >> logs/daily_health.log 2>&1
```

#### 2. 基线模板维护
- **更新频率**: UI有合理变更时更新
- **版本管理**: 使用git管理基线模板变更
- **备份策略**: 保留历史基线模板版本

#### 3. 监控告警设置
```bash
# 成功率低于80%时发送告警
if [ $SUCCESS_RATE -lt 80 ]; then
    echo "健康检查失败，成功率: $SUCCESS_RATE%" | mail -s "Hulu健康检查告警" <EMAIL>
fi
```

### 性能优化建议

#### 1. 图像处理优化
```bash
# 推荐安装scikit-image获得最佳性能
pip install scikit-image

# 或使用opencv获得良好性能
pip install opencv-python
```

#### 2. 网络优化
- 使用CDN或镜像源加速依赖安装
- 配置合理的网络超时时间
- 考虑使用本地测试环境

#### 3. 存储优化
- 定期清理旧的截图文件
- 使用压缩格式存储基线模板
- 实现截图文件的自动归档

---

## 🛡️ 安全注意事项

### 假凭据安全
- ✅ 使用明显的假邮箱域名 (`fake-domain.test`)
- ✅ 使用无效的密码组合
- ✅ 在最后阶段安全退出，不执行真实登录
- ✅ 网络拦截确保不会发送真实请求

### 数据隐私
- ✅ 不存储任何真实用户数据
- ✅ 截图文件仅包含UI元素，不含敏感信息
- ✅ 日志文件不记录真实凭据

### 合规使用
- ✅ 仅用于内部系统健康检查
- ✅ 遵守网站服务条款
- ✅ 合理控制检查频率

---

## 📞 技术支持

### 问题反馈
- **项目地址**: `/path/to/project`
- **日志文件**: `logs/selector_healthcheck.log`
- **配置文件**: `selectors.yml`

### 常用排查命令
```bash
# 系统环境检查
python -c "
import sys
print(f'Python: {sys.version}')
try:
    import playwright
    print(f'Playwright: {playwright.__version__}')
except:
    print('Playwright: 未安装')

# 图像处理能力检查
try:
    from skimage.metrics import structural_similarity
    print('图像处理: scikit-image (最佳)')
except:
    try:
        import cv2
        print('图像处理: opencv-python (良好)')
    except:
        try:
            from PIL import Image
            import numpy
            print('图像处理: PIL+numpy (基础)')
        except:
            print('图像处理: 不可用')
"

# 配置文件检查
python -c "
import yaml
try:
    with open('selectors.yml') as f:
        config = yaml.safe_load(f)
    print('selectors.yml: 格式正确')
    print(f'健康检查阶段: {len(config[\"healthcheck\"][\"flow_sequence\"])}')
except Exception as e:
    print(f'selectors.yml: 错误 - {e}')
"
```

---

## 📚 相关文档

- **[反检测系统使用指南](Anti_Detection_Usage_Guide.md)** - 完整反检测功能
- **[性能优化指南](Performance_Optimization_Guide.md)** - Layer 0性能优化
- **[Quick-Wins实施总结](QUICK_WINS_IMPLEMENTATION_SUMMARY.md)** - 整体架构说明
- **[API参考文档](API_REFERENCE.md)** - 详细API说明

---

**文档版本**: v2.0.0  
**更新时间**: 2025-07-29  
**维护状态**: 🚀 **生产就绪**