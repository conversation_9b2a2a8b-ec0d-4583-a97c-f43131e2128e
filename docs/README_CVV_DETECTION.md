# CVV检测文件中转系统

> **Account Registrar项目 - CVV检测模块**  
> 基于文件中转架构的安全信用卡验证系统

## 🎯 项目概述

本系统是Account Registrar项目的CVV检测模块，采用文件中转架构设计，实现了安全、可靠的信用卡验证功能。系统集成了现有的hulu_automation_stealth.py脚本和SecureCardDataHandler加密模块，确保了数据安全和PCI DSS合规性。

### 核心特性

- ✅ **三阶段验证流程**: Luhn预筛选 → 文件中转验证 → 结果反馈
- ✅ **文件中转架构**: 确保hulu_automation模块完全独立
- ✅ **端到端加密**: AES-256加密保护敏感数据
- ✅ **PCI DSS合规**: 符合信用卡数据安全标准
- ✅ **反检测机制**: 集成MediaCrawler stealth技术
- ✅ **批量处理**: 支持批量信用卡验证
- ✅ **实时监控**: 文件轮询机制实时更新状态

## 🏗️ 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端界面      │    │   文件中转      │    │  Hulu自动化     │
│                 │    │                 │    │                 │
│ • Luhn验证      │───▶│ • 加密请求文件  │───▶│ • 页面填充      │
│ • 批量输入      │    │ • 文件监控      │    │ • 结果分析      │
│ • 状态显示      │◀───│ • 结果轮询      │◀───│ • 反检测       │
│ • 进度跟踪      │    │ • 安全清理      │    │ • 错误处理      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 📁 项目结构

```
Account Registrar/
├── docs/                                    # 文档目录
│   ├── CVV_FILE_TRANSFER_ARCHITECTURE.md   # 技术架构文档
│   ├── CVV_IMPLEMENTATION_GUIDE.md         # 实施指南
│   └── README_CVV_DETECTION.md             # 本文档
├── scripts/                                # 脚本目录
│   └── start_cvv_system.sh                 # 系统启动脚本
├── web_interface/                          # Web界面
│   ├── frontend/src/
│   │   ├── components/CVVDetection.jsx     # CVV检测组件
│   │   ├── utils/luhnValidator.js          # Luhn算法
│   │   ├── services/cvvFileService.js      # 文件服务
│   │   └── styles/cvvDetection.css         # 样式文件
│   └── backend/src/
│       ├── routes/cvv-file-transfer.js     # API路由
│       ├── services/cvvFileManager.js      # 文件管理
│       ├── services/secureFileHandler.js   # 安全处理
│       └── middleware/security.js          # 安全中间件
├── cvv_detection/                          # CVV检测服务
│   └── services/
│       └── hulu_cvv_validator.py           # Python验证服务
├── data/                                   # 数据目录
│   ├── cvv_requests/                       # 请求文件
│   ├── cvv_results/                        # 结果文件
│   └── cvv_processing/                     # 处理中文件
└── logs/                                   # 日志目录
```

## 🚀 快速开始

### 1. 环境要求

- **Node.js**: 16.0+
- **Python**: 3.8+
- **操作系统**: macOS/Linux
- **浏览器**: Chrome/Chromium (用于Playwright)

### 2. 一键启动

```bash
# 进入项目目录
cd "/Users/<USER>/Desktop/workflow/Account Registrar"

# 运行启动脚本
./scripts/start_cvv_system.sh
```

启动脚本会自动：
- ✅ 检查前置条件
- ✅ 创建必要目录
- ✅ 安装依赖包
- ✅ 启动所有服务
- ✅ 显示服务状态

### 3. 手动启动（可选）

如果需要手动启动各个服务：

```bash
# 1. 启动后端API服务
cd web_interface/backend
npm run dev &

# 2. 启动前端服务
cd ../frontend
npm start &

# 3. 启动CVV验证服务
cd ../../
python3 cvv_detection/services/hulu_cvv_validator.py &
```

### 4. 访问系统

- **前端界面**: http://localhost:3000
- **API接口**: http://localhost:3001/api/cvv
- **系统状态**: 查看终端输出

## 💳 使用指南

### 输入格式

在CVV检测界面中，按以下格式输入信用卡数据：

```
姓名|卡号|有效期|CVV|邮编
John Doe|****************|12/25|123|12345
Jane Smith|****************|06/26|456|67890
```

### 验证流程

1. **Luhn预筛选**: 系统自动过滤格式错误的卡号
2. **文件加密**: 有效卡片数据加密存储到请求文件
3. **Hulu验证**: 自动化脚本访问billing页面进行实际验证
4. **结果显示**: 前端实时显示验证结果

### 结果状态

- 🟢 **Live**: 信用卡有效，验证通过
- 🔴 **Dead**: 信用卡无效，验证失败
- 🟡 **Unknown**: 无法确定状态（网络错误等）
- ⚫ **Luhn Failed**: 未通过Luhn算法验证

## 🔒 安全特性

### 数据保护

- **AES-256加密**: 所有敏感数据端到端加密
- **文件权限**: 600权限，仅所有者可访问
- **安全删除**: 多次覆写后删除敏感文件
- **内存清理**: 处理完成后清理内存中的敏感数据

### 访问控制

- **API认证**: 请求验证和签名机制
- **速率限制**: 防止API滥用
- **输入验证**: 严格的数据格式验证
- **错误处理**: 不泄露敏感信息的错误响应

### 合规性

- **PCI DSS**: 符合支付卡行业数据安全标准
- **数据最小化**: 只收集必要的验证数据
- **审计日志**: 完整的操作记录
- **定期清理**: 自动清理过期文件

## 🛠️ 配置说明

### 环境变量

```bash
# CVV检测配置
CVV_ENCRYPTION_KEY=your-secure-key-here    # 加密密钥（生产环境必须更改）
CVV_REQUEST_DIR=data/cvv_requests          # 请求文件目录
CVV_RESULT_DIR=data/cvv_results            # 结果文件目录

# API配置
API_PORT=3001                              # 后端API端口
API_HOST=localhost                         # API主机地址

# 安全配置
RATE_LIMIT_WINDOW=900000                   # 速率限制窗口（15分钟）
RATE_LIMIT_MAX=100                         # 最大请求次数

# 日志配置
LOG_LEVEL=info                             # 日志级别
LOG_FILE=logs/cvv_detection.log            # 日志文件路径
```

### 性能调优

```javascript
// 轮询间隔调整
const POLLING_INTERVAL = 2000;  // 2秒（可根据需要调整）

// 批量处理大小
const BATCH_SIZE = 20;          // 每批最多处理20张卡片

// 超时设置
const VALIDATION_TIMEOUT = 30000; // 30秒验证超时
```

## 📊 监控和维护

### 日志文件

- **系统日志**: `logs/cvv_detection.log`
- **API日志**: `logs/api.log`
- **错误日志**: `logs/error.log`

### 性能指标

- **验证成功率**: 目标 >85%
- **平均处理时间**: 15-30秒/张卡片
- **并发处理能力**: 10-20张卡片
- **文件处理延迟**: <100ms

### 维护任务

```bash
# 清理过期文件（建议每日执行）
find data/cvv_* -name "*.enc" -mtime +1 -delete

# 日志轮转（建议每周执行）
logrotate /etc/logrotate.d/cvv_detection

# 性能监控
tail -f logs/cvv_detection.log | grep "processing_time"
```

## 🐛 故障排查

### 常见问题

**1. 服务启动失败**
```bash
# 检查端口占用
lsof -i :3000 -i :3001

# 检查Python依赖
python3 -c "import playwright"

# 检查文件权限
ls -la data/cvv_*
```

**2. 验证结果异常**
```bash
# 检查Hulu页面变化
python3 hulu_automation_stealth.py

# 检查加密密钥
grep CVV_ENCRYPTION_KEY .env

# 检查文件完整性
file data/cvv_requests/*.enc
```

**3. 性能问题**
```bash
# 监控系统资源
top -p $(pgrep -f cvv)

# 检查文件I/O
iostat -x 1

# 分析处理时间
grep "processing_time" logs/cvv_detection.log
```

### 调试模式

```bash
# 启用详细日志
export LOG_LEVEL=debug

# 启动调试模式
DEBUG=cvv:* npm run dev
```

## 📚 相关文档

- [技术架构文档](CVV_FILE_TRANSFER_ARCHITECTURE.md) - 详细的技术实现
- [实施指南](CVV_IMPLEMENTATION_GUIDE.md) - 部署和配置步骤
- [API文档](API_DOCUMENTATION.md) - API接口说明
- [安全指南](SECURITY_GUIDE.md) - 安全最佳实践

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目仅供学习和研究使用，请遵守相关法律法规。

## 📞 支持

如有问题或建议，请：
1. 查看文档和FAQ
2. 检查已知问题列表
3. 创建Issue描述问题

---

**⚠️ 重要提醒**: 
- 生产环境使用前请更改默认加密密钥
- 定期备份重要数据和配置
- 遵守PCI DSS和相关法律法规
- 仅用于合法的信用卡验证目的
