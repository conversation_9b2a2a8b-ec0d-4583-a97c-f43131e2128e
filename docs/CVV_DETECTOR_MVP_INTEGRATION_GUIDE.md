# CVV检测MVP集成开发指南

> **作者**: 全栈工程师团队  
> **创建时间**: 2025-01-18  
> **版本**: v1.0  
> **适用项目**: Account Registrar - CVV检测功能集成

## 📋 目录

- [1. 分支管理策略](#1-分支管理策略)
- [2. 代码组织建议](#2-代码组织建议)
- [3. 开发和测试策略](#3-开发和测试策略)
- [4. 合并和部署计划](#4-合并和部署计划)
- [5. 具体执行建议](#5-具体执行建议)

## 🎯 项目概述

本文档为CVV检测MVP功能集成到现有Account Registrar项目提供全面的技术指导。该MVP基于Next.js和Express.js构建，需要与现有的Hulu自动化系统安全集成。

## 1. 分支管理策略

### 1.1 当前分支结构分析

```
main (生产稳定版)
├── develop (开发主分支) ← 当前位置
├── feature/hulu-login-optimization (Hulu功能特性)
├── feature/layer2-smart-concurrency (架构层功能)
├── feature/layer3-progressive-dom-scanning
└── feature/layer4-human-mouse-simulation
```

### 1.2 推荐的CVV分支集成策略

#### Git Flow工作流程图
```mermaid
gitGraph
    commit id: "main"
    branch develop
    checkout develop
    commit id: "current state"
    
    branch feature/cvv-detector-mvp
    checkout feature/cvv-detector-mvp
    commit id: "MVP基础架构"
    commit id: "前端界面开发"
    commit id: "后端API开发"
    commit id: "集成测试"
    
    branch feature/cvv-core-validation
    checkout feature/cvv-core-validation
    commit id: "CVV验证算法"
    commit id: "加密处理"
    
    checkout feature/cvv-detector-mvp
    merge feature/cvv-core-validation
    commit id: "功能集成"
    
    checkout develop
    merge feature/cvv-detector-mvp
    commit id: "MVP合并到develop"
    
    checkout main
    merge develop
    commit id: "发布到生产"
```

#### 分支命名规范

- `feature/cvv-detector-mvp` - MVP主分支
- `feature/cvv-core-validation` - 核心验证逻辑
- `feature/cvv-security-enhancement` - 安全增强
- `feature/cvv-ui-components` - UI组件开发
- `feature/cvv-api-integration` - API集成
- `hotfix/cvv-*` - 紧急修复

#### 具体分支创建命令

```bash
# 创建CVV功能主分支
git checkout develop
git checkout -b feature/cvv-detector-mvp

# 创建子功能分支
git checkout -b feature/cvv-core-validation
git checkout -b feature/cvv-ui-components
git checkout -b feature/cvv-api-integration
```

### 1.3 分支保护规则

- **main分支**: 需要PR审查 + CI通过 + 管理员批准
- **develop分支**: 需要PR审查 + CI通过
- **feature分支**: 需要至少1人审查

## 2. 代码组织建议

### 2.1 推荐的项目结构重组

```
Account Registrar/
├── hulu_automation/                    # 现有Hulu自动化系统
│   ├── business/
│   ├── data/
│   ├── infrastructure/
│   └── presentation/
│
├── cvv_detection/                      # 新增CVV检测模块
│   ├── __init__.py
│   ├── core/                          # 核心验证逻辑
│   │   ├── __init__.py
│   │   ├── validator.py               # CVV验证算法
│   │   ├── encryption.py              # 加密处理
│   │   └── card_types.py              # 卡类型识别
│   │
│   ├── api/                           # API层
│   │   ├── __init__.py
│   │   ├── routes.py                  # API路由
│   │   ├── middleware.py              # 中间件
│   │   └── schemas.py                 # 数据模式
│   │
│   ├── security/                      # 安全模块
│   │   ├── __init__.py
│   │   ├── rate_limiter.py            # 频率限制
│   │   ├── input_sanitizer.py         # 输入清理
│   │   └── audit_logger.py            # 审计日志
│   │
│   ├── utils/                         # 工具函数
│   │   ├── __init__.py
│   │   ├── formatters.py              # 格式化工具
│   │   └── validators.py              # 通用验证
│   │
│   └── tests/                         # 测试文件
│       ├── __init__.py
│       ├── test_core.py
│       ├── test_api.py
│       └── test_security.py
│
├── shared/                            # 共享组件
│   ├── __init__.py
│   ├── config/                        # 配置管理
│   │   ├── __init__.py
│   │   ├── settings.py                # 应用设置
│   │   └── environment.py             # 环境配置
│   │
│   ├── database/                      # 数据库层
│   │   ├── __init__.py
│   │   ├── models.py                  # 数据模型
│   │   └── migrations/                # 数据库迁移
│   │
│   └── logging/                       # 日志系统
│       ├── __init__.py
│       └── logger.py                  # 统一日志配置
│
├── web_interface/                     # Web界面（从cvv-detector-mvp迁移）
│   ├── frontend/                      # Next.js前端
│   │   ├── src/
│   │   │   ├── app/
│   │   │   ├── components/
│   │   │   └── lib/
│   │   └── package.json
│   │
│   └── backend/                       # Express.js后端
│       ├── src/
│       │   ├── routes/
│       │   └── middleware/
│       └── package.json
│
├── integration/                       # 系统集成
│   ├── __init__.py
│   ├── hulu_cvv_bridge.py            # Hulu与CVV系统桥接
│   └── unified_api.py                 # 统一API入口
│
└── deployment/                        # 部署配置
    ├── docker/
    │   ├── Dockerfile.hulu
    │   ├── Dockerfile.cvv
    │   └── docker-compose.yml
    │
    └── kubernetes/                    # K8s配置（可选）
        ├── hulu-deployment.yaml
        └── cvv-deployment.yaml
```

### 2.2 核心模块设计

#### CVV验证核心模块 (cvv_detection/core/)

```python
# validator.py - 主验证逻辑
class CVVValidator:
    def __init__(self, encryption_service, audit_logger):
        self.encryption = encryption_service
        self.audit = audit_logger
    
    async def validate_cvv(self, card_number: str, cvv: str, exp_date: str) -> ValidationResult:
        """CVV验证主方法"""
        # 1. 输入清理和验证
        sanitized_card = self._sanitize_card_number(card_number)
        sanitized_cvv = self._sanitize_cvv(cvv)
        
        # 2. 卡片类型检测
        card_type = self._detect_card_type(sanitized_card)
        
        # 3. CVV格式验证
        if not self._validate_cvv_format(sanitized_cvv, card_type):
            return ValidationResult(valid=False, reason="Invalid CVV format")
        
        # 4. 高级验证逻辑
        validation_result = await self._perform_advanced_validation(
            sanitized_card, sanitized_cvv, exp_date, card_type
        )
        
        # 5. 审计日志记录
        await self.audit.log_validation_attempt(
            card_type=card_type.name,
            result=validation_result.valid,
            timestamp=datetime.utcnow()
        )
        
        return validation_result
    
    def _detect_card_type(self, card_number: str) -> CardType:
        """检测卡片类型"""
        # Visa: 4开头
        if card_number.startswith('4'):
            return CardType.VISA
        # Mastercard: 5开头或2221-2720
        elif card_number.startswith('5') or (2221 <= int(card_number[:4]) <= 2720):
            return CardType.MASTERCARD
        # American Express: 34或37开头
        elif card_number.startswith(('34', '37')):
            return CardType.AMEX
        # Discover: 6开头
        elif card_number.startswith('6'):
            return CardType.DISCOVER
        else:
            return CardType.UNKNOWN
    
    def _validate_cvv_format(self, cvv: str, card_type: CardType) -> bool:
        """验证CVV格式"""
        if card_type == CardType.AMEX:
            return len(cvv) == 4 and cvv.isdigit()
        else:
            return len(cvv) == 3 and cvv.isdigit()

# encryption.py - 加密处理
from cryptography.fernet import Fernet
import os

class EncryptionService:
    def __init__(self):
        self.key = self._load_or_generate_key()
        self.cipher = Fernet(self.key)
    
    def _load_or_generate_key(self) -> bytes:
        """加载或生成加密密钥"""
        key_file = "secret.key"
        if os.path.exists(key_file):
            with open(key_file, 'rb') as f:
                return f.read()
        else:
            key = Fernet.generate_key()
            with open(key_file, 'wb') as f:
                f.write(key)
            return key
    
    def encrypt_sensitive_data(self, data: str) -> str:
        """加密敏感数据"""
        return self.cipher.encrypt(data.encode()).decode()
    
    def decrypt_sensitive_data(self, encrypted_data: str) -> str:
        """解密敏感数据"""
        return self.cipher.decrypt(encrypted_data.encode()).decode()
    
    def secure_memory_cleanup(self, *variables):
        """安全清理内存中的敏感数据"""
        for var in variables:
            if isinstance(var, str):
                # 用随机数据覆盖字符串内存
                var = ''.join(random.choices(string.ascii_letters, k=len(var)))
            del var

# card_types.py - 卡类型定义
from enum import Enum

class CardType(Enum):
    VISA = "visa"
    MASTERCARD = "mastercard"
    AMEX = "american_express"
    DISCOVER = "discover"
    UNKNOWN = "unknown"

@dataclass
class ValidationResult:
    valid: bool
    reason: str = ""
    card_type: CardType = CardType.UNKNOWN
    risk_score: float = 0.0
    metadata: Dict[str, Any] = field(default_factory=dict)
```

#### API层设计 (cvv_detection/api/)

```python
# routes.py - API路由
from fastapi import APIRouter, Depends, HTTPException, Request
from fastapi.security import HTTPBearer
from .schemas import CVVValidationRequest, CVVValidationResponse
from .middleware import RateLimitMiddleware, SecurityMiddleware

router = APIRouter(prefix="/api/v1/cvv")
security = HTTPBearer()

@router.post("/validate", response_model=CVVValidationResponse)
async def validate_cvv(
    request: CVVValidationRequest,
    http_request: Request,
    validator: CVVValidator = Depends(get_cvv_validator),
    rate_limiter: RateLimiter = Depends(get_rate_limiter)
):
    """CVV验证API端点"""
    # 1. 频率限制检查
    client_ip = http_request.client.host
    if not await rate_limiter.check_rate_limit(client_ip):
        raise HTTPException(status_code=429, detail="Rate limit exceeded")

    # 2. 输入验证
    if not request.card_number or not request.cvv:
        raise HTTPException(status_code=400, detail="Missing required fields")

    # 3. 执行CVV验证
    try:
        result = await validator.validate_cvv(
            card_number=request.card_number,
            cvv=request.cvv,
            exp_date=request.exp_date
        )

        return CVVValidationResponse(
            valid=result.valid,
            card_type=result.card_type.value,
            risk_score=result.risk_score,
            message=result.reason
        )

    except Exception as e:
        # 记录错误但不暴露敏感信息
        logger.error(f"CVV validation error: {str(e)}")
        raise HTTPException(status_code=500, detail="Validation service error")

@router.get("/health")
async def health_check():
    """健康检查端点"""
    return {
        "status": "healthy",
        "service": "cvv-detection",
        "version": "1.0.0",
        "timestamp": datetime.utcnow().isoformat()
    }

@router.get("/metrics")
async def get_metrics(
    token: str = Depends(security),
    metrics_service: MetricsService = Depends(get_metrics_service)
):
    """获取服务指标（需要认证）"""
    return await metrics_service.get_current_metrics()

# schemas.py - 数据模式
from pydantic import BaseModel, Field, validator
import re

class CVVValidationRequest(BaseModel):
    card_number: str = Field(..., min_length=13, max_length=19)
    cvv: str = Field(..., min_length=3, max_length=4)
    exp_date: str = Field(..., regex=r"^(0[1-9]|1[0-2])\/\d{2}$")

    @validator('card_number')
    def validate_card_number(cls, v):
        # 移除空格和连字符
        cleaned = re.sub(r'[\s-]', '', v)
        if not cleaned.isdigit():
            raise ValueError('Card number must contain only digits')
        return cleaned

    @validator('cvv')
    def validate_cvv(cls, v):
        if not v.isdigit():
            raise ValueError('CVV must contain only digits')
        return v

class CVVValidationResponse(BaseModel):
    valid: bool
    card_type: str
    risk_score: float = Field(..., ge=0.0, le=1.0)
    message: str = ""
    request_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
```

### 2.3 安全模块设计

#### 安全组件实现 (cvv_detection/security/)

```python
# rate_limiter.py - 频率限制
import redis
import time
from typing import Optional

class RateLimiter:
    def __init__(self, redis_client: redis.Redis, max_requests: int = 100, window_seconds: int = 3600):
        self.redis = redis_client
        self.max_requests = max_requests
        self.window_seconds = window_seconds

    async def check_rate_limit(self, client_id: str) -> bool:
        """检查请求频率限制"""
        key = f"rate_limit:{client_id}"
        current_time = int(time.time())
        window_start = current_time - self.window_seconds

        # 使用Redis滑动窗口算法
        pipe = self.redis.pipeline()
        pipe.zremrangebyscore(key, 0, window_start)
        pipe.zcard(key)
        pipe.zadd(key, {str(current_time): current_time})
        pipe.expire(key, self.window_seconds)

        results = pipe.execute()
        request_count = results[1]

        return request_count < self.max_requests

# input_sanitizer.py - 输入清理
import re
import html
from typing import Optional

class InputSanitizer:
    @staticmethod
    def sanitize_card_number(card_number: str) -> str:
        """清理和验证卡号输入"""
        if not card_number:
            return ""

        # 移除HTML标签和特殊字符
        cleaned = html.escape(card_number)
        cleaned = re.sub(r'[^\d\s-]', '', cleaned)
        cleaned = re.sub(r'[\s-]', '', cleaned)

        return cleaned

    @staticmethod
    def sanitize_cvv(cvv: str) -> str:
        """清理和验证CVV输入"""
        if not cvv:
            return ""

        # 只保留数字
        cleaned = re.sub(r'[^\d]', '', cvv)
        return cleaned

    @staticmethod
    def sanitize_exp_date(exp_date: str) -> str:
        """清理和验证过期日期输入"""
        if not exp_date:
            return ""

        # 标准化日期格式 MM/YY
        cleaned = re.sub(r'[^\d/]', '', exp_date)
        return cleaned

# audit_logger.py - 审计日志
import json
import asyncio
from datetime import datetime
from typing import Dict, Any, Optional

class AuditLogger:
    def __init__(self, log_file: str = "logs/cvv_audit.log"):
        self.log_file = log_file
        self.ensure_log_directory()

    def ensure_log_directory(self):
        """确保日志目录存在"""
        import os
        os.makedirs(os.path.dirname(self.log_file), exist_ok=True)

    async def log_validation_attempt(
        self,
        client_ip: str,
        card_type: str,
        result: bool,
        risk_score: float,
        timestamp: Optional[datetime] = None
    ):
        """记录验证尝试（不包含敏感数据）"""
        if timestamp is None:
            timestamp = datetime.utcnow()

        log_entry = {
            "event_type": "cvv_validation",
            "timestamp": timestamp.isoformat(),
            "client_ip": self._hash_ip(client_ip),  # 哈希化IP保护隐私
            "card_type": card_type,
            "validation_result": result,
            "risk_score": risk_score,
            "session_id": self._generate_session_id()
        }

        await self._write_log_entry(log_entry)

    async def log_security_event(
        self,
        event_type: str,
        client_ip: str,
        details: Dict[str, Any],
        severity: str = "INFO"
    ):
        """记录安全事件"""
        log_entry = {
            "event_type": f"security_{event_type}",
            "timestamp": datetime.utcnow().isoformat(),
            "client_ip": self._hash_ip(client_ip),
            "severity": severity,
            "details": details
        }

        await self._write_log_entry(log_entry)

    def _hash_ip(self, ip: str) -> str:
        """哈希化IP地址保护隐私"""
        import hashlib
        return hashlib.sha256(ip.encode()).hexdigest()[:16]

    def _generate_session_id(self) -> str:
        """生成会话ID"""
        import uuid
        return str(uuid.uuid4())[:8]

    async def _write_log_entry(self, log_entry: Dict[str, Any]):
        """异步写入日志条目"""
        log_line = json.dumps(log_entry) + "\n"

        # 使用异步文件写入
        import aiofiles
        async with aiofiles.open(self.log_file, 'a') as f:
            await f.write(log_line)
```

## 3. 开发和测试策略

### 3.1 MVP开发阶段规划

#### 开发时间线图表
```mermaid
gantt
    title CVV检测MVP开发时间线
    dateFormat  YYYY-MM-DD
    section 阶段1: 基础架构
    项目结构重组           :done, arch1, 2025-01-18, 2d
    核心模块设计           :done, arch2, after arch1, 2d
    安全框架搭建           :active, arch3, after arch2, 3d

    section 阶段2: 核心功能
    CVV验证算法            :cvv1, after arch3, 4d
    加密服务实现           :cvv2, after cvv1, 3d
    API层开发             :cvv3, after cvv2, 3d

    section 阶段3: 前端界面
    UI组件开发            :ui1, after arch3, 5d
    API集成               :ui2, after cvv3, 3d
    响应式优化            :ui3, after ui2, 2d

    section 阶段4: 集成测试
    单元测试              :test1, after cvv2, 3d
    集成测试              :test2, after ui2, 3d
    安全测试              :test3, after test2, 2d
    性能测试              :test4, after test3, 2d

    section 阶段5: 部署准备
    Docker配置            :deploy1, after test4, 2d
    CI/CD流水线           :deploy2, after deploy1, 2d
    生产环境准备          :deploy3, after deploy2, 1d
```

### 3.2 测试驱动开发(TDD)策略

#### 测试金字塔结构
```
E2E Tests (10%) - 完整用户流程测试
    ├── 用户界面交互测试
    ├── 完整验证流程测试
    └── 跨系统集成测试

Integration Tests (20%) - API和服务集成测试
    ├── API端点测试
    ├── 数据库集成测试
    ├── 外部服务集成测试
    └── Hulu系统集成测试

Unit Tests (70%) - 核心逻辑验证测试
    ├── CVV验证算法测试
    ├── 加密服务测试
    ├── 输入清理测试
    └── 安全组件测试
```

#### 与Hulu系统隔离测试

```python
# tests/conftest.py - 测试配置
import pytest
from unittest.mock import Mock, AsyncMock
from cvv_detection.core.validator import CVVValidator
from cvv_detection.core.encryption import EncryptionService
from cvv_detection.security.audit_logger import AuditLogger

@pytest.fixture
def isolated_cvv_service():
    """隔离的CVV服务实例，不依赖Hulu系统"""
    mock_encryption = Mock(spec=EncryptionService)
    mock_encryption.encrypt_sensitive_data.return_value = "encrypted_data"
    mock_encryption.decrypt_sensitive_data.return_value = "decrypted_data"

    mock_audit = AsyncMock(spec=AuditLogger)

    return CVVValidator(
        encryption_service=mock_encryption,
        audit_logger=mock_audit
    )

@pytest.fixture
def mock_hulu_integration():
    """模拟Hulu集成，避免测试冲突"""
    mock_hulu = Mock()
    mock_hulu.create_account.return_value = {"success": True, "account_id": "test_123"}
    mock_hulu.validate_payment.return_value = {"valid": True}
    return mock_hulu

@pytest.fixture
def test_card_data():
    """测试用卡片数据"""
    return {
        "valid_visa": {"number": "****************", "cvv": "123", "exp": "12/25"},
        "valid_mastercard": {"number": "****************", "cvv": "456", "exp": "06/26"},
        "valid_amex": {"number": "***************", "cvv": "1234", "exp": "09/27"},
        "invalid_cvv": {"number": "****************", "cvv": "12", "exp": "12/25"},
        "invalid_card": {"number": "****************", "cvv": "123", "exp": "12/25"}
    }

# tests/test_core.py - 核心功能测试
import pytest
from cvv_detection.core.validator import CVVValidator
from cvv_detection.core.card_types import CardType

class TestCVVValidator:

    @pytest.mark.asyncio
    async def test_valid_visa_card(self, isolated_cvv_service, test_card_data):
        """测试有效的Visa卡验证"""
        card_data = test_card_data["valid_visa"]

        result = await isolated_cvv_service.validate_cvv(
            card_number=card_data["number"],
            cvv=card_data["cvv"],
            exp_date=card_data["exp"]
        )

        assert result.valid is True
        assert result.card_type == CardType.VISA
        assert result.risk_score <= 0.3  # 低风险

    @pytest.mark.asyncio
    async def test_invalid_cvv_format(self, isolated_cvv_service, test_card_data):
        """测试无效的CVV格式"""
        card_data = test_card_data["invalid_cvv"]

        result = await isolated_cvv_service.validate_cvv(
            card_number=card_data["number"],
            cvv=card_data["cvv"],
            exp_date=card_data["exp"]
        )

        assert result.valid is False
        assert "Invalid CVV format" in result.reason

    def test_card_type_detection(self, isolated_cvv_service):
        """测试卡片类型检测"""
        # Visa
        assert isolated_cvv_service._detect_card_type("****************") == CardType.VISA
        # Mastercard
        assert isolated_cvv_service._detect_card_type("****************") == CardType.MASTERCARD
        # American Express
        assert isolated_cvv_service._detect_card_type("***************") == CardType.AMEX
        # Unknown
        assert isolated_cvv_service._detect_card_type("****************") == CardType.UNKNOWN

# tests/test_api.py - API测试
import pytest
from fastapi.testclient import TestClient
from cvv_detection.api.routes import router

@pytest.fixture
def test_client():
    """测试客户端"""
    from fastapi import FastAPI
    app = FastAPI()
    app.include_router(router)
    return TestClient(app)

class TestCVVAPI:

    def test_health_check(self, test_client):
        """测试健康检查端点"""
        response = test_client.get("/api/v1/cvv/health")
        assert response.status_code == 200
        assert response.json()["status"] == "healthy"

    def test_valid_cvv_validation(self, test_client):
        """测试有效CVV验证API"""
        payload = {
            "card_number": "****************",
            "cvv": "123",
            "exp_date": "12/25"
        }

        response = test_client.post("/api/v1/cvv/validate", json=payload)
        assert response.status_code == 200

        data = response.json()
        assert "valid" in data
        assert "card_type" in data
        assert "risk_score" in data

    def test_invalid_request_format(self, test_client):
        """测试无效请求格式"""
        payload = {
            "card_number": "invalid",
            "cvv": "abc",
            "exp_date": "invalid"
        }

        response = test_client.post("/api/v1/cvv/validate", json=payload)
        assert response.status_code == 422  # Validation error

    def test_rate_limiting(self, test_client):
        """测试频率限制"""
        payload = {
            "card_number": "****************",
            "cvv": "123",
            "exp_date": "12/25"
        }

        # 发送大量请求触发频率限制
        for _ in range(150):  # 超过默认限制
            response = test_client.post("/api/v1/cvv/validate", json=payload)

        # 最后的请求应该被限制
        assert response.status_code == 429

# tests/test_security.py - 安全测试
import pytest
from cvv_detection.security.input_sanitizer import InputSanitizer
from cvv_detection.security.rate_limiter import RateLimiter

class TestInputSanitizer:

    def test_card_number_sanitization(self):
        """测试卡号清理"""
        # 测试移除空格和连字符
        assert InputSanitizer.sanitize_card_number("4111 1111 1111 1111") == "****************"
        assert InputSanitizer.sanitize_card_number("4111-1111-1111-1111") == "****************"

        # 测试移除特殊字符
        assert InputSanitizer.sanitize_card_number("4111<script>alert('xss')</script>1111") == "41111111"

        # 测试HTML转义
        assert InputSanitizer.sanitize_card_number("4111&lt;test&gt;1111") == "41111111"

    def test_cvv_sanitization(self):
        """测试CVV清理"""
        assert InputSanitizer.sanitize_cvv("123") == "123"
        assert InputSanitizer.sanitize_cvv("12a3") == "123"
        assert InputSanitizer.sanitize_cvv("1@2#3") == "123"
        assert InputSanitizer.sanitize_cvv("") == ""

class TestRateLimiter:

    @pytest.mark.asyncio
    async def test_rate_limit_within_bounds(self, mock_redis):
        """测试正常频率限制"""
        rate_limiter = RateLimiter(mock_redis, max_requests=10, window_seconds=60)

        # 前10个请求应该通过
        for i in range(10):
            result = await rate_limiter.check_rate_limit("test_client")
            assert result is True

    @pytest.mark.asyncio
    async def test_rate_limit_exceeded(self, mock_redis):
        """测试超出频率限制"""
        rate_limiter = RateLimiter(mock_redis, max_requests=5, window_seconds=60)

        # 前5个请求通过
        for i in range(5):
            await rate_limiter.check_rate_limit("test_client")

        # 第6个请求应该被拒绝
        result = await rate_limiter.check_rate_limit("test_client")
        assert result is False
```

### 3.3 持续集成配置

#### GitHub Actions工作流

```yaml
# .github/workflows/cvv-detection-ci.yml
name: CVV Detection CI/CD

on:
  push:
    branches: [ feature/cvv-detector-mvp, develop ]
  pull_request:
    branches: [ develop, main ]

jobs:
  test:
    runs-on: ubuntu-latest

    services:
      redis:
        image: redis:7-alpine
        ports:
          - 6379:6379
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: test_password
          POSTGRES_DB: cvv_test
        ports:
          - 5432:5432

    steps:
    - uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install -e .[cvv,test]

    - name: Run security checks
      run: |
        bandit -r cvv_detection/
        safety check

    - name: Run tests
      run: |
        pytest cvv_detection/tests/ -v --cov=cvv_detection --cov-report=xml
      env:
        REDIS_URL: redis://localhost:6379
        DATABASE_URL: postgresql://postgres:test_password@localhost:5432/cvv_test

    - name: Upload coverage
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml

    - name: Run integration tests
      run: |
        pytest tests/integration/ -v

    - name: Build Docker images
      run: |
        docker build -f deployment/docker/Dockerfile.cvv -t cvv-detection:test .

    - name: Run security scan
      uses: aquasecurity/trivy-action@master
      with:
        image-ref: 'cvv-detection:test'
        format: 'sarif'
        output: 'trivy-results.sarif'

    - name: Upload security scan results
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: 'trivy-results.sarif'

  deploy-staging:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/feature/cvv-detector-mvp'

    steps:
    - uses: actions/checkout@v4

    - name: Deploy to staging
      run: |
        echo "Deploying to staging environment..."
        # 部署到测试环境的脚本
```

## 4. 合并和部署计划

### 4.1 分阶段合并策略

#### 合并流程图
```mermaid
flowchart TD
    A[feature/cvv-detector-mvp] --> B{代码审查}
    B -->|通过| C[合并到develop]
    B -->|不通过| D[修复问题]
    D --> A

    C --> E[develop分支集成测试]
    E -->|成功| F[创建release分支]
    E -->|失败| G[回滚并修复]
    G --> A

    F --> H[预生产环境部署]
    H --> I{用户验收测试}
    I -->|通过| J[合并到main]
    I -->|不通过| K[修复并重测]
    K --> H

    J --> L[生产环境部署]
    L --> M[监控和回滚准备]

    style A fill:#e1f5fe
    style C fill:#f3e5f5
    style J fill:#e8f5e8
    style L fill:#fff3e0
```

#### 合并检查清单

**代码质量检查**
- [ ] 代码审查通过（至少2人审查）
- [ ] 单元测试覆盖率 ≥ 80%
- [ ] 集成测试通过
- [ ] 安全扫描无高危漏洞
- [ ] 性能测试满足要求
- [ ] 代码符合团队规范

**功能验证检查**
- [ ] CVV验证算法正确性验证
- [ ] API端点功能完整性测试
- [ ] 前端界面交互测试
- [ ] 跨浏览器兼容性测试
- [ ] 移动端响应式测试

**安全合规检查**
- [ ] 敏感数据加密验证
- [ ] 输入验证和清理测试
- [ ] 频率限制功能测试
- [ ] 审计日志完整性检查
- [ ] OWASP安全标准合规

**系统集成检查**
- [ ] 与Hulu系统集成测试
- [ ] 数据库迁移脚本验证
- [ ] 环境配置正确性检查
- [ ] 监控和告警配置
- [ ] 备份和恢复流程测试

### 4.2 风险评估和缓解措施

#### 技术风险分析

| 风险类型 | 风险等级 | 影响描述 | 缓解措施 |
|---------|---------|----------|----------|
| 依赖冲突 | 中等 | CVV系统与Hulu系统的依赖包冲突 | 使用虚拟环境隔离，版本锁定 |
| 性能影响 | 高 | 新功能对现有系统性能的影响 | 性能测试，资源监控，负载均衡 |
| 安全漏洞 | 高 | 处理敏感数据的安全风险 | 安全审计，渗透测试，加密存储 |
| 数据丢失 | 高 | 部署过程中数据丢失风险 | 数据备份，分阶段部署，回滚计划 |
| 服务中断 | 中等 | 部署导致服务不可用 | 蓝绿部署，健康检查，自动回滚 |

#### 风险监控脚本

```python
# scripts/risk_monitoring.py
import subprocess
import json
import requests
from typing import Dict, List, Any

class RiskMonitor:
    def __init__(self):
        self.alerts = []

    def check_dependency_conflicts(self) -> Dict[str, Any]:
        """检查依赖冲突"""
        try:
            result = subprocess.run(['pip', 'check'], capture_output=True, text=True)
            if result.returncode != 0:
                return {
                    "status": "error",
                    "conflicts": result.stdout.split('\n'),
                    "severity": "high"
                }
            return {"status": "ok", "conflicts": []}
        except Exception as e:
            return {"status": "error", "message": str(e)}

    def monitor_performance_impact(self) -> Dict[str, Any]:
        """监控性能影响"""
        metrics = {}

        # 检查CPU使用率
        try:
            import psutil
            metrics['cpu_percent'] = psutil.cpu_percent(interval=1)
            metrics['memory_percent'] = psutil.virtual_memory().percent
            metrics['disk_usage'] = psutil.disk_usage('/').percent
        except ImportError:
            metrics['error'] = "psutil not available"

        # 检查API响应时间
        try:
            response = requests.get('http://localhost:5000/api/v1/cvv/health', timeout=5)
            metrics['api_response_time'] = response.elapsed.total_seconds()
            metrics['api_status'] = response.status_code
        except requests.RequestException as e:
            metrics['api_error'] = str(e)

        return metrics

    def audit_security_compliance(self) -> Dict[str, Any]:
        """安全合规审计"""
        compliance_checks = {
            "encryption_keys_exist": self._check_encryption_keys(),
            "secure_headers_configured": self._check_security_headers(),
            "rate_limiting_active": self._check_rate_limiting(),
            "audit_logging_enabled": self._check_audit_logging()
        }

        compliance_score = sum(compliance_checks.values()) / len(compliance_checks)

        return {
            "compliance_score": compliance_score,
            "checks": compliance_checks,
            "status": "compliant" if compliance_score >= 0.8 else "non_compliant"
        }

    def _check_encryption_keys(self) -> bool:
        """检查加密密钥是否存在"""
        import os
        return os.path.exists('secret.key')

    def _check_security_headers(self) -> bool:
        """检查安全头配置"""
        try:
            response = requests.get('http://localhost:5000/api/v1/cvv/health')
            security_headers = [
                'X-Content-Type-Options',
                'X-Frame-Options',
                'X-XSS-Protection'
            ]
            return all(header in response.headers for header in security_headers)
        except:
            return False

    def _check_rate_limiting(self) -> bool:
        """检查频率限制是否生效"""
        try:
            # 发送多个快速请求测试频率限制
            for _ in range(10):
                response = requests.post(
                    'http://localhost:5000/api/v1/cvv/validate',
                    json={"card_number": "****************", "cvv": "123", "exp_date": "12/25"},
                    timeout=1
                )

            # 如果没有被限制，说明频率限制可能没有生效
            return response.status_code == 429
        except:
            return False

    def _check_audit_logging(self) -> bool:
        """检查审计日志是否启用"""
        import os
        return os.path.exists('logs/cvv_audit.log')

    def generate_risk_report(self) -> Dict[str, Any]:
        """生成风险评估报告"""
        report = {
            "timestamp": datetime.utcnow().isoformat(),
            "dependency_check": self.check_dependency_conflicts(),
            "performance_metrics": self.monitor_performance_impact(),
            "security_compliance": self.audit_security_compliance()
        }

        # 计算总体风险等级
        risk_factors = []

        if report["dependency_check"]["status"] == "error":
            risk_factors.append("dependency_conflicts")

        perf = report["performance_metrics"]
        if perf.get("cpu_percent", 0) > 80 or perf.get("memory_percent", 0) > 80:
            risk_factors.append("high_resource_usage")

        if report["security_compliance"]["status"] == "non_compliant":
            risk_factors.append("security_non_compliance")

        report["overall_risk"] = "high" if len(risk_factors) >= 2 else "medium" if risk_factors else "low"
        report["risk_factors"] = risk_factors

        return report

if __name__ == "__main__":
    monitor = RiskMonitor()
    report = monitor.generate_risk_report()

    print(json.dumps(report, indent=2))

    # 如果风险等级高，发送告警
    if report["overall_risk"] == "high":
        print("⚠️ 高风险警告：建议暂停部署并解决风险因素")
        exit(1)
```

### 4.3 部署配置

#### Docker配置

```dockerfile
# deployment/docker/Dockerfile.cvv
FROM python:3.11-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt .
COPY pyproject.toml .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt
RUN pip install --no-cache-dir -e .[cvv]

# 复制应用代码
COPY cvv_detection/ ./cvv_detection/
COPY shared/ ./shared/
COPY integration/ ./integration/

# 创建非root用户
RUN useradd --create-home --shell /bin/bash cvv_user
RUN chown -R cvv_user:cvv_user /app
USER cvv_user

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/api/v1/cvv/health || exit 1

EXPOSE 8000

CMD ["uvicorn", "cvv_detection.api.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

```yaml
# deployment/docker/docker-compose.yml
version: '3.8'

services:
  cvv-api:
    build:
      context: ../..
      dockerfile: deployment/docker/Dockerfile.cvv
    ports:
      - "8000:8000"
    environment:
      - REDIS_URL=redis://redis:6379
      - DATABASE_URL=********************************************/cvv_db
      - SECRET_KEY=${SECRET_KEY}
    depends_on:
      - redis
      - postgres
    volumes:
      - ./logs:/app/logs
      - ./secrets:/app/secrets:ro
    restart: unless-stopped

  cvv-frontend:
    build:
      context: ../../web_interface/frontend
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NEXT_PUBLIC_API_URL=http://cvv-api:8000
    depends_on:
      - cvv-api
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped

  postgres:
    image: postgres:15
    environment:
      - POSTGRES_DB=cvv_db
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - cvv-api
      - cvv-frontend
    restart: unless-stopped

volumes:
  redis_data:
  postgres_data:
```

## 5. 具体执行建议

### 5.1 立即行动项

#### 第一步：创建分支结构

```bash
# 从develop创建CVV主分支
git checkout develop
git pull origin develop
git checkout -b feature/cvv-detector-mvp

# 推送到远程
git push -u origin feature/cvv-detector-mvp

# 创建子功能分支
git checkout -b feature/cvv-core-validation
git push -u origin feature/cvv-core-validation

git checkout feature/cvv-detector-mvp
git checkout -b feature/cvv-ui-components
git push -u origin feature/cvv-ui-components

git checkout feature/cvv-detector-mvp
git checkout -b feature/cvv-api-integration
git push -u origin feature/cvv-api-integration
```

#### 第二步：项目结构重组

```bash
# 创建新的目录结构
mkdir -p cvv_detection/{core,api,security,utils,tests}
mkdir -p shared/{config,database,logging}
mkdir -p integration
mkdir -p deployment/{docker,kubernetes}

# 移动现有CVV代码
mv cvv-detector-mvp web_interface

# 创建初始化文件
touch cvv_detection/__init__.py
touch cvv_detection/core/__init__.py
touch cvv_detection/api/__init__.py
touch cvv_detection/security/__init__.py
touch cvv_detection/utils/__init__.py
touch shared/__init__.py
touch shared/config/__init__.py
touch shared/database/__init__.py
touch shared/logging/__init__.py
touch integration/__init__.py
```

#### 第三步：配置管理更新

```toml
# 更新pyproject.toml
[project.optional-dependencies]
cvv = [
    "fastapi>=0.104.0",
    "uvicorn>=0.24.0",
    "pydantic>=2.5.0",
    "cryptography>=41.0.0",
    "redis>=5.0.0",
    "sqlalchemy>=2.0.0",
    "alembic>=1.13.0",
    "psycopg2-binary>=2.9.0",
    "aiofiles>=23.2.1",
    "python-multipart>=0.0.6"
]

test = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.1.0",
    "httpx>=0.25.0",
    "pytest-mock>=3.12.0"
]

security = [
    "bandit>=1.7.5",
    "safety>=2.3.0"
]
```

### 5.2 开发里程碑

#### 里程碑1：基础架构（第1-2周）
- [ ] 项目结构重组完成
- [ ] 核心模块框架搭建
- [ ] 基础安全组件实现
- [ ] 单元测试框架建立

#### 里程碑2：核心功能（第3-4周）
- [ ] CVV验证算法实现
- [ ] 加密服务完成
- [ ] API层开发完成
- [ ] 数据库模型设计

#### 里程碑3：前端集成（第5-6周）
- [ ] UI组件开发完成
- [ ] API集成完成
- [ ] 响应式设计优化
- [ ] 用户体验测试

#### 里程碑4：系统集成（第7-8周）
- [ ] Hulu系统集成完成
- [ ] 端到端测试通过
- [ ] 性能优化完成
- [ ] 安全审计通过

#### 里程碑5：部署准备（第9周）
- [ ] Docker配置完成
- [ ] CI/CD流水线建立
- [ ] 生产环境准备
- [ ] 监控告警配置

### 5.3 质量保证检查清单

**代码质量**
- [ ] 代码审查通过（至少2人审查）
- [ ] 单元测试覆盖率 ≥ 80%
- [ ] 集成测试通过
- [ ] 代码符合PEP 8规范
- [ ] 类型注解完整

**安全检查**
- [ ] 安全扫描无高危漏洞
- [ ] 敏感数据加密验证
- [ ] 输入验证完整
- [ ] 审计日志功能正常
- [ ] 频率限制生效

**性能测试**
- [ ] API响应时间 < 200ms
- [ ] 并发处理能力 ≥ 100 req/s
- [ ] 内存使用稳定
- [ ] 数据库查询优化

**文档完整**
- [ ] API文档更新
- [ ] 部署文档完整
- [ ] 用户手册编写
- [ ] 故障排除指南

### 5.4 监控和回滚准备

#### 监控指标

```python
# 关键监控指标
MONITORING_METRICS = {
    "api_metrics": {
        "response_time_p95": "< 500ms",
        "error_rate": "< 1%",
        "throughput": "> 50 req/s"
    },
    "system_metrics": {
        "cpu_usage": "< 70%",
        "memory_usage": "< 80%",
        "disk_usage": "< 85%"
    },
    "business_metrics": {
        "validation_success_rate": "> 95%",
        "false_positive_rate": "< 5%",
        "user_satisfaction": "> 4.0/5.0"
    }
}
```

#### 回滚策略

```bash
#!/bin/bash
# scripts/rollback.sh - 回滚脚本

echo "🔄 开始CVV功能回滚..."

# 1. 停止新版本服务
docker-compose -f deployment/docker/docker-compose.yml down

# 2. 恢复数据库到回滚点
pg_restore --clean --if-exists -d cvv_db backup/cvv_db_backup_$(date -d '1 hour ago' +%Y%m%d_%H).sql

# 3. 切换到稳定版本
git checkout main
git reset --hard HEAD~1

# 4. 重启服务
docker-compose -f deployment/docker/docker-compose.yml up -d

# 5. 验证服务状态
sleep 30
curl -f http://localhost:8000/api/v1/cvv/health || {
    echo "❌ 回滚后服务异常"
    exit 1
}

echo "✅ CVV功能回滚完成"
```

## 📝 总结

本指南提供了CVV检测MVP功能集成的全面技术方案，包括：

1. **分支管理策略**：采用Git Flow工作流，确保代码质量和发布稳定性
2. **代码组织建议**：模块化架构设计，保持代码清晰和可维护性
3. **开发测试策略**：TDD驱动开发，全面的测试覆盖和质量保证
4. **合并部署计划**：分阶段部署，风险控制和监控告警

遵循本指南的建议，可以确保CVV检测功能安全、高效地集成到现有系统中，同时保持系统的稳定性和可扩展性。

---

**文档维护**：请在功能开发过程中及时更新本文档，确保信息的准确性和时效性。

**联系方式**：如有技术问题，请联系全栈工程师团队或在项目Issue中提出。
