# Hulu Account Creator - 环境变量配置模板
# 复制此文件为 .env 并填入真实的配置值

# =============================================================================
# 临时邮箱 API 配置
# =============================================================================

# API 基础地址（不包含末尾的斜杠）
API_BASE=https://testkuroneko.xyz/api

# API 密钥（可选，如果API需要认证则必填）
API_KEY=your_api_key_here

# 默认邮箱域名
DEFAULT_EMAIL_DOMAIN=testkuroneko.xyz

# API 请求超时时间（秒）
API_TIMEOUT=30

# =============================================================================
# 浏览器自动化配置
# =============================================================================

# 浏览器超时时间（毫秒）
BROWSER_TIMEOUT=30000

# 默认是否使用无头模式 (true/false)
DEFAULT_HEADLESS=true

# 页面加载超时时间（毫秒）
PAGE_LOAD_TIMEOUT=30000

# =============================================================================
# 输出配置
# =============================================================================

# 默认输出文件名
DEFAULT_OUTPUT_FILE=accounts.csv

# 日志文件名
LOG_FILE=hulu_creator.log

# 日志级别 (DEBUG, INFO, WARNING, ERROR)
LOG_LEVEL=INFO

# =============================================================================
# 应用配置
# =============================================================================

# 应用模式 (development, production)
APP_MODE=development

# 是否启用调试模式 (true/false)
DEBUG=false

# 验证码等待超时时间（秒）
VERIFICATION_TIMEOUT=300

# 验证码轮询间隔（秒）
VERIFICATION_POLL_INTERVAL=5

# =============================================================================
# reCAPTCHA 求解服务配置
# =============================================================================

# 2captcha API配置（主力服务商）
TWOCAPTCHA_API_KEY=your_2captcha_api_key_here

# Death By Captcha API配置（后备服务商）
DBC_USERNAME=your_deathbycaptcha_username_here
DBC_PASSWORD=your_deathbycaptcha_password_here

# reCAPTCHA 求解超时时间（秒）
RECAPTCHA_TIMEOUT=120

# reCAPTCHA 最低分数要求 (0.1-0.9)
RECAPTCHA_MIN_SCORE=0.3

# =============================================================================
# 截图功能配置
# =============================================================================

# 截图质量 (1-100, 数值越高质量越好)
SCREENSHOT_QUALITY=90

# 是否包含浏览器地址栏 (true/false)
SCREENSHOT_INCLUDE_ADDRESS_BAR=true

# 截图保存目录
SCREENSHOT_DIRECTORY=screenshots

# 截图文件清理天数（超过此天数的截图会被自动删除）
SCREENSHOT_CLEANUP_DAYS=7

# 浏览器窗口识别关键词（逗号分隔）
SCREENSHOT_BROWSER_KEYWORDS=Chrome,Chromium,Firefox,Safari,Edge,Hulu

# 是否启用浏览器窗口截图 (true/false)
ENABLE_BROWSER_SCREENSHOT=true