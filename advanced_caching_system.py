#!/usr/bin/env python3
"""
高级缓存和状态复用系统 - Advanced Caching and State Reuse System
为hulu_automation_stealth.py提供元素缓存、页面状态复用和智能缓存管理
与现有CDPChromeManager和StealthAntiDetectionService完全集成
"""

import asyncio
import hashlib
import json
import logging
import time
import weakref
from collections import OrderedDict
from dataclasses import dataclass, asdict
from typing import Dict, List, Optional, Any, Tuple, Union, Callable
from urllib.parse import urlparse, parse_qs, urljoin
from playwright.async_api import Page, ElementHandle, Locator


@dataclass
class CacheKey:
    """缓存键数据结构"""
    page_url: str                    # 页面URL（标准化后）
    selector: str                    # 选择器
    element_attributes: Dict[str, str] # 元素属性
    timestamp: float                 # 创建时间戳
    context_hash: str               # 上下文哈希

    def __hash__(self) -> int:
        """生成缓存键的哈希值"""
        key_string = f"{self.page_url}|{self.selector}|{self.context_hash}"
        return hash(key_string)
    
    def __eq__(self, other) -> bool:
        """缓存键相等性比较"""
        if not isinstance(other, CacheKey):
            return False
        return (self.page_url == other.page_url and 
                self.selector == other.selector and 
                self.context_hash == other.context_hash)


@dataclass
class CacheEntry:
    """缓存条目数据结构"""
    key: CacheKey                    # 缓存键
    element_handle: Optional[ElementHandle] # 元素句柄（弱引用）
    locator_info: Dict[str, Any]     # 定位器信息
    element_properties: Dict[str, Any] # 元素属性快照
    creation_time: float             # 创建时间
    last_access_time: float          # 最后访问时间
    access_count: int                # 访问次数
    ttl: float                       # 生存时间（秒）
    is_valid: bool = True            # 是否有效

    def is_expired(self, current_time: float = None) -> bool:
        """检查是否过期"""
        if current_time is None:
            current_time = time.time()
        return (current_time - self.creation_time) > self.ttl
    
    def touch(self):
        """更新访问时间和计数"""
        self.last_access_time = time.time()
        self.access_count += 1
    
    async def validate_element(self, page: Page) -> bool:
        """验证缓存的元素是否仍然有效"""
        try:
            if not self.element_handle:
                return False
                
            # 检查元素是否仍然附加到DOM
            is_attached = await self.element_handle.is_attached()
            if not is_attached:
                self.is_valid = False
                return False
                
            # 检查元素是否可见
            is_visible = await self.element_handle.is_visible()
            if not is_visible:
                # 元素存在但不可见，可能是页面状态变化
                self.is_valid = False
                return False
                
            # 检查元素属性是否发生变化
            current_properties = await self._get_element_properties()
            if current_properties != self.element_properties:
                # 属性发生变化，缓存失效
                self.is_valid = False
                return False
                
            self.is_valid = True
            return True
            
        except Exception as e:
            logging.debug(f"元素验证失败: {e}")
            self.is_valid = False
            return False
    
    async def _get_element_properties(self) -> Dict[str, Any]:
        """获取元素属性"""
        if not self.element_handle:
            return {}
            
        try:
            return await self.element_handle.evaluate("""
                element => {
                    return {
                        tagName: element.tagName,
                        className: element.className,
                        id: element.id,
                        textContent: element.textContent?.substring(0, 100), // 限制长度
                        visible: element.offsetParent !== null,
                        boundingRect: element.getBoundingClientRect()
                    };
                }
            """)
        except:
            return {}


class LRUCache:
    """LRU（最近最少使用）缓存实现"""
    
    def __init__(self, max_size: int = 1000):
        self.max_size = max_size
        self.cache: OrderedDict[CacheKey, CacheEntry] = OrderedDict()
        self.logger = logging.getLogger(__name__)
    
    def get(self, key: CacheKey) -> Optional[CacheEntry]:
        """获取缓存条目"""
        if key in self.cache:
            entry = self.cache[key]
            # 移动到末尾（最近使用）
            self.cache.move_to_end(key)
            entry.touch()
            return entry
        return None
    
    def put(self, key: CacheKey, entry: CacheEntry):
        """放入缓存条目"""
        if key in self.cache:
            # 更新现有条目
            self.cache[key] = entry
            self.cache.move_to_end(key)
        else:
            # 添加新条目
            self.cache[key] = entry
            
            # 检查是否超出最大大小
            if len(self.cache) > self.max_size:
                # 移除最老的条目
                oldest_key, oldest_entry = self.cache.popitem(last=False)
                self.logger.debug(f"LRU缓存清理: 移除 {oldest_key.selector}")
    
    def remove(self, key: CacheKey):
        """移除缓存条目"""
        if key in self.cache:
            del self.cache[key]
    
    def clear(self):
        """清空缓存"""
        self.cache.clear()
    
    def size(self) -> int:
        """获取缓存大小"""
        return len(self.cache)
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        total_accesses = sum(entry.access_count for entry in self.cache.values())
        avg_access_count = total_accesses / len(self.cache) if self.cache else 0
        
        return {
            "size": len(self.cache),
            "max_size": self.max_size,
            "total_accesses": total_accesses,
            "avg_access_count": avg_access_count,
            "utilization": len(self.cache) / self.max_size
        }


class URLNormalizer:
    """URL标准化器"""
    
    @staticmethod
    def normalize_url(url: str, ignore_params: List[str] = None) -> str:
        """
        标准化URL，处理查询参数、锚点、重定向
        
        Args:
            url: 原始URL
            ignore_params: 要忽略的查询参数列表
            
        Returns:
            str: 标准化后的URL
        """
        if ignore_params is None:
            ignore_params = ['timestamp', 'cache_bust', '_t', 'utm_source', 'utm_medium', 'utm_campaign']
        
        parsed = urlparse(url)
        
        # 标准化域名（小写）
        hostname = parsed.hostname.lower() if parsed.hostname else ''
        
        # 标准化路径（移除尾部斜杠，除非是根路径）
        path = parsed.path.rstrip('/') if parsed.path != '/' else '/'
        
        # 处理查询参数
        query_params = parse_qs(parsed.query)
        filtered_params = {
            k: v for k, v in query_params.items() 
            if k not in ignore_params
        }
        
        # 重新构建查询字符串（排序以确保一致性）
        sorted_params = sorted(filtered_params.items())
        query_string = '&'.join(
            f"{k}={'&'.join(v)}" for k, v in sorted_params
        ) if sorted_params else ''
        
        # 重新构建URL（忽略锚点）
        normalized = f"{parsed.scheme}://{hostname}"
        if parsed.port and parsed.port not in [80, 443]:
            normalized += f":{parsed.port}"
        normalized += path
        if query_string:
            normalized += f"?{query_string}"
            
        return normalized
    
    @staticmethod
    def urls_match(url1: str, url2: str, ignore_params: List[str] = None) -> bool:
        """比较两个URL是否匹配"""
        norm1 = URLNormalizer.normalize_url(url1, ignore_params)
        norm2 = URLNormalizer.normalize_url(url2, ignore_params)
        return norm1 == norm2


@dataclass
class PageState:
    """页面状态数据结构"""
    url: str                         # 标准化URL
    title: str                       # 页面标题
    dom_structure_hash: str          # DOM结构哈希
    key_elements: Dict[str, Dict]    # 关键元素信息
    cookies: List[Dict]              # Cookies
    local_storage: Dict[str, str]    # LocalStorage
    session_storage: Dict[str, str]  # SessionStorage
    viewport: Dict[str, int]         # 视口信息
    user_agent: str                  # User Agent
    timestamp: float                 # 创建时间戳
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'PageState':
        """从字典创建实例"""
        return cls(**data)


class PageStateManager:
    """页面状态管理器"""
    
    def __init__(self, max_states: int = 100):
        self.max_states = max_states
        self.states: OrderedDict[str, PageState] = OrderedDict()
        self.logger = logging.getLogger(__name__)
    
    async def capture_page_state(self, page: Page) -> PageState:
        """捕获页面状态"""
        try:
            # 获取基础页面信息
            url = URLNormalizer.normalize_url(page.url)
            title = await page.title()
            
            # 获取DOM结构哈希
            dom_hash = await self._calculate_dom_hash(page)
            
            # 获取关键元素信息
            key_elements = await self._capture_key_elements(page)
            
            # 获取存储信息
            cookies = await page.context.cookies()
            local_storage = await self._get_local_storage(page)
            session_storage = await self._get_session_storage(page)
            
            # 获取视口和User Agent
            viewport = page.viewport_size or {"width": 1920, "height": 1080}
            user_agent = await page.evaluate("navigator.userAgent")
            
            state = PageState(
                url=url,
                title=title,
                dom_structure_hash=dom_hash,
                key_elements=key_elements,
                cookies=cookies,
                local_storage=local_storage,
                session_storage=session_storage,
                viewport=viewport,
                user_agent=user_agent,
                timestamp=time.time()
            )
            
            self.logger.debug(f"页面状态捕获完成: {url}")
            return state
            
        except Exception as e:
            self.logger.error(f"捕获页面状态失败: {e}")
            # 返回基础状态
            return PageState(
                url=page.url,
                title="",
                dom_structure_hash="",
                key_elements={},
                cookies=[],
                local_storage={},
                session_storage={},
                viewport={"width": 1920, "height": 1080},
                user_agent="",
                timestamp=time.time()
            )
    
    async def _calculate_dom_hash(self, page: Page) -> str:
        """计算DOM结构哈希"""
        try:
            dom_structure = await page.evaluate("""
                () => {
                    function getStructure(element, depth = 0) {
                        if (depth > 5) return null; // 限制深度避免过深递归
                        
                        const tag = element.tagName;
                        const id = element.id ? `#${element.id}` : '';
                        const className = element.className ? `.${element.className.replace(/\\s+/g, '.')}` : '';
                        const children = Array.from(element.children)
                            .map(child => getStructure(child, depth + 1))
                            .filter(Boolean);
                            
                        return `${tag}${id}${className}${children.length > 0 ? `[${children.join(',')}]` : ''}`;
                    }
                    
                    return getStructure(document.body);
                }
            """)
            
            return hashlib.md5(dom_structure.encode()).hexdigest()
            
        except Exception as e:
            self.logger.debug(f"DOM哈希计算失败: {e}")
            return ""
    
    async def _capture_key_elements(self, page: Page) -> Dict[str, Dict]:
        """捕获关键元素信息"""
        key_selectors = [
            'button', 'input', 'form', 'a[href]', 
            '[data-testid]', '[id]', '.button', '.btn'
        ]
        
        elements_info = {}
        
        for selector in key_selectors:
            try:
                elements = await page.query_selector_all(selector)
                selector_info = []
                
                for i, element in enumerate(elements[:10]):  # 限制数量避免过多数据
                    try:
                        info = await element.evaluate("""
                            element => ({
                                tagName: element.tagName,
                                id: element.id,
                                className: element.className,
                                textContent: element.textContent?.substring(0, 50),
                                visible: element.offsetParent !== null
                            })
                        """)
                        selector_info.append(info)
                    except:
                        continue
                
                if selector_info:
                    elements_info[selector] = selector_info
                    
            except:
                continue
        
        return elements_info
    
    async def _get_local_storage(self, page: Page) -> Dict[str, str]:
        """获取LocalStorage"""
        try:
            return await page.evaluate("""
                () => {
                    const storage = {};
                    for (let i = 0; i < localStorage.length; i++) {
                        const key = localStorage.key(i);
                        storage[key] = localStorage.getItem(key);
                    }
                    return storage;
                }
            """)
        except:
            return {}
    
    async def _get_session_storage(self, page: Page) -> Dict[str, str]:
        """获取SessionStorage"""
        try:
            return await page.evaluate("""
                () => {
                    const storage = {};
                    for (let i = 0; i < sessionStorage.length; i++) {
                        const key = sessionStorage.key(i);
                        storage[key] = sessionStorage.getItem(key);
                    }
                    return storage;
                }
            """)
        except:
            return {}
    
    def store_state(self, state: PageState):
        """存储页面状态"""
        url_key = state.url
        
        # 检查是否已存在相同URL的状态
        if url_key in self.states:
            # 更新现有状态
            self.states[url_key] = state
            self.states.move_to_end(url_key)
        else:
            # 添加新状态
            self.states[url_key] = state
            
            # 检查是否超出最大限制
            if len(self.states) > self.max_states:
                oldest_url, _ = self.states.popitem(last=False)
                self.logger.debug(f"页面状态清理: 移除 {oldest_url}")
    
    def get_state(self, url: str) -> Optional[PageState]:
        """获取页面状态"""
        normalized_url = URLNormalizer.normalize_url(url)
        
        if normalized_url in self.states:
            state = self.states[normalized_url]
            self.states.move_to_end(normalized_url)  # 更新访问顺序
            return state
        
        return None
    
    def find_similar_state(self, url: str, similarity_threshold: float = 0.8) -> Optional[PageState]:
        """查找相似的页面状态"""
        normalized_url = URLNormalizer.normalize_url(url)
        
        for stored_url, state in self.states.items():
            similarity = self._calculate_url_similarity(normalized_url, stored_url)
            if similarity >= similarity_threshold:
                return state
        
        return None
    
    def _calculate_url_similarity(self, url1: str, url2: str) -> float:
        """计算URL相似度"""
        parsed1 = urlparse(url1)
        parsed2 = urlparse(url2)
        
        # 域名必须相同
        if parsed1.netloc != parsed2.netloc:
            return 0.0
        
        # 路径相似度
        path1_parts = [p for p in parsed1.path.split('/') if p]
        path2_parts = [p for p in parsed2.path.split('/') if p]
        
        if not path1_parts and not path2_parts:
            return 1.0
        
        if not path1_parts or not path2_parts:
            return 0.0
        
        # 计算路径部分的相似度
        common_parts = 0
        max_parts = max(len(path1_parts), len(path2_parts))
        
        for i in range(min(len(path1_parts), len(path2_parts))):
            if path1_parts[i] == path2_parts[i]:
                common_parts += 1
            else:
                break
        
        return common_parts / max_parts
    
    def get_stats(self) -> Dict[str, Any]:
        """获取状态管理统计"""
        if not self.states:
            return {"size": 0, "max_size": self.max_states}
        
        avg_age = (time.time() - sum(s.timestamp for s in self.states.values()) / len(self.states))
        
        return {
            "size": len(self.states),
            "max_size": self.max_states,
            "utilization": len(self.states) / self.max_states,
            "avg_age_seconds": avg_age,
            "oldest_timestamp": min(s.timestamp for s in self.states.values()),
            "newest_timestamp": max(s.timestamp for s in self.states.values())
        }


class ElementCacheManager:
    """元素缓存管理器"""
    
    def __init__(self, max_cache_size: int = 1000, default_ttl: float = 300.0):
        """
        初始化元素缓存管理器
        
        Args:
            max_cache_size: 最大缓存条目数
            default_ttl: 默认TTL（秒）
        """
        self.lru_cache = LRUCache(max_cache_size)
        self.default_ttl = default_ttl
        self.logger = logging.getLogger(__name__)
        self.hit_count = 0
        self.miss_count = 0
    
    def _generate_cache_key(
        self, 
        page_url: str, 
        selector: str, 
        context: Dict[str, Any] = None
    ) -> CacheKey:
        """生成缓存键"""
        
        # 标准化URL
        normalized_url = URLNormalizer.normalize_url(page_url)
        
        # 生成上下文哈希
        context_data = context or {}
        context_str = json.dumps(context_data, sort_keys=True)
        context_hash = hashlib.md5(context_str.encode()).hexdigest()[:8]
        
        return CacheKey(
            page_url=normalized_url,
            selector=selector,
            element_attributes={},  # 稍后填充
            timestamp=time.time(),
            context_hash=context_hash
        )
    
    async def get_cached_element(
        self, 
        page: Page, 
        selector: str,
        context: Dict[str, Any] = None,
        validate: bool = True
    ) -> Optional[ElementHandle]:
        """
        获取缓存的元素
        
        Args:
            page: Playwright页面实例
            selector: 元素选择器
            context: 上下文信息
            validate: 是否验证元素有效性
            
        Returns:
            Optional[ElementHandle]: 缓存的元素句柄或None
        """
        
        cache_key = self._generate_cache_key(page.url, selector, context)
        entry = self.lru_cache.get(cache_key)
        
        if entry is None:
            self.miss_count += 1
            self.logger.debug(f"缓存未命中: {selector}")
            return None
        
        # 检查是否过期
        if entry.is_expired():
            self.lru_cache.remove(cache_key)
            self.miss_count += 1
            self.logger.debug(f"缓存过期: {selector}")
            return None
        
        # 验证元素有效性
        if validate and not await entry.validate_element(page):
            self.lru_cache.remove(cache_key)
            self.miss_count += 1
            self.logger.debug(f"元素验证失败: {selector}")
            return None
        
        self.hit_count += 1
        self.logger.debug(f"缓存命中: {selector}")
        return entry.element_handle
    
    async def cache_element(
        self, 
        page: Page, 
        selector: str, 
        element: ElementHandle,
        context: Dict[str, Any] = None,
        ttl: Optional[float] = None
    ):
        """
        缓存元素
        
        Args:
            page: Playwright页面实例
            selector: 元素选择器
            element: 元素句柄
            context: 上下文信息
            ttl: 生存时间（秒）
        """
        
        if ttl is None:
            ttl = self.default_ttl
        
        try:
            # 获取元素属性
            element_properties = await element.evaluate("""
                element => ({
                    tagName: element.tagName,
                    id: element.id,
                    className: element.className,
                    textContent: element.textContent?.substring(0, 100),
                    visible: element.offsetParent !== null,
                    rect: element.getBoundingClientRect()
                })
            """)
            
            cache_key = self._generate_cache_key(page.url, selector, context)
            cache_key.element_attributes = element_properties
            
            # 获取定位器信息
            locator_info = {
                "selector": selector,
                "page_url": page.url,
                "context": context or {}
            }
            
            # 创建缓存条目
            entry = CacheEntry(
                key=cache_key,
                element_handle=element,
                locator_info=locator_info,
                element_properties=element_properties,
                creation_time=time.time(),
                last_access_time=time.time(),
                access_count=1,
                ttl=ttl,
                is_valid=True
            )
            
            self.lru_cache.put(cache_key, entry)
            self.logger.debug(f"元素已缓存: {selector}, TTL: {ttl}s")
            
        except Exception as e:
            self.logger.warning(f"缓存元素失败: {selector}, 错误: {e}")
    
    async def find_element_with_cache(
        self, 
        page: Page, 
        selector: str,
        context: Dict[str, Any] = None,
        timeout: float = 10000,
        ttl: Optional[float] = None
    ) -> Optional[ElementHandle]:
        """
        智能查找元素（先查缓存，再实际查找）
        
        Args:
            page: Playwright页面实例
            selector: 元素选择器
            context: 上下文信息
            timeout: 查找超时时间（毫秒）
            ttl: 缓存TTL（秒）
            
        Returns:
            Optional[ElementHandle]: 元素句柄或None
        """
        
        # 1. 先尝试从缓存获取
        cached_element = await self.get_cached_element(page, selector, context)
        if cached_element:
            return cached_element
        
        # 2. 缓存未命中，执行实际查找
        try:
            element = await page.wait_for_selector(selector, timeout=timeout)
            if element:
                # 3. 将找到的元素加入缓存
                await self.cache_element(page, selector, element, context, ttl)
                return element
        except Exception as e:
            self.logger.debug(f"元素查找失败: {selector}, 错误: {e}")
        
        return None
    
    async def batch_validate_cache(self, page: Page) -> int:
        """批量验证当前页面的缓存条目"""
        current_url = URLNormalizer.normalize_url(page.url)
        invalid_keys = []
        
        for cache_key, entry in self.lru_cache.cache.items():
            if cache_key.page_url == current_url:
                if not await entry.validate_element(page):
                    invalid_keys.append(cache_key)
        
        # 移除无效条目
        for key in invalid_keys:
            self.lru_cache.remove(key)
        
        self.logger.debug(f"批量验证完成: 移除 {len(invalid_keys)} 个无效条目")
        return len(invalid_keys)
    
    def clear_page_cache(self, page_url: str):
        """清理特定页面的缓存"""
        normalized_url = URLNormalizer.normalize_url(page_url)
        keys_to_remove = [
            key for key in self.lru_cache.cache.keys() 
            if key.page_url == normalized_url
        ]
        
        for key in keys_to_remove:
            self.lru_cache.remove(key)
        
        self.logger.debug(f"页面缓存清理完成: {page_url}, 移除 {len(keys_to_remove)} 个条目")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        total_requests = self.hit_count + self.miss_count
        hit_rate = self.hit_count / total_requests if total_requests > 0 else 0
        
        lru_stats = self.lru_cache.get_stats()
        
        return {
            "hit_count": self.hit_count,
            "miss_count": self.miss_count,
            "hit_rate": hit_rate,
            "total_requests": total_requests,
            **lru_stats
        }


class AdvancedCachingSystem:
    """高级缓存系统主类"""
    
    def __init__(
        self, 
        max_element_cache: int = 1000,
        max_page_states: int = 100,
        default_element_ttl: float = 300.0
    ):
        """
        初始化高级缓存系统
        
        Args:
            max_element_cache: 最大元素缓存数
            max_page_states: 最大页面状态数
            default_element_ttl: 默认元素TTL（秒）
        """
        self.element_cache = ElementCacheManager(max_element_cache, default_element_ttl)
        self.page_state_manager = PageStateManager(max_page_states)
        self.logger = logging.getLogger(__name__)
        
        # 性能监控
        self.operation_times = []
        self.cache_performance = {
            "element_cache_saves": 0,
            "page_state_saves": 0,
            "batch_validations": 0
        }
    
    async def setup_page_caching(self, page: Page):
        """为页面设置缓存"""
        # 捕获当前页面状态
        page_state = await self.page_state_manager.capture_page_state(page)
        self.page_state_manager.store_state(page_state)
        
        # 批量验证现有缓存
        await self.element_cache.batch_validate_cache(page)
        
        self.logger.info(f"页面缓存设置完成: {page.url}")
    
    async def intelligent_element_search(
        self, 
        page: Page, 
        selectors: List[str],
        context: Dict[str, Any] = None,
        parallel: bool = True,
        cache_ttl: float = None
    ) -> Dict[str, Optional[ElementHandle]]:
        """
        智能元素搜索（支持并行搜索和缓存）
        
        Args:
            page: Playwright页面实例
            selectors: 选择器列表
            context: 上下文信息
            parallel: 是否并行搜索
            cache_ttl: 缓存TTL
            
        Returns:
            Dict[str, Optional[ElementHandle]]: 选择器到元素的映射
        """
        start_time = time.time()
        results = {}
        
        if parallel:
            # 并行搜索
            tasks = [
                self.element_cache.find_element_with_cache(
                    page, selector, context, ttl=cache_ttl
                )
                for selector in selectors
            ]
            
            elements = await asyncio.gather(*tasks, return_exceptions=True)
            
            for selector, element in zip(selectors, elements):
                if isinstance(element, Exception):
                    results[selector] = None
                    self.logger.debug(f"并行搜索异常: {selector}, {element}")
                else:
                    results[selector] = element
        else:
            # 串行搜索
            for selector in selectors:
                element = await self.element_cache.find_element_with_cache(
                    page, selector, context, ttl=cache_ttl
                )
                results[selector] = element
        
        elapsed_time = time.time() - start_time
        self.operation_times.append(elapsed_time)
        
        found_count = sum(1 for element in results.values() if element is not None)
        self.logger.info(
            f"智能元素搜索完成: {found_count}/{len(selectors)} 个元素找到, "
            f"耗时: {elapsed_time:.3f}s, 并行: {parallel}"
        )
        
        return results
    
    async def restore_page_state(self, page: Page, target_url: str) -> bool:
        """
        恢复页面状态
        
        Args:
            page: Playwright页面实例
            target_url: 目标URL
            
        Returns:
            bool: 是否成功恢复状态
        """
        try:
            # 查找匹配的页面状态
            page_state = self.page_state_manager.get_state(target_url)
            
            if not page_state:
                # 查找相似状态
                page_state = self.page_state_manager.find_similar_state(target_url)
            
            if not page_state:
                self.logger.debug(f"未找到匹配的页面状态: {target_url}")
                return False
            
            # 检查是否需要导航
            current_url = URLNormalizer.normalize_url(page.url)
            if current_url != page_state.url:
                await page.goto(page_state.url, wait_until="networkidle")
            
            # 恢复存储状态
            if page_state.local_storage:
                await self._restore_local_storage(page, page_state.local_storage)
            
            if page_state.session_storage:
                await self._restore_session_storage(page, page_state.session_storage)
            
            # 设置视口
            if page_state.viewport:
                await page.set_viewport_size(page_state.viewport)
            
            self.logger.info(f"页面状态恢复成功: {target_url}")
            return True
            
        except Exception as e:
            self.logger.error(f"页面状态恢复失败: {target_url}, 错误: {e}")
            return False
    
    async def _restore_local_storage(self, page: Page, storage_data: Dict[str, str]):
        """恢复LocalStorage"""
        try:
            await page.evaluate("""
                (storageData) => {
                    localStorage.clear();
                    for (const [key, value] of Object.entries(storageData)) {
                        localStorage.setItem(key, value);
                    }
                }
            """, storage_data)
        except Exception as e:
            self.logger.debug(f"LocalStorage恢复失败: {e}")
    
    async def _restore_session_storage(self, page: Page, storage_data: Dict[str, str]):
        """恢复SessionStorage"""
        try:
            await page.evaluate("""
                (storageData) => {
                    sessionStorage.clear();
                    for (const [key, value] of Object.entries(storageData)) {
                        sessionStorage.setItem(key, value);
                    }
                }
            """, storage_data)
        except Exception as e:
            self.logger.debug(f"SessionStorage恢复失败: {e}")
    
    async def validate_required_elements(
        self, 
        page: Page, 
        required_selectors: List[str],
        context: Dict[str, Any] = None
    ) -> Dict[str, bool]:
        """
        批量验证必需元素的存在性和可用性
        
        Args:
            page: Playwright页面实例
            required_selectors: 必需的选择器列表
            context: 上下文信息
            
        Returns:
            Dict[str, bool]: 选择器到验证结果的映射
        """
        start_time = time.time()
        
        # 使用缓存进行快速验证
        elements = await self.intelligent_element_search(
            page, required_selectors, context, parallel=True
        )
        
        # 验证元素状态
        validation_tasks = []
        for selector, element in elements.items():
            if element:
                validation_tasks.append(self._validate_element_usability(element))
            else:
                validation_tasks.append(asyncio.create_task(asyncio.coroutine(lambda: False)()))
        
        validation_results = await asyncio.gather(*validation_tasks, return_exceptions=True)
        
        results = {}
        for selector, result in zip(required_selectors, validation_results):
            if isinstance(result, Exception):
                results[selector] = False
            else:
                results[selector] = result
        
        elapsed_time = time.time() - start_time
        valid_count = sum(1 for valid in results.values() if valid)
        
        self.logger.info(
            f"必需元素验证完成: {valid_count}/{len(required_selectors)} 个有效, "
            f"耗时: {elapsed_time:.3f}s"
        )
        
        return results
    
    async def _validate_element_usability(self, element: ElementHandle) -> bool:
        """验证元素可用性"""
        try:
            is_attached = await element.is_attached()
            if not is_attached:
                return False
            
            is_visible = await element.is_visible()
            if not is_visible:
                return False
            
            is_enabled = await element.is_enabled()
            return is_enabled
            
        except:
            return False
    
    def clear_all_cache(self):
        """清空所有缓存"""
        self.element_cache.lru_cache.clear()
        self.page_state_manager.states.clear()
        
        # 重置性能计数器
        self.element_cache.hit_count = 0
        self.element_cache.miss_count = 0
        self.operation_times.clear()
        
        self.logger.info("所有缓存已清空")
    
    def get_comprehensive_stats(self) -> Dict[str, Any]:
        """获取综合统计信息"""
        element_stats = self.element_cache.get_cache_stats()
        page_stats = self.page_state_manager.get_stats()
        
        avg_operation_time = (
            sum(self.operation_times) / len(self.operation_times) 
            if self.operation_times else 0
        )
        
        return {
            "element_cache": element_stats,
            "page_state_manager": page_stats,
            "performance": {
                "avg_operation_time": avg_operation_time,
                "total_operations": len(self.operation_times),
                **self.cache_performance
            },
            "memory_usage": {
                "element_cache_size": element_stats.get("size", 0),
                "page_state_size": page_stats.get("size", 0),
                "total_cache_utilization": (
                    element_stats.get("utilization", 0) + 
                    page_stats.get("utilization", 0)
                ) / 2
            }
        }


# 工厂函数和集成接口
def create_advanced_caching_system(
    cdp_manager=None,
    stealth_service=None,
    max_element_cache: int = 1000,
    max_page_states: int = 100,
    default_ttl: float = 300.0
) -> AdvancedCachingSystem:
    """
    创建高级缓存系统实例，与现有CDPChromeManager和StealthAntiDetectionService集成
    
    Args:
        cdp_manager: CDPChromeManager实例
        stealth_service: StealthAntiDetectionService实例
        max_element_cache: 最大元素缓存数
        max_page_states: 最大页面状态数
        default_ttl: 默认TTL
        
    Returns:
        AdvancedCachingSystem: 配置好的高级缓存系统
    """
    caching_system = AdvancedCachingSystem(
        max_element_cache=max_element_cache,
        max_page_states=max_page_states,
        default_element_ttl=default_ttl
    )
    
    # 可以在这里添加与CDPChromeManager和StealthAntiDetectionService的集成逻辑
    # 例如监听页面变化事件、自动清理缓存等
    
    return caching_system