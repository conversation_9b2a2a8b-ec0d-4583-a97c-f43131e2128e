"""
信用卡校验学习模块
提供安全的信用卡数据处理和验证功能

主要功能:
- 安全的数据加密和解密
- <PERSON>hn算法验证
- BIN码识别
- 信用卡格式验证
- 有效期和CVV验证
- 数据清洗和转换
- 风险评分
"""

__version__ = "1.0.0"
__author__ = "Credit Card Validation Engineer"
__description__ = "Secure Credit Card Validation Learning Module"

# 导入主要类和函数
from .core.encryption import SecureCardDataHandler, FileEncryption
from .core.validator import (
    CreditCardValidator, 
    LuhnValidator, 
    BINIdentifier, 
    ExpiryValidator, 
    CVVValidator,
    CardValidationResult
)
from .data_processor import SecureDataProcessor, CardData, DataParser

# 导出的公共接口（仅供内部使用）
__all__ = [
    # 核心加密类
    'SecureCardDataHandler',
    'FileEncryption',

    # 验证器类
    'CreditCardValidator',
    'LuhnValidator',
    'BINIdentifier',
    'ExpiryValidator',
    'CVVValidator',

    # 数据类
    'CardValidationResult',
    'CardData',

    # 处理器类
    'SecureDataProcessor',
    'DataParser'
]

# 编程接口已移除 - 请使用 credit_card.py 作为唯一程序入口


# 模块级配置
import logging

# 配置日志
import os
os.makedirs('logs', exist_ok=True)  # 确保logs目录存在

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('logs/credit_card_validator.log')
    ]
)

# 安全提醒
def _security_reminder():
    """安全使用提醒"""
    print("""
    🔒 信用卡校验模块安全提醒:
    
    1. 本模块仅用于学习和演示目的
    2. 请勿在生产环境中处理真实信用卡数据
    3. 所有敏感数据都会被加密处理
    4. 处理完成后会安全清理源文件
    5. 请妥善保管加密密钥
    6. 遵循PCI DSS等相关安全标准
    
    ⚠️  使用前请确保了解相关法律法规要求
    """)

# 模块导入时显示安全提醒
_security_reminder()
