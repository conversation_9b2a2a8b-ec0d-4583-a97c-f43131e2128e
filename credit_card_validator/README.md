# 🔒 信用卡校验学习模块

一个专业的信用卡校验学习模块，提供安全的数据处理和验证功能，严格遵循PCI DSS安全标准。

## ⚠️ 重要声明

**本模块仅用于学习和演示目的，请勿在生产环境中处理真实信用卡数据！**

## 🎯 功能特性

### 🔐 安全特性
- **端到端加密**：使用AES-256和Fernet加密算法
- **安全内存管理**：自动清理敏感数据
- **PCI DSS合规**：遵循支付卡行业数据安全标准
- **安全文件删除**：多次覆写后删除敏感文件

### 🔍 验证功能
- **Luhn算法验证**：标准信用卡号校验算法
- **BIN码识别**：自动识别卡片品牌和类型
- **格式验证**：支持主流信用卡格式
- **有效期验证**：智能有效期检查
- **CVV验证**：安全码格式验证
- **风险评分**：多维度风险评估

### 📊 数据处理
- **多格式支持**：TXT、CSV等格式
- **数据清洗**：自动清洗和标准化
- **加密存储**：处理结果加密保存
- **详细报告**：生成完整的验证报告

## 🚀 快速开始

### 安装依赖

```bash
pip install -r credit_card_requirements.txt
```

### 基本使用

#### 使用说明

本模块不提供编程接口，请直接运行主程序进行演示和学习：

```bash
python credit_card.py
```

程序将自动执行以下功能：
- 创建示例数据
- 快速验证演示
- 文件处理演示
- 安全功能演示

### 运行演示程序

```bash
# 运行完整演示程序（包含所有功能演示）
python credit_card.py
```

## 📁 项目结构

```
credit_card_validator/
├── __init__.py                 # 模块入口
├── core/                       # 核心功能
│   ├── encryption.py          # 加密处理
│   └── validator.py           # 验证逻辑
├── data_processor.py          # 数据处理器
data/
├── data_input/                 # 输入数据目录
│   └── fake_data.txt          # 示例输入文件
└── data_output/               # 输出结果目录
    └── validation_results_*/   # 带时间戳的结果目录
tests/
├── test_validator.py          # 测试套件
credit_card_demo.py            # 演示程序
README.md                      # 说明文档
USAGE_GUIDE.md                 # 详细使用指南
credit_card_requirements.txt   # 依赖包
```

## 🔧 API 文档

### 核心类

#### `CreditCardValidator`
主要的信用卡验证器类。

```python
validator = CreditCardValidator()
result = validator.validate_card(
    card_number="****************",
    expiry_month="12", 
    expiry_year="25",
    cvv="123"
)
```

#### `SecureCardDataHandler`
安全数据处理器，提供加密功能。

```python
handler = SecureCardDataHandler("password")
encrypted_info = handler.encrypt_card_number("****************")
```

#### `SecureDataProcessor`
完整的数据处理流程管理器。

```python
processor = SecureDataProcessor("master_password")
stats = processor.process_txt_file("input.txt", "output")
```

### 验证结果

`CardValidationResult` 对象包含以下字段：

- `is_valid`: 总体验证结果
- `card_brand`: 卡片品牌 (visa, mastercard, amex等)
- `card_type`: 卡片类型名称
- `luhn_valid`: Luhn算法验证结果
- `format_valid`: 格式验证结果
- `expiry_valid`: 有效期验证结果
- `cvv_valid`: CVV验证结果
- `risk_score`: 风险评分 (0-1)
- `errors`: 错误信息列表

## 🧪 运行测试

```bash
# 运行测试套件
python tests/test_validator.py

# 或使用pytest
pytest tests/ -v
```

## 🔒 安全最佳实践

1. **密码管理**
   - 使用强密码作为主密码
   - 定期更换加密密钥
   - 安全存储密码

2. **数据处理**
   - 处理完成后立即清理源文件
   - 加密存储所有敏感数据
   - 限制数据访问权限

3. **合规要求**
   - 遵循PCI DSS标准
   - 定期安全审计
   - 员工安全培训

## 📋 支持的卡片类型

| 品牌 | 长度 | CVV长度 | 示例BIN |
|------|------|---------|---------|
| Visa | 13,16,19 | 3 | 4*** |
| Mastercard | 16 | 3 | 5*** |
| American Express | 15 | 4 | 34**, 37** |
| Discover | 16 | 3 | 6011, 65** |
| JCB | 16 | 3 | 35** |
| Diners Club | 14 | 3 | 30**, 36**, 38** |

## ⚡ 性能指标

- **验证速度**: >10,000 QPS
- **内存使用**: <100MB (1万条记录)
- **加密性能**: AES-256, >1000 ops/sec
- **文件处理**: 支持GB级文件

## 🐛 故障排除

### 常见问题

1. **加密失败**
   - 检查密码是否正确
   - 确认cryptography库版本

2. **文件处理失败**
   - 检查文件格式和编码
   - 确认文件访问权限

3. **验证结果异常**
   - 检查输入数据格式
   - 查看错误信息详情

### 日志查看

```bash
# 查看日志文件
tail -f credit_card_validator.log
```

## 📄 许可证

本项目仅用于教育和学习目的。请遵循相关法律法规，不得用于非法用途。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个学习模块。

## 📞 联系方式

如有问题或建议，请通过以下方式联系：

- 创建GitHub Issue
- 发送邮件至项目维护者

---

**⚠️ 再次提醒：本模块仅用于学习目的，请勿处理真实信用卡数据！**
