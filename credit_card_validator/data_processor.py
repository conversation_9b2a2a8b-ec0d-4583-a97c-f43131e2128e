"""
信用卡数据处理模块
负责数据读取、清洗、转换和安全存储
"""

import os
import csv
import json
import pandas as pd
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass
import logging
from pathlib import Path
from datetime import datetime

from .core.encryption import SecureCardDataHandler, FileEncryption
from .core.validator import CreditCardValidator, CardValidationResult

logger = logging.getLogger(__name__)

def generate_output_directory_name(base_dir: str = "data/data_output") -> str:
    """
    生成带时间戳的输出目录名称

    Args:
        base_dir: 基础输出目录

    Returns:
        完整的输出目录路径
    """
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir_name = f"validation_results_{timestamp}"
    return os.path.join(base_dir, output_dir_name)

@dataclass
class CardData:
    """信用卡数据结构"""
    card_number: str
    expiry_month: str
    expiry_year: str
    cvv: str
    cardholder_name: str
    zip_code: str
    address: str = ""
    city: str = ""
    state: str = ""
    country: str = ""
    
    def to_dict(self) -> Dict:
        """转换为字典"""
        return {
            'card_number': self.card_number,
            'expiry_month': self.expiry_month,
            'expiry_year': self.expiry_year,
            'cvv': self.cvv,
            'cardholder_name': self.cardholder_name,
            'zip_code': self.zip_code,
            'address': self.address,
            'city': self.city,
            'state': self.state,
            'country': self.country
        }


class DataParser:
    """数据解析器"""
    
    @staticmethod
    def _contains_chinese(text: str) -> bool:
        """检查文本是否包含中文字符"""
        return any('\u4e00' <= char <= '\u9fff' for char in text)

    @staticmethod
    def _is_service_identifier(text: str) -> bool:
        """检查是否为服务标识符"""
        service_keywords = ['G', 'NG', '可', '红', '黑', '爱奇艺', 'hulu', 'splice', '紫钢琴', 'YOU', 'plex', '单车']
        return any(keyword in text for keyword in service_keywords)

    @staticmethod
    def _is_zip_code(text: str) -> bool:
        """检查是否为邮编"""
        return text.isdigit() and len(text) == 5

    @staticmethod
    def _is_state_code(text: str) -> bool:
        """检查是否为美国州代码"""
        us_states = {
            'AL', 'AK', 'AZ', 'AR', 'CA', 'CO', 'CT', 'DE', 'FL', 'GA',
            'HI', 'ID', 'IL', 'IN', 'IA', 'KS', 'KY', 'LA', 'ME', 'MD',
            'MA', 'MI', 'MN', 'MS', 'MO', 'MT', 'NE', 'NV', 'NH', 'NJ',
            'NM', 'NY', 'NC', 'ND', 'OH', 'OK', 'OR', 'PA', 'RI', 'SC',
            'SD', 'TN', 'TX', 'UT', 'VT', 'VA', 'WA', 'WV', 'WI', 'WY'
        }
        return text.upper() in us_states

    @staticmethod
    def parse_txt_line(line: str) -> Optional[CardData]:
        """
        解析TXT文件中的一行数据 (优化版)
        支持中文字符过滤、智能地址解析、重复邮编处理

        Args:
            line: 数据行

        Returns:
            解析后的CardData对象
        """
        try:
            # 清理行数据
            line = line.strip()
            if not line:
                return None

            # 按空格分割数据
            parts = line.split()

            if len(parts) < 5:
                logger.warning(f"Insufficient data in line: {line}")
                return None

            # 基础字段解析
            card_number = parts[0]
            expiry_month = parts[1].zfill(2)  # 确保两位数
            expiry_year = parts[2]
            cvv = parts[3]
            zip_code = parts[4]

            # 初始化地址相关字段
            address_parts = []
            address = ""
            city = ""
            state = ""
            country = ""
            cardholder_name = ""

            # 处理剩余部分（地址信息）
            if len(parts) > 5:
                remaining_parts = parts[5:]

                # 第一步：过滤中文字符和服务标识符
                filtered_parts = []
                for part in remaining_parts:
                    if not DataParser._contains_chinese(part) and not DataParser._is_service_identifier(part):
                        filtered_parts.append(part)

                # 第二步：识别和提取结构化信息
                country_found = False
                state_found = False

                for part in filtered_parts:
                    # 识别国家代码
                    if part.upper() in ['US', 'USA', 'CN', 'UK', 'CA', 'UNITED', 'STATES']:
                        if not country_found:
                            country = 'US' if part.upper() in ['US', 'USA', 'UNITED', 'STATES'] else part.upper()
                            country_found = True
                        continue

                    # 识别州代码
                    elif DataParser._is_state_code(part):
                        if not state_found:
                            state = part.upper()
                            state_found = True
                        continue

                    # 跳过重复的邮编
                    elif DataParser._is_zip_code(part) and part == zip_code:
                        continue

                    # 识别城市名（纯字母，且不是州代码）
                    elif part.replace('-', '').replace("'", '').isalpha() and len(part) > 2:
                        if not city:
                            city = part.title()  # 首字母大写
                        elif not cardholder_name:
                            cardholder_name = part.title()
                        else:
                            address_parts.append(part)

                    # 其他部分作为地址
                    else:
                        address_parts.append(part)

                # 构建地址字符串，排除已识别的城市、州、国家
                address = ' '.join(address_parts).strip()

            return CardData(
                card_number=card_number,
                expiry_month=expiry_month,
                expiry_year=expiry_year,
                cvv=cvv,
                cardholder_name=cardholder_name,
                zip_code=zip_code,
                address=address,
                city=city,
                state=state,
                country=country
            )

        except Exception as e:
            logger.error(f"Error parsing line '{line}': {e}")
            return None
    
    @staticmethod
    def parse_csv_line(row: Dict) -> Optional[CardData]:
        """
        解析CSV行数据
        
        Args:
            row: CSV行字典
            
        Returns:
            解析后的CardData对象
        """
        try:
            return CardData(
                card_number=row.get('card_number', ''),
                expiry_month=row.get('expiry_month', ''),
                expiry_year=row.get('expiry_year', ''),
                cvv=row.get('cvv', ''),
                cardholder_name=row.get('cardholder_name', ''),
                zip_code=row.get('zip_code', ''),
                address=row.get('address', ''),
                city=row.get('city', ''),
                state=row.get('state', ''),
                country=row.get('country', '')
            )
        except Exception as e:
            logger.error(f"Error parsing CSV row: {e}")
            return None


class SecureDataProcessor:
    """安全数据处理器"""
    
    def __init__(self, master_password: str = None):
        """
        初始化数据处理器
        
        Args:
            master_password: 主密码
        """
        self.encryption_handler = SecureCardDataHandler(master_password)
        self.file_encryption = FileEncryption(self.encryption_handler)
        self.validator = CreditCardValidator()
        self.parser = DataParser()
        
        # 处理统计
        self.stats = {
            'total_records': 0,
            'valid_records': 0,
            'invalid_records': 0,
            'parsing_errors': 0
        }
    
    def process_txt_file(self, file_path: str) -> Dict:
        """
        处理TXT格式的信用卡数据文件

        Args:
            file_path: 输入文件路径

        Returns:
            处理结果统计
        """
        try:
            # 自动生成带时间戳的输出目录
            output_dir = generate_output_directory_name()

            # 创建输出目录
            Path(output_dir).mkdir(parents=True, exist_ok=True)
            
            # 读取原始数据
            logger.info(f"Reading data from: {file_path}")
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            # 解析和验证数据
            processed_data = []
            validation_results = []
            
            for line_num, line in enumerate(lines, 1):
                self.stats['total_records'] += 1
                
                # 解析数据
                card_data = self.parser.parse_txt_line(line)
                if not card_data:
                    self.stats['parsing_errors'] += 1
                    logger.warning(f"Failed to parse line {line_num}: {line.strip()}")
                    continue
                
                # 验证信用卡
                validation_result = self.validator.validate_card(
                    card_data.card_number,
                    card_data.expiry_month,
                    card_data.expiry_year,
                    card_data.cvv
                )
                
                # 加密敏感数据
                encrypted_card_info = self.encryption_handler.encrypt_card_number(
                    card_data.card_number
                )
                
                # 准备输出数据
                processed_record = {
                    'record_id': line_num,
                    'encrypted_card_number': encrypted_card_info['encrypted_full'],
                    'masked_card_number': encrypted_card_info['masked_display'],
                    'bin': encrypted_card_info['bin'],
                    'last4': encrypted_card_info['last4'],
                    'expiry_month': card_data.expiry_month,
                    'expiry_year': card_data.expiry_year,
                    'encrypted_cvv': self.encryption_handler.encrypt_data(card_data.cvv),
                    'cardholder_name': card_data.cardholder_name,
                    'zip_code': card_data.zip_code,
                    'address': card_data.address,
                    'city': card_data.city,
                    'state': card_data.state,
                    'country': card_data.country,
                    'validation_result': validation_result.to_dict()
                }
                
                processed_data.append(processed_record)
                validation_results.append(validation_result)
                
                if validation_result.is_valid:
                    self.stats['valid_records'] += 1
                else:
                    self.stats['invalid_records'] += 1
            
            # 保存处理结果
            self._save_processed_data(processed_data, validation_results, output_dir)
            
            # 安全清理源文件
            self._secure_wipe_source_file(file_path)
            
            logger.info(f"Processing completed. Stats: {self.stats}")
            return self.stats
            
        except Exception as e:
            logger.error(f"Error processing file: {e}")
            raise
    
    def _save_processed_data(self, processed_data: List[Dict],
                           validation_results: List[CardValidationResult],
                           output_dir: str):
        """保存处理后的数据"""

        # 保存加密的详细数据
        detailed_output_path = os.path.join(output_dir, "processed_data_encrypted.json")
        encrypted_detailed_data = self.encryption_handler.encrypt_data(
            json.dumps(processed_data, indent=2, ensure_ascii=False)
        )

        with open(detailed_output_path, 'w', encoding='utf-8') as f:
            f.write(encrypted_detailed_data)

        # 保存验证报告（增强字段和状态分类）
        report_data = []
        for i, result in enumerate(validation_results):
            # 获取对应的原始数据
            original_data = processed_data[i] if i < len(processed_data) else {}

            # 确定验证状态分类
            validation_status = self._determine_validation_status(result)

            # 格式化有效期
            expiry_month = original_data.get('expiry_month', '')
            expiry_year = original_data.get('expiry_year', '')
            expiry_date = f"{expiry_month.zfill(2)}/{expiry_year}" if expiry_month and expiry_year else ""

            report_record = {
                'masked_card_number': result.card_number_masked,
                'card_brand': result.card_brand,
                'card_type': result.card_type,
                'validation_status': validation_status,  # 新的状态分类
                'is_valid': result.is_valid,  # 保留原有字段
                'luhn_valid': result.luhn_valid,
                'format_valid': result.format_valid,
                'expiry_valid': result.expiry_valid,
                'cvv_valid': result.cvv_valid,
                'risk_score': result.risk_score,
                'validation_timestamp': result.validation_timestamp,
                'errors': result.errors,
                # 新增的加密字段
                'encrypted_cardholder_name': self.encryption_handler.encrypt_data(original_data.get('cardholder_name', '')),
                'encrypted_expiry_date': self.encryption_handler.encrypt_data(expiry_date),
                'encrypted_cvc': original_data.get('encrypted_cvv', ''),
                'encrypted_zip_code': self.encryption_handler.encrypt_data(original_data.get('zip_code', ''))
            }
            report_data.append(report_record)
        
        # 保存CSV格式报告
        csv_output_path = os.path.join(output_dir, "validation_report.csv")
        df = pd.DataFrame(report_data)
        df.to_csv(csv_output_path, index=False, encoding='utf-8')
        
        # 保存JSON格式报告
        json_output_path = os.path.join(output_dir, "validation_report.json")
        with open(json_output_path, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False)
        
        # 保存统计摘要
        summary_path = os.path.join(output_dir, "processing_summary.json")
        summary = {
            'processing_stats': self.stats,
            'validation_summary': {
                'total_processed': len(validation_results),
                'valid_cards': sum(1 for r in validation_results if r.is_valid),
                'invalid_cards': sum(1 for r in validation_results if not r.is_valid),
                'status_distribution': self._get_status_distribution(validation_results),
                'brand_distribution': self._get_brand_distribution(validation_results),
                'risk_distribution': self._get_risk_distribution(validation_results)
            },
            'timestamp': pd.Timestamp.now().isoformat()
        }
        
        with open(summary_path, 'w', encoding='utf-8') as f:
            json.dump(summary, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Data saved to {output_dir}")

    def _determine_validation_status(self, result: CardValidationResult) -> str:
        """
        确定验证状态分类

        Args:
            result: 验证结果对象

        Returns:
            验证状态：'valid', 'invalid', 'unknown'
        """
        # 如果通过所有验证且没有错误，状态为"有效"
        if (result.is_valid and result.luhn_valid and result.format_valid and
            result.expiry_valid and result.cvv_valid and not result.errors):
            return "valid"

        # 如果明确验证失败（Luhn算法失败或格式错误），状态为"无效"
        if not result.luhn_valid or not result.format_valid:
            return "invalid"

        # 如果基础验证通过但其他验证失败，或存在错误信息，状态为"未知"
        if result.errors or not result.expiry_valid or not result.cvv_valid:
            return "unknown"

        # 默认情况下，如果无法明确分类，返回"未知"
        return "unknown"

    def _get_status_distribution(self, results: List[CardValidationResult]) -> Dict:
        """获取验证状态分布"""
        distribution = {
            'valid': 0,
            'invalid': 0,
            'unknown': 0
        }

        for result in results:
            status = self._determine_validation_status(result)
            distribution[status] += 1

        return distribution

    def _get_brand_distribution(self, results: List[CardValidationResult]) -> Dict:
        """获取卡片品牌分布"""
        distribution = {}
        for result in results:
            brand = result.card_brand
            distribution[brand] = distribution.get(brand, 0) + 1
        return distribution
    
    def _get_risk_distribution(self, results: List[CardValidationResult]) -> Dict:
        """获取风险分布"""
        risk_ranges = {
            'low_risk': 0,      # 0.0 - 0.3
            'medium_risk': 0,   # 0.3 - 0.7
            'high_risk': 0      # 0.7 - 1.0
        }
        
        for result in results:
            if result.risk_score <= 0.3:
                risk_ranges['low_risk'] += 1
            elif result.risk_score <= 0.7:
                risk_ranges['medium_risk'] += 1
            else:
                risk_ranges['high_risk'] += 1
        
        return risk_ranges
    
    def _secure_wipe_source_file(self, file_path: str):
        """安全清空源文件内容但保留文件"""
        try:
            logger.info(f"Securely wiping source file content: {file_path}")
            self.file_encryption.secure_wipe_file_content(file_path)
        except Exception as e:
            logger.error(f"Failed to securely wipe source file content: {e}")
    
    def decrypt_processed_data(self, encrypted_file_path: str) -> List[Dict]:
        """
        解密处理后的数据
        
        Args:
            encrypted_file_path: 加密文件路径
            
        Returns:
            解密后的数据列表
        """
        try:
            decrypted_content = self.file_encryption.decrypt_file_content(encrypted_file_path)
            return json.loads(decrypted_content)
        except Exception as e:
            logger.error(f"Failed to decrypt data: {e}")
            raise
    
    def __del__(self):
        """清理资源"""
        if hasattr(self, 'encryption_handler'):
            self.encryption_handler.secure_wipe_memory()
