"""
信用卡数据加密核心模块
实现端到端加密保护，符合PCI DSS安全要求
"""

import os
import base64
import hashlib
from typing import Union, Dict, Any
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
import secrets
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SecureCardDataHandler:
    """
    安全的信用卡数据处理器
    实现AES-256加密和Fernet对称加密
    """

    def __init__(self, master_password: str = None):
        """
        初始化加密处理器

        Args:
            master_password: 主密码，用于生成加密密钥
        """
        self.master_password = master_password or self._generate_master_password()
        self.key_file_path = "data/.secret.key"
        self.encryption_key = self._load_or_create_key()
        self.cipher_suite = Fernet(self.encryption_key)

        # 内存清理标记
        self._sensitive_data_refs = []

        logger.info("SecureCardDataHandler initialized with encryption ready")
    
    def _generate_master_password(self) -> str:
        """生成安全的主密码"""
        return secrets.token_urlsafe(32)

    def _load_or_create_key(self) -> bytes:
        """
        加载或创建加密密钥文件

        Returns:
            Fernet兼容的加密密钥
        """
        # 确保密钥文件目录存在
        os.makedirs(os.path.dirname(self.key_file_path), exist_ok=True)

        if os.path.exists(self.key_file_path):
            # 加载现有密钥
            try:
                with open(self.key_file_path, 'rb') as f:
                    return f.read()
            except Exception as e:
                logger.warning(f"Failed to load existing key file: {e}")
                # 如果加载失败，创建新密钥
                return self._create_new_key()
        else:
            # 创建新密钥
            return self._create_new_key()

    def _create_new_key(self) -> bytes:
        """
        创建新的加密密钥并保存到文件

        Returns:
            Fernet兼容的加密密钥
        """
        # 生成Fernet密钥
        key = Fernet.generate_key()

        # 保存密钥到文件
        try:
            with open(self.key_file_path, 'wb') as f:
                f.write(key)

            # 设置文件权限（仅所有者可读写）
            os.chmod(self.key_file_path, 0o600)
            logger.info(f"New encryption key created and saved to {self.key_file_path}")

        except Exception as e:
            logger.error(f"Failed to save encryption key: {e}")
            raise

        return key
    
    def encrypt_data(self, data: Union[str, bytes]) -> str:
        """
        加密敏感数据
        
        Args:
            data: 待加密的数据
            
        Returns:
            base64编码的加密数据
        """
        try:
            if isinstance(data, str):
                data = data.encode('utf-8')
            
            encrypted_data = self.cipher_suite.encrypt(data)
            return base64.urlsafe_b64encode(encrypted_data).decode('utf-8')
            
        except Exception as e:
            logger.error(f"Encryption failed: {e}")
            raise
    
    def decrypt_data(self, encrypted_data: str) -> str:
        """
        解密数据
        
        Args:
            encrypted_data: base64编码的加密数据
            
        Returns:
            解密后的原始数据
        """
        try:
            encrypted_bytes = base64.urlsafe_b64decode(encrypted_data.encode('utf-8'))
            decrypted_data = self.cipher_suite.decrypt(encrypted_bytes)
            return decrypted_data.decode('utf-8')
            
        except Exception as e:
            logger.error(f"Decryption failed: {e}")
            raise
    
    def encrypt_card_number(self, card_number: str) -> Dict[str, str]:
        """
        专门用于信用卡号的加密处理
        保留前6位和后4位用于BIN识别和显示
        
        Args:
            card_number: 信用卡号
            
        Returns:
            包含加密数据和显示格式的字典
        """
        # 数据验证
        clean_number = ''.join(filter(str.isdigit, card_number))
        if len(clean_number) < 13 or len(clean_number) > 19:
            raise ValueError("Invalid card number length")
        
        # 加密完整卡号
        encrypted_full = self.encrypt_data(clean_number)
        
        # 生成显示格式（PCI DSS合规）
        if len(clean_number) >= 10:
            masked_display = clean_number[:6] + '*' * (len(clean_number) - 10) + clean_number[-4:]
        else:
            masked_display = '*' * len(clean_number)
        
        return {
            'encrypted_full': encrypted_full,
            'masked_display': masked_display,
            'bin': clean_number[:6],  # BIN码用于卡片类型识别
            'last4': clean_number[-4:] if len(clean_number) >= 4 else clean_number
        }
    
    def hash_sensitive_data(self, data: str) -> str:
        """
        对敏感数据进行不可逆哈希
        用于数据完整性验证
        """
        return hashlib.sha256(data.encode('utf-8')).hexdigest()
    
    def secure_wipe_memory(self):
        """
        安全清理内存中的敏感数据
        """
        try:
            # 覆写敏感变量
            if hasattr(self, 'master_password'):
                self.master_password = '0' * len(self.master_password)
            
            # 清理引用
            for ref in self._sensitive_data_refs:
                if hasattr(self, ref):
                    setattr(self, ref, None)
            
            logger.info("Memory securely wiped")
            
        except Exception as e:
            logger.error(f"Memory wipe failed: {e}")
    
    def __del__(self):
        """析构函数，确保敏感数据被清理"""
        self.secure_wipe_memory()


class FileEncryption:
    """
    文件级加密处理器
    用于加密存储和读取文件
    """
    
    def __init__(self, encryption_handler: SecureCardDataHandler):
        self.encryption_handler = encryption_handler
    
    def encrypt_file_content(self, file_path: str, output_path: str = None) -> str:
        """
        加密文件内容
        
        Args:
            file_path: 源文件路径
            output_path: 输出文件路径，默认为源文件路径+.encrypted
            
        Returns:
            加密文件路径
        """
        if output_path is None:
            output_path = file_path + '.encrypted'
        
        try:
            # 读取原文件
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 加密内容
            encrypted_content = self.encryption_handler.encrypt_data(content)
            
            # 写入加密文件
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(encrypted_content)
            
            logger.info(f"File encrypted: {file_path} -> {output_path}")
            return output_path
            
        except Exception as e:
            logger.error(f"File encryption failed: {e}")
            raise
    
    def decrypt_file_content(self, encrypted_file_path: str) -> str:
        """
        解密文件内容
        
        Args:
            encrypted_file_path: 加密文件路径
            
        Returns:
            解密后的文件内容
        """
        try:
            with open(encrypted_file_path, 'r', encoding='utf-8') as f:
                encrypted_content = f.read()
            
            decrypted_content = self.encryption_handler.decrypt_data(encrypted_content)
            return decrypted_content
            
        except Exception as e:
            logger.error(f"File decryption failed: {e}")
            raise
    
    def secure_delete_file(self, file_path: str, passes: int = 3):
        """
        安全删除文件
        多次覆写文件内容后删除

        Args:
            file_path: 文件路径
            passes: 覆写次数
        """
        try:
            if not os.path.exists(file_path):
                return

            file_size = os.path.getsize(file_path)

            with open(file_path, 'r+b') as f:
                for _ in range(passes):
                    f.seek(0)
                    f.write(os.urandom(file_size))
                    f.flush()
                    os.fsync(f.fileno())

            os.remove(file_path)
            logger.info(f"File securely deleted: {file_path}")

        except Exception as e:
            logger.error(f"Secure file deletion failed: {e}")
            raise

    def secure_wipe_file_content(self, file_path: str, passes: int = 3):
        """
        安全清空文件内容但保留文件

        Args:
            file_path: 文件路径
            passes: 覆写次数
        """
        return self.encryption_handler.secure_wipe_file_content(file_path, passes)

    def secure_wipe_file_content(self, file_path: str, passes: int = 3):
        """
        安全清空文件内容但保留文件
        多次覆写文件内容后清空

        Args:
            file_path: 文件路径
            passes: 覆写次数
        """
        try:
            if not os.path.exists(file_path):
                return

            file_size = os.path.getsize(file_path)

            if file_size > 0:
                # 多次覆写原有内容
                with open(file_path, 'r+b') as f:
                    for _ in range(passes):
                        f.seek(0)
                        f.write(os.urandom(file_size))
                        f.flush()
                        os.fsync(f.fileno())

            # 清空文件内容
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write('')
                f.flush()
                os.fsync(f.fileno())

            logger.info(f"File content securely wiped: {file_path}")

        except Exception as e:
            logger.error(f"Secure file content wipe failed: {e}")
            raise
