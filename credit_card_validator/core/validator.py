"""
信用卡校验核心模块
实现Luhn算法、BIN识别、格式验证等核心功能
"""

import re
import json
from typing import Dict, List, Optional, Tuple
from datetime import datetime, date
from dataclasses import dataclass, asdict
import logging

logger = logging.getLogger(__name__)

@dataclass
class CardValidationResult:
    """信用卡验证结果数据类"""
    card_number_masked: str
    is_valid: bool
    card_brand: str
    card_type: str
    luhn_valid: bool
    format_valid: bool
    expiry_valid: bool
    cvv_valid: bool
    bin_info: Dict
    risk_score: float
    validation_timestamp: str
    errors: List[str]
    
    def to_dict(self) -> Dict:
        """转换为字典格式"""
        return asdict(self)
    
    def to_json(self) -> str:
        """转换为JSON格式"""
        return json.dumps(self.to_dict(), indent=2, ensure_ascii=False)


class LuhnValidator:
    """Luhn算法验证器"""
    
    @staticmethod
    def validate(card_number: str) -> bool:
        """
        Luhn算法验证
        
        Args:
            card_number: 信用卡号
            
        Returns:
            验证结果
        """
        try:
            # 清理输入，只保留数字
            digits = [int(d) for d in card_number if d.isdigit()]
            
            if len(digits) < 2:
                return False
            
            # Luhn算法实现
            checksum = 0
            is_even = False
            
            # 从右到左处理每一位
            for digit in reversed(digits):
                if is_even:
                    digit *= 2
                    if digit > 9:
                        digit = digit // 10 + digit % 10
                
                checksum += digit
                is_even = not is_even
            
            return checksum % 10 == 0
            
        except Exception as e:
            logger.error(f"Luhn validation error: {e}")
            return False
    
    @staticmethod
    def generate_check_digit(partial_number: str) -> int:
        """
        生成Luhn校验位
        
        Args:
            partial_number: 不含校验位的卡号
            
        Returns:
            校验位
        """
        try:
            digits = [int(d) for d in partial_number if d.isdigit()]
            checksum = 0
            is_even = True
            
            for digit in reversed(digits):
                if is_even:
                    digit *= 2
                    if digit > 9:
                        digit = digit // 10 + digit % 10
                
                checksum += digit
                is_even = not is_even
            
            return (10 - (checksum % 10)) % 10
            
        except Exception as e:
            logger.error(f"Check digit generation error: {e}")
            return 0


class BINIdentifier:
    """BIN码识别器"""
    
    # 主要卡片品牌的BIN规则
    CARD_PATTERNS = {
        'visa': {
            'pattern': re.compile(r'^4[0-9]{12}(?:[0-9]{3})?$'),
            'lengths': [13, 16, 19],
            'cvv_length': 3,
            'name': 'Visa'
        },
        'mastercard': {
            'pattern': re.compile(r'^5[1-5][0-9]{14}$|^2(?:2(?:2[1-9]|[3-9][0-9])|[3-6][0-9][0-9]|7(?:[01][0-9]|20))[0-9]{12}$'),
            'lengths': [16],
            'cvv_length': 3,
            'name': 'Mastercard'
        },
        'amex': {
            'pattern': re.compile(r'^3[47][0-9]{13}$'),
            'lengths': [15],
            'cvv_length': 4,
            'name': 'American Express'
        },
        'discover': {
            'pattern': re.compile(r'^6(?:011|5[0-9]{2})[0-9]{12}$'),
            'lengths': [16],
            'cvv_length': 3,
            'name': 'Discover'
        },
        'jcb': {
            'pattern': re.compile(r'^(?:2131|1800|35\d{3})\d{11}$'),
            'lengths': [16],
            'cvv_length': 3,
            'name': 'JCB'
        },
        'diners': {
            'pattern': re.compile(r'^3[0689][0-9]{12}$'),
            'lengths': [14],
            'cvv_length': 3,
            'name': 'Diners Club'
        }
    }
    
    @classmethod
    def identify_card_brand(cls, card_number: str) -> Dict:
        """
        识别信用卡品牌
        
        Args:
            card_number: 信用卡号
            
        Returns:
            卡片信息字典
        """
        clean_number = ''.join(filter(str.isdigit, card_number))
        
        for brand_key, brand_info in cls.CARD_PATTERNS.items():
            if brand_info['pattern'].match(clean_number):
                return {
                    'brand': brand_key,
                    'name': brand_info['name'],
                    'valid_lengths': brand_info['lengths'],
                    'cvv_length': brand_info['cvv_length'],
                    'length_valid': len(clean_number) in brand_info['lengths']
                }
        
        return {
            'brand': 'unknown',
            'name': 'Unknown',
            'valid_lengths': [],
            'cvv_length': 3,
            'length_valid': False
        }
    
    @classmethod
    def get_bin_info(cls, card_number: str) -> Dict:
        """
        获取BIN码信息
        
        Args:
            card_number: 信用卡号
            
        Returns:
            BIN信息字典
        """
        clean_number = ''.join(filter(str.isdigit, card_number))
        
        if len(clean_number) < 6:
            return {'bin': '', 'valid': False}
        
        bin_code = clean_number[:6]
        brand_info = cls.identify_card_brand(clean_number)
        
        return {
            'bin': bin_code,
            'valid': True,
            'brand': brand_info['brand'],
            'brand_name': brand_info['name'],
            'mii': clean_number[0] if clean_number else '',  # Major Industry Identifier
            'issuer_category': cls._get_issuer_category(clean_number[0] if clean_number else '')
        }
    
    @staticmethod
    def _get_issuer_category(mii: str) -> str:
        """根据MII获取发卡机构类别"""
        mii_categories = {
            '1': 'Airlines',
            '2': 'Airlines and other future industry assignments',
            '3': 'Travel and entertainment',
            '4': 'Banking and financial',
            '5': 'Banking and financial',
            '6': 'Merchandising and banking/financial',
            '7': 'Petroleum and other future industry assignments',
            '8': 'Healthcare, telecommunications and other future industry assignments',
            '9': 'For assignment by national standards bodies'
        }
        return mii_categories.get(mii, 'Unknown')


class ExpiryValidator:
    """有效期验证器"""
    
    @staticmethod
    def validate_expiry(month: str, year: str) -> Tuple[bool, str]:
        """
        验证信用卡有效期
        
        Args:
            month: 月份 (MM格式)
            year: 年份 (YY或YYYY格式)
            
        Returns:
            (是否有效, 错误信息)
        """
        try:
            # 格式验证
            if not month.isdigit() or not year.isdigit():
                return False, "Invalid format: month and year must be numeric"
            
            month_int = int(month)
            year_int = int(year)
            
            # 月份范围验证
            if month_int < 1 or month_int > 12:
                return False, "Invalid month: must be between 01 and 12"
            
            # 年份格式处理
            if len(year) == 2:
                # YY格式，假设20YY
                current_year = datetime.now().year
                century = (current_year // 100) * 100
                year_int = century + year_int
                
                # 如果年份小于当前年份，可能是下个世纪
                if year_int < current_year:
                    year_int += 100
            
            # 过期验证
            expiry_date = date(year_int, month_int, 1)
            current_date = date.today().replace(day=1)  # 当月第一天
            
            if expiry_date < current_date:
                return False, f"Card expired: {month}/{year}"
            
            # 未来时间合理性验证（不超过20年）
            max_future_date = date.today().replace(year=date.today().year + 20)
            if expiry_date > max_future_date:
                return False, "Invalid expiry date: too far in the future"
            
            return True, ""
            
        except Exception as e:
            return False, f"Expiry validation error: {str(e)}"


class CVVValidator:
    """CVV验证器"""
    
    @staticmethod
    def validate_cvv(cvv: str, expected_length: int = 3) -> Tuple[bool, str]:
        """
        验证CVV码
        
        Args:
            cvv: CVV码
            expected_length: 期望长度
            
        Returns:
            (是否有效, 错误信息)
        """
        try:
            # 基本格式验证
            if not cvv.isdigit():
                return False, "CVV must contain only digits"
            
            # 长度验证
            if len(cvv) != expected_length:
                return False, f"CVV length must be {expected_length} digits"
            
            # 简单模式验证（避免明显无效的CVV）
            if cvv == '000' or cvv == '0000':
                return False, "Invalid CVV pattern"
            
            return True, ""
            
        except Exception as e:
            return False, f"CVV validation error: {str(e)}"


class CreditCardValidator:
    """信用卡综合验证器"""
    
    def __init__(self):
        self.luhn_validator = LuhnValidator()
        self.bin_identifier = BINIdentifier()
        self.expiry_validator = ExpiryValidator()
        self.cvv_validator = CVVValidator()
    
    def validate_card(self, card_number: str, expiry_month: str = "", 
                     expiry_year: str = "", cvv: str = "") -> CardValidationResult:
        """
        综合验证信用卡信息
        
        Args:
            card_number: 信用卡号
            expiry_month: 有效期月份
            expiry_year: 有效期年份
            cvv: CVV码
            
        Returns:
            验证结果对象
        """
        errors = []
        
        # 清理卡号
        clean_number = ''.join(filter(str.isdigit, card_number))
        
        # 基本格式验证
        format_valid = self._validate_basic_format(clean_number, errors)
        
        # Luhn算法验证
        luhn_valid = self.luhn_validator.validate(clean_number)
        if not luhn_valid:
            errors.append("Luhn algorithm validation failed")
        
        # BIN识别和品牌验证
        brand_info = self.bin_identifier.identify_card_brand(clean_number)
        bin_info = self.bin_identifier.get_bin_info(clean_number)
        
        # 有效期验证
        expiry_valid = True
        if expiry_month and expiry_year:
            expiry_valid, expiry_error = self.expiry_validator.validate_expiry(
                expiry_month, expiry_year
            )
            if not expiry_valid:
                errors.append(expiry_error)
        
        # CVV验证
        cvv_valid = True
        if cvv:
            expected_cvv_length = brand_info.get('cvv_length', 3)
            cvv_valid, cvv_error = self.cvv_validator.validate_cvv(cvv, expected_cvv_length)
            if not cvv_valid:
                errors.append(cvv_error)
        
        # 计算风险评分
        risk_score = self._calculate_risk_score(
            luhn_valid, format_valid, brand_info, expiry_valid, cvv_valid
        )
        
        # 生成掩码显示
        masked_number = self._mask_card_number(clean_number)
        
        # 综合验证结果
        is_valid = luhn_valid and format_valid and brand_info['length_valid']
        
        return CardValidationResult(
            card_number_masked=masked_number,
            is_valid=is_valid,
            card_brand=brand_info['brand'],
            card_type=brand_info['name'],
            luhn_valid=luhn_valid,
            format_valid=format_valid,
            expiry_valid=expiry_valid,
            cvv_valid=cvv_valid,
            bin_info=bin_info,
            risk_score=risk_score,
            validation_timestamp=datetime.now().isoformat(),
            errors=errors
        )
    
    def _validate_basic_format(self, card_number: str, errors: List[str]) -> bool:
        """基本格式验证"""
        if not card_number:
            errors.append("Card number is empty")
            return False
        
        if not card_number.isdigit():
            errors.append("Card number contains non-digit characters")
            return False
        
        if len(card_number) < 13 or len(card_number) > 19:
            errors.append(f"Invalid card number length: {len(card_number)}")
            return False
        
        return True
    
    def _calculate_risk_score(self, luhn_valid: bool, format_valid: bool, 
                            brand_info: Dict, expiry_valid: bool, cvv_valid: bool) -> float:
        """计算风险评分 (0-1, 越低越安全)"""
        score = 0.0
        
        if not luhn_valid:
            score += 0.4
        if not format_valid:
            score += 0.3
        if not brand_info['length_valid']:
            score += 0.2
        if not expiry_valid:
            score += 0.05
        if not cvv_valid:
            score += 0.05
        
        return min(1.0, score)
    
    def _mask_card_number(self, card_number: str) -> str:
        """生成掩码卡号"""
        if len(card_number) < 10:
            return '*' * len(card_number)
        
        return card_number[:6] + '*' * (len(card_number) - 10) + card_number[-4:]
