#!/usr/bin/env python3
"""
Prometheus指标系统 - Quick-Wins优化版本
与现有 SimpleMonitor 系统集成的生产级监控指标

核心功能:
1. Prometheus指标定义和暴露
2. 与现有 SimpleMonitor 系统无缝集成
3. HTTP服务器在9100端口提供指标抓取
4. 自动化指标收集和更新
5. Grafana Dashboard 数据源

设计原则:
- 低开销: <5ms/操作的指标收集开销
- 实时性: 指标实时更新，支持Prometheus抓取
- 兼容性: 与现有监控系统无缝集成
- 可扩展: 易于添加新的业务指标
"""

import threading
import time
from typing import Dict, Any, Optional, List
import logging
from pathlib import Path
import sys

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from prometheus_client import (
        Counter, Histogram, Gauge, Info, Enum,
        start_http_server, CollectorRegistry, 
        generate_latest, CONTENT_TYPE_LATEST
    )
    PROMETHEUS_AVAILABLE = True
except ImportError:
    PROMETHEUS_AVAILABLE = False
    print("⚠️ prometheus-client未安装，指标功能将不可用")
    
    # 创建Mock类避免NameError
    class CollectorRegistry:
        pass

try:
    from infrastructure.simple_monitor import SimpleMonitor, MonitoringRecord
    SIMPLE_MONITOR_AVAILABLE = True
except ImportError:
    SIMPLE_MONITOR_AVAILABLE = False
    print("⚠️ SimpleMonitor未找到，将使用独立模式")


class HuluMetrics:
    """
    Hulu自动化系统的Prometheus指标管理器
    
    指标类别:
    1. 任务指标 - 整体任务成功率和执行时间
    2. 选择器指标 - 页面元素定位的成功率和延迟
    3. 阶段指标 - 登录流程各阶段的性能指标
    4. reCAPTCHA指标 - 验证码处理的成功率和分数分布
    5. 系统指标 - 系统健康状态和资源使用
    """
    
    def __init__(self, 
                 port: int = 9100,
                 registry: Optional[CollectorRegistry] = None,
                 auto_start_server: bool = True):
        """
        初始化指标管理器
        
        Args:
            port: HTTP服务器端口 (默认9100)
            registry: Prometheus注册表，None表示使用默认
            auto_start_server: 是否自动启动HTTP服务器
        """
        self.port = port
        self.registry = registry
        self.logger = logging.getLogger('metrics')
        self._server_started = False
        self._lock = threading.Lock()
        
        if not PROMETHEUS_AVAILABLE:
            self.logger.warning("❌ Prometheus client 不可用，指标功能禁用")
            return
        
        # 初始化指标
        self._setup_metrics()
        
        # 启动HTTP服务器
        if auto_start_server:
            self.start_server()
        
        self.logger.info(f"📊 Prometheus指标系统已初始化，端口: {port}")
    
    def _setup_metrics(self):
        """设置所有Prometheus指标"""
        
        # === 任务级别指标 ===
        
        # 任务总数计数器 (按阶段和状态分组)
        self.tasks_total = Counter(
            'hulu_tasks_total',
            'Total number of automation tasks executed',
            ['phase', 'status'],  # labels: phase=welcome_page|login_page|etc, status=success|failure
            registry=self.registry
        )
        
        # 任务执行时间直方图
        self.task_duration = Histogram(
            'hulu_task_duration_seconds',
            'Duration of automation tasks',
            ['phase'],  # labels: phase
            buckets=[0.1, 0.5, 1.0, 2.0, 5.0, 10.0, 30.0, 60.0],
            registry=self.registry
        )
        
        # === 选择器级别指标 ===
        
        # 选择器失败计数器
        self.selector_failures = Counter(
            'hulu_selector_failures_total',
            'Total number of selector failures',
            ['selector_name', 'phase'],  # labels: selector_name, phase
            registry=self.registry
        )
        
        # 选择器查找时间直方图
        self.selector_duration = Histogram(
            'hulu_selector_duration_seconds',
            'Time spent finding selectors',
            ['phase'],
            buckets=[0.01, 0.05, 0.1, 0.2, 0.5, 1.0, 2.0, 5.0],
            registry=self.registry
        )
        
        # === reCAPTCHA指标 ===
        
        # reCAPTCHA分数分布
        self.recaptcha_score = Histogram(
            'hulu_recaptcha_score',
            'Distribution of reCAPTCHA scores',
            buckets=[0.1, 0.3, 0.5, 0.7, 0.9, 1.0],
            registry=self.registry
        )
        
        # reCAPTCHA处理计数器
        self.recaptcha_processed = Counter(
            'hulu_recaptcha_processed_total',
            'Total number of reCAPTCHA challenges processed',
            ['result'],  # labels: result=success|failure|timeout
            registry=self.registry
        )
        
        # === 系统指标 ===
        
        # 当前活跃任务数
        self.active_tasks = Gauge(
            'hulu_active_tasks',
            'Number of currently active automation tasks',
            registry=self.registry
        )
        
        # 系统健康状态
        self.system_health = Enum(
            'hulu_system_health',
            'Overall system health status',
            states=['healthy', 'degraded', 'unhealthy'],
            registry=self.registry
        )
        
        # 最后成功任务时间
        self.last_success_timestamp = Gauge(
            'hulu_last_success_timestamp_seconds',
            'Timestamp of last successful task completion',
            registry=self.registry
        )
        
        # === 性能指标 ===
        
        # iframe检测性能 (Layer 0优化相关)
        self.iframe_detection_duration = Histogram(
            'hulu_iframe_detection_duration_seconds',
            'Duration of iframe detection operations',
            buckets=[0.001, 0.005, 0.01, 0.05, 0.1, 0.2, 0.5, 1.0],
            registry=self.registry
        )
        
        # 宿主发现性能
        self.host_discovery_duration = Histogram(
            'hulu_host_discovery_duration_seconds',
            'Duration of host discovery operations',
            buckets=[0.01, 0.05, 0.1, 0.2, 0.5, 1.0, 2.0, 5.0],
            registry=self.registry
        )
        
        # === 应用信息 ===
        
        # 应用信息标签
        self.app_info = Info(
            'hulu_app_info',
            'Application information',
            registry=self.registry
        )
        self.app_info.info({
            'version': '2.1.0',
            'component': 'hulu-automation',
            'build_date': time.strftime('%Y-%m-%d'),
            'python_version': f"{sys.version_info.major}.{sys.version_info.minor}"
        })
        
        # 初始化系统健康状态
        self.system_health.state('healthy')
        
        self.logger.info("✅ Prometheus指标定义完成")
    
    def start_server(self) -> bool:
        """
        启动Prometheus HTTP服务器
        
        Returns:
            bool: 启动是否成功
        """
        if not PROMETHEUS_AVAILABLE:
            self.logger.error("❌ 无法启动服务器：Prometheus client 不可用")
            return False
        
        with self._lock:
            if self._server_started:
                self.logger.warning(f"⚠️ HTTP服务器已在端口 {self.port} 运行")
                return True
            
            try:
                start_http_server(self.port, registry=self.registry)
                self._server_started = True
                self.logger.info(f"🚀 Prometheus HTTP服务器已启动，端口: {self.port}")
                self.logger.info(f"📊 指标访问URL: http://localhost:{self.port}/metrics")
                return True
                
            except OSError as e:
                self.logger.error(f"❌ 服务器启动失败: {e}")
                return False
    
    def record_task_completion(self, phase: str, success: bool, duration_seconds: float):
        """
        记录任务完成指标
        
        Args:
            phase: 任务阶段
            success: 是否成功
            duration_seconds: 执行时间（秒）
        """
        if not PROMETHEUS_AVAILABLE:
            return
        
        status = 'success' if success else 'failure'
        
        # 更新计数器
        self.tasks_total.labels(phase=phase, status=status).inc()
        
        # 更新执行时间
        self.task_duration.labels(phase=phase).observe(duration_seconds)
        
        # 更新最后成功时间
        if success:
            self.last_success_timestamp.set(time.time())
    
    def record_selector_operation(self, phase: str, selector_name: str, 
                                 success: bool, duration_seconds: float):
        """
        记录选择器操作指标
        
        Args:
            phase: 操作阶段
            selector_name: 选择器名称
            success: 是否成功找到
            duration_seconds: 查找时间（秒）
        """
        if not PROMETHEUS_AVAILABLE:
            return
        
        # 记录查找时间
        self.selector_duration.labels(phase=phase).observe(duration_seconds)
        
        # 记录失败情况
        if not success:
            self.selector_failures.labels(
                selector_name=selector_name, 
                phase=phase
            ).inc()
    
    def record_recaptcha_score(self, score: float, success: bool = True):
        """
        记录reCAPTCHA分数
        
        Args:
            score: reCAPTCHA分数 (0.0-1.0)
            success: 处理是否成功
        """
        if not PROMETHEUS_AVAILABLE:
            return
        
        # 记录分数分布
        self.recaptcha_score.observe(score)
        
        # 记录处理结果
        result = 'success' if success else 'failure'
        self.recaptcha_processed.labels(result=result).inc()
    
    def record_performance_metric(self, operation: str, duration_seconds: float):
        """
        记录性能指标
        
        Args:
            operation: 操作类型 ('iframe_detection', 'host_discovery')
            duration_seconds: 执行时间（秒）
        """
        if not PROMETHEUS_AVAILABLE:
            return
        
        if operation == 'iframe_detection':
            self.iframe_detection_duration.observe(duration_seconds)
        elif operation == 'host_discovery':
            self.host_discovery_duration.observe(duration_seconds)
    
    def update_active_tasks(self, count: int):
        """
        更新活跃任务数
        
        Args:
            count: 当前活跃任务数
        """
        if not PROMETHEUS_AVAILABLE:
            return
        
        self.active_tasks.set(count)
    
    def update_system_health(self, status: str):
        """
        更新系统健康状态
        
        Args:
            status: 健康状态 ('healthy', 'degraded', 'unhealthy')
        """
        if not PROMETHEUS_AVAILABLE:
            return
        
        if status in ['healthy', 'degraded', 'unhealthy']:
            self.system_health.state(status)
        else:
            self.logger.warning(f"⚠️ 无效的健康状态: {status}")
    
    def get_metrics_text(self) -> str:
        """
        获取当前指标的文本格式 (用于调试)
        
        Returns:
            str: Prometheus格式的指标文本
        """
        if not PROMETHEUS_AVAILABLE:
            return "Prometheus client not available"
        
        return generate_latest(self.registry).decode('utf-8')
    
    def is_server_running(self) -> bool:
        """检查HTTP服务器是否运行"""
        return self._server_started


class SimpleMonitorIntegration:
    """
    SimpleMonitor 与 Prometheus 指标集成器
    
    将现有的 SimpleMonitor 数据自动转换为 Prometheus 指标
    """
    
    def __init__(self, 
                 metrics: HuluMetrics,
                 monitor: Optional['SimpleMonitor'] = None,
                 sync_interval: float = 60.0):
        """
        初始化集成器
        
        Args:
            metrics: HuluMetrics实例
            monitor: SimpleMonitor实例，None表示自动创建
            sync_interval: 同步间隔（秒）
        """
        self.metrics = metrics
        self.sync_interval = sync_interval
        self.logger = logging.getLogger('monitor_integration')
        
        if SIMPLE_MONITOR_AVAILABLE and monitor is None:
            self.monitor = SimpleMonitor()
        else:
            self.monitor = monitor
        
        self._last_sync = 0
        self._running = False
        self._thread = None
        
        if self.monitor:
            self.logger.info("🔗 SimpleMonitor集成已初始化")
    
    def sync_metrics(self):
        """同步 SimpleMonitor 数据到 Prometheus 指标"""
        if not self.monitor or not PROMETHEUS_AVAILABLE:
            return
        
        try:
            # 获取最近的统计数据
            recent_stats = {
                'iframe_detection': self.monitor.get_recent_stats('iframe_detection', 10),
                'host_discovery': self.monitor.get_recent_stats('host_discovery', 10),
                'element_location': self.monitor.get_recent_stats('element_location', 10)
            }
            
            # 更新系统健康状态
            overall_success_rate = 0
            total_operations = 0
            
            for operation, stats in recent_stats.items():
                if stats['count'] > 0:
                    success_rate = stats['success_rate']
                    total_operations += stats['count']
                    overall_success_rate += success_rate * stats['count']
                    
                    # 更新性能指标
                    if operation in ['iframe_detection', 'host_discovery']:
                        # 这里我们模拟记录平均时间作为单次观察
                        avg_duration = stats['avg_duration_ms'] / 1000.0
                        self.metrics.record_performance_metric(operation, avg_duration)
            
            # 计算整体健康状态
            if total_operations > 0:
                overall_success_rate /= total_operations
                
                if overall_success_rate >= 0.95:
                    health_status = 'healthy'
                elif overall_success_rate >= 0.80:
                    health_status = 'degraded'
                else:
                    health_status = 'unhealthy'
                
                self.metrics.update_system_health(health_status)
            
            self._last_sync = time.time()
            self.logger.debug(f"📊 指标同步完成，操作数: {total_operations}")
            
        except Exception as e:
            self.logger.error(f"❌ 指标同步失败: {e}")
    
    def start_background_sync(self):
        """启动后台同步线程"""
        if self._running:
            self.logger.warning("⚠️ 后台同步已在运行")
            return
        
        self._running = True
        self._thread = threading.Thread(target=self._sync_loop, daemon=True)
        self._thread.start()
        
        self.logger.info(f"🔄 后台指标同步已启动，间隔: {self.sync_interval}秒")
    
    def stop_background_sync(self):
        """停止后台同步"""
        self._running = False
        if self._thread:
            self._thread.join(timeout=5)
        
        self.logger.info("⏹️ 后台指标同步已停止")
    
    def _sync_loop(self):
        """后台同步循环"""
        while self._running:
            try:
                self.sync_metrics()
                time.sleep(self.sync_interval)
            except Exception as e:
                self.logger.error(f"❌ 同步循环异常: {e}")
                time.sleep(5)  # 出错时短暂等待


# 全局实例管理
_global_metrics: Optional[HuluMetrics] = None
_global_integration: Optional[SimpleMonitorIntegration] = None

def get_metrics(port: int = 9100, auto_start: bool = True) -> Optional[HuluMetrics]:
    """
    获取全局指标实例
    
    Args:
        port: HTTP服务器端口
        auto_start: 是否自动启动服务器
        
    Returns:
        HuluMetrics: 指标实例，如果Prometheus不可用则返回None
    """
    global _global_metrics
    
    if not PROMETHEUS_AVAILABLE:
        return None
    
    if _global_metrics is None:
        _global_metrics = HuluMetrics(port=port, auto_start_server=auto_start)
    
    return _global_metrics

def get_integration(sync_interval: float = 60.0) -> Optional[SimpleMonitorIntegration]:
    """
    获取全局集成器实例
    
    Args:
        sync_interval: 同步间隔（秒）
        
    Returns:
        SimpleMonitorIntegration: 集成器实例
    """
    global _global_integration
    
    metrics = get_metrics()
    if metrics is None:
        return None
    
    if _global_integration is None:
        _global_integration = SimpleMonitorIntegration(
            metrics=metrics, 
            sync_interval=sync_interval
        )
        # 启动后台同步
        _global_integration.start_background_sync()
    
    return _global_integration

def start_metrics_server(port: int = 9100) -> bool:
    """
    启动Prometheus指标服务器 (便捷函数)
    
    Args:
        port: 服务器端口
        
    Returns:
        bool: 启动是否成功
    """
    metrics = get_metrics(port=port, auto_start=True)
    return metrics is not None and metrics.is_server_running()


if __name__ == "__main__":
    # 测试代码
    print("🧪 测试Prometheus指标系统...")
    
    if not PROMETHEUS_AVAILABLE:
        print("❌ prometheus-client未安装，跳过测试")
        sys.exit(1)
    
    # 创建指标实例
    metrics = HuluMetrics(port=9101, auto_start_server=False)  # 使用不同端口避免冲突
    
    # 测试指标记录
    print("📊 记录测试指标...")
    metrics.record_task_completion("welcome_page", True, 1.5)
    metrics.record_task_completion("login_page", False, 3.2)
    metrics.record_selector_operation("email_detection", "#email-field", True, 0.123)
    metrics.record_recaptcha_score(0.8)
    metrics.update_active_tasks(3)
    metrics.update_system_health("healthy")
    
    # 启动服务器
    print("🚀 启动HTTP服务器...")
    success = metrics.start_server()
    
    if success:
        print(f"✅ 指标服务器启动成功: http://localhost:9101/metrics")
        print("📊 测试指标输出:")
        print(metrics.get_metrics_text()[:500] + "...")
        
        # 测试集成器
        if SIMPLE_MONITOR_AVAILABLE:
            print("🔗 测试SimpleMonitor集成...")
            integration = SimpleMonitorIntegration(metrics)
            integration.sync_metrics()
            print("✅ 集成测试完成")
        
        print("✅ 所有测试通过！服务器将保持运行...")
        print("💡 使用 curl http://localhost:9101/metrics 查看完整指标")
        
        # 保持运行用于测试
        try:
            import time
            time.sleep(10)
        except KeyboardInterrupt:
            print("\n🛑 用户中断测试")
    else:
        print("❌ 服务器启动失败")
        sys.exit(1)