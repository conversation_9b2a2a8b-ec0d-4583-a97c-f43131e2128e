#!/usr/bin/env python3
"""
错误处理器 - 基础设施层
提供统一的错误处理、截图和重试机制
"""

import logging
import time
import os
from typing import Dict, Any, Callable, Optional
from playwright.sync_api import Page as SyncPage

# from browser_screenshot import BrowserScreenshot  # 模块不存在，已注释
# from utils import CONFIG  # CONFIG不存在，使用默认配置

# 默认配置
_DEFAULT_CONFIG = {
    'SCREENSHOT_BROWSER_KEYWORDS': ['Chrome', 'Firefox', 'Safari'],
    'SCREENSHOT_INCLUDE_ADDRESS_BAR': True,
    'SCREENSHOT_QUALITY': 90
}


class ErrorHandler:
    """错误处理器 - 统一错误处理和恢复机制"""
    
    def __init__(self, page: Optional[SyncPage] = None, headless: bool = True):
        """
        初始化错误处理器
        
        Args:
            page: Playwright页面对象
            headless: 是否无头模式运行
        """
        self.logger = logging.getLogger(__name__)
        self.page = page
        self.headless = headless
        
        # 初始化浏览器截图模块 (已禁用不存在的模块)
        self.browser_screenshot = None  # BrowserScreenshot模块不存在
        
        # 错误统计
        self.error_count = 0
        self.retry_count = 0
        
        self.logger.debug("✅ 错误处理器初始化完成")
    
    def set_page(self, page: SyncPage) -> None:
        """
        设置页面对象
        
        Args:
            page: Playwright页面对象
        """
        self.page = page
    
    def capture_error_screenshot(self, operation: str, error: Exception) -> Dict[str, str]:
        """
        捕获错误截图
        
        Args:
            operation: 操作名称，用于截图文件命名
            error: 异常对象
            
        Returns:
            Dict[str, str]: 包含截图路径的字典
        """
        screenshot_paths = {}
        
        try:
            timestamp = int(time.time())
            
            # 创建截图目录
            screenshot_dir = CONFIG.get('SCREENSHOT_DIRECTORY', 'screenshots')
            os.makedirs(screenshot_dir, exist_ok=True)
            
            # 生成截图文件路径
            page_screenshot_path = os.path.join(screenshot_dir, f"error_{operation}_{timestamp}_page.png")
            browser_screenshot_path = os.path.join(screenshot_dir, f"error_{operation}_{timestamp}_browser.png")
            
            # 方法1：使用Playwright获取页面内容截图
            if self.page:
                try:
                    self.page.screenshot(path=page_screenshot_path, full_page=True)
                    screenshot_paths['page'] = page_screenshot_path
                    self.logger.error(f"📸 页面内容截图已保存: {page_screenshot_path}")
                except Exception as page_error:
                    self.logger.warning(f"⚠️ 页面内容截图失败: {page_error}")
            
            # 方法2：使用浏览器窗口截图（包含地址栏）- 仅在非无头模式下
            if not self.headless and self.browser_screenshot is not None:
                try:
                    success = self.browser_screenshot.capture_browser_window(
                        output_path=browser_screenshot_path,
                        browser_title_keywords=_DEFAULT_CONFIG['SCREENSHOT_BROWSER_KEYWORDS'],
                        include_address_bar=_DEFAULT_CONFIG['SCREENSHOT_INCLUDE_ADDRESS_BAR'],
                        quality=_DEFAULT_CONFIG['SCREENSHOT_QUALITY']
                    )
                    
                    if success:
                        screenshot_paths['browser'] = browser_screenshot_path
                        self.logger.error(f"📸 浏览器窗口截图已保存: {browser_screenshot_path}")
                    else:
                        # 备用方案：使用全屏截图
                        fullscreen_path = os.path.join(screenshot_dir, f"error_{operation}_{timestamp}_fullscreen.png")
                        if self.browser_screenshot._capture_fullscreen(fullscreen_path, 90):
                            screenshot_paths['fullscreen'] = fullscreen_path
                            self.logger.error(f"📸 全屏截图已保存(备用): {fullscreen_path}")
                        
                except Exception as browser_error:
                    self.logger.warning(f"⚠️ 浏览器窗口截图失败: {browser_error}")
            else:
                if self.headless:
                    self.logger.debug("ℹ️ Headless模式下跳过浏览器窗口截图")
                else:
                    self.logger.debug("📸 浏览器截图模块不可用，跳过浏览器窗口截图")
            
            # 记录详细的页面状态信息
            self._log_error_context(operation, error)
            
            self.error_count += 1
            
        except Exception as e:
            self.logger.error(f"截图过程中出错: {e}")
        
        return screenshot_paths
    
    def handle_exception(self, error: Exception, operation: str = "unknown", 
                        capture_screenshot: bool = True) -> Dict[str, Any]:
        """
        处理异常
        
        Args:
            error: 异常对象
            operation: 操作名称
            capture_screenshot: 是否捕获截图
            
        Returns:
            Dict[str, Any]: 错误处理结果
        """
        error_info = {
            'success': False,
            'operation': operation,
            'error': str(error),
            'error_type': type(error).__name__,
            'timestamp': time.strftime("%Y-%m-%d %H:%M:%S"),
            'error_count': self.error_count + 1
        }
        
        # 捕获截图
        if capture_screenshot:
            screenshot_paths = self.capture_error_screenshot(operation, error)
            error_info['screenshots'] = screenshot_paths
        
        # 获取恢复建议
        error_info['recovery_suggestions'] = self._get_recovery_suggestions(error)
        
        # 记录错误
        self.logger.error(f"❌ 异常处理: {operation} - {str(error)}")
        
        self.error_count += 1
        
        return error_info
    
    def retry_operation(self, operation: Callable, max_retries: int = 3, 
                       delay: float = 1.0, backoff_factor: float = 2.0) -> Any:
        """
        重试操作
        
        Args:
            operation: 要重试的操作函数
            max_retries: 最大重试次数
            delay: 初始延迟时间（秒）
            backoff_factor: 退避因子
            
        Returns:
            Any: 操作结果
            
        Raises:
            Exception: 如果所有重试都失败
        """
        last_exception = None
        current_delay = delay
        
        for attempt in range(max_retries + 1):
            try:
                if attempt > 0:
                    self.logger.info(f"🔄 重试操作 {attempt}/{max_retries}，延迟 {current_delay:.1f}s")
                    time.sleep(current_delay)
                    current_delay *= backoff_factor
                
                result = operation()
                
                if attempt > 0:
                    self.logger.info(f"✅ 重试成功，第 {attempt + 1} 次尝试")
                
                return result
                
            except Exception as e:
                last_exception = e
                self.retry_count += 1
                
                if attempt < max_retries:
                    self.logger.warning(f"⚠️ 第 {attempt + 1} 次尝试失败: {str(e)}")
                else:
                    self.logger.error(f"❌ 所有重试都失败，最后错误: {str(e)}")
        
        # 如果所有重试都失败，抛出最后一个异常
        if last_exception:
            raise last_exception
    
    def log_error_context(self, context: Dict) -> None:
        """
        记录错误上下文
        
        Args:
            context: 错误上下文信息
        """
        self.logger.error("🔍 错误上下文信息:")
        for key, value in context.items():
            self.logger.error(f"  {key}: {value}")
    
    def _log_error_context(self, operation: str, error: Exception) -> None:
        """记录详细的错误上下文信息"""
        self.logger.error(f"❌ 操作失败: {operation}")
        self.logger.error(f"💥 错误信息: {str(error)}")
        self.logger.error(f"🔢 错误类型: {type(error).__name__}")
        
        if self.page:
            try:
                self.logger.error(f"🌐 当前URL: {self.page.url}")
            except:
                self.logger.error(f"🌐 当前URL: 无法获取")
            
            try:
                page_title = self.page.title()
                self.logger.error(f"📄 页面标题: {page_title}")
            except:
                self.logger.error(f"📄 页面标题: 无法获取")
    
    def _get_recovery_suggestions(self, error: Exception) -> list:
        """
        获取恢复建议
        
        Args:
            error: 异常对象
            
        Returns:
            list: 恢复建议列表
        """
        error_type = type(error).__name__
        error_message = str(error).lower()
        
        suggestions = []
        
        # 根据错误类型提供建议
        if 'timeout' in error_message:
            suggestions.extend([
                "增加等待时间",
                "检查网络连接",
                "确认页面是否正常加载"
            ])
        
        if 'element' in error_message and 'not found' in error_message:
            suggestions.extend([
                "检查元素选择器是否正确",
                "等待页面完全加载",
                "确认页面结构是否发生变化"
            ])
        
        if 'network' in error_message or 'connection' in error_message:
            suggestions.extend([
                "检查网络连接",
                "重试请求",
                "检查代理设置"
            ])
        
        if 'permission' in error_message:
            suggestions.extend([
                "检查浏览器权限设置",
                "确认是否需要用户交互",
                "检查安全策略"
            ])
        
        # 通用建议
        if not suggestions:
            suggestions.extend([
                "重试操作",
                "检查页面状态",
                "查看详细日志"
            ])
        
        return suggestions
    
    def get_error_statistics(self) -> Dict[str, int]:
        """
        获取错误统计信息
        
        Returns:
            Dict[str, int]: 错误统计
        """
        return {
            'total_errors': self.error_count,
            'total_retries': self.retry_count
        }
    
    def reset_statistics(self) -> None:
        """重置错误统计"""
        self.error_count = 0
        self.retry_count = 0
        self.logger.debug("✅ 错误统计已重置")
