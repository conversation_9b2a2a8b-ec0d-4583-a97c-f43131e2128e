#!/usr/bin/env python3
"""
页面选择器管理器 - 基础设施层
集中管理所有页面元素的选择器，避免硬编码和重复定义
"""

from typing import List


class HuluPageSelectors:
    """
    Hulu页面选择器集中管理
    
    按功能模块组织选择器，提供统一的访问接口
    """
    
    # ================== 登录相关选择器 ==================
    
    # Welcome页面的LOG IN按钮 - 统一使用 {HOST} >> 格式
    WELCOME_LOGIN_BUTTONS: List[str] = [
        # 动态宿主模板选择器 (优先级最高)
        '{HOST} >> button:has-text("Log In")',
        '{HOST} >> button:has-text("LOG IN")',
        '{HOST} >> a:has-text("Log In")',
        '{HOST} >> a:has-text("LOG IN")',
        # 传统选择器 (降级兼容)
        'button:has-text("Log In")',
        'button:has-text("LOG IN")',
        'a:has-text("Log In")',
        'a:has-text("LOG IN")',
        'text="Log In"',
        'text="LOG IN"',
        'nav a[href*="login"]',
        'header a[href*="login"]',
        '[href*="login"]'
    ]
    
    # 登录页面的邮箱字段
    EMAIL_FIELDS: List[str] = [
        '#email-field',          # 实际的ID
        'input[name="email-field"]',  # 实际的name
        '#email_id',             # 备用ID
        'input[id="email_id"]',
        'input[type="email"]',   # 通用选择器
        'input[placeholder="Email"]',
        'input[name="email"]',
        'input[placeholder*="email"]',
        'input[data-testid*="email"]',
        '#email',
        'input[autocomplete="email"]'
    ]
    
    # Continue按钮（分步登录） - 统一使用 {HOST} >> 格式
    CONTINUE_BUTTONS: List[str] = [
        # 动态宿主模板选择器 (优先级最高)
        '{HOST} >> button:has-text("Continue")',
        '{HOST} >> button:has-text("继续")',
        '{HOST} >> button[type="submit"]:has-text("Continue")',
        '{HOST} >> input[type="submit"][value="Continue"]',
        # 传统选择器 (降级兼容)
        'button:has-text("Continue")',
        'button:has-text("继续")',
        'input[type="submit"][value="Continue"]',
        'button[type="submit"]:has-text("Continue")',
        'button[type="submit"]',
        'input[type="submit"]',
        'button:contains("Continue")',
        '[data-testid*="continue"]',
        '.continue-btn',
        '.continue-button',
        '#continue-btn',
        '#continue-button'
    ]
    
    # 密码字段
    PASSWORD_FIELDS: List[str] = [
        "#password",                    # 正确的密码字段ID
        'input[name="password"]',       # 正确的密码字段name
        'input[type="password"]',       # 最通用的选择器
        'input[placeholder*="password"]',  # 占位符包含password
        'input[placeholder*="Password"]',  # 占位符包含Password
        'input[data-testid*="password"]'   # 测试ID包含password
    ]
    
    # 登录提交按钮 - 统一使用 {HOST} >> 格式
    LOGIN_SUBMIT_BUTTONS: List[str] = [
        # 动态宿主模板选择器 (优先级最高)
        '{HOST} >> button:has-text("Log In")',           # 主要登录按钮
        '{HOST} >> button:has-text("LOGIN")',            # 大写版本
        '{HOST} >> button:has-text("Submit")',           # 提交按钮
        '{HOST} >> button[type="submit"]:has-text("Log In")',  # 精确匹配
        '{HOST} >> button[type="submit"]',               # 通用提交按钮
        
        # 传统选择器 (降级兼容) - 基于真实DOM测试结果优化
        'button:has-text("Log In")',                     # 调试脚本验证成功的选择器（最高优先级）
        'button[type="submit"]:has-text("Log In")',      # 精确：type=submit + 文本匹配
        'form button[type="submit"]',                    # 表单中的提交按钮（真实DOM确认）
        'button[type="submit"]',                         # 提交按钮类型
        
        # 基于真实DOM的CSS类选择器（调试脚本发现的实际类名）
        'button.cursor-pointer[type="submit"]',          # 带cursor-pointer类的提交按钮
        'button.border-solid[type="submit"]',            # 带border-solid类的提交按钮
        'button.transition-colors[type="submit"]',       # 带transition-colors类的提交按钮
        'button.rounded-xl[type="submit"]',              # 带rounded-xl类的提交按钮
        'button.flex.items-center[type="submit"]',       # 带flex和items-center类的提交按钮
        
        # 备用选择器（按优先级排序）
        'button:has-text("LOGIN")',                      # 大写文本匹配
        'input[type="submit"]',                          # 输入提交
        'button:has-text("Sign In")',                    # Sign In文本
        'button[data-testid*="login"]',                 # 测试ID包含login
        'button[data-testid*="signin"]',                # 测试ID包含signin
        
        # CSS类名选择器
        '.login-button',                                 # CSS类名
        '.signin-button',                                # CSS类名
        'button[class*="login"]',                        # class包含login
        'button[class*="submit"]',                       # class包含submit
        
        # 属性选择器
        'button[value="Log In"]',                        # 按钮值
        'button[value="LOGIN"]',                         # 按钮值大写
        'button[aria-label*="log in"]',                  # aria-label属性
        'button[aria-label*="login"]',                   # aria-label属性
        
        # 角色选择器（最后备选）
        '[role="button"]:has-text("Log In")',            # 角色为按钮的元素
        '[role="button"]:has-text("LOGIN")',             # 角色为按钮的元素
        'a[role="button"]:has-text("Log In")',           # 链接作为按钮
        'div[role="button"]:has-text("Log In")'          # div作为按钮
    ]
    
    # ================== 验证相关选择器 ==================
    
    # 验证码输入字段
    VERIFICATION_CODE_FIELDS: List[str] = [
        'input[name="verificationCode"]',
        'input[name="verification_code"]',
        'input[name="code"]',
        'input[name="otp"]',           # Hulu的OTP字段
        'input[name="authCode"]',
        'input[placeholder*="code"]',
        'input[placeholder*="verification"]',
        'input[placeholder*="verify"]',
        'input[data-testid*="verification"]',
        'input[data-testid*="code"]',
        'input[maxlength="6"]',        # 6位验证码字段
        'input[type="text"]'           # 通用文本输入
    ]
    
    # 验证提交按钮 - 统一使用 {HOST} >> 格式
    VERIFICATION_SUBMIT_BUTTONS: List[str] = [
        # 动态宿主模板选择器 (优先级最高)
        '{HOST} >> button:has-text("Verify")',
        '{HOST} >> button:has-text("Submit")',
        '{HOST} >> button:has-text("Continue")',
        '{HOST} >> button:has-text("Confirm")',
        '{HOST} >> button[type="submit"]',
        # 传统选择器 (降级兼容)
        'button:has-text("Verify")',
        'button:has-text("Submit")',
        'button:has-text("Continue")',
        'button:has-text("Confirm")',
        'button:has-text("Next")',
        'button[type="submit"]',
        'button[data-testid*="submit"]',
        'button[data-testid*="verify"]',
        'button[data-testid*="continue"]',
        'input[type="submit"]'
    ]
    
    # ================== 登录状态检查选择器 ==================
    
    # 登录成功指示元素
    LOGIN_SUCCESS_ELEMENTS: List[str] = [
        '[data-testid*="profile"]',
        '[data-testid*="account"]',
        '[data-testid*="user"]',
        '.profile-menu',
        '.user-menu',
        '.account-menu',
        'nav[data-testid*="navigation"]',
        '[aria-label*="Profile"]',
        '[aria-label*="Account"]',
        'button[aria-label*="Profile"]',
        '[data-testid="user-menu"]',    # 具体的用户菜单
        '.header-user-menu',            # 头部用户菜单
        '[class*="user-avatar"]'        # 用户头像
    ]
    
    # 登录错误指示元素
    LOGIN_ERROR_ELEMENTS: List[str] = [
        '[data-testid*="error"]',
        '.error-message',
        '.alert-error',
        '[role="alert"]',
        '.notification-error'
    ]
    
    # ================== 注册相关选择器 ==================
    
    # 注册按钮
    REGISTER_BUTTONS: List[str] = [
        'button:has-text("Sign Up")',
        'button:has-text("Get Started")',
        'button:has-text("Create Account")',
        'a:has-text("Sign Up")',
        '[href*="signup"]',
        '[href*="register"]'
    ]
    
    # 用户名字段
    NAME_FIELDS: List[str] = [
        'input[name="first_name"]',
        'input[name="firstName"]',
        'input[name="name"]',
        'input[placeholder*="name"]',
        'input[placeholder*="Name"]',
        'input[data-testid*="name"]'
    ]
    
    @classmethod
    def get_login_selectors(cls) -> dict:
        """
        获取所有登录相关的选择器
        
        Returns:
            dict: 登录选择器字典
        """
        return {
            'welcome_login_buttons': cls.WELCOME_LOGIN_BUTTONS,
            'email_fields': cls.EMAIL_FIELDS,
            'continue_buttons': cls.CONTINUE_BUTTONS,
            'password_fields': cls.PASSWORD_FIELDS,
            'login_submit_buttons': cls.LOGIN_SUBMIT_BUTTONS
        }
    
    @classmethod
    def get_verification_selectors(cls) -> dict:
        """
        获取所有验证相关的选择器
        
        Returns:
            dict: 验证选择器字典
        """
        return {
            'verification_code_fields': cls.VERIFICATION_CODE_FIELDS,
            'verification_submit_buttons': cls.VERIFICATION_SUBMIT_BUTTONS
        }
    
    @classmethod
    def get_status_check_selectors(cls) -> dict:
        """
        获取所有状态检查相关的选择器
        
        Returns:
            dict: 状态检查选择器字典
        """
        return {
            'login_success_elements': cls.LOGIN_SUCCESS_ELEMENTS,
            'login_error_elements': cls.LOGIN_ERROR_ELEMENTS
        }


# 为了向后兼容，提供简化的访问方式
class Selectors:
    """简化的选择器访问接口"""
    
    # 直接访问常用选择器
    LOGIN_BUTTONS = HuluPageSelectors.LOGIN_SUBMIT_BUTTONS
    EMAIL_INPUTS = HuluPageSelectors.EMAIL_FIELDS
    PASSWORD_INPUTS = HuluPageSelectors.PASSWORD_FIELDS
    CONTINUE_BUTTONS = HuluPageSelectors.CONTINUE_BUTTONS
    
    @staticmethod
    def login() -> dict:
        """获取登录相关选择器"""
        return HuluPageSelectors.get_login_selectors()
    
    @staticmethod
    def verification() -> dict:
        """获取验证相关选择器"""
        return HuluPageSelectors.get_verification_selectors()
    
    @staticmethod
    def status() -> dict:
        """获取状态检查相关选择器"""
        return HuluPageSelectors.get_status_check_selectors()