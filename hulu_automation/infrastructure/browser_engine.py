#!/usr/bin/env python3
"""
浏览器引擎 - 基础设施层
提供浏览器生命周期管理和基础操作服务
"""

import logging
import asyncio
import concurrent.futures
from typing import Dict, Optional
from playwright.sync_api import sync_playwright, <PERSON><PERSON><PERSON> as Sync<PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> as Sync<PERSON><PERSON>er<PERSON>ontext, Page as SyncPage
from fake_useragent import UserAgent

from ..data import ConfigurationProvider


class BrowserEngine:
    """浏览器引擎 - 管理浏览器生命周期和基础操作"""
    
    def __init__(self, config_provider: Optional[ConfigurationProvider] = None):
        """
        初始化浏览器引擎
        
        Args:
            config_provider: 配置提供者实例，如果不提供则自动创建
        """
        self.config_provider = config_provider or ConfigurationProvider()
        self.logger = logging.getLogger(__name__)
        
        # 浏览器相关实例
        self.playwright = None
        self.browser: Optional[SyncBrowser] = None
        self.context: Optional[SyncBrowserContext] = None
        self.page: Optional[SyncPage] = None
        
        # User-Agent管理
        self.ua = UserAgent()
        self.current_user_agent = None
        
        # 状态标志
        self._initialized = False
    
    def initialize_browser(self, headless: bool = True, storage_state_path: Optional[str] = None) -> SyncBrowser:
        """
        初始化浏览器实例
        
        Args:
            headless: 是否无头模式运行
            storage_state_path: 存储状态文件路径
            
        Returns:
            SyncBrowser: 浏览器实例
        """
        try:
            if self._initialized:
                self.logger.warning("浏览器已经初始化，跳过重复初始化")
                return self.browser
            
            # 启动Playwright
            self.playwright = sync_playwright().start()
            
            # 生成随机User-Agent
            self.current_user_agent = self.ua.random
            self.logger.info(f"使用随机User-Agent: {self.current_user_agent}")
            
            # 获取浏览器配置
            browser_config = self._get_browser_config(headless)
            
            # 启动浏览器
            self.browser = self.playwright.chromium.launch(**browser_config)
            
            # 创建上下文
            self.context = self.create_context(storage_state_path)
            
            # 创建页面
            self.page = self.context.new_page()
            
            # 设置超时
            self._configure_timeouts()
            
            self._initialized = True
            self.logger.info("✅ 浏览器引擎初始化完成")
            
            return self.browser
            
        except Exception as e:
            self.logger.error(f"浏览器初始化失败: {e}")
            self.cleanup()
            raise Exception(f"浏览器初始化失败: {e}")
    
    def create_context(self, storage_state_path: Optional[str] = None) -> SyncBrowserContext:
        """
        创建浏览器上下文
        
        Args:
            storage_state_path: 存储状态文件路径
            
        Returns:
            SyncBrowserContext: 浏览器上下文
        """
        if not self.browser:
            raise RuntimeError("浏览器未初始化，请先调用initialize_browser()")
        
        # 获取上下文配置
        context_config = self._get_context_config(storage_state_path)
        
        # 创建上下文
        context = self.browser.new_context(**context_config)
        
        self.logger.debug("✅ 浏览器上下文创建完成")
        return context
    
    def navigate_to(self, url: str) -> None:
        """
        导航到指定URL
        
        Args:
            url: 目标URL
        """
        if not self.page:
            raise RuntimeError("页面未创建，请先初始化浏览器")
        
        try:
            self.page.goto(url)
            self.logger.info(f"✅ 成功导航到: {url}")
        except Exception as e:
            self.logger.error(f"导航失败: {e}")
            raise
    
    def apply_fingerprint(self, fingerprint_masking) -> None:
        """
        应用指纹伪装
        
        Args:
            fingerprint_masking: 指纹伪装服务实例
        """
        if not self.page:
            raise RuntimeError("页面未创建，请先初始化浏览器")
        
        try:
            # 检查是否已经有事件循环在运行
            try:
                loop = asyncio.get_running_loop()
                # 如果有循环在运行，使用线程池执行
                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = executor.submit(self._apply_fingerprint_sync, fingerprint_masking)
                    future.result(timeout=10)
            except RuntimeError:
                # 没有循环在运行，可以创建新循环
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                try:
                    loop.run_until_complete(fingerprint_masking.apply_advanced_fingerprint_masking(self.page))
                finally:
                    loop.close()
            
            self.logger.info("✅ 指纹伪装应用成功")
            
        except Exception as e:
            self.logger.warning(f"指纹伪装应用失败: {e}")
    
    def _apply_fingerprint_sync(self, fingerprint_masking):
        """同步方式应用指纹伪装"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(fingerprint_masking.apply_advanced_fingerprint_masking(self.page))
        finally:
            loop.close()
    
    def cleanup(self) -> None:
        """清理浏览器资源"""
        try:
            if self.page:
                self.page.close()
                self.page = None
                
            if self.context:
                self.context.close()
                self.context = None
                
            if self.browser:
                self.browser.close()
                self.browser = None
                
            if self.playwright:
                self.playwright.stop()
                self.playwright = None
                
            self._initialized = False
            self.logger.info("✅ 浏览器引擎清理完成")
            
        except Exception as e:
            self.logger.error(f"浏览器清理失败: {e}")
    
    def _get_browser_config(self, headless: bool) -> Dict:
        """获取浏览器配置"""
        return {
            'headless': headless,
            'args': [
                '--no-sandbox',
                '--disable-blink-features=AutomationControlled',
                '--disable-features=VizDisplayCompositor',
                '--disable-dev-shm-usage',
                '--disable-extensions',
                '--disable-plugins',
                '--disable-images',
                '--disable-javascript',
                '--disable-default-apps',
                '--disable-background-timer-throttling',
                '--disable-backgrounding-occluded-windows',
                '--disable-renderer-backgrounding',
                '--disable-field-trial-config',
                '--disable-back-forward-cache',
                '--disable-ipc-flooding-protection',
                '--no-first-run',
                '--no-default-browser-check',
                '--no-pings',
                '--password-store=basic',
                '--use-mock-keychain',
                f'--user-agent={self.current_user_agent}',
            ],
            'slow_mo': 50  # 轻微减缓操作速度，更像人类
        }
    
    def _get_context_config(self, storage_state_path: Optional[str] = None) -> Dict:
        """获取上下文配置"""
        config = {
            'user_agent': self.current_user_agent,
            'viewport': {'width': 1920, 'height': 1080},
            'java_script_enabled': True,
            'accept_downloads': True,
            'ignore_https_errors': True,
        }
        
        # 如果提供了存储状态路径且文件存在，则加载状态
        if storage_state_path:
            import os
            if os.path.exists(storage_state_path):
                config['storage_state'] = storage_state_path
                self.logger.debug(f"加载存储状态: {storage_state_path}")
        
        return config
    
    def _configure_timeouts(self) -> None:
        """配置超时时间"""
        if not self.page:
            return
        
        # 从配置提供者获取超时配置
        timeout_config = self.config_provider.get_timeout_config()
        
        # 设置页面超时
        self.page.set_default_timeout(timeout_config.get('default_timeout', 30000))
        self.page.set_default_navigation_timeout(timeout_config.get('navigation_timeout', 60000))
        
        self.logger.debug("✅ 超时配置设置完成")
    
    def get_current_url(self) -> str:
        """获取当前页面URL"""
        if not self.page:
            return ""
        return self.page.url
    
    def is_initialized(self) -> bool:
        """检查是否已初始化"""
        return self._initialized and self.browser is not None
