#!/usr/bin/env python3
"""
人类行为模拟库 - 高级反检测系统 (Playwright版本)
包含人类行为模拟、生物特征模拟和智能延迟系统
支持Playwright同步和异步API
"""

import random
import time
import numpy as np
from typing import Tuple, List, Dict, Optional, Union
from playwright.sync_api import Page as SyncPage, Locator as SyncLocator
from playwright.async_api import Page as AsyncPage, Locator as AsyncLocator
import logging

class HumanBehaviorSimulator:
    """人类行为模拟器 - 模拟真实用户操作"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.session_start_time = time.time()
        self.total_actions = 0
        
        # 45-50 WPM打字配置
        self.wpm_range = (45, 50)
        self.base_wpm = random.uniform(*self.wpm_range)
        self.chars_per_second = (self.base_wpm * 5) / 60  # 假设平均单词长度为5
        self.base_char_delay = 1.0 / self.chars_per_second
        
        # 键盘布局配置
        self._setup_keyboard_layout()
        
        self.logger.debug(f"初始化打字模拟器: {self.base_wpm:.1f} WPM, 基础延迟: {self.base_char_delay:.3f}s")
    
    def _setup_keyboard_layout(self):
        """设置QWERTY键盘布局和手指映射"""
        # QWERTY键盘布局
        self.keyboard_layout = {
            'row1': list('qwertyuiop'),
            'row2': list('asdfghjkl'),
            'row3': list('zxcvbnm'),
            'numbers': list('1234567890')
        }
        
        # 手指到键位的映射
        self.finger_mapping = {
            'left_pinky': ['q', 'a', 'z', '1'],
            'left_ring': ['w', 's', 'x', '2'],
            'left_middle': ['e', 'd', 'c', '3'],
            'left_index': ['r', 'f', 'v', 't', 'g', 'b', '4', '5'],
            'right_index': ['y', 'h', 'n', 'u', 'j', 'm', '6', '7'],
            'right_middle': ['i', 'k', '8'],
            'right_ring': ['o', 'l', '9'],
            'right_pinky': ['p', '0']
        }
        
        # 创建字符到手指的反向映射
        self.char_to_finger = {}
        for finger, chars in self.finger_mapping.items():
            for char in chars:
                self.char_to_finger[char.lower()] = finger
        
        # 相邻键位映射（用于生成打字错误）
        self.adjacent_keys = {
            'q': ['w', 'a'], 'w': ['q', 'e', 's'], 'e': ['w', 'r', 'd'],
            'r': ['e', 't', 'f'], 't': ['r', 'y', 'g'], 'y': ['t', 'u', 'h'],
            'u': ['y', 'i', 'j'], 'i': ['u', 'o', 'k'], 'o': ['i', 'p', 'l'],
            'p': ['o', 'l'],
            'a': ['q', 's', 'z'], 's': ['a', 'd', 'x', 'w'], 'd': ['s', 'f', 'c', 'e'],
            'f': ['d', 'g', 'v', 'r'], 'g': ['f', 'h', 'b', 't'], 'h': ['g', 'j', 'n', 'y'],
            'j': ['h', 'k', 'm', 'u'], 'k': ['j', 'l', 'i'], 'l': ['k', 'o', 'p'],
            'z': ['a', 'x'], 'x': ['z', 'c', 's'], 'c': ['x', 'v', 'd'],
            'v': ['c', 'b', 'f'], 'b': ['v', 'n', 'g'], 'n': ['b', 'm', 'h'],
            'm': ['n', 'j'],
            '1': ['2'], '2': ['1', '3'], '3': ['2', '4'], '4': ['3', '5'],
            '5': ['4', '6'], '6': ['5', '7'], '7': ['6', '8'], '8': ['7', '9'],
            '9': ['8', '0'], '0': ['9']
        }
    
    def _calculate_typing_delay(self, current_char: str, next_char: str = None, 
                              error_correction: bool = False) -> float:
        """
        计算基于WPM和键盘布局的打字延迟
        
        Args:
            current_char: 当前字符
            next_char: 下一个字符
            error_correction: 是否是错误修正过程
            
        Returns:
            延迟时间（秒）
        """
        # 基础延迟（45-50 WPM）
        delay = self.base_char_delay
        
        # 错误修正时打字更慢
        if error_correction:
            delay *= random.uniform(1.3, 1.8)
        
        # 键盘布局感知调整
        if next_char:
            current_finger = self.char_to_finger.get(current_char.lower())
            next_finger = self.char_to_finger.get(next_char.lower())
            
            if current_finger and next_finger:
                if current_finger == next_finger:
                    # 同一手指连续按键，需要更多时间
                    delay *= random.uniform(1.4, 1.8)
                elif self._are_adjacent_keys(current_char, next_char):
                    # 相邻键位，稍快一些
                    delay *= random.uniform(0.8, 1.0)
                elif self._are_same_hand(current_finger, next_finger):
                    # 同一只手，稍慢一些
                    delay *= random.uniform(1.1, 1.3)
                else:
                    # 不同手，可以并行，稍快一些
                    delay *= random.uniform(0.9, 1.1)
        
        # 特殊字符需要更多时间
        if current_char in '!@#$%^&*()_+{}[]|\\:";\'<>?,./ ':
            delay *= random.uniform(1.2, 1.6)
        
        # 大写字母需要Shift键
        if current_char.isupper():
            delay *= random.uniform(1.2, 1.5)
        
        # 数字相对较慢
        if current_char.isdigit():
            delay *= random.uniform(1.1, 1.4)
        
        # 添加自然的随机变化
        delay *= random.uniform(0.8, 1.2)
        
        # 确保延迟在合理范围内
        return max(0.05, min(delay, 0.8))
    
    def _are_adjacent_keys(self, char1: str, char2: str) -> bool:
        """判断两个字符是否在键盘上相邻"""
        char1_lower = char1.lower()
        char2_lower = char2.lower()
        return char2_lower in self.adjacent_keys.get(char1_lower, [])
    
    def _are_same_hand(self, finger1: str, finger2: str) -> bool:
        """判断两个手指是否在同一只手"""
        left_fingers = ['left_pinky', 'left_ring', 'left_middle', 'left_index']
        right_fingers = ['right_index', 'right_middle', 'right_ring', 'right_pinky']
        
        return (finger1 in left_fingers and finger2 in left_fingers) or \
               (finger1 in right_fingers and finger2 in right_fingers)
    
    def _get_adjacent_key_error(self, char: str) -> str:
        """获取键盘相邻位置的错误字符"""
        char_lower = char.lower()
        adjacent = self.adjacent_keys.get(char_lower, [char_lower])
        if adjacent:
            error_char = random.choice(adjacent)
            # 保持原字符的大小写
            return error_char.upper() if char.isupper() else error_char
        return char
        
    def generate_bezier_curve(self, start: Tuple[int, int], end: Tuple[int, int], 
                            control_points: int = 2) -> List[Tuple[int, int]]:
        """
        生成贝塞尔曲线路径点
        
        Args:
            start: 起始点 (x, y)
            end: 结束点 (x, y)
            control_points: 控制点数量
            
        Returns:
            路径点列表
        """
        # 生成控制点
        control_x = np.linspace(start[0], end[0], control_points + 2)
        control_y = np.linspace(start[1], end[1], control_points + 2)
        
        # 添加随机偏移使路径更自然
        for i in range(1, len(control_x) - 1):
            control_x[i] += random.uniform(-50, 50)
            control_y[i] += random.uniform(-50, 50)
        
        # 生成贝塞尔曲线点
        t_values = np.linspace(0, 1, 20)  # 20个点组成平滑曲线
        path_points = []
        
        for t in t_values:
            x = self._bezier_interpolate(control_x, t)
            y = self._bezier_interpolate(control_y, t)
            path_points.append((int(x), int(y)))
        
        return path_points
    
    def _bezier_interpolate(self, points: np.ndarray, t: float) -> float:
        """贝塞尔曲线插值计算"""
        n = len(points) - 1
        result = 0
        for i in range(n + 1):
            binomial = self._binomial_coefficient(n, i)
            result += binomial * (t ** i) * ((1 - t) ** (n - i)) * points[i]
        return result
    
    def _binomial_coefficient(self, n: int, k: int) -> int:
        """计算二项式系数"""
        if k > n - k:
            k = n - k
        result = 1
        for i in range(k):
            result = result * (n - i) // (i + 1)
        return result
    
    def human_mouse_movement(self, page: Union[SyncPage, AsyncPage], selector: str) -> None:
        """
        模拟人类鼠标移动到元素 (Playwright版本)
        
        Args:
            page: Playwright页面实例
            selector: 目标元素选择器
        """
        try:
            # 检查是否为异步页面
            is_async = hasattr(page, '_impl_obj') and hasattr(page._impl_obj, '_channel')
            
            if is_async:
                # 异步页面 - 跳过复杂的鼠标移动模拟，直接使用简单hover
                try:
                    # 注意：这里不await，因为这是同步方法
                    # 异步调用应该由调用者处理
                    self.logger.debug("检测到异步页面，跳过复杂鼠标移动模拟")
                    # 不执行hover操作，让调用者处理
                    return
                except:
                    pass
            else:
                # 同步页面 - 执行完整的鼠标移动模拟
                # 获取页面视口大小
                viewport = page.viewport_size
                current_pos = (viewport['width'] // 2, viewport['height'] // 2)
                
                # 获取目标元素位置
                element_box = page.locator(selector).bounding_box()
                if not element_box:
                    self.logger.debug("无法获取元素位置，跳过鼠标移动模拟")
                    return
                    
                target_pos = (
                    element_box['x'] + element_box['width'] // 2,
                    element_box['y'] + element_box['height'] // 2
                )
                
                # 生成贝塞尔曲线路径
                path = self.generate_bezier_curve(current_pos, target_pos)
                
                # 模拟沿路径移动（Playwright不支持复杂鼠标路径，使用hover模拟）
                for i, (x, y) in enumerate(path[::4]):  # 每4个点取一个，减少操作次数
                    if i == 0:
                        continue
                        
                    # 移动到路径点附近
                    try:
                        page.mouse.move(x, y)
                        if random.random() < 0.2:  # 20%概率停顿
                            time.sleep(random.uniform(0.01, 0.03))
                    except:
                        break
                
                # 最终hover到目标元素（仅同步页面）
                page.locator(selector).hover()
            
        except Exception as e:
            self.logger.debug(f"鼠标移动模拟失败: {e}")
            # 降级处理 - 仅同步页面
            if not (hasattr(page, '_impl_obj') and hasattr(page._impl_obj, '_channel')):
                try:
                    page.locator(selector).hover()
                except:
                    pass
    
    def human_typing_with_wpm(self, page: Union[SyncPage, AsyncPage], selector: str, text: str, 
                            simulate_errors: bool = False) -> None:
        """
        基于45-50 WPM的真实打字模拟 (Playwright版本)
        
        Args:
            page: Playwright页面实例
            selector: 输入元素选择器
            text: 要输入的文本
            simulate_errors: 是否模拟打字错误和修正
        """
        try:
            # 检查是否为异步页面
            is_async = hasattr(page, '_impl_obj') and hasattr(page._impl_obj, '_channel')
            
            if is_async:
                # 异步页面 - 跳过复杂打字模拟，使用简单填充
                self.logger.debug("检测到异步页面，跳过WPM打字模拟")
                # 不执行异步操作，让调用者处理
                return
            else:
                # 同步页面 - 执行完整的WPM打字模拟
                locator = page.locator(selector)
                locator.click()  # 聚焦到输入字段
                
                if simulate_errors:
                    self._type_with_realistic_errors_playwright(page, selector, text)
                else:
                    self._type_normally_with_wpm_playwright(page, selector, text)
                
        except Exception as e:
            self.logger.warning(f"WPM打字失败: {e}")
            # 降级到简单输入（仅同步页面）
            if not (hasattr(page, '_impl_obj') and hasattr(page._impl_obj, '_channel')):
                try:
                    page.locator(selector).fill(text)
                except:
                    self.logger.error("所有打字方法都失败了")
    
    def _type_normally_with_wpm_playwright(self, page: Union[SyncPage, AsyncPage], selector: str, text: str) -> None:
        """正常45-50 WPM打字，无错误 (Playwright版本)"""
        # 检查是否为异步页面
        is_async = hasattr(page, '_impl_obj') and hasattr(page._impl_obj, '_channel')
        
        if is_async:
            # 异步页面，跳过WPM打字
            self.logger.debug("异步页面，跳过WPM逐字符打字")
            return
            
        locator = page.locator(selector)
        
        for i, char in enumerate(text):
            try:
                # 使用Playwright的type方法逐字符输入（仅同步页面）
                locator.type(char)
                
                # 计算下一个字符的延迟
                next_char = text[i+1] if i+1 < len(text) else None
                delay = self._calculate_typing_delay(char, next_char)
                time.sleep(delay)
                
            except Exception as e:
                self.logger.debug(f"打字过程中出错 (字符 {i}): {e}")
                # 尝试一次性输入剩余文本
                remaining_text = text[i:]
                try:
                    locator.type(remaining_text)
                    break
                except:
                    self.logger.warning("打字失败，停止输入")
                    break
    
    def _type_with_realistic_errors_playwright(self, page: Union[SyncPage, AsyncPage], selector: str, text: str) -> None:
        """
        模拟真实的打字错误和修正过程 (Playwright版本)
        第一次：故意在中间犯错并修正
        第二次：清空重新正确输入
        """
        # 检查是否为异步页面
        is_async = hasattr(page, '_impl_obj') and hasattr(page._impl_obj, '_channel')
        
        if is_async:
            # 异步页面，跳过复杂错误模拟
            self.logger.debug("异步页面，跳过真实打字错误模拟")
            return
            
        self.logger.info("🎭 模拟真实打字错误和修正过程...")
        locator = page.locator(selector)
        
        # 第一次输入：故意犯错并修正
        error_position = random.randint(2, max(3, len(text) - 2))
        self.logger.debug(f"计划在位置 {error_position} 犯错")
        
        # 正常输入到错误位置
        for i in range(error_position):
            char = text[i]
            locator.type(char)
            next_char = text[i+1] if i+1 < len(text) else None
            delay = self._calculate_typing_delay(char, next_char)
            time.sleep(delay)
        
        # 输入错误字符（相邻键位错误）
        correct_char = text[error_position]
        wrong_char = self._get_adjacent_key_error(correct_char)
        locator.type(wrong_char)
        self.logger.debug(f"输入错误字符: '{wrong_char}' (应为 '{correct_char}')")
        
        # 继续输入1-2个字符（模拟未立即发现错误）
        continue_count = random.randint(1, min(2, len(text) - error_position - 1))
        for i in range(continue_count):
            if error_position + 1 + i < len(text):
                char = text[error_position + 1 + i]
                locator.type(char)
                delay = self._calculate_typing_delay(char)
                time.sleep(delay)
        
        # 停顿（发现错误）
        discovery_pause = random.uniform(0.3, 0.8)
        time.sleep(discovery_pause)
        self.logger.debug(f"发现错误，停顿 {discovery_pause:.2f}s")
        
        # 删除错误部分（退格键）
        delete_count = continue_count + 1
        for i in range(delete_count):
            page.keyboard.press('Backspace')
            # 删除时的延迟稍短
            time.sleep(random.uniform(0.1, 0.2))
        
        # 重新输入正确内容（稍微更小心，速度稍慢）
        remaining_text = text[error_position:]
        for i, char in enumerate(remaining_text):
            locator.type(char)
            next_char = remaining_text[i+1] if i+1 < len(remaining_text) else None
            delay = self._calculate_typing_delay(char, next_char, error_correction=True)
            time.sleep(delay)
        
        # 第二次：清空重新输入（模拟检查密码后重新输入）
        time.sleep(random.uniform(0.5, 1.0))  # 检查已输入内容的停顿
        self.logger.debug("清空重新输入...")
        
        # 清空字段（更可靠的方法）
        locator.fill("")  # 使用Playwright的fill方法清空
        time.sleep(random.uniform(0.2, 0.4))
        
        # 重新输入全部内容（更加小心，速度稍慢）
        for i, char in enumerate(text):
            locator.type(char)
            next_char = text[i+1] if i+1 < len(text) else None
            # 第二次输入更加小心
            delay = self._calculate_typing_delay(char, next_char, error_correction=True)
            time.sleep(delay)
        
        self.logger.info("✅ 真实打字错误和修正模拟完成")
    
    def human_typing(self, page_or_element: Union[SyncPage, AsyncPage], selector_or_text: str, 
                    text_or_error_rate: Union[str, float] = None, error_rate: float = 0.02) -> None:
        """
        兼容性方法 - 支持Playwright和旧接口
        
        Args:
            page_or_element: Playwright页面实例或旧的WebElement
            selector_or_text: 选择器字符串或要输入的文本
            text_or_error_rate: 输入文本或错误率
            error_rate: 错误率(0-1) - 用于决定是否模拟错误
        """
        # 判断是否为Playwright调用
        if hasattr(page_or_element, 'locator'):  # Playwright页面
            page = page_or_element
            selector = selector_or_text
            text = text_or_error_rate if isinstance(text_or_error_rate, str) else ""
            if not text:
                self.logger.warning("Playwright调用需要提供文本参数")
                return
            
            # 使用错误率来决定是否模拟打字错误
            simulate_errors = random.random() < error_rate
            self.human_typing_with_wpm(page, selector, text, simulate_errors)
        else:
            # 兼容旧的WebElement接口（暂时保留）
            self.logger.warning("检测到旧的WebElement接口，建议升级到Playwright")
            # 这里可以添加旧接口的兼容代码，或者直接抛出异常提示升级
    
    
    def reading_pause(self, text_length: int) -> float:
        """
        模拟阅读停顿时间
        
        Args:
            text_length: 文本长度
            
        Returns:
            停顿时间（秒）
        """
        # 基础阅读速度: 每个字符0.05秒
        base_time = text_length * 0.05
        
        # 添加随机变化
        reading_time = max(0.5, np.random.normal(base_time, base_time * 0.3))
        
        return reading_time
    
    def decision_pause(self, complexity: str = "normal") -> float:
        """
        模拟决策停顿
        
        Args:
            complexity: 复杂度 ("simple", "normal", "complex")
            
        Returns:
            停顿时间（秒）
        """
        if complexity == "simple":
            return random.uniform(0.3, 0.8)
        elif complexity == "normal":
            return random.uniform(0.8, 2.0)
        elif complexity == "complex":
            return random.uniform(2.0, 5.0)
        else:
            return random.uniform(0.8, 2.0)
    
    async def human_click(self, page: Union[SyncPage, AsyncPage], selector: str) -> bool:
        """
        模拟人类点击行为
        
        Args:
            page: Playwright页面实例
            selector: 目标元素选择器
            
        Returns:
            点击是否成功
        """
        try:
            # 人类反应时间延迟
            reaction_delay = random.uniform(0.2, 0.8)
            await page.wait_for_timeout(int(reaction_delay * 1000))
            
            # 查找元素
            element = page.locator(selector).first
            if not await element.is_visible():
                self.logger.warning(f"元素不可见: {selector}")
                return False
            
            # 模拟鼠标移动到元素
            try:
                self.human_mouse_movement(page, selector)
            except Exception as e:
                self.logger.debug(f"鼠标移动模拟失败: {e}")
            
            # 点击前短暂停顿
            pre_click_delay = random.uniform(0.1, 0.3)
            await page.wait_for_timeout(int(pre_click_delay * 1000))
            
            # 执行点击
            await element.click()
            
            # 点击后短暂停顿
            post_click_delay = random.uniform(0.2, 0.5)
            await page.wait_for_timeout(int(post_click_delay * 1000))
            
            self.logger.debug(f"人类点击成功: {selector}")
            return True
            
        except Exception as e:
            self.logger.error(f"人类点击失败 {selector}: {e}")
            return False


class BiometricSimulator:
    """生物特征模拟器 - 模拟人类生物特征"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.session_start_time = time.time()
        self.fatigue_level = 0.0
        self.stress_level = 0.0
        
    def update_fatigue(self, session_duration: float) -> None:
        """
        更新疲劳等级
        
        Args:
            session_duration: 会话持续时间（秒）
        """
        # 疲劳随时间增加
        self.fatigue_level = min(1.0, session_duration / 3600)  # 1小时后完全疲劳
        
    def update_stress(self, failed_attempts: int) -> None:
        """
        更新压力等级
        
        Args:
            failed_attempts: 失败尝试次数
        """
        # 压力随失败次数增加
        self.stress_level = min(1.0, failed_attempts * 0.2)
        
    def generate_mouse_jitter(self) -> Tuple[int, int]:
        """
        生成鼠标微颤偏移
        
        Returns:
            (x_offset, y_offset)
        """
        # 基础微颤
        base_jitter = 2.0
        
        # 疲劳和压力增加微颤
        fatigue_factor = 1.0 + self.fatigue_level * 2.0
        stress_factor = 1.0 + self.stress_level * 1.5
        
        total_jitter = base_jitter * fatigue_factor * stress_factor
        
        x_offset = int(random.uniform(-total_jitter, total_jitter))
        y_offset = int(random.uniform(-total_jitter, total_jitter))
        
        return (x_offset, y_offset)
    
    def calculate_reaction_time(self, stimulus_complexity: str = "normal") -> float:
        """
        计算反应时间
        
        Args:
            stimulus_complexity: 刺激复杂度
            
        Returns:
            反应时间（秒）
        """
        # 基础反应时间
        base_times = {
            "simple": 0.2,
            "normal": 0.4,
            "complex": 0.8
        }
        
        base_time = base_times.get(stimulus_complexity, 0.4)
        
        # 疲劳增加反应时间
        fatigue_delay = self.fatigue_level * 0.3
        
        # 压力可能增加或减少反应时间
        stress_modifier = random.uniform(-0.1, 0.2) * self.stress_level
        
        total_time = base_time + fatigue_delay + stress_modifier
        
        # 添加随机变化
        return max(0.1, np.random.normal(total_time, total_time * 0.2))
    
    def simulate_hesitation(self, uncertainty: float = 0.5) -> float:
        """
        模拟犹豫时间
        
        Args:
            uncertainty: 不确定性等级 (0-1)
            
        Returns:
            犹豫时间（秒）
        """
        # 基础犹豫时间
        base_hesitation = uncertainty * 2.0
        
        # 压力增加犹豫
        stress_hesitation = self.stress_level * 1.0
        
        # 疲劳可能增加犹豫
        fatigue_hesitation = self.fatigue_level * 0.5
        
        total_hesitation = base_hesitation + stress_hesitation + fatigue_hesitation
        
        return max(0.1, np.random.normal(total_hesitation, total_hesitation * 0.3))
    
    def generate_typing_rhythm(self, text_length: int) -> List[float]:
        """
        生成个性化打字节奏
        
        Args:
            text_length: 文本长度
            
        Returns:
            每个字符的延迟时间列表
        """
        # 基础打字速度 (字符/秒)
        base_speed = random.uniform(3.0, 6.0)  # 3-6字符/秒
        
        # 疲劳降低速度
        fatigue_factor = 1.0 + self.fatigue_level * 0.5
        
        # 压力可能提高或降低速度
        stress_factor = 1.0 + random.uniform(-0.2, 0.3) * self.stress_level
        
        adjusted_speed = base_speed / (fatigue_factor * stress_factor)
        
        # 生成每个字符的延迟
        delays = []
        for i in range(text_length):
            # 基础延迟
            base_delay = 1.0 / adjusted_speed
            
            # 添加随机变化
            random_factor = random.uniform(0.7, 1.3)
            
            # 某些位置打字更慢（思考点）
            if i % 10 == 0 and i > 0:  # 每10个字符
                random_factor *= random.uniform(1.5, 2.5)
            
            delay = base_delay * random_factor
            delays.append(max(0.05, delay))
        
        return delays


class SmartDelay:
    """智能延迟系统 - 基于上下文的延迟控制"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.behavior_sim = HumanBehaviorSimulator()
        self.biometric_sim = BiometricSimulator()
        
    def wait_for_page_load(self, expected_content: str = None) -> None:
        """
        智能等待页面加载
        
        Args:
            expected_content: 预期内容（用于确认加载完成）
        """
        # 基础等待时间
        base_wait = random.uniform(1.0, 3.0)
        
        # 模拟网络延迟感知
        network_delay = random.uniform(0.5, 2.0)
        
        total_wait = base_wait + network_delay
        
        self.logger.debug(f"智能等待页面加载: {total_wait:.2f}秒")
        time.sleep(total_wait)
    
    def wait_before_action(self, action_type: str, context: Dict = None) -> None:
        """
        操作前智能等待
        
        Args:
            action_type: 操作类型 ("click", "type", "scroll", "navigate")
            context: 上下文信息
        """
        context = context or {}
        
        # 基础延迟
        base_delays = {
            "click": (0.3, 1.0),
            "type": (0.2, 0.8),
            "scroll": (0.5, 1.5),
            "navigate": (1.0, 3.0)
        }
        
        min_delay, max_delay = base_delays.get(action_type, (0.5, 1.5))
        
        # 计算反应时间
        complexity = context.get("complexity", "normal")
        reaction_time = self.biometric_sim.calculate_reaction_time(complexity)
        
        # 添加决策时间
        decision_time = self.behavior_sim.decision_pause(complexity)
        
        # 计算总延迟
        total_delay = random.uniform(min_delay, max_delay) + reaction_time + decision_time
        
        self.logger.debug(f"操作前等待 ({action_type}): {total_delay:.2f}秒")
        time.sleep(total_delay)
    
    def adaptive_delay(self, success_rate: float, attempt_count: int) -> float:
        """
        自适应延迟 - 根据成功率调整
        
        Args:
            success_rate: 成功率 (0-1)
            attempt_count: 尝试次数
            
        Returns:
            延迟时间（秒）
        """
        # 成功率低时增加延迟
        if success_rate < 0.3:
            base_delay = random.uniform(5.0, 10.0)
        elif success_rate < 0.6:
            base_delay = random.uniform(2.0, 5.0)
        else:
            base_delay = random.uniform(1.0, 3.0)
        
        # 尝试次数多时增加延迟
        attempt_factor = 1.0 + (attempt_count * 0.2)
        
        total_delay = base_delay * attempt_factor
        
        return min(total_delay, 30.0)  # 最大30秒