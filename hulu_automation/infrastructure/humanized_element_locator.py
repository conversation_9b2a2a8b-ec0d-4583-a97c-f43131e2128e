#!/usr/bin/env python3
"""
人类化元素定位器 - 基础设施层
集成人类行为模拟的元素定位和操作功能
"""

import time
import logging
from typing import List, Optional, Union
from playwright.sync_api import Page, Locator

from .element_locator import ElementLocator
from .human_behavior import HumanBehaviorSimulator
from .page_selectors import HuluPageSelectors


class HumanizedElementLocator(ElementLocator):
    """带人类行为模拟的元素定位器"""
    
    def __init__(self, page: Page, behavior_simulator: Optional[HumanBehaviorSimulator] = None):
        """
        初始化人类化元素定位器
        
        Args:
            page: Playwright页面对象
            behavior_simulator: 人类行为模拟器实例，如果不提供则自动创建
        """
        super().__init__(page)
        self.behavior_simulator = behavior_simulator or HumanBehaviorSimulator()
        self.logger = logging.getLogger(__name__)
    
    def click_element(self, selectors: List[str], name: str, use_human_behavior: bool = True) -> bool:
        """
        点击元素，可选择是否使用人类行为模拟
        
        Args:
            selectors: 选择器列表
            name: 元素名称，用于日志
            use_human_behavior: 是否使用人类行为模拟
            
        Returns:
            bool: 是否成功点击
        """
        try:
            for selector in selectors:
                try:
                    locator = self.page.locator(selector)
                    if locator.count() > 0 and locator.is_visible():
                        if use_human_behavior:
                            # 人类行为：鼠标移动到元素
                            try:
                                self.behavior_simulator.human_mouse_movement(self.page, selector)
                            except Exception as e:
                                self.logger.debug(f"鼠标移动模拟失败，使用普通点击: {e}")
                            
                            # 人类行为：决策延迟
                            delay = self.behavior_simulator.decision_pause("normal")
                            time.sleep(delay)
                        
                        locator.click()
                        self.logger.info(f"✅ 成功点击{name}: {selector}")
                        return True
                except:
                    continue
            
            self.logger.warning(f"⚠️ 未找到{name}")
            return False
            
        except Exception as e:
            self.logger.error(f"点击{name}时出错: {str(e)}")
            return False
    
    def fill_input(self, selectors: List[str], value: str, name: str, use_human_behavior: bool = True) -> bool:
        """
        填写输入字段，可选择是否使用人类行为模拟
        
        Args:
            selectors: 选择器列表
            value: 要填写的值
            name: 字段名称，用于日志
            use_human_behavior: 是否使用人类行为模拟
            
        Returns:
            bool: 是否成功填写
        """
        try:
            for selector in selectors:
                try:
                    locator = self.page.locator(selector)
                    if locator.count() > 0 and locator.is_visible():
                        # 清空字段
                        locator.fill("")
                        
                        if use_human_behavior:
                            # 使用人类行为模拟打字
                            try:
                                # 判断是否为密码字段
                                is_password_field = (name and '密码' in name) or 'password' in selector.lower()
                                
                                # 使用45-50 WPM人类打字模拟
                                self.behavior_simulator.human_typing_with_wpm(
                                    self.page, selector, value, simulate_errors=is_password_field
                                )
                                self.logger.info(f"✅ 成功填写{name}（人类行为模拟）: {selector}")
                                return True
                            except Exception as e:
                                self.logger.warning(f"人类行为模拟失败，降级到普通输入: {e}")
                                # 降级方案：普通填写
                                locator.fill(value)
                                self.logger.info(f"✅ 成功填写{name}（普通模式）: {selector}")
                                return True
                        else:
                            # 普通填写模式
                            locator.fill(value)
                            self.logger.info(f"✅ 成功填写{name}: {selector}")
                            return True
                except:
                    continue
            
            self.logger.warning(f"⚠️ 未找到{name}")
            return False
            
        except Exception as e:
            self.logger.error(f"填写{name}时出错: {str(e)}")
            return False
    
    def reading_pause(self, text_length: int = 100) -> float:
        """
        模拟阅读停顿
        
        Args:
            text_length: 文本长度
            
        Returns:
            float: 停顿时间（秒）
        """
        return self.behavior_simulator.reading_pause(text_length)
    
    def decision_pause(self, complexity: str = "normal") -> float:
        """
        模拟决策停顿
        
        Args:
            complexity: 复杂度 ("simple", "normal", "complex")
            
        Returns:
            float: 停顿时间（秒）
        """
        return self.behavior_simulator.decision_pause(complexity)
    
    def click_element_with_focus(self, selectors: List[str], name: str) -> bool:
        """
        先焦点定位到元素，然后点击（专门用于按钮点击）
        
        Args:
            selectors: 选择器列表
            name: 元素名称，用于日志
            
        Returns:
            bool: 是否成功点击
        """
        try:
            for selector in selectors:
                try:
                    element = self.page.query_selector(selector)
                    if element and element.is_visible() and element.is_enabled():
                        # 先焦点定位到按钮
                        element.focus()
                        time.sleep(0.5)
                        # 然后点击按钮
                        element.click()
                        self.logger.info(f"✅ 焦点定位+点击成功: {selector}")
                        return True
                except Exception as e:
                    self.logger.debug(f"选择器 {selector} 失败: {e}")
                    continue
            
            self.logger.error(f"❌ 找不到可用的{name}")
            return False
            
        except Exception as e:
            self.logger.error(f"焦点定位+点击{name}时出错: {str(e)}")
            return False
    
    def click_element_with_enhanced_logic(self, selectors: List[str], name: str) -> bool:
        """
        完全复制debug_login_button_dom.py的成功点击逻辑
        
        Args:
            selectors: 选择器列表
            name: 元素名称，用于日志
            
        Returns:
            bool: 是否成功点击
        """
        try:
            self.logger.info(f"🎯 完全复制debug_login_button_dom.py逻辑点击{name}")
            
            # 完全复制debug脚本的逻辑
            for i, selector in enumerate(selectors):
                try:
                    self.logger.info(f"尝试选择器 {i+1}: {selector}")
                    
                    # 检查元素存在性（完全复制debug脚本）
                    elements = self.page.query_selector_all(selector)
                    self.logger.info(f"  找到 {len(elements)} 个匹配元素")
                    
                    for j, element in enumerate(elements):
                        try:
                            text = element.inner_text()
                            is_visible = element.is_visible()
                            is_enabled = element.is_enabled()
                            
                            self.logger.info(f"    元素 {j+1}: 文本='{text}', 可见={is_visible}, 启用={is_enabled}")
                            
                            # 完全复制debug脚本的条件检查（184行）
                            if is_visible and is_enabled and 'log' in text.lower():
                                self.logger.info(f"    尝试点击这个元素...")
                                
                                # 记录点击前状态（复制debug脚本188-197行）
                                page_url_before = self.page.url
                                self.logger.info(f"    点击前 URL: {page_url_before}")
                                
                                try:
                                    button_html_before = element.inner_html()
                                    self.logger.info(f"    点击前按钮 HTML: {button_html_before[:200]}...")
                                except:
                                    self.logger.info("    无法获取点击前按钮 HTML")
                                
                                # 点击按钮（完全复制debug脚本200-202行）
                                self.logger.info("    正在点击按钮...")
                                element.click()
                                self.logger.info("    点击命令已执行")
                                
                                # 等待页面响应（完全复制debug脚本205行）
                                time.sleep(5)
                                
                                # 记录点击后状态（复制debug脚本207-226行）
                                page_url_after = self.page.url
                                self.logger.info(f"    点击后 URL: {page_url_after}")
                                self.logger.info(f"    URL是否变化: {page_url_before != page_url_after}")
                                
                                try:
                                    button_html_after = element.inner_html()
                                    self.logger.info(f"    点击后按钮 HTML: {button_html_after[:200]}...")
                                    
                                    if 'loading' in button_html_after.lower() or 'spinner' in button_html_after.lower():
                                        self.logger.info("    ✅ 按钮已进入加载状态！")
                                    else:
                                        self.logger.info("    ❌ 按钮没有进入加载状态")
                                except Exception as e:
                                    self.logger.info(f"    无法获取点击后按钮状态: {e}")
                                
                                # 检查页面错误信息（复制debug脚本229-247行）
                                self.logger.info("    检查页面错误信息...")
                                error_selectors = [
                                    '[role="alert"]',
                                    '.error-message',
                                    '[data-testid*="error"]',
                                    '.alert-error'
                                ]
                                
                                for error_selector in error_selectors:
                                    try:
                                        error_elements = self.page.query_selector_all(error_selector)
                                        for error_elem in error_elements:
                                            if error_elem.is_visible():
                                                error_text = error_elem.inner_text()
                                                self.logger.info(f"    发现错误信息: {error_text}")
                                    except:
                                        pass
                                        
                                self.logger.info("    等待更多页面响应...")
                                time.sleep(3)
                                
                                # 关键：完全复制debug脚本的退出逻辑（252行）
                                # debug脚本在这里直接return，不管点击是否生效！
                                self.logger.info("✅ 完全复制debug脚本逻辑：找到并测试了按钮，直接返回成功")
                                return True  # 找到并测试了一个按钮，退出
                                
                        except Exception as e:
                            self.logger.error(f"    分析元素 {j+1} 时出错: {e}")
                    
                except Exception as e:
                    self.logger.error(f"测试选择器 {selector} 时出错: {e}")
            
            self.logger.error(f"❌ 所有选择器都失败，无法点击{name}")
            return False
            
        except Exception as e:
            self.logger.error(f"点击{name}时出错: {str(e)}")
            return False
    
    def click_login_button_with_focus(self) -> bool:
        """
        点击登录按钮，使用增强的点击逻辑 + JavaScript表单提交备用方案
        
        Returns:
            bool: 是否成功点击登录按钮
        """
        self.logger.info("🎯 使用增强点击逻辑定位到LOG IN按钮后点击")
        
        # 记录点击前的URL
        initial_url = self.page.url
        
        # 尝试点击按钮
        click_success = self.click_element_with_enhanced_logic(
            HuluPageSelectors.LOGIN_SUBMIT_BUTTONS,
            "LOG IN按钮"
        )
        
        if click_success:
            # 等待一下看是否有效果
            time.sleep(3)
            current_url = self.page.url
            
            # 检查点击是否有效果
            url_changed = (initial_url != current_url)
            button_state_changed = self._check_button_state_change()
            
            if url_changed or button_state_changed:
                self.logger.info("✅ 按钮点击有效果，表单提交成功")
                return True
            else:
                self.logger.warning("⚠️ 按钮点击无效果，尝试JavaScript表单提交")
                # 尝试JavaScript表单提交作为备用方案
                return self._submit_form_via_javascript()
        else:
            self.logger.error("❌ 按钮点击失败，尝试JavaScript表单提交")
            return self._submit_form_via_javascript()
    
    def _check_button_state_change(self) -> bool:
        """
        检查按钮状态是否发生变化（加载、禁用等）
        
        Returns:
            bool: 按钮状态是否发生变化
        """
        try:
            # 检查登录按钮是否有状态变化
            button_selectors = [
                'button:has-text("Log In")',
                'button[type="submit"]',
                'form button'
            ]
            
            for selector in button_selectors:
                try:
                    button = self.page.query_selector(selector)
                    if button and button.is_visible():
                        # 检查按钮是否被禁用
                        if not button.is_enabled():
                            self.logger.info("✅ 按钮已被禁用，表明提交正在进行")
                            return True
                        
                        # 检查按钮文本是否变化
                        button_text = button.inner_text()
                        if any(keyword in button_text.lower() for keyword in ['loading', 'submitting', 'wait']):
                            self.logger.info(f"✅ 按钮文本变为加载状态: {button_text}")
                            return True
                        
                        # 检查按钮HTML是否包含加载指示器
                        button_html = button.inner_html()
                        if any(keyword in button_html.lower() for keyword in ['loading', 'spinner', 'circle']):
                            self.logger.info("✅ 按钮包含加载指示器")
                            return True
                        
                except Exception:
                    continue
            
            return False
            
        except Exception as e:
            self.logger.warning(f"检查按钮状态变化时出错: {e}")
            return False
    
    def _wait_for_form_validation(self) -> bool:
        """
        等待表单验证完成
        
        Returns:
            bool: 表单是否通过验证
        """
        try:
            # 等待表单状态稳定
            time.sleep(2)
            
            # 检查邮箱字段
            email_field = self.page.query_selector('#email-field')
            if email_field:
                email_value = email_field.input_value()
                if not email_value or '@' not in email_value:
                    self.logger.warning(f"⚠️ 邮箱字段值不正确: {email_value}")
                    return False
            
            # 检查密码字段
            password_field = self.page.query_selector('#password')
            if password_field:
                password_value = password_field.input_value()
                if not password_value or len(password_value) < 6:
                    self.logger.warning(f"⚠️ 密码字段值不正确（长度: {len(password_value) if password_value else 0}）")
                    return False
            
            # 检查是否有明显的验证错误
            error_selectors = [
                '[role="alert"]',
                '.error-message',
                '[data-testid*="error"]',
                '.form-error'
            ]
            
            for error_selector in error_selectors:
                try:
                    error_elements = self.page.query_selector_all(error_selector)
                    for error_elem in error_elements:
                        if error_elem.is_visible():
                            error_text = error_elem.inner_text()
                            if error_text.strip():
                                self.logger.warning(f"⚠️ 发现表单验证错误: {error_text}")
                                return False
                except:
                    pass
            
            self.logger.info("✅ 表单验证状态正常")
            return True
            
        except Exception as e:
            self.logger.warning(f"表单验证检查失败: {e}")
            return False
    
    def _verify_click_effect(self, selector: str, timeout: int = 5) -> bool:
        """
        验证点击效果，基于debug_login_button_dom.py真实测试结果优化
        
        Args:
            selector: 被点击的元素选择器
            timeout: 等待超时时间（秒，保持5秒）
            
        Returns:
            bool: 点击是否生效
        """
        try:
            initial_url = self.page.url
            self.logger.info(f"🔍 验证点击效果 - 初始URL: {initial_url}")
            
            # 等待页面响应（保持5秒）
            time.sleep(timeout)
            
            # 检查URL是否有变化（包括参数变化）
            current_url = self.page.url
            if initial_url != current_url:
                self.logger.info(f"✅ URL发生变化: {initial_url} -> {current_url}")
                return True
            
            # 检查URL参数是否包含password（基于真实用户行为）
            if '?password=' in current_url:
                self.logger.info(f"✅ URL包含密码参数，表明GET表单提交成功")
                return True
            
            # 检查页面是否有错误信息（基于调试脚本的错误检测逻辑）
            error_selectors = [
                '[role="alert"]',
                '.error-message',
                '[data-testid*="error"]',
                '.form-error',
                '[class*="error"]',
                '.alert-error'  # 新增基于调试脚本的错误选择器
            ]
            
            for error_selector in error_selectors:
                try:
                    error_elements = self.page.query_selector_all(error_selector)
                    for error_elem in error_elements:
                        if error_elem.is_visible():
                            error_text = error_elem.inner_text()
                            if error_text.strip():
                                self.logger.info(f"⚠️ 检测到错误信息: {error_text}")
                                return True  # 有错误也算是有效果
                except:
                    pass
            
            # 检查是否有加载指示器或状态变化
            loading_selectors = [
                '[class*="loading"]',
                '[class*="spinner"]',
                '[class*="submitting"]',
                '[aria-busy="true"]'
            ]
            
            for loading_selector in loading_selectors:
                try:
                    loading_elements = self.page.query_selector_all(loading_selector)
                    for loading_elem in loading_elements:
                        if loading_elem.is_visible():
                            self.logger.info(f"✅ 发现加载指示器: {loading_selector}")
                            return True
                except:
                    pass
            
            # 检查按钮是否被禁用或文本变化（基于调试脚本的按钮状态检测）
            try:
                button_element = self.page.query_selector(selector)
                if button_element:
                    if not button_element.is_enabled():
                        self.logger.info(f"✅ 按钮已被禁用，表明点击生效")
                        return True
                    
                    button_text = button_element.inner_text()
                    if 'loading' in button_text.lower() or 'submitting' in button_text.lower():
                        self.logger.info(f"✅ 按钮文本变为加载状态: {button_text}")
                        return True
            except:
                pass
            
            # 检查页面内容是否有变化（额外延迟检测）
            try:
                # 等待一段时间后再次检查URL
                time.sleep(2)
                final_url = self.page.url
                if final_url != initial_url:
                    self.logger.info(f"✅ 延迟检测到URL变化: {initial_url} -> {final_url}")
                    return True
            except:
                pass
            
            # 新增：检查页面标题变化（基于调试脚本的页面状态检测）
            try:
                page_title = self.page.title()
                if 'Login' not in page_title and 'Sign' not in page_title:
                    # 如果页面标题不再包含Login或Sign，可能登录成功
                    if 'Hulu' in page_title or 'Stream' in page_title:
                        self.logger.info(f"✅ 页面标题变化，可能登录成功: {page_title}")
                        return True
            except:
                pass
            
            # 新增：检查是否还在登录页面（基于调试脚本的URL检测）
            login_indicators = [
                '/web/login/enter-password',
                '/web/login',
                '/signin',
                '/authentication'
            ]
            
            still_in_login = any(indicator in current_url for indicator in login_indicators)
            if not still_in_login:
                self.logger.info(f"✅ 已离开登录页面: {current_url}")
                return True
            
            self.logger.warning("⚠️ 未检测到明显的点击效果")
            return False
            
        except Exception as e:
            self.logger.warning(f"验证点击效果失败: {e}")
            return False
    
    def _submit_form_via_javascript(self) -> bool:
        """
        使用JavaScript直接提交表单，基于debug_real_login_behavior.json的发现优化
        特别处理GET方法表单
        
        Returns:
            bool: 是否成功提交
        """
        try:
            result = self.page.evaluate("""
                () => {
                    // 查找表单
                    const form = document.querySelector('form');
                    if (form) {
                        console.log('📝 表单信息:', {
                            action: form.action,
                            method: form.method,
                            elements: form.elements.length
                        });
                        
                        // 基于debug_real_login_behavior.json发现：表单使用GET方法
                        // 需要特别处理GET表单提交
                        if (form.method.toLowerCase() === 'get') {
                            console.log('🔍 检测到GET表单，使用特殊处理方式');
                            
                            // 对于GET表单，直接触发提交
                            try {
                                // 先触发表单验证
                                const isValid = form.checkValidity();
                                if (!isValid) {
                                    console.log('⚠️ 表单验证失败');
                                    return { success: false, method: 'form validation failed' };
                                }
                                
                                // 触发表单提交事件
                                const submitEvent = new Event('submit', {
                                    bubbles: true,
                                    cancelable: true
                                });
                                
                                const dispatched = form.dispatchEvent(submitEvent);
                                if (dispatched) {
                                    // 如果事件没有被阻止，直接提交
                                    form.submit();
                                    return { success: true, method: 'GET form.submit()' };
                                } else {
                                    return { success: false, method: 'submit event prevented' };
                                }
                            } catch (e) {
                                console.log('❌ GET表单提交失败:', e.message);
                                return { success: false, method: 'GET form submit error: ' + e.message };
                            }
                        } else {
                            // 常规POST表单处理
                            try {
                                form.submit();
                                return { success: true, method: 'POST form.submit()' };
                            } catch (e) {
                                // 如果submit被阻止，尝试触发事件
                                const submitEvent = new Event('submit', {
                                    bubbles: true,
                                    cancelable: true
                                });
                                const dispatched = form.dispatchEvent(submitEvent);
                                return { success: dispatched, method: 'POST dispatchEvent' };
                            }
                        }
                    }
                    return { success: false, method: 'no form found' };
                }
            """)
            
            if result.get('success'):
                self.logger.info(f"✅ JavaScript表单提交成功（方法: {result.get('method')}）")
                return True
            else:
                self.logger.warning(f"⚠️ JavaScript表单提交失败（原因: {result.get('method')}）")
                return False
                
        except Exception as e:
            self.logger.warning(f"JavaScript表单提交失败: {e}")
            return False
    
    def click_element_debug_style(self, selectors: List[str], name: str) -> bool:
        """
        完全复制debug_login_button_dom.py的点击逻辑
        找到按钮就点击，不做额外验证 - 这是debug脚本成功的关键
        """
        try:
            self.logger.info(f"🎯 使用debug风格点击{name}")
            
            # 完全复制debug脚本的逻辑（debug_login_button_dom.py第168-252行）
            for i, selector in enumerate(selectors):
                try:
                    self.logger.info(f"尝试选择器 {i+1}: {selector}")
                    
                    # 检查元素存在性（完全复制debug脚本第173-174行）
                    elements = self.page.query_selector_all(selector)
                    self.logger.info(f"  找到 {len(elements)} 个匹配元素")
                    
                    for j, element in enumerate(elements):
                        try:
                            text = element.inner_text()
                            is_visible = element.is_visible()
                            is_enabled = element.is_enabled()
                            
                            self.logger.info(f"    元素 {j+1}: 文本='{text}', 可见={is_visible}, 启用={is_enabled}")
                            
                            # 完全复制debug脚本的条件检查（第184行）
                            if is_visible and is_enabled and 'log' in text.lower():
                                self.logger.info(f"    找到符合条件的登录按钮，准备点击...")
                                
                                # 记录点击前状态（复制debug脚本188-197行）
                                page_url_before = self.page.url
                                self.logger.info(f"    点击前 URL: {page_url_before}")
                                
                                # 关键：使用element.click()而不是locator.click()（复制debug脚本200-202行）
                                self.logger.info("    正在点击按钮...")
                                element.click()  # 这是debug脚本成功的关键！
                                self.logger.info("    点击命令已执行")
                                
                                # 等待页面响应（完全复制debug脚本205行）
                                time.sleep(5)  # debug脚本使用5秒等待
                                
                                # 记录点击后状态
                                page_url_after = self.page.url
                                self.logger.info(f"    点击后 URL: {page_url_after}")
                                self.logger.info(f"    URL是否变化: {page_url_before != page_url_after}")
                                
                                # 关键：完全复制debug脚本的退出逻辑（252行）
                                # debug脚本在这里直接return，不管点击是否生效！
                                self.logger.info("✅ 完全复制debug脚本逻辑：找到并点击了按钮，直接返回成功")
                                return True  # 找到并点击了一个按钮，直接退出
                                
                        except Exception as e:
                            self.logger.error(f"    分析元素 {j+1} 时出错: {e}")
                    
                except Exception as e:
                    self.logger.error(f"测试选择器 {selector} 时出错: {e}")
            
            self.logger.error(f"❌ 所有选择器都失败，无法找到可点击的{name}")
            return False
            
        except Exception as e:
            self.logger.error(f"debug风格点击{name}时出错: {str(e)}")
            return False