#!/usr/bin/env python3
"""
元素定位器 - 基础设施层
提供基础的元素定位和操作功能
"""

import time
import logging
from typing import List, Optional, Union
from playwright.sync_api import Page, Locator


class ElementLocator:
    """基础元素定位器"""
    
    def __init__(self, page: Page):
        """
        初始化元素定位器
        
        Args:
            page: Playwright页面对象
        """
        self.page = page
        self.logger = logging.getLogger(__name__)
    
    def find_element(self, selectors: List[str]) -> Optional[Locator]:
        """
        使用选择器列表查找元素
        
        Args:
            selectors: 选择器列表，按优先级排序
            
        Returns:
            Optional[Locator]: 找到的元素定位器，如果没找到则返回None
        """
        for selector in selectors:
            try:
                locator = self.page.locator(selector)
                if locator.count() > 0 and locator.is_visible():
                    return locator
            except:
                continue
        return None
    
    def click_element(self, selectors: List[str], name: str) -> bool:
        """
        点击元素
        
        Args:
            selectors: 选择器列表
            name: 元素名称，用于日志
            
        Returns:
            bool: 是否成功点击
        """
        try:
            for selector in selectors:
                try:
                    locator = self.page.locator(selector)
                    if locator.count() > 0 and locator.is_visible():
                        locator.click()
                        self.logger.info(f"✅ 成功点击{name}: {selector}")
                        return True
                except:
                    continue
            
            self.logger.warning(f"⚠️ 未找到{name}")
            return False
            
        except Exception as e:
            self.logger.error(f"点击{name}时出错: {str(e)}")
            return False
    
    def fill_input(self, selectors: List[str], value: str, name: str) -> bool:
        """
        填写输入字段
        
        Args:
            selectors: 选择器列表
            value: 要填写的值
            name: 字段名称，用于日志
            
        Returns:
            bool: 是否成功填写
        """
        try:
            for selector in selectors:
                try:
                    locator = self.page.locator(selector)
                    if locator.count() > 0 and locator.is_visible():
                        locator.fill("")  # 清空
                        locator.fill(value)  # 填写值
                        self.logger.info(f"✅ 成功填写{name}: {selector}")
                        return True
                except:
                    continue
            
            self.logger.warning(f"⚠️ 未找到{name}")
            return False
            
        except Exception as e:
            self.logger.error(f"填写{name}时出错: {str(e)}")
            return False
    
    def wait_for_element(self, selectors: List[str], timeout: int = 5000) -> Optional[Locator]:
        """
        等待元素出现
        
        Args:
            selectors: 选择器列表
            timeout: 超时时间（毫秒）
            
        Returns:
            Optional[Locator]: 找到的元素定位器
        """
        for selector in selectors:
            try:
                locator = self.page.locator(selector)
                locator.wait_for(state="visible", timeout=timeout)
                return locator
            except:
                continue
        return None