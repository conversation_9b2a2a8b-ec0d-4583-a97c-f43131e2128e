#!/usr/bin/env python3
"""
反检测服务 - 基础设施层
提供指纹伪装和反检测功能
"""

import logging
import random
from typing import Optional
from playwright.sync_api import Page as SyncPage, BrowserContext as SyncBrowserContext
from fake_useragent import UserAgent

# 导入指纹伪装模块 (已移除不存在的模块依赖)
# from fingerprint_masking import FingerprintMasking, ConsistentSessionFingerprints


class AntiDetectionService:
    """反检测服务 - 提供指纹伪装和隐身功能"""
    
    def __init__(self):
        """初始化反检测服务"""
        self.logger = logging.getLogger(__name__)
        self.ua = UserAgent()
        
        # 初始化指纹伪装组件 (使用内置反检测系统)
        self.fingerprint_masking = None  # 使用infrastructure.anti_detection替代
        self.session_fingerprints = None  # 使用infrastructure.anti_detection替代
        
        self.logger.debug("✅ 反检测服务初始化完成")
    
    def apply_fingerprint_masking(self, page: SyncPage) -> None:
        """
        应用指纹伪装 (使用内置反检测系统)
        
        Args:
            page: Playwright页面对象
        """
        try:
            # 使用基础反检测功能，不依赖外部模块
            self.logger.debug("应用基础指纹伪装措施...")
            
            # 基础User-Agent设置
            user_agent = self.ua.random
            if hasattr(page, 'set_extra_http_headers'):
                page.set_extra_http_headers({'User-Agent': user_agent})
            
            self.logger.debug("✅ 基础指纹伪装应用完成")
            
        except Exception as e:
            self.logger.debug(f"指纹伪装应用失败: {e}")
            # 不影响主流程
    
    def _apply_fingerprint_safe(self, page: SyncPage) -> bool:
        """
        安全的指纹伪装应用方法 (使用内置反检测系统)
        
        Args:
            page: Playwright页面对象
            
        Returns:
            bool: 是否成功应用
        """
        try:
            # 使用基础反检测功能
            self.apply_fingerprint_masking(page)
            return True
                
        except Exception as e:
            self.logger.debug(f"安全指纹伪装失败: {e}")
            return False
    
    def execute_anti_detection_scripts(self, page: SyncPage) -> None:
        """
        执行反检测脚本
        
        Args:
            page: Playwright页面对象
        """
        try:
            # 基础反检测脚本
            basic_script = """
                // 删除webdriver标识
                delete window.navigator.webdriver;
                
                // 修改permissions查询
                const originalQuery = window.navigator.permissions.query;
                window.navigator.permissions.query = (parameters) => (
                    parameters.name === 'notifications' ?
                        Promise.resolve({ state: Notification.permission }) :
                        originalQuery(parameters)
                );
                
                // 修改插件信息
                Object.defineProperty(navigator, 'plugins', {
                    get: () => [1, 2, 3, 4, 5],
                });
                
                // 隐藏自动化标识
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => undefined,
                });
                
                // 修改语言设置
                Object.defineProperty(navigator, 'languages', {
                    get: () => ['en-US', 'en'],
                });
                
                // 修改平台信息
                Object.defineProperty(navigator, 'platform', {
                    get: () => 'MacIntel',
                });
            """
            
            page.add_init_script(basic_script)
            
            # 高级反检测脚本
            advanced_script = """
                // 修改Chrome对象
                window.chrome = {
                    runtime: {},
                    loadTimes: function() {},
                    csi: function() {},
                    app: {}
                };
                
                // 修改权限API
                const originalPermissions = navigator.permissions;
                navigator.permissions = {
                    query: function(parameters) {
                        return Promise.resolve({
                            state: 'granted'
                        });
                    }
                };
                
                // 修改媒体设备
                Object.defineProperty(navigator, 'mediaDevices', {
                    get: () => ({
                        enumerateDevices: () => Promise.resolve([
                            { deviceId: 'default', kind: 'audioinput', label: 'Default - MacBook Pro Microphone' },
                            { deviceId: 'default', kind: 'audiooutput', label: 'Default - MacBook Pro Speakers' }
                        ])
                    })
                });
                
                // 修改连接信息
                Object.defineProperty(navigator, 'connection', {
                    get: () => ({
                        effectiveType: '4g',
                        rtt: 50,
                        downlink: 10
                    })
                });
            """
            
            page.add_init_script(advanced_script)
            
            self.logger.debug("✅ 反检测脚本应用成功")
            
        except Exception as e:
            self.logger.error(f"应用反检测脚本时出错: {str(e)}")
    
    def randomize_user_agent(self) -> str:
        """
        随机化User-Agent
        
        Returns:
            str: 随机生成的User-Agent字符串
        """
        try:
            user_agent = self.ua.random
            self.logger.debug(f"生成随机User-Agent: {user_agent}")
            return user_agent
        except Exception as e:
            self.logger.warning(f"生成随机User-Agent失败: {e}")
            # 返回默认User-Agent
            return "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
    
    def setup_stealth_mode(self, context: SyncBrowserContext) -> None:
        """
        设置隐身模式
        
        Args:
            context: 浏览器上下文
        """
        try:
            # 设置额外的隐身选项
            context.set_extra_http_headers({
                'Accept-Language': 'en-US,en;q=0.9',
                'Accept-Encoding': 'gzip, deflate, br',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
                'Upgrade-Insecure-Requests': '1',
                'Sec-Fetch-Site': 'none',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-User': '?1',
                'Cache-Control': 'max-age=0'
            })
            
            # 设置地理位置（美国）
            context.set_geolocation({'latitude': 40.7128, 'longitude': -74.0060})
            context.grant_permissions(['geolocation'])

            # 注意：set_timezone_id在某些Playwright版本中可能不可用
            try:
                context.set_timezone_id('America/New_York')
            except AttributeError:
                self.logger.debug("set_timezone_id方法不可用，跳过时区设置")
            
            self.logger.debug("✅ 隐身模式设置完成")
            
        except Exception as e:
            self.logger.error(f"设置隐身模式时出错: {str(e)}")
    
    def apply_viewport_randomization(self, context: SyncBrowserContext) -> None:
        """
        应用视口随机化
        
        Args:
            context: 浏览器上下文
        """
        try:
            # 常见的屏幕分辨率
            viewports = [
                {'width': 1920, 'height': 1080},
                {'width': 1366, 'height': 768},
                {'width': 1440, 'height': 900},
                {'width': 1536, 'height': 864},
                {'width': 1280, 'height': 720}
            ]
            
            # 随机选择一个视口
            viewport = random.choice(viewports)
            
            # 添加小幅随机变化
            viewport['width'] += random.randint(-50, 50)
            viewport['height'] += random.randint(-30, 30)
            
            # 确保最小尺寸
            viewport['width'] = max(viewport['width'], 1024)
            viewport['height'] = max(viewport['height'], 600)
            
            self.logger.debug(f"设置随机视口: {viewport}")
            
        except Exception as e:
            self.logger.error(f"视口随机化失败: {e}")
    
    def get_session_fingerprints(self) -> dict:
        """
        获取会话指纹
        
        Returns:
            dict: 会话指纹信息
        """
        return self.session_fingerprints.generate_session_fingerprints()
    
    def get_session_id(self) -> str:
        """
        获取会话ID
        
        Returns:
            str: 会话ID
        """
        return self.session_fingerprints.get_session_id()
    
    def apply_comprehensive_anti_detection(self, page: SyncPage, context: SyncBrowserContext) -> None:
        """
        应用综合反检测措施
        
        Args:
            page: Playwright页面对象
            context: 浏览器上下文
        """
        try:
            # 1. 应用基础反检测脚本
            self.execute_anti_detection_scripts(page)
            
            # 2. 应用指纹伪装
            self.apply_fingerprint_masking(page)
            
            # 3. 设置隐身模式
            self.setup_stealth_mode(context)
            
            # 4. 应用视口随机化
            self.apply_viewport_randomization(context)
            
            self.logger.info("✅ 综合反检测措施应用完成")
            
        except Exception as e:
            self.logger.error(f"应用综合反检测措施失败: {e}")
