#!/usr/bin/env python3
"""
HuluBotFacade - 表示层门面服务
提供统一的外部接口，隐藏内部分层架构的复杂性
"""

import logging
from typing import Dict, Optional
from playwright.sync_api import sync_playwright, <PERSON><PERSON>er as S<PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> as SyncBrowserContext, Page as SyncPage
from fake_useragent import UserAgent
import time
import random

# 导入分层架构的服务
from utils import CONFIG, cleanup_old_screenshots, ensure_screenshot_directory
from utils.account_loader import AccountLoader, get_session_path_for_account

# 导入表示层服务
from .command_processor import CommandProcessor

# 导入数据层服务
from ..data import ConfigurationProvider, StateManager, LoggingService

# 导入基础设施层服务
from ..infrastructure import (
    BrowserEngine, AntiDetectionService, ErrorHandler,
    HumanBehaviorSimulator, BiometricSimulator
)

# 导入业务逻辑层服务
from ..business import WorkflowOrchestrator, LoginService, RegistrationService, VerificationService


class HuluBotFacade:
    """
    HuluBot门面类 - 表示层统一接口
    
    提供与原HuluBot类完全相同的API接口，隐藏内部分层架构复杂性
    保持100%向后兼容性
    """

    def __init__(self, headless: bool = None, timeout: int = None, storage_state_path: str = None,
                 account_index: int = None, account_email: str = None):
        """
        初始化Hulu机器人门面
        
        Args:
            headless: 是否无头模式运行（如果未提供，从环境变量获取）
            timeout: 页面操作超时时间（秒，如果未提供，从环境变量获取）
            storage_state_path: 存储状态文件路径，用于保存和恢复登录状态
            account_index: 使用第N个账户（从0开始，与account_email互斥）
            account_email: 使用指定email的账户（与account_index互斥）
        """
        self.headless = headless if headless is not None else CONFIG['DEFAULT_HEADLESS']
        # Convert timeout from milliseconds to seconds for Playwright
        self.timeout = (timeout if timeout is not None else CONFIG['BROWSER_TIMEOUT']) / 1000
        self.page_load_timeout = CONFIG['PAGE_LOAD_TIMEOUT'] / 1000

        # 多账户管理
        self.account_loader = AccountLoader()
        self.current_account = None
        
        # 处理账户选择逻辑
        if account_index is not None and account_email is not None:
            raise ValueError("account_index和account_email参数不能同时指定")
        
        if account_index is not None:
            self.current_account = self.account_loader.get_account_by_index(account_index)
            if not self.current_account:
                raise ValueError(f"账户索引 {account_index} 不存在")
        elif account_email is not None:
            self.current_account = self.account_loader.get_account_by_email(account_email)
            if not self.current_account:
                raise ValueError(f"账户email '{account_email}' 不存在")

        # 登录状态管理
        if self.current_account:
            # 如果指定了账户，使用该账户对应的session文件
            self.storage_state_path = get_session_path_for_account(self.current_account.email)
            self.logger.info(f"使用账户: {self.current_account.email}")
            self.logger.info(f"Session文件: {self.storage_state_path}")
        else:
            # 使用传统的方式
            self.storage_state_path = storage_state_path or "hulu_auth_state.json"
        
        self.auto_save_state = True  # 自动保存登录状态

        # 初始化日志
        self.logger = logging.getLogger(__name__)

        # 初始化表示层服务
        self.command_processor = CommandProcessor()

        # 初始化数据层服务
        self.config_provider = ConfigurationProvider()
        self.state_manager = StateManager()
        self.logging_service = LoggingService()

        # 初始化基础设施层服务
        self.browser_engine = BrowserEngine(self.config_provider)
        self.anti_detection_service = AntiDetectionService()
        self.error_handler = ErrorHandler(headless=self.headless)
        self.human_behavior = HumanBehaviorSimulator()
        self.biometric_sim = BiometricSimulator()
        
        # 待应用的反检测配置
        self._pending_anti_detection_config = None

        # Playwright browser instances
        self.playwright = None
        self.browser: Optional[SyncBrowser] = None
        self.context: Optional[SyncBrowserContext] = None
        self.page: Optional[SyncPage] = None

        # 业务逻辑层服务（延迟初始化）
        self.workflow_orchestrator: Optional[WorkflowOrchestrator] = None
        self.login_service: Optional[LoginService] = None
        self.registration_service: Optional[RegistrationService] = None
        self.verification_service: Optional[VerificationService] = None

        # 状态标志
        self._initialized = False
        self._captcha_solver_enabled = True
        self._debug_mode_pending = False  # 待应用的debug模式标志

        # 会话状态跟踪
        self.session_phase = "initialization"
        self.visited_sites = []
        self.failed_attempts = 0
        self.session_start_time = time.time()

        # 验证码求解器（延迟初始化）
        self._captcha_solver = None
        
        # 初始化截图目录和清理过期文件
        ensure_screenshot_directory()
        cleanup_old_screenshots()

        # 临时设置当前邮箱地址（用于邮件验证）
        self.current_email = None

        self.logger.info("🏗️ HuluBotFacade门面服务初始化完成")

    def _command_executor(self, command: str, args: Dict[str, str]) -> Dict[str, str]:
        """
        统一的命令执行器 - 表示层职责分离的核心

        Args:
            command: 命令名称
            args: 处理后的参数

        Returns:
            Dict[str, str]: 执行结果
        """
        # 确保服务已初始化
        self._initialize_services()

        if command == "register_account":
            return self._execute_registration(args)
        elif command == "login_account":
            return self._execute_login(args)
        elif command == "is_logged_in":
            return self._execute_status_check(args)
        else:
            return {
                'status': 'error',
                'message': f'未知命令: {command}'
            }

    def _execute_registration(self, args: Dict[str, str]) -> Dict[str, str]:
        """执行注册业务逻辑"""
        try:
            result = self.workflow_orchestrator.execute_registration_workflow(
                email=args.get('email'),
                password=args.get('password'),
                name=args.get('name')
            )

            if result['success']:
                return result['account_info']
            else:
                return {
                    'status': 'failed',
                    'message': result.get('error', '注册失败'),
                    'email': args.get('email'),
                    'password': args.get('password'),
                    'name': args.get('name')
                }
        except Exception as e:
            return {
                'status': 'error',
                'message': str(e),
                'email': args.get('email'),
                'password': args.get('password'),
                'name': args.get('name')
            }

    def _execute_login(self, args: Dict[str, str]) -> Dict[str, str]:
        """执行登录业务逻辑"""
        try:
            # 设置当前邮箱（为邮件验证做准备）
            self.current_email = args.get('email')

            result = self.workflow_orchestrator.execute_login_workflow(
                email=args.get('email'),
                password=args.get('password')
            )

            if result['success']:
                # 确保返回值包含 login_status 字段以保持向后兼容
                login_info = result['login_info']
                if 'login_status' not in login_info:
                    login_info['login_status'] = login_info.get('status', 'success')
                return login_info
            else:
                return {
                    'login_status': 'failed',
                    'message': result.get('error', '登录失败'),
                    'email': args.get('email'),
                    'password': args.get('password')
                }
        except Exception as e:
            return {
                'login_status': 'error',
                'message': str(e),
                'email': args.get('email'),
                'password': args.get('password')
            }

    def _execute_status_check(self, args: Dict[str, str]) -> Dict[str, str]:
        """执行状态检查业务逻辑"""
        try:
            is_logged = self.is_logged_in()
            return {
                'status': 'success',
                'logged_in': is_logged,
                'message': '状态检查完成'
            }
        except Exception as e:
            return {
                'status': 'error',
                'logged_in': False,
                'message': str(e)
            }
    
    def _initialize_services(self):
        """初始化业务逻辑层服务"""
        if not self._initialized and self.page:
            # 初始化工作流编排器，传递状态管理器和上下文
            self.workflow_orchestrator = WorkflowOrchestrator(
                page=self.page,
                context=self.context,
                state_manager=self.state_manager,
                storage_state_path=self.storage_state_path
            )

            # 设置上下文（如果还没有设置）
            if self.context:
                self.workflow_orchestrator.set_context(self.context)

            # 获取各个服务实例
            self.login_service = self.workflow_orchestrator.login_service
            self.registration_service = self.workflow_orchestrator.registration_service
            self.verification_service = self.workflow_orchestrator.verification_service

            # 设置验证码求解器状态
            self.verification_service.enable_captcha_solver(self._captcha_solver_enabled)
            
            # 应用待设置的debug模式
            if hasattr(self, '_debug_mode_pending') and self._debug_mode_pending:
                self.login_service.debug_mode = True
                self.logger.info("🎯 应用待设置的DEBUG模式")

            self._initialized = True
            self.logger.info("🎯 业务逻辑层服务初始化完成")
    
    def enable_captcha_solver(self, enabled: bool = True):
        """
        启用或禁用验证码求解功能
        
        Args:
            enabled: 是否启用验证码求解
        """
        self._captcha_solver_enabled = enabled
        if self.verification_service:
            self.verification_service.enable_captcha_solver(enabled)
        if enabled:
            self.logger.info("🛡️ 验证码求解功能已启用")
        else:
            self.logger.info("⚠️  验证码求解功能已禁用")
    
    def enable_debug_mode(self, enabled: bool = True):
        """
        启用或禁用DEBUG模式 - 解决与debug_login_button_dom.py的差异
        
        Args:
            enabled: 是否启用DEBUG模式
        """
        if self.login_service:
            self.login_service.debug_mode = enabled
            if enabled:
                self.logger.info("🎯 DEBUG模式已启用 - 使用debug_login_button_dom.py的简化逻辑")
            else:
                self.logger.info("🔄 DEBUG模式已禁用 - 使用标准登录逻辑")
        else:
            if enabled:
                self.logger.warning("⚠️ LoginService未初始化，DEBUG模式将在服务初始化后启用")
            # 设置一个标志，在服务初始化时应用
            self._debug_mode_pending = enabled
    
    def apply_anti_detection_config(self, config):
        """
        应用反检测配置
        
        Args:
            config: AntiDetectionConfig实例
        """
        self._pending_anti_detection_config = config
        
        # 如果反检测服务已初始化，立即应用配置
        if hasattr(self.anti_detection_service, 'apply_config'):
            try:
                self.anti_detection_service.apply_config(config)
                self.logger.info("✅ 反检测配置已应用")
            except Exception as e:
                self.logger.warning(f"⚠️ 立即应用反检测配置失败: {e}")
                # 配置将在浏览器初始化时应用
        else:
            self.logger.info("⚠️ 反检测服务未完全初始化，配置将稍后应用")

    def register_account(self, email: str = None, password: str = None, name: str = None) -> Dict[str, str]:
        """
        执行完整的注册流程 - 统一命令处理流程

        Args:
            email: 邮箱地址（如果未提供，将自动生成）
            password: 密码（如果未提供，将自动生成）
            name: 用户名（如果未提供，将自动生成）

        Returns:
            Dict[str, str]: 包含注册结果和账户信息的字典
        """
        # 通过CommandProcessor统一处理命令
        command_args = {
            'email': email,
            'password': password,
            'name': name
        }

        return self.command_processor.process_command(
            'register_account',
            command_args,
            self._command_executor
        )

    def login_account(self, email: str = None, password: str = None) -> Dict[str, str]:
        """
        执行Hulu账户登录流程 - 统一命令处理流程

        Args:
            email: 登录邮箱（如果未提供且指定了账户，将使用指定账户的信息）
            password: 登录密码（如果未提供且指定了账户，将使用指定账户的信息）

        Returns:
            Dict[str, str]: 包含登录状态和会话信息的字典
        """
        # 如果有指定账户且没有提供email/password，使用指定账户的信息
        if self.current_account:
            actual_email = email or self.current_account.email
            actual_password = password or self.current_account.password
        else:
            # 使用传统的默认值
            actual_email = email or "<EMAIL>"
            actual_password = password or "Fiona127,."
        
        # 通过CommandProcessor统一处理命令
        command_args = {
            'email': actual_email,
            'password': actual_password
        }

        return self.command_processor.process_command(
            'login_account',
            command_args,
            self._command_executor
        )

    def is_logged_in(self) -> bool:
        """
        检查当前是否处于登录状态
        
        Returns:
            bool: 是否已登录
        """
        # 优先使用业务服务的状态检查
        try:
            # 如果服务已初始化，使用LoginService检查
            if self._initialized and self.login_service:
                return self.login_service.check_login_status()
            
            # 否则使用基本检查
            if not self.page:
                return False
            
            current_url = self.page.url
            
            # 检查URL是否表明已登录
            logged_in_indicators = [
                'welcome', 'hub', 'profiles', 'dashboard',
                'home', 'account', 'settings', 'my-account'
            ]
            
            url_indicates_login = any(indicator in current_url.lower() for indicator in logged_in_indicators)
            
            self.logger.debug(f"当前URL: {current_url}")
            self.logger.debug(f"URL登录状态指示: {url_indicates_login}")
            
            return url_indicates_login
            
        except Exception as e:
            self.logger.error(f"检查登录状态时出错: {str(e)}")
            return False

    def __enter__(self):
        """上下文管理器入口 - 使用Playwright高级反检测浏览器"""
        try:
            # 使用基础设施层的浏览器引擎
            self.browser = self.browser_engine.initialize_browser(
                headless=self.headless,
                storage_state_path=self.storage_state_path if self._storage_state_exists() else None
            )

            # 获取浏览器引擎创建的实例
            self.context = self.browser_engine.context
            self.page = self.browser_engine.page

            # 设置错误处理器的页面引用
            self.error_handler.set_page(self.page)

            # 应用待定的反检测配置（如果有）
            if self._pending_anti_detection_config:
                try:
                    if hasattr(self.anti_detection_service, 'apply_config'):
                        self.anti_detection_service.apply_config(self._pending_anti_detection_config)
                        self.logger.info("✅ 待定的反检测配置已应用")
                    self._pending_anti_detection_config = None
                except Exception as e:
                    self.logger.warning(f"⚠️ 应用待定反检测配置失败: {e}")

            # 应用综合反检测措施
            self.anti_detection_service.apply_comprehensive_anti_detection(self.page, self.context)

            # 初始化业务逻辑层服务
            self._initialize_services()
            
            self.logger.info("🚀 HuluBotFacade门面服务启动成功")
            return self
            
        except Exception as e:
            self.logger.error(f"启动门面服务时出错: {str(e)}")
            self._cleanup()
            raise

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器退出"""
        self._cleanup()
    
    def _storage_state_exists(self) -> bool:
        """检查存储状态文件是否存在"""
        import os
        return os.path.exists(self.storage_state_path)
    

    
    def restore_session_if_needed(self) -> bool:
        """
        恢复会话状态（如果需要）
        
        Returns:
            bool: 是否成功恢复会话
        """
        try:
            # 确保服务已初始化
            self._initialize_services()
            
            if self.login_service:
                return self.login_service.restore_session()
            else:
                self.logger.warning("登录服务未初始化，无法恢复会话")
                return False
                
        except Exception as e:
            self.logger.error(f"恢复会话时出错: {str(e)}")
            return False
    
    def save_storage_state(self, force: bool = False) -> bool:
        """
        保存存储状态（暂时禁用，专注于表单提交测试）
        
        Args:
            force: 是否强制保存
            
        Returns:
            bool: 是否成功保存
        """
        try:
            # 暂时禁用状态保存，专注于表单提交测试
            self.logger.info("🔧 状态保存已禁用，专注于表单提交测试")
            return False
                
        except Exception as e:
            self.logger.error(f"保存存储状态时出错: {str(e)}")
            return False
    
    def _load_storage_state(self) -> bool:
        """
        加载存储状态
        
        Returns:
            bool: 是否成功加载
        """
        try:
            import os
            if os.path.exists(self.storage_state_path):
                # 状态文件存在，在创建浏览器上下文时会自动加载
                self.logger.info(f"✅ 存储状态文件存在: {self.storage_state_path}")
                return True
            else:
                self.logger.info("📁 存储状态文件不存在，将创建新会话")
                return False
                
        except Exception as e:
            self.logger.error(f"加载存储状态时出错: {str(e)}")
            return False
    
    def clear_storage_state(self) -> bool:
        """
        清除存储状态文件
        
        Returns:
            bool: 是否成功清除
        """
        try:
            import os
            if os.path.exists(self.storage_state_path):
                os.remove(self.storage_state_path)
                self.logger.info(f"🗑️ 存储状态文件已清除: {self.storage_state_path}")
                return True
            else:
                self.logger.info("📁 存储状态文件不存在，无需清除")
                return True
                
        except Exception as e:
            self.logger.error(f"清除存储状态时出错: {str(e)}")
            return False
    
    def get_session_summary(self) -> dict:
        """
        获取会话摘要信息
        
        Returns:
            dict: 会话摘要信息
        """
        try:
            import time
            
            # 计算会话时长
            duration_minutes = (time.time() - self.session_start_time) / 60
            
            session_summary = {
                'session_id': f"session_{int(self.session_start_time)}",
                'duration_minutes': round(duration_minutes, 2),
                'phase': self.session_phase,
                'visited_sites': len(self.visited_sites),
                'sites': self.visited_sites,
                'failed_attempts': self.failed_attempts,
                'start_time': self.session_start_time
            }
            
            return session_summary
            
        except Exception as e:
            self.logger.error(f"获取会话摘要时出错: {str(e)}")
            return {
                'session_id': 'error',
                'duration_minutes': 0,
                'phase': 'error',
                'visited_sites': 0,
                'sites': [],
                'failed_attempts': 0
            }

    @classmethod
    def with_account_index(cls, index: int, **kwargs):
        """
        使用第N个账户创建实例
        
        Args:
            index: 账户索引（从0开始）
            **kwargs: 其他初始化参数
            
        Returns:
            HuluBotFacade: 配置好的实例
        """
        return cls(account_index=index, **kwargs)
    
    @classmethod
    def with_account_email(cls, email: str, **kwargs):
        """
        使用指定email的账户创建实例
        
        Args:
            email: 账户邮箱地址
            **kwargs: 其他初始化参数
            
        Returns:
            HuluBotFacade: 配置好的实例
        """
        return cls(account_email=email, **kwargs)

    def _cleanup(self):
        """清理资源（移除自动状态保存）"""
        try:
            # 移除自动保存状态的逻辑，专注于测试表单提交
            self.logger.info("🔧 跳过自动状态保存，专注于表单提交测试")
            
            # 使用基础设施层的浏览器引擎清理
            if self.browser_engine:
                self.browser_engine.cleanup()

            # 清理引用
            self.page = None
            self.context = None
            self.browser = None
                
            self.logger.info("🧹 HuluBotFacade门面服务清理完成")
            
        except Exception as e:
            self.logger.error(f"清理资源时出错: {str(e)}")