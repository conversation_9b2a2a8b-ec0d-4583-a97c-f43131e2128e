#!/usr/bin/env python3
"""
CommandProcessor - 表示层命令处理器
处理参数验证、命令执行协调和响应格式化
"""

import logging
from typing import Dict, Any, Optional, Callable
import re


class CommandProcessor:
    """
    命令处理器 - 表示层命令处理服务
    
    负责参数验证、命令执行协调和响应格式化
    """

    def __init__(self):
        """初始化命令处理器"""
        self.logger = logging.getLogger(__name__)
        self.logger.info("🎯 CommandProcessor命令处理器初始化完成")

    def process_command(self, command: str, args: Dict[str, Any], executor: Callable = None) -> Dict[str, Any]:
        """
        处理外部命令 - 支持依赖注入的执行器

        Args:
            command: 命令名称
            args: 命令参数
            executor: 可选的命令执行器函数

        Returns:
            Dict[str, Any]: 格式化后的命令执行结果
        """
        try:
            self.logger.info(f"📨 处理命令: {command}")

            # 参数验证
            if not self.validate_parameters(command, args):
                return self.format_error_response(
                    "参数验证失败",
                    "提供的参数不符合要求"
                )

            # 参数预处理
            processed_args = self._preprocess_parameters(command, args)

            # 命令执行 - 使用注入的执行器或默认实现
            if executor:
                result = executor(command, processed_args)
            else:
                result = self._execute_command(command, processed_args)

            # 检查结果是否为错误，如果是则进行统一格式化
            if self._is_error_result(result):
                return self._normalize_error_format(result, command, processed_args)

            # 响应格式化
            return self.format_response(result)

        except Exception as e:
            self.logger.error(f"❌ 命令处理失败: {str(e)}")
            return self.handle_command_errors(e, command, args)

    def validate_parameters(self, command: str, args: Dict[str, Any]) -> bool:
        """
        验证参数有效性
        
        Args:
            command: 命令名称
            args: 参数字典
            
        Returns:
            bool: 验证是否通过
        """
        try:
            if command == "register_account":
                return self._validate_register_params(args)
            elif command == "login_account":
                return self._validate_login_params(args)
            elif command == "is_logged_in":
                return True  # 无需参数验证
            else:
                self.logger.warning(f"⚠️ 未知命令: {command}")
                return False
                
        except Exception as e:
            self.logger.error(f"参数验证时出错: {str(e)}")
            return False

    def _validate_register_params(self, args: Dict[str, Any]) -> bool:
        """验证注册参数"""
        # 邮箱验证
        if 'email' in args and args['email'] is not None:
            if not self._is_valid_email(args['email']):
                self.logger.error("❌ 无效的邮箱地址")
                return False
        
        # 密码验证
        if 'password' in args and args['password'] is not None:
            if len(args['password']) < 6:
                self.logger.error("❌ 密码长度至少6位")
                return False
        
        # 用户名验证
        if 'name' in args and args['name'] is not None:
            if len(args['name']) < 2:
                self.logger.error("❌ 用户名长度至少2位")
                return False
        
        return True

    def _validate_login_params(self, args: Dict[str, Any]) -> bool:
        """验证登录参数"""
        # 邮箱验证
        if 'email' in args and args['email'] is not None:
            if not self._is_valid_email(args['email']):
                self.logger.error("❌ 无效的邮箱地址")
                return False
        
        # 密码验证
        if 'password' in args and args['password'] is not None:
            if len(args['password']) < 1:
                self.logger.error("❌ 密码不能为空")
                return False
        
        return True

    def _is_valid_email(self, email: str) -> bool:
        """验证邮箱格式"""
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(pattern, email) is not None

    def _preprocess_parameters(self, command: str, args: Dict[str, Any]) -> Dict[str, Any]:
        """
        参数预处理
        
        Args:
            command: 命令名称
            args: 原始参数
            
        Returns:
            Dict[str, Any]: 预处理后的参数
        """
        processed_args = args.copy()
        
        # 邮箱地址标准化
        if 'email' in processed_args and processed_args['email']:
            processed_args['email'] = processed_args['email'].lower().strip()
        
        # 密码处理
        if 'password' in processed_args and processed_args['password']:
            processed_args['password'] = processed_args['password'].strip()
        
        # 用户名处理
        if 'name' in processed_args and processed_args['name']:
            processed_args['name'] = processed_args['name'].strip()
        
        return processed_args

    def _execute_command(self, command: str, args: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行命令（临时实现）
        
        Args:
            command: 命令名称
            args: 处理后的参数
            
        Returns:
            Dict[str, Any]: 执行结果
        """
        # TODO: 在后续阶段，这里将调用HuluBotFacade执行实际命令
        # 现在只返回模拟结果
        
        if command == "register_account":
            return {
                'status': 'pending',
                'message': '注册命令已接收，等待门面类集成',
                'email': args.get('email'),
                'password': args.get('password'),
                'name': args.get('name')
            }
        elif command == "login_account":
            return {
                'status': 'pending',
                'message': '登录命令已接收，等待门面类集成',
                'email': args.get('email'),
                'password': args.get('password')
            }
        elif command == "is_logged_in":
            return {
                'status': 'success',
                'logged_in': False,
                'message': '状态检查完成，等待门面类集成'
            }
        else:
            return {
                'status': 'error',
                'message': f'未知命令: {command}'
            }

    def format_response(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """
        格式化返回结果 - 保持API兼容性

        Args:
            result: 原始结果

        Returns:
            Dict[str, Any]: 格式化后的结果
        """
        # 为了保持向后兼容性，直接返回原始结果
        # 只在必要时添加时间戳
        if isinstance(result, dict):
            # 创建副本以避免修改原始数据
            formatted_result = result.copy()

            # 只在没有时间戳时添加
            if 'timestamp' not in formatted_result:
                formatted_result['timestamp'] = self._get_timestamp()

            return formatted_result
        else:
            # 如果不是字典，包装成标准格式
            return {
                'status': 'success',
                'data': result,
                'timestamp': self._get_timestamp()
            }

    def format_error_response(self, error_type: str, error_message: str) -> Dict[str, Any]:
        """
        格式化错误响应
        
        Args:
            error_type: 错误类型
            error_message: 错误信息
            
        Returns:
            Dict[str, Any]: 格式化后的错误响应
        """
        return {
            'status': 'error',
            'error_type': error_type,
            'message': error_message,
            'timestamp': self._get_timestamp()
        }

    def handle_command_errors(self, error: Exception, command: str = None, args: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        处理命令执行错误 - 根据命令类型返回兼容格式

        Args:
            error: 异常对象
            command: 命令名称（可选，用于确定错误格式）
            args: 命令参数（可选，用于包含在错误响应中）

        Returns:
            Dict[str, Any]: 错误处理结果
        """
        error_message = str(error)
        error_type = type(error).__name__

        self.logger.error(f"命令执行错误 - {error_type}: {error_message}")

        # 根据命令类型返回兼容的错误格式
        if command == 'login_account':
            return {
                'login_status': 'error',
                'message': error_message,
                'email': args.get('email') if args else None,
                'password': args.get('password') if args else None,
                'timestamp': self._get_timestamp()
            }
        elif command == 'register_account':
            return {
                'status': 'error',
                'message': error_message,
                'email': args.get('email') if args else None,
                'password': args.get('password') if args else None,
                'name': args.get('name') if args else None,
                'timestamp': self._get_timestamp()
            }
        else:
            # 默认错误格式
            return {
                'status': 'error',
                'error_type': error_type,
                'message': error_message,
                'timestamp': self._get_timestamp()
            }

    def _get_timestamp(self) -> str:
        """获取当前时间戳"""
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    def _is_error_result(self, result: Dict[str, Any]) -> bool:
        """
        检查结果是否为错误

        Args:
            result: 执行结果

        Returns:
            bool: 是否为错误结果
        """
        if not isinstance(result, dict):
            return False

        # 检查各种错误状态
        return (
            result.get('status') == 'error' or
            result.get('status') == 'failed' or
            result.get('login_status') == 'error' or
            result.get('login_status') == 'failed'
        )

    def _normalize_error_format(self, result: Dict[str, Any], command: str, args: Dict[str, Any]) -> Dict[str, Any]:
        """
        标准化错误格式

        Args:
            result: 原始错误结果
            command: 命令名称
            args: 命令参数

        Returns:
            Dict[str, Any]: 标准化后的错误格式
        """
        error_message = result.get('message', '未知错误')

        if command == 'login_account':
            return {
                'login_status': 'failed' if result.get('login_status') == 'failed' else 'error',
                'message': error_message,
                'email': args.get('email'),
                'password': args.get('password'),
                'timestamp': self._get_timestamp()
            }
        elif command == 'register_account':
            return {
                'status': 'failed' if result.get('status') == 'failed' else 'error',
                'message': error_message,
                'email': args.get('email'),
                'password': args.get('password'),
                'name': args.get('name'),
                'timestamp': self._get_timestamp()
            }
        else:
            return {
                'status': 'error',
                'message': error_message,
                'timestamp': self._get_timestamp()
            }