#!/usr/bin/env python3
"""
VerificationService - 业务逻辑层验证服务
处理reCAPTCHA检测求解和邮件验证码处理
"""

import logging
import re
import time
from typing import Dict, Optional, Any
from playwright.sync_api import Page

# 导入依赖组件
from utils import TempMailAPI
from captcha_solver import get_captcha_solver


class VerificationService:
    """
    验证服务 - 处理各种验证逻辑
    
    职责：
    1. reCAPTCHA检测和求解
    2. 邮件验证码处理
    3. 验证码提交和确认
    4. 双验证码服务商切换
    """
    
    def __init__(self, page: Page = None):
        """
        初始化验证服务
        
        Args:
            page: Playwright页面对象
        """
        self.page = page
        self.logger = logging.getLogger(__name__)
        self._captcha_solver = None
        self._captcha_solver_enabled = True
        
        self.logger.info("🛡️ VerificationService验证服务初始化完成")
    
    def set_page(self, page: Page):
        """设置页面对象"""
        self.page = page
    
    def enable_captcha_solver(self, enabled: bool = True):
        """
        启用或禁用验证码求解功能
        
        Args:
            enabled: 是否启用验证码求解
        """
        self._captcha_solver_enabled = enabled
        if enabled:
            self.logger.info("🛡️ 验证码求解功能已启用")
        else:
            self.logger.info("⚠️ 验证码求解功能已禁用")
    
    def _get_captcha_solver(self):
        """获取验证码求解器实例（延迟初始化）"""
        if self._captcha_solver is None:
            try:
                self._captcha_solver = get_captcha_solver()
                self.logger.info("🛡️ 验证码求解器初始化成功")
            except Exception as e:
                self.logger.error(f"❌ 验证码求解器初始化失败: {str(e)}")
                self._captcha_solver = None
        return self._captcha_solver
    
    def detect_recaptcha(self) -> Dict[str, Optional[str]]:
        """
        检测页面中的reCAPTCHA
        
        Returns:
            Dict: 包含site_key和page_url的字典，如果没有检测到则返回None值
        """
        try:
            if not self.page:
                self.logger.error("❌ 页面对象未设置")
                return {'site_key': None, 'page_url': None}
            
            self.logger.info("🔍 检测页面中的reCAPTCHA...")
            
            # 常见的reCAPTCHA选择器
            recaptcha_selectors = [
                '.g-recaptcha',
                '[data-sitekey]',
                '.recaptcha',
                'iframe[src*="recaptcha"]',
                'script[src*="recaptcha"]',
                '.grecaptcha',
                '[class*="recaptcha"]',
                '[id*="recaptcha"]'
            ]
            
            site_key = None
            page_url = self.page.url
            
            # 尝试从各种元素中提取site key
            for selector in recaptcha_selectors:
                try:
                    elements = self.page.query_selector_all(selector)
                    for element in elements:
                        # 检查data-sitekey属性
                        sitekey = element.get_attribute('data-sitekey')
                        if sitekey:
                            site_key = sitekey
                            self.logger.info(f"🎯 从 {selector} 检测到reCAPTCHA site key: {site_key[:20]}...")
                            break
                        
                        # 检查其他可能的属性
                        for attr in ['data-key', 'key', 'sitekey']:
                            key = element.get_attribute(attr)
                            if key and len(key) > 20:  # reCAPTCHA site key通常比较长
                                site_key = key
                                self.logger.info(f"🎯 从 {selector}[{attr}] 检测到reCAPTCHA site key: {site_key[:20]}...")
                                break
                        
                        if site_key:
                            break
                    
                    if site_key:
                        break
                        
                except Exception as e:
                    self.logger.debug(f"检查选择器 {selector} 时出错: {str(e)}")
                    continue
            
            # 如果没有找到site key，尝试从页面源码中提取
            if not site_key:
                try:
                    page_content = self.page.content()
                    
                    # 正则表达式匹配常见的reCAPTCHA配置
                    patterns = [
                        r'data-sitekey["\']?\s*[:=]\s*["\']([^"\']+)["\']',
                        r'sitekey["\']?\s*[:=]\s*["\']([^"\']+)["\']',
                        r'grecaptcha\.render\([^,]*,\s*["\']([^"\']+)["\']',
                        r'"sitekey"\s*:\s*"([^"]+)"',
                        r"'sitekey'\s*:\s*'([^']+)'",
                    ]
                    
                    for pattern in patterns:
                        matches = re.findall(pattern, page_content, re.IGNORECASE)
                        for match in matches:
                            if len(match) > 20:  # 确保是有效的site key
                                site_key = match
                                self.logger.info(f"🎯 从页面源码检测到reCAPTCHA site key: {site_key[:20]}...")
                                break
                        if site_key:
                            break
                            
                except Exception as e:
                    self.logger.debug(f"从页面源码提取site key时出错: {str(e)}")
            
            if site_key:
                return {
                    'site_key': site_key,
                    'page_url': page_url
                }
            else:
                self.logger.debug("未检测到reCAPTCHA")
                return {
                    'site_key': None,
                    'page_url': None
                }
                
        except Exception as e:
            self.logger.error(f"reCAPTCHA检测过程中出错: {str(e)}")
            return {
                'site_key': None,
                'page_url': None
            }
    
    def solve_recaptcha(self, site_key: str, page_url: str) -> Optional[str]:
        """
        求解reCAPTCHA
        
        Args:
            site_key: reCAPTCHA site key
            page_url: 页面URL
            
        Returns:
            Optional[str]: reCAPTCHA token，求解失败返回None
        """
        try:
            if not self._captcha_solver_enabled:
                self.logger.info("⚠️ 验证码求解功能已禁用")
                return None
            
            self.logger.info("🧩 开始求解reCAPTCHA...")
            
            # 获取验证码求解器
            captcha_solver = self._get_captcha_solver()
            if not captcha_solver:
                self.logger.error("❌ 验证码求解器不可用")
                return None
            
            # 求解reCAPTCHA
            recaptcha_token = captcha_solver.solve_recaptcha_v3(
                site_key=site_key,
                page_url=page_url,
                min_score=0.3
            )
            
            if not recaptcha_token:
                self.logger.error("❌ reCAPTCHA求解失败")
                return None
            
            self.logger.info("✅ reCAPTCHA求解成功！")
            return recaptcha_token
                
        except Exception as e:
            self.logger.error(f"❌ reCAPTCHA求解过程中出错: {str(e)}")
            return None
    
    def submit_recaptcha_token(self, token: str) -> bool:
        """
        提交reCAPTCHA token到页面
        
        Args:
            token: reCAPTCHA响应token
            
        Returns:
            bool: 是否成功提交
        """
        try:
            if not self.page:
                self.logger.error("❌ 页面对象未设置")
                return False
            
            self.logger.info("📝 提交reCAPTCHA token...")
            
            # 常见的提交方法
            submission_methods = [
                # 方法1: 直接设置g-recaptcha-response隐藏字段
                lambda: self.page.evaluate(f"""
                    const element = document.querySelector('textarea[name="g-recaptcha-response"]');
                    if (element) {{
                        element.value = '{token}';
                        element.style.display = 'block';
                        return true;
                    }}
                    return false;
                """),
                
                # 方法2: 调用grecaptcha.getResponse设置token
                lambda: self.page.evaluate(f"""
                    if (typeof grecaptcha !== 'undefined' && grecaptcha.getResponse) {{
                        try {{
                            const widgets = document.querySelectorAll('.g-recaptcha');
                            for (let widget of widgets) {{
                                const widgetId = widget.getAttribute('data-widget-id');
                                if (widgetId) {{
                                    grecaptcha.reset(widgetId);
                                    // 设置响应
                                    const responseArea = document.querySelector('textarea[name="g-recaptcha-response"]');
                                    if (responseArea) {{
                                        responseArea.value = '{token}';
                                        responseArea.style.display = 'block';
                                    }}
                                    return true;
                                }}
                            }}
                        }} catch (e) {{
                            console.log('grecaptcha method failed:', e);
                        }}
                    }}
                    return false;
                """),
                
                # 方法3: 寻找并设置所有相关的隐藏字段
                lambda: self.page.evaluate(f"""
                    let success = false;
                    const selectors = [
                        'textarea[name="g-recaptcha-response"]',
                        'input[name="g-recaptcha-response"]',
                        'textarea[id*="recaptcha"]',
                        'input[id*="recaptcha"]'
                    ];
                    
                    for (let selector of selectors) {{
                        const elements = document.querySelectorAll(selector);
                        for (let element of elements) {{
                            element.value = '{token}';
                            element.style.display = 'block';
                            success = true;
                        }}
                    }}
                    return success;
                """)
            ]
            
            # 尝试各种提交方法
            for i, method in enumerate(submission_methods):
                try:
                    result = method()
                    if result:
                        self.logger.info(f"✅ reCAPTCHA token提交成功 (方法 {i+1})")
                        return True
                except Exception as e:
                    self.logger.debug(f"提交方法 {i+1} 失败: {str(e)}")
                    continue
            
            self.logger.error("❌ 所有reCAPTCHA token提交方法都失败")
            return False
            
        except Exception as e:
            self.logger.error(f"❌ reCAPTCHA token提交过程中出错: {str(e)}")
            return False
    
    def solve_and_submit_recaptcha(self, site_key: str, page_url: str) -> bool:
        """
        求解并提交reCAPTCHA（组合操作）
        
        Args:
            site_key: reCAPTCHA site key
            page_url: 页面URL
            
        Returns:
            bool: 是否成功求解并提交
        """
        try:
            # 求解reCAPTCHA
            token = self.solve_recaptcha(site_key, page_url)
            if not token:
                return False
            
            # 提交token
            success = self.submit_recaptcha_token(token)
            if success:
                self.logger.info("✅ reCAPTCHA求解和提交完成")
                return True
            else:
                self.logger.error("❌ reCAPTCHA token提交失败")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ reCAPTCHA求解和提交过程中出错: {str(e)}")
            return False
    
    def handle_email_verification(self, email: str) -> Dict[str, Any]:
        """
        处理邮箱验证 - 使用TempMail API获取验证码
        
        Args:
            email: 邮箱地址
            
        Returns:
            Dict[str, Any]: 包含验证码和处理结果的字典
        """
        try:
            self.logger.info("🔍 处理邮箱验证...")
            
            if not self.page:
                return {
                    'success': False,
                    'message': '页面对象未设置',
                    'verification_code': None
                }
            
            # 检查当前页面状态
            current_url = self.page.url
            self.logger.info(f"当前页面URL: {current_url}")
            
            # 检查是否有验证码输入字段 (基于hulu_bot.py的成功实现)
            verification_selectors = [
                'input[name="otp"]',  # 🔑 Hulu特有的隐藏OTP输入框 (最高优先级)
                'input[name="verificationCode"]',
                'input[name="verification_code"]',
                'input[name="code"]',
                'input[name="authCode"]',
                'input[maxlength="6"]',  # 6位验证码字段
                'input[maxlength="1"]',  # 单字符输入框
                'input[placeholder*="code"]',
                'input[placeholder*="verification"]',
                'input[placeholder*="verify"]',
                'input[data-testid*="verification"]',
                'input[data-testid*="code"]',
                'input[data-testid*="verify"]',
                'input[id*="verification"]',
                'input[id*="code"]',
                'input[type="text"]',  # 通用文本输入 (最低优先级)
                'div[role="textbox"]',   # 自定义输入组件
                '[contenteditable="true"]'  # 可编辑内容
            ]
            
            verification_field_found = False
            found_selector = None
            for selector in verification_selectors:
                try:
                    locator = self.page.locator(selector)
                    if locator.count() > 0:
                        # 🔑 Hulu的OTP输入框是隐藏的，所以检查存在性而不是可见性
                        if selector == 'input[name="otp"]':
                            # 对于Hulu特有的OTP字段，验证它确实是用于验证码的
                            maxlength = locator.get_attribute("maxlength")
                            autocomplete = locator.get_attribute("autocomplete")
                            if maxlength == "6" and "code" in (autocomplete or ""):
                                self.logger.info(f"✅ 发现Hulu隐藏OTP输入字段: {selector} (maxlength={maxlength}, autocomplete={autocomplete})")
                                verification_field_found = True
                                found_selector = selector
                                break
                        elif locator.is_visible():
                            self.logger.info(f"✅ 发现可见验证码输入字段: {selector} (共{locator.count()}个)")
                            verification_field_found = True
                            found_selector = selector
                            break
                except:
                    continue
            
            if not verification_field_found:
                self.logger.info("ℹ️ 未发现验证码输入字段，可能无需邮箱验证")
                return {
                    'success': True,
                    'message': '无需邮箱验证',
                    'verification_code': None
                }
            
            # 如果需要验证码，使用API获取
            self.logger.info(f"📧 等待邮箱验证码: {email}")
            self.logger.info("⏳ 这可能需要几分钟时间，请耐心等待...")
            
            # 创建TempMail API实例
            mail_api = TempMailAPI()
            verification_code = mail_api.wait_for_verification_code(email)
            
            if verification_code:
                self.logger.info(f"✅ 成功获取验证码: {verification_code}")
                
                # 填写验证码
                filled = self.submit_verification_code(verification_code)
                if filled:
                    return {
                        'success': True,
                        'message': '邮箱验证完成',
                        'verification_code': verification_code
                    }
                else:
                    return {
                        'success': False,
                        'message': '获取到验证码但填写失败',
                        'verification_code': verification_code
                    }
            else:
                self.logger.warning("⚠️ 未能获取到验证码")
                return {
                    'success': False,
                    'message': '未能获取到验证码',
                    'verification_code': None
                }
                
        except Exception as e:
            self.logger.error(f"❌ 处理邮箱验证时出错: {str(e)}")
            return {
                'success': False,
                'message': f'处理邮箱验证时出错: {str(e)}',
                'verification_code': None
            }
    
    def submit_verification_code(self, code: str) -> bool:
        """
        智能提交验证码 - 支持Hulu特殊OTP输入框和传统6个独立输入框
        基于hulu_bot.py的成功实现，支持45-50 WPM人类行为模拟
        
        Args:
            code: 验证码
            
        Returns:
            bool: 是否成功提交
        """
        try:
            if not self.page:
                self.logger.error("❌ 页面对象未设置")
                return False
            
            if len(code) != 6:
                self.logger.error(f"❌ 验证码长度错误: {len(code)}, 需要6位")
                return False
            
            self.logger.info(f"📝 智能提交验证码: {code}")
            
            # 首先检查是否是Hulu特殊的验证码输入格式
            if self._detect_separate_input_boxes():
                self.logger.info("🎯 检测到特殊验证码输入格式 (Hulu OTP或独立输入框)")
                return self._fill_separate_verification_inputs(code)
            else:
                self.logger.info("🎯 使用传统单个输入框格式")
                return self._fill_traditional_verification_input(code)
            
        except Exception as e:
            self.logger.error(f"❌ 提交验证码时出错: {str(e)}")
            return False
    
    def _detect_separate_input_boxes(self) -> bool:
        """
        检测是否使用特殊的验证码输入格式（Hulu使用隐藏的单个输入框）
        基于hulu_bot.py的成功实现
        
        Returns:
            bool: 是否需要特殊处理
        """
        try:
            # 检查是否有Hulu特有的隐藏OTP输入框
            otp_input = self.page.locator('input[name="otp"]')
            if otp_input.count() > 0:
                maxlength = otp_input.get_attribute("maxlength")
                autocomplete = otp_input.get_attribute("autocomplete")
                self.logger.debug(f"🔍 检测到Hulu OTP输入框: maxlength={maxlength}, autocomplete={autocomplete}")
                return True
            
            # 检查是否有多个maxlength="1"的输入框（传统6位独立格式）
            single_char_inputs = self.page.locator('input[maxlength="1"]').all()
            if len(single_char_inputs) >= 6:
                self.logger.debug(f"🔍 检测到 {len(single_char_inputs)} 个传统单字符输入框")
                return True
            
            # 检查页面是否包含验证码框的特征文本
            page_text = self.page.text_content("body").lower()
            verification_indicators = [
                "enter it below",  # Hulu特有文本
                "6-digit code",
                "digit code",
                "passcode"
            ]
            
            for indicator in verification_indicators:
                if indicator in page_text:
                    self.logger.debug(f"🔍 通过文本指示器检测到验证码格式: {indicator}")
                    return True
            
            return False
            
        except Exception as e:
            self.logger.debug(f"🔍 检测验证码输入格式时出错: {e}")
            return False
    
    def _fill_separate_verification_inputs(self, code: str) -> bool:
        """
        填写特殊验证码输入框（支持Hulu隐藏OTP输入框和传统6个独立输入框）
        使用45-50 WPM人类行为模拟
        
        Args:
            code: 6位验证码
            
        Returns:
            bool: 是否成功填写
        """
        try:
            self.logger.info(f"🔢 开始人类化填写6位验证码: {code}")
            
            # 首先尝试Hulu特有的OTP输入框
            otp_input = self.page.locator('input[name="otp"]')
            if otp_input.count() > 0:
                self.logger.info("🎯 使用Hulu特有的OTP输入框")
                return self._fill_hulu_otp_input(otp_input, code)
            
            # 如果没有找到OTP输入框，尝试传统6个独立输入框
            single_char_inputs = self.page.locator('input[maxlength="1"]').all()
            if len(single_char_inputs) >= 6:
                self.logger.info(f"🎯 使用传统6个独立输入框 (找到{len(single_char_inputs)}个)")
                return self._fill_individual_digit_inputs(single_char_inputs, code)
            
            self.logger.error("❌ 未找到合适的特殊验证码输入格式")
            return False
            
        except Exception as e:
            self.logger.error(f"❌ 填写特殊验证码输入框时出错: {e}")
            return False
    
    def _clear_otp_input_playwright(self, otp_locator) -> bool:
        """
        使用Playwright原生API清空OTP输入框 - 安全的人类化删除方式
        参考hulu_bot_original.py中的_clear_otp_input_humanly方法

        Args:
            otp_locator: OTP输入框的Playwright locator

        Returns:
            bool: 是否成功清空
        """
        try:
            import random

            # 获取当前输入框内容
            current_value = otp_locator.input_value()
            if not current_value:
                self.logger.debug("✅ 输入框已为空，无需清空")
                return True

            content_length = len(current_value)
            self.logger.info(f"🔄 开始逐字删除 {content_length} 个字符: '{current_value}'")

            # 聚焦到输入框 - 确保正确的焦点状态
            otp_locator.focus()

            # 将光标移到末尾 - 确保从正确位置开始删除
            otp_locator.press("End")

            # 逐字符删除 - 模拟真实用户按退格键
            for i in range(content_length):
                # 人类化延迟 - 删除速度稍快于输入
                delete_delay = random.uniform(0.1, 0.2)
                time.sleep(delete_delay)

                # 按Backspace键删除一个字符 - 触发完整的键盘事件序列
                otp_locator.press("Backspace")

                self.logger.debug(f"🔙 删除第 {i+1}/{content_length} 个字符")

            # 验证是否完全清空
            final_value = otp_locator.input_value()
            if final_value:
                self.logger.warning(f"⚠️ 清空不完整，剩余内容: '{final_value}'")
                # 备选方案：全选+删除
                otp_locator.press("Control+a")
                time.sleep(random.uniform(0.1, 0.2))
                otp_locator.press("Delete")

                # 再次验证
                final_value = otp_locator.input_value()
                if final_value:
                    self.logger.error(f"❌ 清空失败，仍有内容: '{final_value}'")
                    return False

            self.logger.info("✅ 验证码输入框已完全清空")
            return True

        except Exception as e:
            self.logger.error(f"❌ 人类化清空输入框失败: {e}")
            return False
    
    def _type_otp_character_playwright(self, otp_locator, char: str) -> bool:
        """
        使用Playwright原生API输入单个字符到OTP输入框 - 安全的人类化输入方式

        Args:
            otp_locator: OTP输入框的Playwright locator
            char: 要输入的字符

        Returns:
            bool: 是否成功输入
        """
        try:
            # 使用Playwright的type方法 - 触发完整的键盘事件序列
            # 包含keydown, keypress, input, keyup等事件
            otp_locator.type(char)

            self.logger.debug(f"⌨️ Playwright输入字符 '{char}' 成功")
            return True

        except Exception as e:
            self.logger.error(f"❌ Playwright输入字符 '{char}' 失败: {e}")
            return False
    
    def _get_otp_locator(self):
        """
        获取OTP输入框的Playwright locator

        Returns:
            Playwright locator或None
        """
        try:
            otp_locator = self.page.locator('input[name="otp"]')
            if otp_locator.count() > 0:
                return otp_locator
            else:
                self.logger.error("❌ 未找到OTP输入框")
                return None
        except Exception as e:
            self.logger.error(f"❌ 获取OTP输入框失败: {e}")
            return None
    
    def _get_otp_value_playwright(self, otp_locator) -> str:
        """
        使用Playwright原生API获取OTP输入框的当前值

        Args:
            otp_locator: OTP输入框的Playwright locator

        Returns:
            str: 当前输入框的值
        """
        try:
            return otp_locator.input_value() or ""

        except Exception as e:
            self.logger.error(f"❌ Playwright获取OTP值失败: {e}")
            return ""
    
    def clear_otp_input_humanized(self) -> bool:
        """
        人类化清空OTP输入框 - 使用Playwright原生API，安全的逐字符删除模拟
        参考hulu_bot_original.py中的实现方式

        Returns:
            bool: 是否成功清空
        """
        try:
            # 获取OTP输入框locator
            otp_locator = self._get_otp_locator()
            if not otp_locator:
                self.logger.error("❌ 无法获取OTP输入框")
                return False

            # 使用Playwright原生API清空输入框
            return self._clear_otp_input_playwright(otp_locator)

        except Exception as e:
            self.logger.error(f"❌ 人类化清空OTP输入框失败: {e}")
            return False



    def _fill_hulu_otp_input(self, otp_locator, code: str) -> bool:
        """
        填写Hulu特有的OTP隐藏输入框 - 使用Playwright原生API，安全的45-50 WPM人类行为模拟
        参考hulu_bot_original.py中的实现方式

        Args:
            otp_locator: OTP输入框的Playwright locator
            code: 6位验证码

        Returns:
            bool: 是否成功填写并提交
        """
        try:
            import random

            # 计算人类化延迟（45-50 WPM对应约每字符0.15-0.25秒）
            char_delay = random.uniform(0.15, 0.25)

            # 先清空输入框确保覆盖
            current_value = self._get_otp_value_playwright(otp_locator)
            if current_value:
                self.logger.debug(f"🧹 清空现有内容: '{current_value}'")
                if not self._clear_otp_input_playwright(otp_locator):
                    self.logger.error("❌ 人类化清空OTP输入框失败")
                    return False
                time.sleep(char_delay)

            # 逐字符输入模拟人类打字速度 - 使用Playwright原生API
            self.logger.info("⌨️ 开始45-50 WPM人类化输入...")
            for i, char in enumerate(code):
                if not self._type_otp_character_playwright(otp_locator, char):
                    self.logger.error(f"❌ 输入字符 '{char}' 失败")
                    return False

                if i < len(code) - 1:
                    time.sleep(char_delay)

            self.logger.info("✅ 验证码填写完成（45-50 WPM模拟）")

            # 最终验证填写结果
            final_value = self._get_otp_value_playwright(otp_locator)
            if final_value == code:
                self.logger.info(f"✅ 验证码填写验证成功: '{final_value}'")

                # 触发blur事件 - 使用Playwright原生API
                otp_locator.dispatch_event("blur")

                # 点击提交按钮
                return self._click_verification_submit_button()
            else:
                self.logger.error(f"❌ 验证码填写验证失败: 期望'{code}', 实际'{final_value}'")
                return False

        except Exception as e:
            self.logger.error(f"❌ 填写Hulu OTP输入框失败: {e}")
            return False
    
    def _fill_individual_digit_inputs(self, input_elements, code: str) -> bool:
        """
        填写传统的6个独立数字输入框
        
        Args:
            input_elements: 输入框元素列表
            code: 6位验证码
            
        Returns:
            bool: 是否成功填写
        """
        try:
            import random
            
            # 确保有足够的输入框
            if len(input_elements) < 6:
                self.logger.error(f"❌ 输入框数量不足: {len(input_elements)}, 需要至少6个")
                return False
            
            # 人类化填写每个输入框
            char_delay = random.uniform(0.15, 0.25)
            
            for i, digit in enumerate(code):
                if i >= len(input_elements):
                    break
                
                try:
                    # 获取当前输入框的locator
                    input_locator = self.page.locator(f'input[maxlength="1"]:nth-child({i+1})')
                    if input_locator.count() > 0:
                        input_locator.fill(digit)
                        self.logger.debug(f"📝 填写第{i+1}位数字: {digit}")
                        
                        # 人类化延迟
                        if i < len(code) - 1:
                            time.sleep(char_delay)
                    else:
                        self.logger.warning(f"⚠️ 第{i+1}个输入框不可用")
                        
                except Exception as e:
                    self.logger.warning(f"⚠️ 填写第{i+1}位数字失败: {e}")
                    continue
            
            self.logger.info("✅ 独立输入框验证码填写完成")
            
            # 点击提交按钮
            return self._click_verification_submit_button()
            
        except Exception as e:
            self.logger.error(f"❌ 填写独立数字输入框失败: {e}")
            return False
    
    def _fill_traditional_verification_input(self, code: str) -> bool:
        """
        填写传统的单个验证码输入框
        
        Args:
            code: 验证码
            
        Returns:
            bool: 是否成功填写
        """
        try:
            # 验证码输入字段选择器 (基于更新后的列表)
            verification_selectors = [
                'input[name="otp"]',  # 🔑 Hulu特有的隐藏OTP输入框 (最高优先级)
                'input[name="verificationCode"]',
                'input[name="verification_code"]',
                'input[name="code"]',
                'input[name="authCode"]',
                'input[maxlength="6"]',  # 6位验证码字段
                'input[placeholder*="code"]',
                'input[placeholder*="verification"]',
                'input[placeholder*="verify"]',
                'input[data-testid*="verification"]',
                'input[data-testid*="code"]',
                'input[data-testid*="verify"]',
                'input[id*="verification"]',
                'input[id*="code"]',
                'input[type="text"]',  # 通用文本输入 (最低优先级)
            ]
            
            # 填写验证码
            for selector in verification_selectors:
                try:
                    locator = self.page.locator(selector)
                    if locator.count() > 0 and locator.is_visible():
                        locator.fill("")
                        locator.fill(code)
                        self.logger.info(f"✅ 成功填写验证码到: {selector}")
                        
                        # 点击提交按钮
                        return self._click_verification_submit_button()
                except Exception as e:
                    self.logger.debug(f"🔍 选择器 {selector} 失败: {e}")
                    continue
            
            self.logger.error("❌ 未能找到传统验证码输入字段")
            return False
            
        except Exception as e:
            self.logger.error(f"❌ 填写传统验证码输入框时出错: {e}")
            return False
    
    def _click_verification_submit_button(self) -> bool:
        """
        点击验证码提交按钮
        
        Returns:
            bool: 是否成功点击
        """
        try:
            # 提交按钮选择器
            submit_selectors = [
                'button[type="submit"]',
                'button:has-text("Verify")',
                'button:has-text("Submit")',
                'button:has-text("Continue")',
                'button:has-text("Confirm")',
                'button:has-text("Next")',
                'button[data-testid*="submit"]',
                'button[data-testid*="verify"]',
                'button[data-testid*="continue"]',
                'input[type="submit"]'
            ]
            
            for selector in submit_selectors:
                try:
                    locator = self.page.locator(selector)
                    if locator.count() > 0 and locator.is_visible() and locator.is_enabled():
                        locator.click()
                        self.logger.info(f"✅ 成功点击提交按钮: {selector}")
                        time.sleep(2)  # 等待页面响应
                        return True
                except Exception as e:
                    self.logger.debug(f"🔍 提交按钮选择器 {selector} 失败: {e}")
                    continue
            
            self.logger.warning("⚠️ 未找到可用的提交按钮")
            return False
            
        except Exception as e:
            self.logger.error(f"❌ 点击提交按钮时出错: {e}")
            return False