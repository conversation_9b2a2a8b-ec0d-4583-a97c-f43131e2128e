#!/usr/bin/env python3
"""
WorkflowOrchestrator - 业务逻辑层工作流编排器
协调各个业务服务完成完整的工作流程
"""

import logging
import time
from typing import Dict, Optional, Any, Callable
from playwright.sync_api import Page, BrowserContext

# 导入业务服务
from .verification_service import VerificationService
from .login_service import LoginService
from .registration_service import RegistrationService
from ..data.state_manager import StateManager


class WorkflowOrchestrator:
    """
    工作流编排器 - 协调业务服务执行
    
    职责：
    1. 协调服务间的工作流程
    2. 管理服务依赖关系
    3. 处理流程异常和回滚
    4. 提供统一的执行接口
    """
    
    def __init__(self, page: Page = None, context: BrowserContext = None, state_manager: StateManager = None, storage_state_path: str = None):
        """
        初始化工作流编排器

        Args:
            page: Playwright页面对象
            context: 浏览器上下文
            state_manager: 状态管理器实例
            storage_state_path: 存储状态文件路径
        """
        self.page = page
        self.context = context
        self.storage_state_path = storage_state_path or "hulu_auth_state.json"
        self.logger = logging.getLogger(__name__)

        # 初始化状态管理器
        self.state_manager = state_manager or StateManager()

        # 初始化各个业务服务，传递状态管理器
        self.verification_service = VerificationService()
        self.login_service = LoginService(
            page=page,
            state_manager=self.state_manager,
            context=context,
            storage_state_path=storage_state_path
        )
        self.registration_service = RegistrationService()

        # 设置初始页面对象
        if page:
            self._set_page_for_all_services(page)

        self.logger.info("🎯 WorkflowOrchestrator工作流编排器初始化完成")
    
    def set_page(self, page: Page):
        """
        设置页面对象并传播到所有服务

        Args:
            page: Playwright页面对象
        """
        self.page = page
        self._set_page_for_all_services(page)

    def set_context(self, context: BrowserContext):
        """
        设置浏览器上下文并传播到所有服务

        Args:
            context: 浏览器上下文
        """
        self.context = context
        if self.login_service:
            self.login_service.set_context(context)
    
    def _set_page_for_all_services(self, page: Page):
        """
        为所有服务设置页面对象
        
        Args:
            page: Playwright页面对象
        """
        self.verification_service.set_page(page)
        self.login_service.set_page(page)
        self.registration_service.set_page(page)
    
    def execute_registration_workflow(self, 
                                    email: str = None, 
                                    password: str = None, 
                                    name: str = None) -> Dict[str, Any]:
        """
        执行完整的注册工作流程
        
        Args:
            email: 邮箱地址（可选）
            password: 密码（可选）
            name: 姓名（可选）
            
        Returns:
            Dict[str, Any]: 包含注册结果和详细信息
        """
        try:
            self.logger.info("🚀 开始执行注册工作流程...")
            
            # 验证页面对象
            if not self.page:
                raise Exception("页面对象未设置，无法执行工作流")
            
            # 记录开始时间
            start_time = time.time()
            
            # 执行注册流程
            registration_result = self.registration_service.register(
                email=email,
                password=password,
                name=name
            )
            
            # 构建工作流结果
            workflow_result = {
                'success': True,
                'workflow_type': 'registration',
                'account_info': registration_result,
                'execution_time': time.time() - start_time,
                'services_used': ['RegistrationService', 'VerificationService'],
                'timestamp': time.strftime("%Y-%m-%d %H:%M:%S")
            }
            
            self.logger.info(f"✅ 注册工作流程完成，耗时: {workflow_result['execution_time']:.2f}秒")
            return workflow_result
            
        except Exception as e:
            self.logger.error(f"❌ 注册工作流程失败: {str(e)}")
            return self._handle_workflow_error('registration', e)
    
    def execute_login_workflow(self, 
                             email: str, 
                             password: str,
                             auto_verify: bool = True) -> Dict[str, Any]:
        """
        执行完整的登录工作流程
        
        Args:
            email: 登录邮箱
            password: 登录密码
            auto_verify: 是否自动处理验证
            
        Returns:
            Dict[str, Any]: 包含登录结果和详细信息
        """
        try:
            self.logger.info("🚀 开始执行登录工作流程...")
            
            # 验证页面对象
            if not self.page:
                raise Exception("页面对象未设置，无法执行工作流")
            
            # 记录开始时间
            start_time = time.time()
            
            # 执行登录流程
            login_result = self.login_service.login(email, password)
            
            # 构建工作流结果
            workflow_result = {
                'success': True,
                'workflow_type': 'login',
                'login_info': login_result,
                'execution_time': time.time() - start_time,
                'services_used': ['LoginService', 'VerificationService'],
                'timestamp': time.strftime("%Y-%m-%d %H:%M:%S")
            }
            
            self.logger.info(f"✅ 登录工作流程完成，耗时: {workflow_result['execution_time']:.2f}秒")
            return workflow_result
            
        except Exception as e:
            self.logger.error(f"❌ 登录工作流程失败: {str(e)}")
            return self._handle_workflow_error('login', e)
    
    def execute_custom_workflow(self, 
                              workflow_name: str,
                              steps: list,
                              context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        执行自定义工作流程
        
        Args:
            workflow_name: 工作流名称
            steps: 工作流步骤列表
            context: 工作流上下文
            
        Returns:
            Dict[str, Any]: 工作流执行结果
        """
        try:
            self.logger.info(f"🚀 开始执行自定义工作流程: {workflow_name}")
            
            # 验证页面对象
            if not self.page:
                raise Exception("页面对象未设置，无法执行工作流")
            
            # 初始化上下文
            if context is None:
                context = {}
            
            # 记录开始时间
            start_time = time.time()
            results = []
            services_used = set()
            
            # 执行每个步骤
            for i, step in enumerate(steps):
                self.logger.info(f"📋 执行步骤 {i+1}/{len(steps)}: {step.get('name', 'Unknown')}")
                
                step_result = self._execute_workflow_step(step, context)
                results.append(step_result)
                
                # 记录使用的服务
                if 'service' in step:
                    services_used.add(step['service'])
                
                # 如果步骤失败，根据配置决定是否继续
                if not step_result['success'] and not step.get('continue_on_error', False):
                    raise Exception(f"步骤 {step.get('name', 'Unknown')} 执行失败")
                
                # 更新上下文
                if 'output_key' in step and 'result' in step_result:
                    context[step['output_key']] = step_result['result']
            
            # 构建工作流结果
            workflow_result = {
                'success': True,
                'workflow_type': 'custom',
                'workflow_name': workflow_name,
                'steps_executed': len(results),
                'step_results': results,
                'execution_time': time.time() - start_time,
                'services_used': list(services_used),
                'timestamp': time.strftime("%Y-%m-%d %H:%M:%S"),
                'context': context
            }
            
            self.logger.info(f"✅ 自定义工作流程完成，耗时: {workflow_result['execution_time']:.2f}秒")
            return workflow_result
            
        except Exception as e:
            self.logger.error(f"❌ 自定义工作流程失败: {str(e)}")
            return self._handle_workflow_error(workflow_name, e)
    
    def coordinate_verification_flow(self, 
                                   email: str = None,
                                   enable_captcha_solver: bool = False) -> Dict[str, Any]:
        """
        协调验证流程（邮件验证 + reCAPTCHA）
        
        Args:
            email: 邮箱地址（用于邮件验证）
            enable_captcha_solver: 是否启用验证码求解
            
        Returns:
            Dict[str, Any]: 验证结果
        """
        try:
            self.logger.info("🔍 开始协调验证流程...")
            
            # 配置验证码求解器
            self.verification_service.enable_captcha_solver(enable_captcha_solver)
            
            verification_results = {
                'recaptcha': None,
                'email': None
            }
            
            # 1. 检测并处理reCAPTCHA
            recaptcha_info = self.verification_service.detect_recaptcha()
            if recaptcha_info['site_key']:
                self.logger.info("🎯 检测到reCAPTCHA，开始处理...")
                
                if enable_captcha_solver:
                    success = self.verification_service.solve_and_submit_recaptcha(
                        recaptcha_info['site_key'],
                        recaptcha_info['page_url']
                    )
                    verification_results['recaptcha'] = {
                        'detected': True,
                        'solved': success,
                        'site_key': recaptcha_info['site_key'][:20] + '...'
                    }
                else:
                    self.logger.warning("⚠️ reCAPTCHA求解器未启用")
                    verification_results['recaptcha'] = {
                        'detected': True,
                        'solved': False,
                        'message': '求解器未启用'
                    }
            
            # 2. 处理邮件验证
            if email:
                self.logger.info("📧 处理邮件验证...")
                email_result = self.verification_service.handle_email_verification(email)
                verification_results['email'] = email_result
            
            return {
                'success': True,
                'verification_results': verification_results,
                'timestamp': time.strftime("%Y-%m-%d %H:%M:%S")
            }
            
        except Exception as e:
            self.logger.error(f"❌ 验证流程协调失败: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'verification_results': {},
                'timestamp': time.strftime("%Y-%m-%d %H:%M:%S")
            }
    
    def check_system_status(self) -> Dict[str, Any]:
        """
        检查系统状态和服务可用性
        
        Returns:
            Dict[str, Any]: 系统状态信息
        """
        try:
            self.logger.info("🔍 检查系统状态...")
            
            status = {
                'orchestrator': {
                    'status': 'operational',
                    'page_available': self.page is not None
                },
                'services': {
                    'verification_service': self._check_service_status(self.verification_service),
                    'login_service': self._check_service_status(self.login_service),
                    'registration_service': self._check_service_status(self.registration_service)
                },
                'timestamp': time.strftime("%Y-%m-%d %H:%M:%S")
            }
            
            # 检查整体状态
            all_operational = all(
                service_status['status'] == 'operational'
                for service_status in status['services'].values()
            )
            
            status['overall_status'] = 'operational' if all_operational else 'degraded'
            
            self.logger.info(f"✅ 系统状态检查完成: {status['overall_status']}")
            return status
            
        except Exception as e:
            self.logger.error(f"❌ 系统状态检查失败: {str(e)}")
            return {
                'overall_status': 'error',
                'error': str(e),
                'timestamp': time.strftime("%Y-%m-%d %H:%M:%S")
            }
    
    def _execute_workflow_step(self, step: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行单个工作流步骤
        
        Args:
            step: 步骤配置
            context: 工作流上下文
            
        Returns:
            Dict[str, Any]: 步骤执行结果
        """
        try:
            service_name = step.get('service')
            method_name = step.get('method')
            params = step.get('params', {})
            
            # 参数替换（从上下文中获取值）
            resolved_params = self._resolve_parameters(params, context)
            
            # 获取服务实例
            service = self._get_service_instance(service_name)
            if not service:
                raise Exception(f"未知的服务: {service_name}")
            
            # 获取方法
            method = getattr(service, method_name, None)
            if not method:
                raise Exception(f"服务 {service_name} 没有方法 {method_name}")
            
            # 执行方法
            result = method(**resolved_params)
            
            return {
                'success': True,
                'step_name': step.get('name', 'Unknown'),
                'service': service_name,
                'method': method_name,
                'result': result
            }
            
        except Exception as e:
            self.logger.error(f"步骤执行失败: {str(e)}")
            return {
                'success': False,
                'step_name': step.get('name', 'Unknown'),
                'error': str(e)
            }
    
    def _get_service_instance(self, service_name: str) -> Optional[Any]:
        """
        获取服务实例
        
        Args:
            service_name: 服务名称
            
        Returns:
            Optional[Any]: 服务实例
        """
        service_map = {
            'verification': self.verification_service,
            'login': self.login_service,
            'registration': self.registration_service
        }
        
        return service_map.get(service_name.lower())
    
    def _resolve_parameters(self, params: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """
        解析参数（支持从上下文中获取值）
        
        Args:
            params: 原始参数
            context: 工作流上下文
            
        Returns:
            Dict[str, Any]: 解析后的参数
        """
        resolved = {}
        
        for key, value in params.items():
            if isinstance(value, str) and value.startswith('$'):
                # 从上下文中获取值
                context_key = value[1:]  # 去掉$前缀
                resolved[key] = context.get(context_key, value)
            else:
                resolved[key] = value
        
        return resolved
    
    def _check_service_status(self, service: Any) -> Dict[str, str]:
        """
        检查服务状态
        
        Args:
            service: 服务实例
            
        Returns:
            Dict[str, str]: 服务状态信息
        """
        try:
            # 检查服务是否有页面对象
            has_page = hasattr(service, 'page') and service.page is not None
            
            # 检查必要的方法是否存在
            required_methods = ['set_page']
            all_methods_exist = all(hasattr(service, method) for method in required_methods)
            
            if has_page and all_methods_exist:
                return {'status': 'operational', 'page_available': True}
            elif all_methods_exist:
                return {'status': 'operational', 'page_available': False}
            else:
                return {'status': 'degraded', 'page_available': False}
                
        except Exception as e:
            return {'status': 'error', 'error': str(e)}
    
    def _handle_workflow_error(self, workflow_type: str, error: Exception) -> Dict[str, Any]:
        """
        处理工作流错误
        
        Args:
            workflow_type: 工作流类型
            error: 异常对象
            
        Returns:
            Dict[str, Any]: 错误信息
        """
        error_result = {
            'success': False,
            'workflow_type': workflow_type,
            'error': str(error),
            'error_type': type(error).__name__,
            'timestamp': time.strftime("%Y-%m-%d %H:%M:%S"),
            'recovery_suggestions': self._get_recovery_suggestions(error)
        }
        
        # 尝试截图
        try:
            if self.page:
                screenshot_path = f"workflow_error_{workflow_type}_{int(time.time())}.png"
                self.page.screenshot(path=screenshot_path)
                error_result['screenshot'] = screenshot_path
                self.logger.info(f"错误截图已保存: {screenshot_path}")
        except Exception:
            pass
        
        return error_result
    
    def _get_recovery_suggestions(self, error: Exception) -> list:
        """
        获取错误恢复建议
        
        Args:
            error: 异常对象
            
        Returns:
            list: 恢复建议列表
        """
        suggestions = []
        
        error_msg = str(error).lower()
        
        if '页面对象未设置' in error_msg:
            suggestions.append("确保在执行工作流前调用 set_page() 方法")
        
        if 'timeout' in error_msg:
            suggestions.append("增加超时时间或检查网络连接")
        
        if 'element' in error_msg:
            suggestions.append("检查页面元素选择器是否正确")
            suggestions.append("确保页面已完全加载")
        
        if 'verification' in error_msg:
            suggestions.append("检查邮箱API配置")
            suggestions.append("确认验证码求解器已启用")
        
        if not suggestions:
            suggestions.append("检查日志获取详细错误信息")
            suggestions.append("确保所有服务配置正确")
        
        return suggestions