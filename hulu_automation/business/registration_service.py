#!/usr/bin/env python3
"""
RegistrationService - 业务逻辑层注册服务
处理Hulu账户注册核心业务逻辑
"""

import logging
import time
from typing import Dict, Optional, Any
from playwright.sync_api import Page

# 导入依赖服务和工具
from .verification_service import VerificationService
from utils import TempMailAPI, generate_random_user_info


class RegistrationService:
    """
    注册服务 - 处理注册核心业务逻辑
    
    职责：
    1. 执行注册流程
    2. 用户信息生成和处理
    3. 表单填写和提交
    4. 注册验证处理
    """
    
    def __init__(self, page: Page = None):
        """
        初始化注册服务
        
        Args:
            page: Playwright页面对象
        """
        self.page = page
        self.logger = logging.getLogger(__name__)
        self.verification_service = VerificationService()
        
        self.logger.info("📝 RegistrationService注册服务初始化完成")
    
    def set_page(self, page: Page):
        """设置页面对象"""
        self.page = page
        self.verification_service.set_page(page)
    
    def register(self, email: str = None, password: str = None, name: str = None) -> Dict[str, str]:
        """
        执行完整的注册流程 - 集成邮箱API版本
        
        Args:
            email: 邮箱地址（可选，如果未提供将自动生成临时邮箱）
            password: 密码（可选，会自动生成）
            name: 姓名（可选，会自动生成）
            
        Returns:
            Dict[str, str]: 包含账户信息的字典
        """
        try:
            if not self.page:
                raise Exception("页面对象未设置")
            
            # 1. 初始化邮箱API和生成用户信息
            mail_api = TempMailAPI()
            
            # 如果没有提供邮箱，生成临时邮箱
            if not email:
                self.logger.info("生成临时邮箱...")
                email = mail_api.generate_email("hulu")
                self.logger.info(f"✅ 生成的临时邮箱: {email}")
            
            # 生成用户信息
            if not password or not name:
                user_info = generate_random_user_info()
                if not password:
                    password = user_info["password"]
                if not name:
                    name = user_info["name"]
            
            self.logger.info(f"开始Hulu账户注册流程...")
            self.logger.info(f"邮箱: {email}")
            self.logger.info(f"姓名: {name}")
            
            # 2. 导航到Hulu主页
            self.logger.info("导航到Hulu主页...")
            self.page.goto("https://www.hulu.com")
            time.sleep(1)
            
            # 3. 点击LOG IN按钮
            self.logger.info("点击LOG IN按钮...")
            login_clicked = self._click_element_by_selectors([
                'text="Log In"',
                'text="LOG IN"',
                'nav a[href*="login"]',
                'header a[href*="login"]',
                '[href*="login"]'
            ], "LOG IN按钮")
            
            if not login_clicked:
                raise Exception("无法找到或点击LOG IN按钮")
            
            time.sleep(1)
            
            # 4. 填写邮箱字段
            self.logger.info(f"填写邮箱: {email}")
            email_filled = self._fill_input_by_selectors([
                'input[placeholder="Email"]',
                'input[name="email"]',
                'input[type="email"]',
                'input[placeholder*="email"]',
                'input[data-testid*="email"]',
                '#email'
            ], email, "邮箱字段")
            
            if not email_filled:
                raise Exception("无法找到邮箱输入字段")
            
            # 5. 点击Continue按钮
            self.logger.info("点击Continue按钮...")
            continue_clicked = self._click_element_by_selectors([
                'text="Continue"',
                'button[type="submit"]',
                'input[type="submit"]',
                '.continue-button',
                '[data-testid*="continue"]'
            ], "Continue按钮")
            
            if not continue_clicked:
                raise Exception("无法找到或点击Continue按钮")
            
            time.sleep(2)
            
            # 6. 处理邮箱验证（使用API获取验证码）
            verification_result = self.verification_service.handle_email_verification(email)
            verification_code = verification_result.get('verification_code')
            
            # 7. 填写注册表单
            form_filled = self.fill_registration_form(password, name, verification_code)
            if not form_filled:
                raise Exception("填写注册表单失败")
            
            # 8. 提交表单
            submitted = self._submit_registration_form()
            if not submitted:
                raise Exception("提交注册表单失败")
            
            # 9. 等待注册完成
            completion_success = self._wait_for_registration_completion()
            if not completion_success:
                raise Exception("注册完成验证失败")
            
            # 返回账户信息
            account_info = {
                "email": email,
                "password": password,
                "name": name,
                "verification_code": verification_code,
                "created_at": time.strftime("%Y-%m-%d %H:%M:%S"),
                "status": "created"
            }
            
            self.logger.info("✅ 注册流程完成!")
            return account_info
            
        except Exception as e:
            self.logger.error(f"❌ 注册失败: {str(e)}")
            # 截图用于调试
            try:
                screenshot_path = f"registration_error_screenshot_{int(time.time())}.png"
                self.page.screenshot(path=screenshot_path)
                self.logger.info(f"注册错误截图已保存: {screenshot_path}")
            except Exception:
                pass
            raise
    
    def fill_registration_form(self, password: str, name: str, verification_code: str = None) -> bool:
        """
        填写注册表单
        
        Args:
            password: 密码
            name: 姓名
            verification_code: 验证码（可选）
            
        Returns:
            bool: 是否成功填写表单
        """
        try:
            self.logger.info("📝 填写注册表单...")
            
            # 如果有验证码但还没填写，先填写验证码
            if verification_code:
                self.logger.info(f"填写验证码: {verification_code}")
                verification_filled = self.verification_service.submit_verification_code(verification_code)
                if not verification_filled:
                    self.logger.warning("⚠️ 验证码填写失败，但继续注册流程")
            
            # 填写密码
            self.logger.info("填写密码...")
            password_filled = self._fill_input_by_selectors([
                'input[name="password"]',
                'input[type="password"]',
                'input[placeholder*="password"]',
                'input[data-testid*="password"]',
                '#password'
            ], password, "密码字段")
            
            if not password_filled:
                self.logger.error("❌ 无法找到密码输入字段")
                return False
            
            # 填写姓名
            self.logger.info(f"填写姓名: {name}")
            name_filled = self._fill_input_by_selectors([
                'input[name="name"]',
                'input[name="firstName"]',
                'input[name="first_name"]',
                'input[placeholder*="name"]',
                'input[placeholder*="Name"]',
                'input[data-testid*="name"]',
                '#name',
                '#firstName',
                '#first_name'
            ], name, "姓名字段")
            
            if not name_filled:
                self.logger.error("❌ 无法找到姓名输入字段")
                return False
            
            # 可能需要填写确认密码
            confirm_password_filled = self._fill_input_by_selectors([
                'input[name="confirmPassword"]',
                'input[name="confirm_password"]',
                'input[name="passwordConfirm"]',
                'input[placeholder*="confirm"]',
                'input[placeholder*="Confirm"]',
                'input[data-testid*="confirm"]',
                '#confirmPassword',
                '#confirm_password'
            ], password, "确认密码字段")
            
            if confirm_password_filled:
                self.logger.info("✅ 确认密码字段已填写")
            else:
                self.logger.info("ℹ️ 未发现确认密码字段")
            
            # 处理可能的服务条款勾选
            self._handle_terms_and_conditions()
            
            self.logger.info("✅ 注册表单填写完成")
            return True
            
        except Exception as e:
            self.logger.error(f"填写注册表单时出错: {str(e)}")
            return False
    
    def handle_registration_errors(self) -> bool:
        """
        处理注册错误
        
        Returns:
            bool: 是否成功处理错误
        """
        try:
            self.logger.info("🔧 检查注册错误...")
            
            # 检查是否有错误信息
            error_selectors = [
                '[data-testid*="error"]',
                '.error-message',
                '.alert-error',
                '[role="alert"]',
                '.notification-error',
                '.form-error',
                '.validation-error'
            ]
            
            error_found = False
            for selector in error_selectors:
                try:
                    error_element = self.page.query_selector(selector)
                    if error_element and error_element.is_visible():
                        error_text = error_element.inner_text()
                        self.logger.error(f"注册错误: {error_text}")
                        error_found = True
                except:
                    continue
            
            if error_found:
                return False
            
            # 检查是否有验证失败的提示
            validation_selectors = [
                '.field-error',
                '.input-error',
                '.form-field-error',
                '[data-testid*="validation"]'
            ]
            
            for selector in validation_selectors:
                try:
                    validation_element = self.page.query_selector(selector)
                    if validation_element and validation_element.is_visible():
                        validation_text = validation_element.inner_text()
                        self.logger.warning(f"验证提示: {validation_text}")
                except:
                    continue
            
            return True
            
        except Exception as e:
            self.logger.error(f"处理注册错误时出错: {str(e)}")
            return False
    
    def confirm_registration(self) -> bool:
        """
        确认注册
        
        Returns:
            bool: 是否成功确认注册
        """
        try:
            self.logger.info("✅ 确认注册...")
            
            # 检查是否有成功指示
            success_selectors = [
                '.success-message',
                '.registration-success',
                '[data-testid*="success"]',
                '.confirmation-message',
                '.welcome-message'
            ]
            
            for selector in success_selectors:
                try:
                    success_element = self.page.query_selector(selector)
                    if success_element and success_element.is_visible():
                        success_text = success_element.inner_text()
                        self.logger.info(f"注册成功: {success_text}")
                        return True
                except:
                    continue
            
            # 检查URL变化
            current_url = self.page.url
            success_url_indicators = [
                'welcome',
                'dashboard',
                'home',
                'profile',
                'success',
                'complete'
            ]
            
            for indicator in success_url_indicators:
                if indicator in current_url.lower():
                    self.logger.info(f"✅ URL指示注册成功: {indicator}")
                    return True
            
            self.logger.warning("⚠️ 无法确认注册状态")
            return False
            
        except Exception as e:
            self.logger.error(f"确认注册时出错: {str(e)}")
            return False
    
    def _click_element_by_selectors(self, selectors: list, element_name: str) -> bool:
        """
        尝试使用多个选择器点击元素
        
        Args:
            selectors: 选择器列表
            element_name: 元素名称，用于日志
            
        Returns:
            bool: 是否成功点击
        """
        try:
            for selector in selectors:
                try:
                    element = self.page.query_selector(selector)
                    if element and element.is_visible():
                        element.click()
                        self.logger.info(f"✅ 成功点击{element_name}: {selector}")
                        return True
                except:
                    continue
            
            self.logger.warning(f"⚠️ 未找到{element_name}")
            return False
            
        except Exception as e:
            self.logger.error(f"点击{element_name}时出错: {str(e)}")
            return False
    
    def _fill_input_by_selectors(self, selectors: list, value: str, field_name: str) -> bool:
        """
        尝试使用多个选择器填写输入字段
        
        Args:
            selectors: 选择器列表
            value: 要填写的值
            field_name: 字段名称，用于日志
            
        Returns:
            bool: 是否成功填写
        """
        try:
            for selector in selectors:
                try:
                    element = self.page.query_selector(selector)
                    if element and element.is_visible():
                        element.clear()
                        element.fill(value)
                        self.logger.info(f"✅ 成功填写{field_name}: {selector}")
                        return True
                except:
                    continue
            
            self.logger.warning(f"⚠️ 未找到{field_name}")
            return False
            
        except Exception as e:
            self.logger.error(f"填写{field_name}时出错: {str(e)}")
            return False
    
    def _handle_terms_and_conditions(self) -> bool:
        """
        处理服务条款和隐私政策勾选
        
        Returns:
            bool: 是否成功处理
        """
        try:
            self.logger.info("🔍 检查服务条款和隐私政策...")
            
            # 常见的服务条款选择器
            terms_selectors = [
                'input[type="checkbox"][name*="terms"]',
                'input[type="checkbox"][name*="agree"]',
                'input[type="checkbox"][name*="accept"]',
                'input[type="checkbox"][data-testid*="terms"]',
                'input[type="checkbox"][data-testid*="agree"]',
                '.terms-checkbox input',
                '.agreement-checkbox input',
                '#terms',
                '#agree',
                '#accept'
            ]
            
            terms_handled = False
            for selector in terms_selectors:
                try:
                    terms_checkbox = self.page.query_selector(selector)
                    if terms_checkbox and terms_checkbox.is_visible():
                        if not terms_checkbox.is_checked():
                            terms_checkbox.check()
                            self.logger.info(f"✅ 勾选服务条款: {selector}")
                            terms_handled = True
                        else:
                            self.logger.info(f"ℹ️ 服务条款已勾选: {selector}")
                            terms_handled = True
                except:
                    continue
            
            if not terms_handled:
                self.logger.info("ℹ️ 未发现服务条款勾选框")
            
            return True
            
        except Exception as e:
            self.logger.error(f"处理服务条款时出错: {str(e)}")
            return False
    
    def _submit_registration_form(self) -> bool:
        """
        提交注册表单
        
        Returns:
            bool: 是否成功提交
        """
        try:
            self.logger.info("📤 提交注册表单...")
            
            # 提交按钮选择器
            submit_selectors = [
                'button[type="submit"]',
                'input[type="submit"]',
                'button:has-text("Create Account")',
                'button:has-text("Sign Up")',
                'button:has-text("Register")',
                'button:has-text("Continue")',
                'button:has-text("Submit")',
                '[data-testid*="submit"]',
                '[data-testid*="create"]',
                '[data-testid*="register"]',
                '.submit-button',
                '.register-button',
                '.create-account-button'
            ]
            
            for selector in submit_selectors:
                try:
                    submit_button = self.page.query_selector(selector)
                    if submit_button and submit_button.is_visible() and submit_button.is_enabled():
                        submit_button.click()
                        self.logger.info(f"✅ 成功提交表单: {selector}")
                        time.sleep(2)  # 等待提交处理
                        return True
                except:
                    continue
            
            self.logger.error("❌ 未找到提交按钮")
            return False
            
        except Exception as e:
            self.logger.error(f"提交注册表单时出错: {str(e)}")
            return False
    
    def _wait_for_registration_completion(self) -> bool:
        """
        等待注册完成
        
        Returns:
            bool: 注册是否完成
        """
        try:
            self.logger.info("⏳ 等待注册完成...")
            
            max_wait_time = 30  # 最多等待30秒
            wait_interval = 2   # 每2秒检查一次
            
            for _ in range(max_wait_time // wait_interval):
                # 检查是否有错误
                if not self.handle_registration_errors():
                    return False
                
                # 检查是否完成
                if self.confirm_registration():
                    return True
                
                # 检查是否需要额外的验证步骤
                current_url = self.page.url
                if 'verify' in current_url.lower() or 'confirm' in current_url.lower():
                    self.logger.info("🔍 检测到额外验证步骤...")
                    # 这里可以添加额外的验证处理逻辑
                    time.sleep(wait_interval)
                    continue
                
                time.sleep(wait_interval)
            
            self.logger.warning("⚠️ 注册完成等待超时")
            return False
            
        except Exception as e:
            self.logger.error(f"等待注册完成时出错: {str(e)}")
            return False