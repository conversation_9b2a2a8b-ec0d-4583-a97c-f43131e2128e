#!/usr/bin/env python3
"""
StateManager - 数据层状态管理器
处理登录状态持久化和会话管理
"""

import os
import json
import time
import logging
from typing import Optional
from playwright.sync_api import BrowserContext, Page

from .configuration_provider import ConfigurationProvider
from .logging_service import LoggingService


class StateManager:
    """
    状态管理器 - 数据层核心服务
    负责登录状态持久化、会话管理和状态验证
    """
    
    def __init__(self):
        """初始化状态管理器"""
        self.config_provider = ConfigurationProvider()
        self.logging_service = LoggingService()
        self.logger = logging.getLogger(__name__)
        self.logger.info("💾 StateManager状态管理器初始化完成")
    
    def load_state(self, path: str) -> bool:
        """
        加载存储状态
        从hulu_bot_original.py的_load_storage_state()方法提取
        
        Args:
            path: 状态文件路径
            
        Returns:
            bool: 是否成功加载状态文件
        """
        try:
            if not os.path.exists(path):
                self.logger.debug(f"状态文件不存在: {path}")
                return False

            # 验证状态文件格式
            with open(path, 'r', encoding='utf-8') as f:
                state_data = json.load(f)

            # 检查必要的字段
            if 'cookies' not in state_data and 'origins' not in state_data:
                self.logger.warning("状态文件格式无效，缺少必要字段")
                return False

            # 检查状态文件的时效性
            file_age = time.time() - os.path.getmtime(path)
            max_age = 24 * 60 * 60  # 24小时

            if file_age > max_age:
                self.logger.warning(f"状态文件过期 ({file_age/3600:.1f}小时)，将创建新会话")
                return False

            self.logger.info(f"状态文件有效，年龄: {file_age/3600:.1f}小时")
            return True

        except Exception as e:
            self.logger.warning(f"加载状态文件失败: {e}")
            return False
    
    async def save_state(self, path: str, context: BrowserContext) -> bool:
        """
        保存存储状态
        从hulu_bot_original.py的save_storage_state()方法提取
        
        Args:
            path: 状态文件路径
            context: 浏览器上下文
            
        Returns:
            bool: 是否成功保存
        """
        try:
            if not context:
                self.logger.warning("浏览器上下文不存在，无法保存状态")
                return False

            # 确保目录存在
            state_dir = os.path.dirname(path)
            if state_dir:
                os.makedirs(state_dir, exist_ok=True)

            # 保存状态到文件 (异步调用)
            await context.storage_state(path=path)
            self.logger.info(f"✅ 登录状态已保存到: {path}")
            return True

        except Exception as e:
            self.logger.error(f"保存登录状态失败: {e}")
            return False
    
    def clear_state(self, path: str) -> bool:
        """
        清除存储状态
        从hulu_bot_original.py的clear_storage_state()方法提取
        
        Args:
            path: 状态文件路径
            
        Returns:
            bool: 是否成功清除
        """
        try:
            if os.path.exists(path):
                os.remove(path)
                self.logger.info(f"✅ 已清除登录状态文件: {path}")
                return True
            else:
                self.logger.info("登录状态文件不存在，无需清除")
                return True

        except Exception as e:
            self.logger.error(f"清除登录状态文件失败: {e}")
            return False

    def is_logged_in(self, page: Page) -> bool:
        """
        检查是否已登录
        从hulu_bot_original.py的is_logged_in()方法提取

        Args:
            page: Playwright页面对象

        Returns:
            bool: 是否已登录
        """
        try:
            if not page:
                return False

            current_url = page.url

            # 检查URL是否表明已登录
            logged_in_indicators = [
                'welcome', 'hub', 'profiles', 'dashboard',
                'home', 'account', 'settings', 'my-account'
            ]

            url_indicates_login = any(indicator in current_url.lower() for indicator in logged_in_indicators)

            if url_indicates_login:
                self.logger.info(f"✅ URL显示已登录: {current_url}")
                return True

            # 检查页面元素是否表明已登录
            login_check_selectors = [
                '[data-testid*="profile"]',
                '[data-testid*="user"]',
                '.user-menu',
                '.profile-menu',
                '.account-menu'
            ]

            for selector in login_check_selectors:
                try:
                    if page.locator(selector).count() > 0:
                        self.logger.info(f"✅ 页面元素显示已登录: {selector}")
                        return True
                except:
                    continue

            self.logger.debug("❌ 未检测到登录状态")
            return False

        except Exception as e:
            self.logger.error(f"检查登录状态时出错: {str(e)}")
            return False

    def get_storage_path(self, default_path: str = "hulu_auth_state.json") -> str:
        """
        获取存储路径

        Args:
            default_path: 默认路径

        Returns:
            str: 存储路径
        """
        return default_path
