#!/usr/bin/env python3
"""
LoggingService - 数据层日志服务
统一日志处理和配置管理
"""

import logging
import os
from typing import Optional


class LoggingService:
    """
    日志服务 - 数据层核心服务
    负责统一日志配置、格式化和文件管理
    """
    
    def __init__(self):
        """初始化日志服务"""
        self.logger = logging.getLogger(__name__)
        self._initialized = False
    
    def setup_logging(self, level: str = "INFO", file_path: str = "logs/hulu_creator.log") -> logging.Logger:
        """
        设置日志记录
        从utils.py的setup_logging()函数提取
        
        Args:
            level: 日志级别 (DEBUG, INFO, WARNING, ERROR)
            file_path: 日志文件路径
            
        Returns:
            logging.Logger: 配置好的日志记录器
        """
        # 获取日志级别
        log_level = getattr(logging, level.upper(), logging.INFO)
        
        # 确保日志目录存在
        log_dir = os.path.dirname(file_path)
        if log_dir:
            os.makedirs(log_dir, exist_ok=True)
        
        # 避免重复配置
        if not self._initialized:
            logging.basicConfig(
                level=log_level,
                format='%(asctime)s - %(levelname)s - %(message)s',
                handlers=[
                    logging.StreamHandler(),
                    logging.FileHandler(file_path, encoding='utf-8')
                ]
            )
            self._initialized = True
        
        logger = logging.getLogger(__name__)
        logger.info("📝 LoggingService日志服务初始化完成")
        return logger
    
    def log_info(self, message: str):
        """记录信息日志"""
        self.logger.info(message)
    
    def log_error(self, message: str, exception: Optional[Exception] = None):
        """记录错误日志"""
        if exception:
            self.logger.error(f"{message}: {str(exception)}")
        else:
            self.logger.error(message)
    
    def log_debug(self, message: str):
        """记录调试日志"""
        self.logger.debug(message)
    
    def log_warning(self, message: str):
        """记录警告日志"""
        self.logger.warning(message)
    
    def get_logger(self, name: str = None) -> logging.Logger:
        """获取日志记录器"""
        if name:
            return logging.getLogger(name)
        return self.logger
