#!/usr/bin/env python3
"""
ConfigurationProvider - 数据层配置提供者
统一配置管理和环境变量处理
"""

import os
import logging
from typing import Dict, Any, Optional


class ConfigurationProvider:
    """
    配置提供者 - 数据层核心服务
    负责统一配置管理、环境变量处理和配置验证
    """
    
    def __init__(self):
        """初始化配置提供者"""
        self.logger = logging.getLogger(__name__)
        self._config = self._load_configuration()
        self.logger.info("🔧 ConfigurationProvider配置提供者初始化完成")
    
    def _load_configuration(self) -> Dict[str, Any]:
        """
        加载环境变量配置
        从utils.py的load_env_config()函数提取
        """
        # 尝试加载 .env 文件
        env_file = os.path.join(os.path.dirname(__file__), '../../.env')
        if os.path.exists(env_file):
            with open(env_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        os.environ[key.strip()] = value.strip()
        
        return {
            'API_BASE': os.getenv('API_BASE', 'https://testkuroneko.xyz/api'),
            'API_KEY': os.getenv('API_KEY'),
            'DEFAULT_EMAIL_DOMAIN': os.getenv('DEFAULT_EMAIL_DOMAIN', 'testkuroneko.xyz'),
            'API_TIMEOUT': int(os.getenv('API_TIMEOUT', '30')),
            'BROWSER_TIMEOUT': int(os.getenv('BROWSER_TIMEOUT', '30000')),
            'DEFAULT_HEADLESS': os.getenv('DEFAULT_HEADLESS', 'true').lower() == 'true',
            'PAGE_LOAD_TIMEOUT': int(os.getenv('PAGE_LOAD_TIMEOUT', '30000')),
            'DEFAULT_OUTPUT_FILE': os.getenv('DEFAULT_OUTPUT_FILE', 'data/accounts.csv'),
            'LOG_FILE': os.getenv('LOG_FILE', 'logs/hulu_creator.log'),
            'LOG_LEVEL': os.getenv('LOG_LEVEL', 'INFO'),
            'APP_MODE': os.getenv('APP_MODE', 'development'),
            'DEBUG': os.getenv('DEBUG', 'false').lower() == 'true',
            'VERIFICATION_TIMEOUT': int(os.getenv('VERIFICATION_TIMEOUT', '300')),
            'VERIFICATION_POLL_INTERVAL': int(os.getenv('VERIFICATION_POLL_INTERVAL', '5')),
            
            # reCAPTCHA 求解服务配置
            'TWOCAPTCHA_API_KEY': os.getenv('TWOCAPTCHA_API_KEY'),
            'DBC_USERNAME': os.getenv('DBC_USERNAME'),
            'DBC_PASSWORD': os.getenv('DBC_PASSWORD'),
            'RECAPTCHA_TIMEOUT': int(os.getenv('RECAPTCHA_TIMEOUT', '120')),
            'RECAPTCHA_MIN_SCORE': float(os.getenv('RECAPTCHA_MIN_SCORE', '0.3')),
            
            # 截图配置
            'SCREENSHOT_QUALITY': int(os.getenv('SCREENSHOT_QUALITY', '90')),
            'SCREENSHOT_INCLUDE_ADDRESS_BAR': os.getenv('SCREENSHOT_INCLUDE_ADDRESS_BAR', 'true').lower() == 'true',
            'SCREENSHOT_DIRECTORY': os.getenv('SCREENSHOT_DIRECTORY', 'screenshots'),
            'SCREENSHOT_CLEANUP_DAYS': int(os.getenv('SCREENSHOT_CLEANUP_DAYS', '7')),
            'SCREENSHOT_BROWSER_KEYWORDS': os.getenv('SCREENSHOT_BROWSER_KEYWORDS', 'Chrome,Chromium,Firefox,Safari,Edge,Hulu').split(','),
            'ENABLE_BROWSER_SCREENSHOT': os.getenv('ENABLE_BROWSER_SCREENSHOT', 'true').lower() == 'true'
        }
    
    def get_browser_config(self) -> Dict[str, Any]:
        """获取浏览器配置"""
        return {
            'headless': self._config.get('DEFAULT_HEADLESS', True),
            'timeout': self._config.get('BROWSER_TIMEOUT', 30000),
            'page_load_timeout': self._config.get('PAGE_LOAD_TIMEOUT', 30000)
        }
    
    def get_api_config(self) -> Dict[str, Any]:
        """获取API配置"""
        return {
            'base_url': self._config.get('API_BASE'),
            'api_key': self._config.get('API_KEY'),
            'timeout': self._config.get('API_TIMEOUT', 30),
            'default_domain': self._config.get('DEFAULT_EMAIL_DOMAIN')
        }
    
    def get_timeout_config(self) -> Dict[str, Any]:
        """获取超时配置"""
        return {
            'verification_timeout': self._config.get('VERIFICATION_TIMEOUT', 300),
            'poll_interval': self._config.get('VERIFICATION_POLL_INTERVAL', 5),
            'recaptcha_timeout': self._config.get('RECAPTCHA_TIMEOUT', 120)
        }
    
    def validate_config(self) -> bool:
        """验证配置完整性"""
        try:
            required_configs = ['API_BASE', 'BROWSER_TIMEOUT', 'PAGE_LOAD_TIMEOUT']
            for config_key in required_configs:
                if config_key not in self._config:
                    self.logger.error(f"❌ 缺少必需配置: {config_key}")
                    return False
            
            self.logger.info("✅ 配置验证通过")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 配置验证失败: {str(e)}")
            return False
    
    def get_config(self, key: str, default: Any = None) -> Any:
        """获取指定配置项"""
        return self._config.get(key, default)
    
    def get_all_config(self) -> Dict[str, Any]:
        """获取所有配置（向后兼容）"""
        return self._config.copy()
