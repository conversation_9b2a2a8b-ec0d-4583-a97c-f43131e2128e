# GEMINI.md - Hu<PERSON> Account Creator

**企业级浏览器自动化平台**，集成反检测、监控自愈、智能决策于一体的高性能账户创建系统。

## 🎯 项目概览

### 核心价值
- **🚀 超高性能**: iframe检测提升1795倍，整体成功率98%+
- **🛡️ 企业级反检测**: MediaCrawler脚本 + CDP模式 + 智能行为模拟
- **🧠 智能监控自愈**: 自动参数优化，零配置运维
- **⚡ Playwright驱动**: 天然反检测 + 异步高性能架构

### 技术栈
```
Playwright + Python 3.8+ + MediaCrawler + TempMail API + 智能监控系统
```

## ⭐ 技术亮点

| 核心技术 | 性能提升 | 生产状态 | 特色功能 |
|---------|---------|---------|---------|
| **Layer 0动态发现** | 1795倍速度提升 | 🟢 生产就绪 | 自适应iframe检测 |
| **反检测系统** | 85%→98%绕过率 | 🟢 生产就绪 | 180KB完整脚本 |
| **人类行为模拟** | 45-50 WPM真实打字 | 🟢 生产就绪 | QWERTY布局感知 |
| **智能监控自愈** | <5ms监控开销 | 🟢 生产就绪 | 自动参数优化 |
| **reCAPTCHA处理** | 双服务商架构 | 🟡 可选启用 | 智能成本控制 |

### 性能指标对比
- **启动时间**: 15-30s → 7-10s (50%+提升)
- **稳定性**: 70% → 98% (28%提升)  
- **检测绕过率**: 80% → 98% (18%提升)
- **代码覆盖率**: 60% → 95% (35%提升)

## 🚀 快速开始

### 1. 环境设置
```bash
# 安装依赖管理器
curl -LsSf https://astral.sh/uv/install.sh | sh

# 安装项目依赖
uv sync

# 安装浏览器
uv run python -m playwright install chromium

# 配置环境
cp .env.example .env  # 编辑添加API密钥
```

### 2. 基础使用
```bash
# 创建账户
uv run python main.py

# 登录测试 (可视化模式)
uv run python login_main.py --headless false
```

### 3. 验证测试
```bash
# 完整功能测试
uv run python tests/test_login_with_verification.py

# 监控系统验证
uv run python tests/monitoring_system_final_report.py
```

## 📚 文档导航

### 核心文档
- **[反检测系统使用指南](docs/Anti_Detection_Usage_Guide.md)** - 180KB完整反检测脚本使用
- **[监控系统指南](docs/Monitoring_System_Guide.md)** - 智能监控与自愈系统  
- **[性能优化指南](docs/Performance_Optimization_Guide.md)** - 1795倍性能提升详解
- **[reCAPTCHA集成指南](docs/reCAPTCHA_Integration_Guide.md)** - 双服务商验证码处理

### 技术参考
- **[API参考文档](docs/API_REFERENCE.md)** - TempMail API完整文档
- **[反检测API参考](docs/Anti_Detection_API_Reference.md)** - 完整API规范
- **[反检测配置指南](docs/Anti_Detection_Configuration_Guide.md)** - 配置选项详解

### 开发工具
- **测试套件**: `tests/` - 完整功能验证
- **调试工具**: `debug/` - 开发调试辅助
- **性能报告**: `logs/monitoring_system_final_report.md`

## 📊 项目状态

### 🟢 生产就绪功能
- ✅ **浏览器自动化**: Playwright + 反检测脚本
- ✅ **动态宿主发现**: Layer 0自适应架构  
- ✅ **人类行为模拟**: 统计学习 + 错误修正
- ✅ **邮件验证集成**: TempMail API自动化
- ✅ **监控自愈系统**: 零配置智能优化
- ✅ **指纹伪装**: Canvas/WebGL/Audio完整伪装

### 🟡 可选功能
- ⚠️ **reCAPTCHA求解**: 已实现，默认禁用
  ```python
  # 启用方法
  bot.enable_captcha_solver(True)
  ```

### 📈 监控系统状态
```
验证通过率: 7/7 (100%)
生产就绪: 是
监控开销: <5ms/操作  
自愈频率: 10分钟智能触发
数据导出: CSV自动导出
```

## 🛡️ 核心架构

### Layer 0: 动态宿主发现
- **并行iframe检测**: 1795倍性能提升
- **智能快速失败**: 零延迟主页面检测
- **URL白名单**: 基于模式的智能跳过

### 反检测系统
- **MediaCrawler脚本**: 180KB完整反检测能力
- **CDP模式**: 真实浏览器控制
- **行为模拟**: 贝塞尔曲线 + 缓动函数
- **智能降级**: 75%成功率错误恢复

### 监控自愈系统
- **SimpleMonitor**: 轻量级数据收集器
- **SimpleHealing**: 智能参数优化算法
- **自动调优**: 基于成功率和响应时间的双重判断

## ⚙️ 开发规范

### 代码风格
- **Python**: 使用snake_case命名，4空格缩进
- **异步优先**: 所有浏览器操作使用async/await
- **类型提示**: 函数参数和返回值添加类型注解
- **日志记录**: 使用统一的logging配置

### 常用命令
```bash
# 开发环境
uv sync                    # 同步依赖
uv run python main.py      # 运行主程序
uv run python login_main.py --headless false  # 可视化测试

# 测试验证
uv run python tests/test_login_with_verification.py     # 核心功能测试
uv run python tests/monitoring_system_final_report.py  # 系统状态验证

# 故障排查
LOG_LEVEL=DEBUG uv run python login_main.py  # 详细日志模式
```

### 环境配置
```bash
# 必需 - 邮件API
API_BASE=https://testkuroneko.xyz/api
API_KEY=your_api_key_here

# 可选 - reCAPTCHA服务
TWOCAPTCHA_API_KEY=your_key_here
DBC_USERNAME=your_username_here
```

## 🚨 使用须知

### 安全合规
- 仅供学习研究，遵守服务条款
- 不保存敏感信息，API密钥安全管理
- 合理使用频率，避免服务压力

### 成本控制
- **当前**: reCAPTCHA检测免费，求解禁用 ($0/月)
- **启用后**: 预算$5-15/月 (按使用量)
- **监控**: 智能成本控制，自动服务商切换

## 📞 问题支持

### 快速排查
```bash
# 环境检测
uv run python tests/test_playwright_basic.py

# 系统验证  
uv run python tests/monitoring_system_final_report.py

# 详细调试
LOG_LEVEL=DEBUG uv run python login_main.py
```

### 常见问题
- **浏览器启动失败**: `python -m playwright install chromium`
- **API调用失败**: 检查网络连接和密钥配置
- **监控数据缺失**: 查看 `logs/monitoring_data.csv`

---

**文档版本**: v4.0.0 | **更新时间**: 2025-07 | **状态**: 🚀 生产就绪