# 基于Python官方镜像的简化Dockerfile，支持Selenium WebDriver
FROM python:3.11-slim

# 设置工作目录
WORKDIR /app

# 安装系统依赖和浏览器
RUN apt-get update && apt-get install -y \
    wget \
    gnupg \
    unzip \
    curl \
    && wget -q -O - https://dl.google.com/linux/linux_signing_key.pub | apt-key add - \
    && echo "deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main" >> /etc/apt/sources.list.d/google-chrome.list \
    && apt-get update \
    && apt-get install -y google-chrome-stable \
    && rm -rf /var/lib/apt/lists/*

# 安装UV包管理器
RUN pip install uv

# 复制依赖文件
COPY requirements.txt .

# 使用UV安装Python依赖
RUN uv pip install --no-cache-dir -r requirements.txt

# 复制源代码
COPY *.py ./

# 创建数据目录用于挂载
RUN mkdir -p /app/data

# 设置环境变量
ENV PYTHONUNBUFFERED=1
ENV PYTHONPATH=/app

# 默认运行命令
CMD ["python", "main.py", "--output", "/app/data/accounts.csv"]

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python -c "import requests; requests.get('http://localhost:8000/health', timeout=5)" || exit 1

# 标签信息
LABEL maintainer="Hulu Account Creator Team"
LABEL version="2.0.0"
LABEL description="Simplified Hulu Account Creator based on Occam's Razor principle"
