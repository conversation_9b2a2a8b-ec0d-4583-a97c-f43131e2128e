#!/usr/bin/env python3
"""
Selector Health-check - 假邮箱真流程检测 + 视觉模板比较系统
简化版本，移除架构逻辑依赖，使用内联选择器定义

核心功能:
1. 假邮箱真流程检测 - 使用假凭据执行真实登录流程的5个关键阶段
2. 视觉模板比较 - SSIM相似度计算，智能漂移检测和基线管理
3. 元素级精准截图 - 避免全屏干扰，减少视觉误报
4. 智能网络拦截 - 动态发现未知端点，自维护拦截规则
5. 多级依赖降级 - scikit-image → opencv → PIL → 无依赖模式

5个关键检测阶段:
1. Welcome页面 → Login按钮 (JavaScript绕过弹窗)
2. 邮箱页面 → 邮箱输入 (假邮箱: <EMAIL>)
3. Continue按钮 → 页面跳转 (验证跳转到密码页)
4. 密码页面 → 密码输入 (假密码: FakePassword123!)
5. Login按钮检测 → 安全退出 (仅检测存在性，不点击)

安全保障:
- 明确的假凭据标识，防止误用
- 多层网络拦截，防止意外登录
- 密码页面后强制退出机制
- 完整的操作审计日志

更新记录:
- 移除 SelectorManager 和 YAML 配置依赖
- 使用内联选择器定义，简化架构
- 保持核心功能和API兼容性
"""

import asyncio
import hashlib
import importlib.util
import json
import logging
import os
import sys
import tempfile
import time
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple, NamedTuple

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 假凭据配置 - 明确标识，防止误用
FAKE_CREDENTIALS = {
    "email": "<EMAIL>",
    "password": "FakePassword123!"
}

# 图像处理库依赖检测
class ImageLibrarySupport(NamedTuple):
    skimage: bool
    cv2: bool
    pil: bool
    numpy: bool

def detect_image_libraries() -> ImageLibrarySupport:
    """检测可用的图像处理库，选择最佳相似度计算方案"""
    return ImageLibrarySupport(
        skimage=importlib.util.find_spec('skimage') is not None,
        cv2=importlib.util.find_spec('cv2') is not None,
        pil=importlib.util.find_spec('PIL') is not None,
        numpy=importlib.util.find_spec('numpy') is not None
    )

# 检测图像处理库支持
IMAGE_LIBS = detect_image_libraries()

try:
    from playwright.async_api import async_playwright, Page, Browser, BrowserContext
except ImportError as e:
    print(f"❌ 导入错误: {e}")
    print("请确保已安装依赖: pip install playwright")
    sys.exit(1)

# 可选图像处理库导入（优雅降级）
if IMAGE_LIBS.skimage:
    try:
        from skimage.metrics import structural_similarity as ssim_skimage
        from skimage import io as skimage_io
        from skimage.color import rgb2gray
        from skimage.transform import resize as skimage_resize
    except ImportError:
        pass

if IMAGE_LIBS.cv2:
    try:
        import cv2
        import numpy as np
    except ImportError:
        pass

if IMAGE_LIBS.pil:
    try:
        from PIL import Image, ImageStat
        if IMAGE_LIBS.numpy:
            import numpy as np
    except ImportError:
        pass


class HealthCheckResult:
    """健康检查结果数据结构"""
    
    def __init__(self):
        self.results: List[Dict[str, Any]] = []
        self.overall_success = True
        self.start_time = time.time()
        self.screenshots: List[str] = []
        
    def add_result(self, phase: str, selector: str, success: bool, 
                   duration_ms: float, screenshot_path: Optional[str] = None,
                   error_message: Optional[str] = None,
                   # 新增假邮箱真流程字段
                   interaction_success: Optional[bool] = None,
                   fake_data_used: Optional[str] = None,
                   # 新增视觉模板比较字段
                   similarity_score: Optional[float] = None,
                   drift_status: Optional[str] = None,
                   baseline_path: Optional[str] = None,
                   baseline_version: Optional[str] = None,
                   comparison_method: Optional[str] = None,
                   # 新增网络活动字段
                   intercepted_requests: Optional[int] = None,
                   unknown_endpoints: Optional[List[str]] = None):
        """添加检查结果 - 支持假邮箱真流程和视觉模板比较"""
        result = {
            # 基础字段
            "phase": phase,
            "selector": selector,
            "success": success,
            "duration_ms": round(duration_ms, 2),
            "timestamp": time.time(),
            "screenshot_path": screenshot_path,
            "error_message": error_message,
            
            # 假邮箱真流程字段
            "interaction_success": interaction_success,
            "fake_data_used": fake_data_used,
            
            # 视觉模板比较字段
            "visual_analysis": {
                "similarity_score": similarity_score,
                "drift_status": drift_status,
                "baseline_path": baseline_path,
                "baseline_version": baseline_version,
                "comparison_method": comparison_method
            } if similarity_score is not None else None,
            
            # 网络活动字段
            "network_activity": {
                "intercepted_requests": intercepted_requests,
                "unknown_endpoints": unknown_endpoints or []
            } if intercepted_requests is not None else None
        }
        self.results.append(result)
        
        if not success:
            self.overall_success = False
            
        if screenshot_path:
            self.screenshots.append(screenshot_path)
    
    def get_summary(self) -> Dict[str, Any]:
        """获取检查摘要"""
        total_duration = time.time() - self.start_time
        success_count = sum(1 for r in self.results if r["success"])
        
        return {
            "overall_success": self.overall_success,
            "total_checks": len(self.results),
            "success_count": success_count,
            "failure_count": len(self.results) - success_count,
            "total_duration_seconds": round(total_duration, 2),
            "success_rate": round(success_count / len(self.results) * 100, 1) if self.results else 0,
            "screenshots_captured": len(self.screenshots),
            "timestamp": time.time()
        }
    
    def save_to_json(self, file_path: str):
        """保存结果到JSON文件"""
        output = {
            "summary": self.get_summary(),
            "results": self.results,
            "screenshots": self.screenshots
        }
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(output, f, indent=2, ensure_ascii=False)


class SelectorHealthChecker:
    """假邮箱真流程检测 + 视觉模板比较系统"""
    
    def __init__(self, headless: bool = True, screenshot_on_failure: bool = True,
                 enable_visual_comparison: bool = True, visual_threshold: float = 0.8,
                 create_baselines: bool = False, element_screenshots: bool = True):
        """
        初始化健康检查器
        
        Args:
            headless: 是否使用无头模式
            screenshot_on_failure: 失败时是否截图
            enable_visual_comparison: 是否启用视觉模板比较
            visual_threshold: 视觉相似度阈值 (0.0-1.0)
            create_baselines: 是否创建基线模板模式
            element_screenshots: 是否使用元素级截图
        """
        self.headless = headless
        self.screenshot_on_failure = screenshot_on_failure
        self.enable_visual_comparison = enable_visual_comparison
        self.visual_threshold = visual_threshold
        self.create_baselines = create_baselines
        self.element_screenshots = element_screenshots
        
        # 移除 SelectorManager 依赖，使用内联选择器定义
        self._init_inline_selectors()
        self.result = HealthCheckResult()
        
        # 设置日志
        self.logger = self._setup_logging()
        
        # 创建目录结构
        self.screenshot_dir = project_root / "screenshots" / "healthcheck"
        self.baseline_dir = project_root / "screenshots" / "baselines"
        self.screenshot_dir.mkdir(parents=True, exist_ok=True)
        self.baseline_dir.mkdir(parents=True, exist_ok=True)
        
        # 网络拦截统计
        self.intercepted_requests = 0
        self.unknown_endpoints = []
        
        # 检测图像处理能力
        self.image_capability = self._detect_image_capability()
        
        self.logger.info("🏥 假邮箱真流程检测系统初始化完成")
        self.logger.info(f"   视觉比较: {'启用' if enable_visual_comparison else '禁用'}")
        self.logger.info(f"   图像处理: {self.image_capability}")
        self.logger.info(f"   假凭据: {FAKE_CREDENTIALS['email']}")
    
    def _init_inline_selectors(self):
        """初始化内联选择器定义，替代 SelectorManager"""
        # 登录按钮选择器（Welcome页面）
        self.welcome_login_selectors = [
            'a[href*="login"]',           # 测试验证的最佳选择器
            'button:has-text("Log In")',
            'button:has-text("LOG IN")',
            'a:has-text("Log In")',
            'a:has-text("LOG IN")',
            'text="Log In"',
            'text="LOG IN"',
            'nav a[href*="login"]',
            'header a[href*="login"]',
            '[href*="login"]'
        ]
        
        # 邮箱输入字段选择器
        self.email_field_selectors = [
            '#email',
            'input[type="email"]',
            'input[name="email"]',
            'input[autocomplete="email"]',
            '#email-field',
            'input[name="email-field"]',
            '#email_id',
            'input[id="email_id"]',
            'input[placeholder="Email"]',
            'input[placeholder*="email"]',
            'input[data-testid*="email"]'
        ]
        
        # Continue按钮选择器
        self.continue_button_selectors = [
            'button:has-text("Continue")',
            'button:has-text("继续")',
            'input[type="submit"][value="Continue"]',
            'button[type="submit"]:has-text("Continue")',
            'button[type="submit"]',
            'input[type="submit"]',
            'button:contains("Continue")',
            '[data-testid*="continue"]',
            '.continue-btn',
            '.continue-button'
        ]
        
        # 密码输入字段选择器
        self.password_field_selectors = [
            '#password',
            'input[name="password"]',
            'input[type="password"]',
            'input[placeholder*="password"]',
            'input[placeholder*="Password"]',
            'input[data-testid*="password"]'
        ]
        
        # 登录提交按钮选择器
        self.login_submit_selectors = [
            'button:has-text("Log In")',
            'button:has-text("LOGIN")',
            'button[type="submit"]:has-text("Log In")',
            'form button[type="submit"]',
            'button[type="submit"]',
            'input[type="submit"]',
            'button:has-text("Sign In")',
            'button[data-testid*="login"]',
            '.login-button',
            'button[class*="login"]'
        ]
    
    def _setup_logging(self) -> logging.Logger:
        """设置结构化日志"""
        logger = logging.getLogger('healthcheck')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            # 控制台输出
            console_handler = logging.StreamHandler()
            console_formatter = logging.Formatter(
                '%(asctime)s | %(levelname)8s | %(message)s',
                datefmt='%H:%M:%S'
            )
            console_handler.setFormatter(console_formatter)
            logger.addHandler(console_handler)
            
            # 文件输出
            log_file = project_root / "logs" / "healthcheck.log"
            log_file.parent.mkdir(exist_ok=True)
            
            file_handler = logging.FileHandler(log_file, mode='w', encoding='utf-8')
            file_formatter = logging.Formatter(
                '%(asctime)s | %(levelname)8s | %(name)s | %(message)s'
            )
            file_handler.setFormatter(file_formatter)
            logger.addHandler(file_handler)
        
        return logger
    
    def _detect_image_capability(self) -> str:
        """检测图像处理能力，返回最佳方案"""
        if IMAGE_LIBS.skimage:
            return "scikit-image (完整SSIM)"
        elif IMAGE_LIBS.cv2 and IMAGE_LIBS.numpy:
            return "opencv-python (轻量SSIM)"
        elif IMAGE_LIBS.pil and IMAGE_LIBS.numpy:
            return "PIL+numpy (基础相似度)"
        else:
            return "无依赖 (跳过视觉比较)"
    
    def _calculate_similarity(self, current_img_path: str, baseline_img_path: str) -> Tuple[Optional[float], str]:
        """
        多级相似度计算，支持依赖降级
        
        Returns:
            Tuple[similarity_score, method_used]
        """
        if not self.enable_visual_comparison:
            return None, "disabled"
            
        try:
            # 方案1: scikit-image SSIM (最精确)
            if IMAGE_LIBS.skimage:
                return self._ssim_skimage(current_img_path, baseline_img_path)
            
            # 方案2: OpenCV SSIM (轻量级)
            elif IMAGE_LIBS.cv2 and IMAGE_LIBS.numpy:
                return self._ssim_opencv(current_img_path, baseline_img_path)
            
            # 方案3: PIL基础相似度 (最基础)
            elif IMAGE_LIBS.pil and IMAGE_LIBS.numpy:
                return self._basic_similarity_pil(current_img_path, baseline_img_path)
            
            else:
                self.logger.warning("⚠️ 无可用图像处理库，跳过视觉比较")
                return None, "no_library"
                
        except Exception as e:
            self.logger.error(f"❌ 相似度计算失败: {e}")
            return None, "error"
    
    def _ssim_skimage(self, current_path: str, baseline_path: str) -> Tuple[float, str]:
        """使用scikit-image计算SSIM相似度"""
        current_img = skimage_io.imread(current_path)
        baseline_img = skimage_io.imread(baseline_path)
        
        # 转为灰度图并调整尺寸
        if len(current_img.shape) == 3:
            current_img = rgb2gray(current_img)
        if len(baseline_img.shape) == 3:
            baseline_img = rgb2gray(baseline_img)
        
        # 统一尺寸 (减少尺寸变化影响)
        target_size = (800, 600)
        current_img = skimage_resize(current_img, target_size, anti_aliasing=True)
        baseline_img = skimage_resize(baseline_img, target_size, anti_aliasing=True)
        
        # 计算SSIM
        ssim_score = ssim_skimage(current_img, baseline_img, data_range=1.0)
        return float(ssim_score), "ssim_skimage"
    
    def _ssim_opencv(self, current_path: str, baseline_path: str) -> Tuple[float, str]:
        """使用OpenCV计算SSIM相似度"""
        current_img = cv2.imread(current_path, cv2.IMREAD_GRAYSCALE)
        baseline_img = cv2.imread(baseline_path, cv2.IMREAD_GRAYSCALE)
        
        # 统一尺寸
        target_size = (800, 600)
        current_img = cv2.resize(current_img, target_size)
        baseline_img = cv2.resize(baseline_img, target_size)
        
        # 简化SSIM计算 (使用OpenCV方法)
        # 这里使用简化的结构相似性度量
        mean_diff = np.mean(np.abs(current_img.astype(float) - baseline_img.astype(float)))
        max_val = 255.0
        similarity = 1.0 - (mean_diff / max_val)
        
        return float(similarity), "ssim_opencv"
    
    def _basic_similarity_pil(self, current_path: str, baseline_path: str) -> Tuple[float, str]:
        """使用PIL计算基础相似度"""
        current_img = Image.open(current_path).convert('L').resize((800, 600))
        baseline_img = Image.open(baseline_path).convert('L').resize((800, 600))
        
        # 转为numpy数组
        current_array = np.array(current_img)
        baseline_array = np.array(baseline_img)
        
        # 计算平均绝对误差相似度
        mae = np.mean(np.abs(current_array.astype(float) - baseline_array.astype(float)))
        similarity = 1.0 - (mae / 255.0)
        
        return float(similarity), "basic_pil"
    
    def _classify_drift_status(self, similarity_score: Optional[float]) -> str:
        """根据相似度分数分类漂移状态"""
        if similarity_score is None:
            return "unknown"
        elif similarity_score >= self.visual_threshold:
            return "normal"
        elif similarity_score >= 0.6:
            return "warning"
        else:
            return "critical"
    
    async def _setup_page_stubs(self, page: Page):
        """设置智能网络拦截和脚本注入"""
        
        # 1. 智能网络请求拦截 - 避免真实登录 + 动态发现
        await self._setup_smart_network_intercept(page)
        
        # 2. reCAPTCHA Mock 注入 (简化版本)
        recaptcha_script = """
        window.grecaptcha = {
            execute: () => Promise.resolve('dummy_token_for_healthcheck'),
            ready: (callback) => callback(),
            render: () => 'dummy_widget_id'
        };
        """
        await page.add_init_script(recaptcha_script)
        self.logger.debug("🤖 reCAPTCHA Mock 已注入")
        
        # 3. 增强 Stealth 补丁
        stealth_script = """
        // 增强反检测补丁
        Object.defineProperty(navigator, 'webdriver', {
            get: () => undefined,
        });
        
        // 伪装Chrome标识
        Object.defineProperty(navigator, 'languages', {
            get: () => ['en-US', 'en'],
        });
        
        // 屏蔽自动化检测
        window.chrome = {
            runtime: {},
        };
        
        // 伪装触摸设备能力
        Object.defineProperty(navigator, 'maxTouchPoints', {
            get: () => 1,
        });
        
        // 隐藏自动化框架痕迹
        delete window.cdc_adoQpoasnfa76pfcZLmcfl_Array;
        delete window.cdc_adoQpoasnfa76pfcZLmcfl_Promise;
        delete window.cdc_adoQpoasnfa76pfcZLmcfl_Symbol;
        """
        
        await page.add_init_script(stealth_script)
        self.logger.debug("🥷 增强Stealth补丁已应用")
    
    async def _setup_smart_network_intercept(self, page: Page):
        """设置智能网络拦截器"""
        
        # 扩展的拦截规则 (基于用户建议)
        intercept_patterns = [
            "**/accounts/**",      # 账户相关API
            "**/recaptcha/**",     # reCAPTCHA相关
            "**/login/**",         # 登录API
            "**/auth/**",          # 认证API
            "**/api/v*/users/**",  # 用户API变体
            "**/authenticate/**",  # 认证变体
            "**/signin/**",        # 登录变体
            "**/oauth/**",         # OAuth相关
        ]
        
        async def handle_request_route(route):
            request = route.request
            url = request.url
            method = request.method
            
            # 检查是否需要拦截
            should_intercept = any(
                self._match_pattern(url, pattern) 
                for pattern in intercept_patterns
            )
            
            if should_intercept:
                self.intercepted_requests += 1
                self.logger.debug(f"🚫 拦截请求: {method} {url}")
                
                # 返回模拟响应
                await route.fulfill(
                    status=200,
                    body='{"status": "mock", "message": "healthcheck_intercepted"}',
                    headers={"Content-Type": "application/json"}
                )
            else:
                # 检查是否为认证失败的未知端点
                if method == "POST" and self._is_potential_auth_endpoint(url):
                    self.logger.info(f"🔍 发现潜在认证端点: {url}")
                    self.unknown_endpoints.append(url)
                
                await route.continue_()
        
        # 应用拦截器
        await page.route("**/*", handle_request_route)
        
        self.logger.info(f"🌐 智能网络拦截器已设置 ({len(intercept_patterns)} 个规则)")
    
    def _match_pattern(self, url: str, pattern: str) -> bool:
        """匹配URL模式（支持通配符）"""
        import fnmatch
        return fnmatch.fnmatch(url.lower(), pattern.lower())
    
    def _is_potential_auth_endpoint(self, url: str) -> bool:
        """判断是否为潜在的认证端点"""
        auth_indicators = [
            'login', 'auth', 'signin', 'oauth', 'token', 
            'verify', 'validate', 'session', 'user'
        ]
        url_lower = url.lower()
        return any(indicator in url_lower for indicator in auth_indicators)
    
    async def _take_screenshot(self, page: Page, phase: str, success: bool, 
                              selector: Optional[str] = None) -> Optional[str]:
        """增强截图功能 - 支持元素级截图和视觉比较"""
        if not self.screenshot_on_failure and success:
            return None
            
        timestamp = int(time.time())
        status = "success" if success else "failure"
        
        # 文件命名：如果是创建基线模式，使用固定名称
        if self.create_baselines:
            filename = f"baseline_{phase}.png"
            screenshot_path = self.baseline_dir / filename
        else:
            filename = f"{phase}_{status}_{timestamp}.png"
            screenshot_path = self.screenshot_dir / filename
        
        try:
            # 元素级截图 vs 全页面截图
            if self.element_screenshots and selector and success:
                screenshot_path = await self._take_element_screenshot(page, selector, str(screenshot_path))
            else:
                # 全页面截图（降级方案）
                await page.screenshot(path=str(screenshot_path), full_page=True)
            
            self.logger.debug(f"📸 截图已保存: {filename}")
            return str(screenshot_path)
            
        except Exception as e:
            self.logger.warning(f"⚠️ 截图失败: {e}")
            return None
    
    async def _take_element_screenshot(self, page: Page, selector: str, output_path: str) -> str:
        """
        元素级精准截图 - 减少视觉噪声和误报
        """
        try:
            # 等待元素出现
            element = await page.wait_for_selector(selector, timeout=3000)
            if not element:
                # 元素不存在，降级到全页面截图
                await page.screenshot(path=output_path, full_page=True)
                self.logger.debug(f"🔄 元素级截图失败，降级到全页面: {selector}")
                return output_path
            
            # 获取元素边界框
            bbox = await element.bounding_box()
            if not bbox:
                # 无法获取边界框，降级到全页面截图
                await page.screenshot(path=output_path, full_page=True)
                self.logger.debug(f"🔄 无法获取元素边界框，降级到全页面: {selector}")
                return output_path
            
            # 扩展边界框，包含更多上下文（减少裁剪过紧的问题）
            padding = 20
            expanded_bbox = {
                'x': max(0, bbox['x'] - padding),
                'y': max(0, bbox['y'] - padding),
                'width': bbox['width'] + 2 * padding,
                'height': bbox['height'] + 2 * padding
            }
            
            # 元素级截图
            await page.screenshot(path=output_path, clip=expanded_bbox)
            self.logger.debug(f"🎯 元素级截图成功: {selector}")
            return output_path
            
        except Exception as e:
            # 出错时降级到全页面截图
            self.logger.debug(f"⚠️ 元素级截图异常，降级到全页面: {e}")
            await page.screenshot(path=output_path, full_page=True)
            return output_path
    
    async def _compare_with_baseline(self, current_screenshot: str, phase: str) -> Tuple[Optional[float], str, Optional[str], Optional[str]]:
        """
        与基线模板比较，返回相似度和漂移状态
        
        Returns:
            Tuple[similarity_score, drift_status, baseline_path, baseline_version]
        """
        if not self.enable_visual_comparison or self.create_baselines:
            return None, "disabled", None, None
        
        # 查找基线文件
        baseline_path = self.baseline_dir / f"baseline_{phase}.png"
        
        if not baseline_path.exists():
            self.logger.warning(f"⚠️ 基线模板不存在: {baseline_path}")
            self.logger.info(f"💡 提示: 使用 --create-baselines 创建基线模板")
            return None, "no_baseline", None, None
        
        # 计算相似度
        similarity_score, method = self._calculate_similarity(current_screenshot, str(baseline_path))
        
        if similarity_score is None:
            return None, "calculation_failed", str(baseline_path), "unknown"
        
        # 分类漂移状态
        drift_status = self._classify_drift_status(similarity_score)
        
        # 记录相似度结果
        self.logger.info(f"🔍 视觉比较 [{phase}]: 相似度={similarity_score:.3f} 状态={drift_status} 方法={method}")
        
        # 严重漂移时的更新提醒
        if drift_status == "critical":
            self.logger.warning(f"🚨 严重模板漂移检测! 相似度: {similarity_score:.3f}")
            self.logger.warning(f"💡 建议运行: --update-baseline={phase}")
        
        return similarity_score, drift_status, str(baseline_path), "1.0.0"
    
    async def _check_selector_visibility(self, page: Page, phase: str, 
                                       selectors: List[str]) -> Tuple[bool, str, float]:
        """
        检查选择器可见性
        
        Returns:
            Tuple[bool, str, float]: (成功状态, 找到的选择器, 耗时毫秒)
        """
        start_time = time.time()
        
        # 检查页面是否仍然可用
        try:
            await page.wait_for_load_state('domcontentloaded', timeout=5000)
        except Exception as e:
            self.logger.warning(f"⚠️ {phase}: 页面状态检查失败 - {e}")
        
        for selector in selectors:
            try:
                # 等待元素可见，超时时间较短以快速失败
                element = await page.wait_for_selector(
                    selector, 
                    timeout=3000,  # 增加到3秒超时
                    state='visible'
                )
                
                if element and await element.is_visible():
                    duration_ms = (time.time() - start_time) * 1000
                    self.logger.info(f"✅ {phase}: 找到可见元素 '{selector}' ({duration_ms:.1f}ms)")
                    return True, selector, duration_ms
                    
            except Exception as e:
                self.logger.debug(f"🔍 {phase}: 选择器 '{selector}' 未找到 - {e}")
                continue
        
        duration_ms = (time.time() - start_time) * 1000
        self.logger.error(f"❌ {phase}: 所有选择器均未找到 ({duration_ms:.1f}ms)")
        return False, "", duration_ms
    
    async def _simulate_form_filling(self, page: Page, selector: str, value: str):
        """模拟表单填充 - 仅触发DOM变化，不提交真实数据"""
        try:
            element = await page.wait_for_selector(selector, timeout=5000)
            if element:
                # 检查元素类型，只对输入字段填充
                tag_name = await element.evaluate("el => el.tagName.toLowerCase()")
                element_type = await element.evaluate("el => el.type || 'text'")
                
                if tag_name == 'input' or tag_name == 'textarea':
                    # 清空并填入测试值
                    await element.clear()
                    await element.fill(value)
                    # 模拟真实用户行为：失焦触发验证
                    await element.blur()
                    self.logger.debug(f"📝 模拟填充: {selector} = {value} ({tag_name}[{element_type}])")
                else:
                    self.logger.debug(f"📝 跳过非输入元素: {selector} ({tag_name})")
        except Exception as e:
            self.logger.warning(f"⚠️ 表单填充失败: {selector} - {e}")

    async def _check_and_handle_exit_intent_modal(self, page: Page) -> bool:
        """
        检查并处理ExitIntentModal弹窗
        基于hulu_automation_stealth.py的弹窗检测和处理逻辑
        """
        try:
            # 检测ExitIntentModal状态（包括--show类）
            modal_info = await asyncio.wait_for(
                page.evaluate("""
                    () => {
                        const modal = document.querySelector('.ExitIntentModal__wrapper');
                        if (!modal) return { exists: false };
                        
                        return {
                            exists: true,
                            has_show_class: modal.classList.contains('--show'),
                            visible: modal.offsetParent !== null,
                            classes: modal.className,
                            display: getComputedStyle(modal).display,
                            intercepts_clicks: modal.classList.contains('--show')
                        };
                    }
                """),
                timeout=5.0  # 5秒超时
            )
            
            if modal_info['exists']:
                if modal_info['has_show_class']:
                    self.logger.info("🚨 检测到ExitIntentModal --show类拦截")
                    self.logger.info(f"   - 可见状态: {modal_info['visible']}")
                    self.logger.info(f"   - CSS类: {modal_info['classes']}")
                    return True  # 存在弹窗拦截
                else:
                    self.logger.info("ℹ️ ExitIntentModal存在但未激活")
                    return False  # 无拦截
            else:
                self.logger.debug("ℹ️ ExitIntentModal不存在")
                return False  # 无拦截
                
        except Exception as e:
            self.logger.debug(f"ExitIntentModal检测异常: {e}")
            return False  # 检测失败，假设无拦截

    async def _click_login_with_js(self, page: Page) -> bool:
        """
        使用JavaScript直接点击LOGIN按钮，绕过ExitIntentModal拦截
        基于hulu_automation_stealth.py的测试验证方案
        """
        try:
            # 先检查是否有弹窗拦截
            self.logger.info("🔧 [DEBUG] 检查ExitIntentModal弹窗...")
            modal_start = time.time()
            has_modal_intercept = await self._check_and_handle_exit_intent_modal(page)
            self.logger.info(f"🔧 [DEBUG] 弹窗检查完成 ({time.time() - modal_start:.2f}s), 结果: {has_modal_intercept}")
            
            if has_modal_intercept:
                self.logger.info("🎯 检测到弹窗拦截，使用JavaScript绕过...")
            else:
                self.logger.info("🎯 使用JavaScript点击LOGIN按钮...")
            
            # 使用测试验证的最佳策略：DOM直接点击
            self.logger.info("🔧 [DEBUG] 执行JavaScript点击逻辑...")
            js_start = time.time()
            click_result = await asyncio.wait_for(
                page.evaluate("""
                    () => {
                        const selectors = [
                            'a[href*="login"]',           // 测试验证的最佳选择器
                            'text="LOG IN"',              // 备用选择器
                            'text="Log In"',              // 备用选择器
                            'a:has-text("Log In")',       // 备用选择器
                            'button:has-text("LOG IN")',  // 备用选择器
                            'nav a:has-text("Log In")',   // 导航栏中的登录链接
                            '.login-button',              // CSS类选择器
                            '[data-testid*="login"]'      // 测试ID选择器
                        ];
                        
                        for (const selector of selectors) {
                            try {
                                let element = null;
                                
                                // 处理Playwright特有的选择器
                                if (selector.startsWith('text=')) {
                                    const text = selector.replace('text="', '').replace('"', '');
                                    const elements = Array.from(document.querySelectorAll('a, button'));
                                    element = elements.find(el => el.textContent?.trim() === text);
                                } else if (selector.includes(':has-text(')) {
                                    // 简化has-text处理
                                    const text = selector.match(/has-text\\("([^"]+)"\\)/)?.[1];
                                    if (text) {
                                        const tagName = selector.split(':')[0];
                                        const elements = Array.from(document.querySelectorAll(tagName));
                                        element = elements.find(el => el.textContent?.includes(text));
                                    }
                                } else {
                                    element = document.querySelector(selector);
                                }
                                
                                if (element && element.offsetParent !== null) {
                                    // 强制点击，绕过事件拦截
                                    element.click();
                                    
                                    return {
                                        success: true,
                                        method: 'javascript_click',
                                        selector: selector,
                                        element_tag: element.tagName,
                                        element_text: element.textContent?.trim() || '',
                                        href: element.href || ''
                                    };
                                }
                            } catch (selectorError) {
                                console.log(`选择器 ${selector} 失败:`, selectorError);
                                continue;
                            }
                        }
                        
                        return { success: false, error: 'No clickable login element found' };
                    }
                """),
                timeout=10.0  # 10秒超时
            )
            self.logger.info(f"🔧 [DEBUG] JavaScript执行完成 ({time.time() - js_start:.2f}s)")
            
            if click_result.get('success'):
                self.logger.info(f"✅ JavaScript点击成功: {click_result['method']}")
                self.logger.info(f"   - 选择器: {click_result['selector']}")
                self.logger.info(f"   - 元素: {click_result['element_tag']} - {click_result['element_text']}")
                if click_result.get('href'):
                    self.logger.info(f"   - 链接: {click_result['href']}")
                return True
            else:
                self.logger.warning(f"❌ JavaScript点击失败: {click_result.get('error', 'Unknown error')}")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ JavaScript点击异常: {e}")
            return False
    
    async def run_healthcheck(self, target_url: str = "https://www.hulu.com") -> int:
        """
        运行完整的健康检查
        
        Args:
            target_url: 目标URL
            
        Returns:
            int: 退出码 (0=成功, 1=失败)
        """
        self.logger.info(f"🚀 开始健康检查: {target_url}")
        
        # 设置总体超时
        timeout = getattr(self, 'timeout', 60.0)
        try:
            return await asyncio.wait_for(self._run_healthcheck_internal(target_url), timeout=timeout)
        except asyncio.TimeoutError:
            self.logger.error(f"❌ 健康检查总体超时 ({timeout}秒)")
            return 1
        except Exception as e:
            self.logger.error(f"❌ 健康检查异常: {e}")
            return 1
    
    async def _run_healthcheck_internal(self, target_url: str) -> int:
        """假邮箱真流程检测 - 5个关键阶段实施"""
        self.logger.info("🔧 [DEBUG] 开始内部健康检查流程")
        
        async with async_playwright() as playwright:
            self.logger.info("🔧 [DEBUG] 启动浏览器...")
            start_time = time.time()
            
            # 启动浏览器 (非CDP模式)
            browser = await playwright.chromium.launch(
                headless=self.headless,
                args=[
                    '--no-sandbox',
                    '--disable-blink-features=AutomationControlled',
                    '--disable-extensions',
                    '--disable-plugins'
                ]
            )
            self.logger.info(f"🔧 [DEBUG] 浏览器启动完成 ({time.time() - start_time:.2f}s)")
            
            try:
                self.logger.info("🔧 [DEBUG] 创建浏览器上下文...")
                start_time = time.time()
                context = await browser.new_context(
                    viewport={'width': 1366, 'height': 768},
                    user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
                )
                self.logger.info(f"🔧 [DEBUG] 上下文创建完成 ({time.time() - start_time:.2f}s)")
                
                self.logger.info("🔧 [DEBUG] 创建新页面...")
                start_time = time.time()
                page = await context.new_page()
                self.logger.info(f"🔧 [DEBUG] 页面创建完成 ({time.time() - start_time:.2f}s)")
                
                # 设置智能网络拦截和脚本注入
                self.logger.info("🔧 [DEBUG] 设置页面拦截...")
                start_time = time.time()
                await self._setup_page_stubs(page)
                self.logger.info(f"🔧 [DEBUG] 页面拦截设置完成 ({time.time() - start_time:.2f}s)")
                
                # 导航到目标页面
                self.logger.info(f"🌐 导航到: {target_url}")
                start_time = time.time()
                try:
                    await page.goto(target_url, wait_until='domcontentloaded', timeout=30000)
                    self.logger.info(f"🔧 [DEBUG] 页面导航完成 ({time.time() - start_time:.2f}s)")
                except Exception as e:
                    self.logger.error(f"❌ 页面导航失败: {e}")
                    return 1
                
                # 执行5个假邮箱真流程阶段
                self.logger.info("🔧 [DEBUG] 开始执行假邮箱真流程...")
                start_time = time.time()
                stages_success = await self._execute_fake_email_flow(page)
                self.logger.info(f"🔧 [DEBUG] 假邮箱真流程完成 ({time.time() - start_time:.2f}s)")
                
            finally:
                self.logger.info("🔧 [DEBUG] 关闭浏览器...")
                start_time = time.time()
                await browser.close()
                self.logger.info(f"🔧 [DEBUG] 浏览器关闭完成 ({time.time() - start_time:.2f}s)")
        
        return 0 if stages_success else 1
    
    async def _execute_fake_email_flow(self, page: Page) -> bool:
        """执行假邮箱真流程的5个关键阶段"""
        
        self.logger.info("🎯 开始假邮箱真流程检测 (5个关键阶段)")
        self.logger.info(f"   假邮箱: {FAKE_CREDENTIALS['email']}")
        self.logger.info(f"   假密码: {FAKE_CREDENTIALS['password']}")
        
        try:
            # 阶段1: Welcome页面 → Login按钮
            self.logger.info("🔧 [DEBUG] 开始阶段1: Welcome页面登录按钮...")
            success1 = await self._stage1_welcome_login(page)
            self.logger.info(f"🔧 [DEBUG] 阶段1完成，结果: {success1}")
            
            # 阶段2: 邮箱页面 → 邮箱输入  
            success2 = await self._stage2_email_input(page)
            
            # 阶段3: Continue按钮 → 页面跳转
            success3 = await self._stage3_continue_button(page)
            
            # 阶段4: 密码页面 → 密码输入
            success4 = await self._stage4_password_input(page)
            
            # 阶段5: Login按钮检测 → 安全退出
            success5 = await self._stage5_login_button_detection(page)
            
            # 计算总体成功率
            stages_passed = sum([success1, success2, success3, success4, success5])
            total_stages = 5
            success_rate = (stages_passed / total_stages) * 100
            
            self.logger.info(f"📊 假邮箱真流程检测完成:")
            self.logger.info(f"   通过阶段: {stages_passed}/{total_stages}")
            self.logger.info(f"   成功率: {success_rate:.1f}%")
            self.logger.info(f"   拦截请求: {self.intercepted_requests}")
            self.logger.info(f"   发现端点: {len(self.unknown_endpoints)}")
            
            # 记录发现的未知端点
            if self.unknown_endpoints:
                self.logger.info("🔍 发现的潜在认证端点:")
                for endpoint in self.unknown_endpoints[:5]:  # 只显示前5个
                    self.logger.info(f"   - {endpoint}")
            
            return stages_passed >= 4  # 至少4个阶段成功才算通过
            
        except Exception as e:
            self.logger.error(f"❌ 假邮箱真流程执行异常: {e}")
            return False
    
    async def _stage1_welcome_login(self, page: Page) -> bool:
        """阶段1: Welcome页面 → Login按钮 (JavaScript绕过弹窗)"""
        phase = "welcome_login"
        self.logger.info(f"🔄 [阶段1] Welcome页面LOGIN按钮检测")
        
        start_time = time.time()
        
        # 使用现有的JavaScript点击方法
        self.logger.info("🔧 [DEBUG] 开始JavaScript点击登录按钮...")
        js_click_success = await self._click_login_with_js(page)
        self.logger.info(f"🔧 [DEBUG] JavaScript点击结果: {js_click_success}")
        
        if js_click_success:
            # 等待页面跳转到登录页
            await self._wait_for_page_transition(page, phase)
            
            # 截图 + 视觉比较
            screenshot_path = await self._take_screenshot(page, phase, True, 'a[href*="login"]')
            similarity_score, drift_status, baseline_path, baseline_version = await self._compare_with_baseline(screenshot_path, phase)
            
            duration_ms = (time.time() - start_time) * 1000
            
            # 记录结果
            self.result.add_result(
                phase=phase,
                selector='a[href*="login"]',
                success=True,
                duration_ms=duration_ms,
                screenshot_path=screenshot_path,
                interaction_success=True,
                fake_data_used=None,
                similarity_score=similarity_score,
                drift_status=drift_status,
                baseline_path=baseline_path,
                baseline_version=baseline_version,
                comparison_method=self.image_capability,
                intercepted_requests=self.intercepted_requests,
                unknown_endpoints=self.unknown_endpoints.copy()
            )
            
            self.logger.info(f"✅ [阶段1] Welcome页面LOGIN按钮检测成功 ({duration_ms:.1f}ms)")
            return True
        else:
            duration_ms = (time.time() - start_time) * 1000
            screenshot_path = await self._take_screenshot(page, phase, False)
            
            self.result.add_result(
                phase=phase,
                selector="javascript_login_failed",
                success=False,
                duration_ms=duration_ms,
                screenshot_path=screenshot_path,
                error_message="JavaScript LOGIN按钮点击失败",
                interaction_success=False
            )
            
            self.logger.error(f"❌ [阶段1] Welcome页面LOGIN按钮检测失败 ({duration_ms:.1f}ms)")
            return False
    
    async def _stage2_email_input(self, page: Page) -> bool:
        """阶段2: 邮箱页面 → 邮箱输入 (假邮箱填入)"""
        phase = "email_input"
        self.logger.info(f"🔄 [阶段2] 邮箱输入框检测和填充")
        
        start_time = time.time()
        
        # 使用内联邮箱字段选择器
        email_selectors = self.email_field_selectors
        
        # 检查邮箱输入框可见性
        success, found_selector, check_duration = await self._check_selector_visibility(
            page, phase, email_selectors
        )
        
        if success and found_selector:
            # 填入假邮箱
            interaction_success = await self._fill_email_field(page, found_selector, FAKE_CREDENTIALS['email'])
            
            # 截图 + 视觉比较
            screenshot_path = await self._take_screenshot(page, phase, True, found_selector)
            similarity_score, drift_status, baseline_path, baseline_version = await self._compare_with_baseline(screenshot_path, phase)
            
            duration_ms = (time.time() - start_time) * 1000
            
            self.result.add_result(
                phase=phase,
                selector=found_selector,
                success=True,
                duration_ms=duration_ms,
                screenshot_path=screenshot_path,
                interaction_success=interaction_success,
                fake_data_used=FAKE_CREDENTIALS['email'],
                similarity_score=similarity_score,
                drift_status=drift_status,
                baseline_path=baseline_path,
                baseline_version=baseline_version,
                comparison_method=self.image_capability
            )
            
            self.logger.info(f"✅ [阶段2] 邮箱输入框检测成功，已填入假邮箱 ({duration_ms:.1f}ms)")
            return True
        else:
            duration_ms = (time.time() - start_time) * 1000
            screenshot_path = await self._take_screenshot(page, phase, False)
            
            self.result.add_result(
                phase=phase,
                selector=f"failed_from_{len(email_selectors)}_selectors",
                success=False,
                duration_ms=duration_ms,
                screenshot_path=screenshot_path,
                error_message="邮箱输入框不可见",
                interaction_success=False
            )
            
            self.logger.error(f"❌ [阶段2] 邮箱输入框检测失败 ({duration_ms:.1f}ms)")
            return False
    
    async def _stage3_continue_button(self, page: Page) -> bool:
        """阶段3: Continue按钮 → 页面跳转 (验证跳转到密码页)"""
        phase = "continue_button"
        self.logger.info(f"🔄 [阶段3] Continue按钮检测和页面跳转")
        
        start_time = time.time()
        
        # 使用内联Continue按钮选择器
        continue_selectors = self.continue_button_selectors
        
        # 检查Continue按钮可见性
        success, found_selector, check_duration = await self._check_selector_visibility(
            page, phase, continue_selectors
        )
        
        if success and found_selector:
            # 点击Continue按钮
            interaction_success = await self._click_continue_button(page, found_selector)
            
            if interaction_success:
                # 等待页面跳转到密码页
                await self._wait_for_page_transition(page, phase)
                
                # 截图 + 视觉比较
                screenshot_path = await self._take_screenshot(page, phase, True, found_selector)
                similarity_score, drift_status, baseline_path, baseline_version = await self._compare_with_baseline(screenshot_path, phase)
                
                duration_ms = (time.time() - start_time) * 1000
                
                self.result.add_result(
                    phase=phase,
                    selector=found_selector,
                    success=True,
                    duration_ms=duration_ms,
                    screenshot_path=screenshot_path,
                    interaction_success=True,
                    fake_data_used=None,
                    similarity_score=similarity_score,
                    drift_status=drift_status,
                    baseline_path=baseline_path,
                    baseline_version=baseline_version,
                    comparison_method=self.image_capability
                )
                
                self.logger.info(f"✅ [阶段3] Continue按钮点击成功，页面已跳转 ({duration_ms:.1f}ms)")
                return True
            else:
                # 按钮找到但点击失败
                duration_ms = (time.time() - start_time) * 1000
                screenshot_path = await self._take_screenshot(page, phase, False)
                
                self.result.add_result(
                    phase=phase,
                    selector=found_selector,
                    success=False,
                    duration_ms=duration_ms,
                    screenshot_path=screenshot_path,
                    error_message="Continue按钮点击失败",
                    interaction_success=False
                )
                
                self.logger.error(f"❌ [阶段3] Continue按钮点击失败 ({duration_ms:.1f}ms)")
                return False
        else:
            duration_ms = (time.time() - start_time) * 1000
            screenshot_path = await self._take_screenshot(page, phase, False)
            
            self.result.add_result(
                phase=phase,
                selector=f"failed_from_{len(continue_selectors)}_selectors",
                success=False,
                duration_ms=duration_ms,
                screenshot_path=screenshot_path,
                error_message="Continue按钮不可见",
                interaction_success=False
            )
            
            self.logger.error(f"❌ [阶段3] Continue按钮检测失败 ({duration_ms:.1f}ms)")
            return False
    
    async def _stage4_password_input(self, page: Page) -> bool:
        """阶段4: 密码页面 → 密码输入 (假密码填入)"""
        phase = "password_input"
        self.logger.info(f"🔄 [阶段4] 密码输入框检测和填充")
        
        start_time = time.time()
        
        # 使用内联密码字段选择器
        password_selectors = self.password_field_selectors
        
        # 检查密码输入框可见性
        success, found_selector, check_duration = await self._check_selector_visibility(
            page, phase, password_selectors
        )
        
        if success and found_selector:
            # 填入假密码
            interaction_success = await self._fill_password_field(page, found_selector, FAKE_CREDENTIALS['password'])
            
            # 截图 + 视觉比较
            screenshot_path = await self._take_screenshot(page, phase, True, found_selector)
            similarity_score, drift_status, baseline_path, baseline_version = await self._compare_with_baseline(screenshot_path, phase)
            
            duration_ms = (time.time() - start_time) * 1000
            
            self.result.add_result(
                phase=phase,
                selector=found_selector,
                success=True,
                duration_ms=duration_ms,
                screenshot_path=screenshot_path,
                interaction_success=interaction_success,
                fake_data_used=FAKE_CREDENTIALS['password'],
                similarity_score=similarity_score,
                drift_status=drift_status,
                baseline_path=baseline_path,
                baseline_version=baseline_version,
                comparison_method=self.image_capability
            )
            
            self.logger.info(f"✅ [阶段4] 密码输入框检测成功，已填入假密码 ({duration_ms:.1f}ms)")
            return True
        else:
            duration_ms = (time.time() - start_time) * 1000
            screenshot_path = await self._take_screenshot(page, phase, False)
            
            self.result.add_result(
                phase=phase,
                selector=f"failed_from_{len(password_selectors)}_selectors",
                success=False,
                duration_ms=duration_ms,
                screenshot_path=screenshot_path,
                error_message="密码输入框不可见",
                interaction_success=False
            )
            
            self.logger.error(f"❌ [阶段4] 密码输入框检测失败 ({duration_ms:.1f}ms)")
            return False
    
    async def _stage5_login_button_detection(self, page: Page) -> bool:
        """阶段5: Login按钮检测 → 安全退出 (仅检测存在性，不点击)"""
        phase = "login_button_detection"
        self.logger.info(f"🔄 [阶段5] 最终LOGIN按钮检测 (仅检测，不点击)")
        
        start_time = time.time()
        
        # 使用内联登录提交按钮选择器
        login_selectors = self.login_submit_selectors
        
        # 检查Login按钮可见性 (关键：仅检测，不点击)
        success, found_selector, check_duration = await self._check_selector_visibility(
            page, phase, login_selectors
        )
        
        if success and found_selector:
            self.logger.warning("🚨 关键安全退出: 检测到LOGIN按钮但不点击，避免真实登录")
            
            # 截图 + 视觉比较
            screenshot_path = await self._take_screenshot(page, phase, True, found_selector)
            similarity_score, drift_status, baseline_path, baseline_version = await self._compare_with_baseline(screenshot_path, phase)
            
            duration_ms = (time.time() - start_time) * 1000
            
            self.result.add_result(
                phase=phase,
                selector=found_selector,
                success=True,
                duration_ms=duration_ms,
                screenshot_path=screenshot_path,
                interaction_success=True,  # 成功检测就算交互成功
                fake_data_used=None,
                similarity_score=similarity_score,
                drift_status=drift_status,
                baseline_path=baseline_path,
                baseline_version=baseline_version,
                comparison_method=self.image_capability
            )
            
            self.logger.info(f"✅ [阶段5] LOGIN按钮检测成功，安全退出 ({duration_ms:.1f}ms)")
            self.logger.info("🛡️ 假邮箱真流程检测完成，已避免真实登录")
            return True
        else:
            duration_ms = (time.time() - start_time) * 1000
            screenshot_path = await self._take_screenshot(page, phase, False)
            
            self.result.add_result(
                phase=phase,
                selector=f"failed_from_{len(login_selectors)}_selectors",
                success=False,
                duration_ms=duration_ms,
                screenshot_path=screenshot_path,
                error_message="LOGIN按钮不可见",
                interaction_success=False
            )
            
            self.logger.error(f"❌ [阶段5] LOGIN按钮检测失败 ({duration_ms:.1f}ms)")
            return False
    
    # 辅助交互方法
    async def _fill_email_field(self, page: Page, selector: str, email: str) -> bool:
        """填充邮箱字段并验证输入成功"""
        try:
            element = await page.wait_for_selector(selector, timeout=5000)
            if element:
                # 清空并填入邮箱
                await element.clear()
                await element.fill(email)
                await element.blur()  # 失焦触发验证
                
                # 验证输入成功
                input_value = await element.input_value()
                if input_value == email:
                    self.logger.debug(f"📧 邮箱字段填充成功: {email}")
                    return True
                else:
                    self.logger.warning(f"⚠️ 邮箱字段填充验证失败: 期望={email}, 实际={input_value}")
                    return False
            else:
                self.logger.warning(f"⚠️ 邮箱字段元素未找到: {selector}")
                return False
        except Exception as e:
            self.logger.error(f"❌ 邮箱字段填充异常: {e}")
            return False
    
    async def _fill_password_field(self, page: Page, selector: str, password: str) -> bool:
        """填充密码字段并验证输入成功"""
        try:
            element = await page.wait_for_selector(selector, timeout=5000)
            if element:
                # 清空并填入密码
                await element.clear()
                await element.fill(password)
                await element.blur()  # 失焦触发验证
                
                # 验证输入成功 (检查长度而不是内容，保护密码)
                input_value = await element.input_value()
                if len(input_value) == len(password):
                    self.logger.debug(f"🔐 密码字段填充成功 (长度: {len(password)})")
                    return True
                else:
                    self.logger.warning(f"⚠️ 密码字段填充验证失败: 期望长度={len(password)}, 实际长度={len(input_value)}")
                    return False
            else:
                self.logger.warning(f"⚠️ 密码字段元素未找到: {selector}")
                return False
        except Exception as e:
            self.logger.error(f"❌ 密码字段填充异常: {e}")
            return False
    
    async def _click_continue_button(self, page: Page, selector: str) -> bool:
        """点击Continue按钮并验证"""
        try:
            element = await page.wait_for_selector(selector, timeout=5000)
            if element:
                # 记录点击前URL
                initial_url = page.url
                
                # 点击按钮
                await element.click()
                self.logger.debug(f"🖱️ Continue按钮已点击: {selector}")
                
                # 等待页面变化 (简单验证)
                await asyncio.sleep(2)
                current_url = page.url
                
                if current_url != initial_url:
                    self.logger.debug(f"✅ Continue点击成功，页面已跳转: {initial_url} -> {current_url}")
                    return True
                else:
                    self.logger.debug(f"⚠️ Continue点击后页面未跳转，可能需要更多时间")
                    return True  # 仍然认为成功，因为按钮点击了
            else:
                self.logger.warning(f"⚠️ Continue按钮元素未找到: {selector}")
                return False
        except Exception as e:
            self.logger.error(f"❌ Continue按钮点击异常: {e}")
            return False
    
    async def _wait_for_page_transition(self, page: Page, phase: str):
        """等待页面跳转和加载完成"""
        try:
            # 记录点击前的URL
            initial_url = page.url
            self.logger.debug(f"🌐 点击前URL: {initial_url}")
            
            # 等待页面跳转（URL变化）
            for i in range(15):  # 最多等待15秒
                await asyncio.sleep(1)
                current_url = page.url
                
                if current_url != initial_url:
                    self.logger.info(f"✅ 页面跳转成功: {initial_url} -> {current_url}")
                    break
                    
                if i == 14:  # 最后一次检查
                    self.logger.warning(f"⚠️ 页面未跳转，仍在: {current_url}")
            
            # 等待页面DOM加载完成
            try:
                await page.wait_for_load_state('domcontentloaded', timeout=10000)
                self.logger.debug(f"📄 页面DOM加载完成: {phase}")
            except Exception as load_error:
                self.logger.debug(f"⚠️ 页面加载等待失败: {load_error}")
            
            # 最终URL记录
            final_url = page.url
            self.logger.debug(f"🏁 最终页面URL: {final_url}")
            
            # 验证是否到达预期页面
            if "welcome" in phase and ("login" in final_url or "signin" in final_url):
                self.logger.info("✅ 成功从welcome页面跳转到登录页面")
                return True
            elif "login" in phase and "login" in final_url:
                self.logger.info("✅ 在登录页面，准备进行下一步")
                return True
            
            return True
            
        except Exception as e:
            self.logger.debug(f"⚠️ 页面跳转等待异常: {e}")
            return False

    async def _simulate_interaction(self, page: Page, phase: str, selector: str):
        """模拟用户交互以触发后续DOM变化"""
        try:
            if "email" in phase:
                # 邮箱字段：填入测试邮箱
                await self._simulate_form_filling(page, selector, "<EMAIL>")
                self.logger.debug(f"📧 已填充邮箱字段，准备进入下一阶段")
                
            elif "password" in phase:
                # 密码字段：填入测试密码
                await self._simulate_form_filling(page, selector, "TestPassword123!")
                self.logger.debug(f"🔐 已填充密码字段，准备进入下一阶段")
                
            elif "button" in selector.lower() or "continue" in phase or "submit" in phase or "welcome" in phase:
                # 按钮点击：特别处理welcome页面的弹窗登录按钮
                if "welcome" in phase:
                    # Welcome页面使用JavaScript点击绕过ExitIntentModal
                    js_success = await self._click_login_with_js(page)
                    
                    if not js_success:
                        # JavaScript点击失败，尝试常规点击
                        self.logger.warning("⚠️ JavaScript点击失败，使用降级方案...")
                        try:
                            element = await page.wait_for_selector(selector, timeout=3000)
                            if element:
                                await element.click()
                                self.logger.debug(f"🖱️ 降级点击: {selector}")
                        except Exception as fallback_error:
                            self.logger.warning(f"⚠️ 降级点击也失败: {fallback_error}")
                else:
                    # 其他页面使用常规点击
                    element = await page.wait_for_selector(selector, timeout=3000)
                    if element:
                        await element.click()
                        self.logger.debug(f"🖱️ 模拟点击: {selector}")
                
                # 点击后智能等待页面跳转和加载
                await self._wait_for_page_transition(page, phase)
                
            elif "continue" in phase:
                # Continue按钮需要特殊处理：先填充邮箱再点击
                # 先尝试找到并填充邮箱字段
                try:
                    email_selectors = ['input[type="email"]', 'input[name="email"]', '#email']
                    for email_selector in email_selectors:
                        try:
                            email_element = await page.wait_for_selector(email_selector, timeout=1000)
                            if email_element:
                                await self._simulate_form_filling(page, email_selector, "<EMAIL>")
                                self.logger.debug(f"📧 继续阶段：已填充邮箱 {email_selector}")
                                break
                        except:
                            continue
                except Exception as e:
                    self.logger.debug(f"⚠️ 继续阶段邮箱填充跳过: {e}")
                
                # 然后点击Continue按钮
                element = await page.wait_for_selector(selector, timeout=3000)
                if element:
                    await element.click()
                    self.logger.debug(f"🖱️ 继续按钮点击: {selector}")
                
                # 等待页面跳转到密码页面
                await self._wait_for_page_transition(page, phase)
            
        except Exception as e:
            self.logger.debug(f"🔧 交互模拟失败: {phase} - {e}")


def main():
    """主入口函数 - 假邮箱真流程检测 + 视觉模板比较系统"""
    import argparse
    
    parser = argparse.ArgumentParser(
        description='假邮箱真流程检测 + 视觉模板比较系统',
        epilog=f"""
示例用法:
  # 基础检测
  {sys.argv[0]}
  
  # 创建基线模板
  {sys.argv[0]} --create-baselines
  
  # 可视化模式
  {sys.argv[0]} --visible
  
  # 禁用视觉比较
  {sys.argv[0]} --skip-visual
  
  # 自定义假邮箱
  {sys.argv[0]} --fake-email <EMAIL>
        """,
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    
    # 基础参数
    parser.add_argument('--url', default='https://www.hulu.com', 
                       help='目标URL (默认: https://www.hulu.com)')
    parser.add_argument('--headless', action='store_true', default=True,
                       help='使用无头模式 (默认: True)')
    parser.add_argument('--visible', action='store_true',
                       help='使用可视模式 (覆盖 --headless)')
    parser.add_argument('--timeout', type=int, default=60,
                       help='总体超时时间（秒，默认60）')
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='详细输出模式')
    
    # 截图和视觉比较参数
    parser.add_argument('--no-screenshots', action='store_true',
                       help='禁用截图功能')
    parser.add_argument('--skip-visual', action='store_true',
                       help='跳过视觉模板比较')
    parser.add_argument('--visual-threshold', type=float, default=0.8,
                       help='视觉相似度阈值 (0.0-1.0, 默认: 0.8)')
    parser.add_argument('--create-baselines', action='store_true',
                       help='创建基线模板模式')
    parser.add_argument('--element-screenshots', action='store_true', default=True,
                       help='使用元素级截图 (默认: True)')
    
    # 假邮箱流程参数
    parser.add_argument('--fake-email', default=FAKE_CREDENTIALS['email'],
                       help=f'自定义假邮箱地址 (默认: {FAKE_CREDENTIALS["email"]})')
    parser.add_argument('--fake-password', default=FAKE_CREDENTIALS['password'],
                       help='自定义假密码')
    
    # 调试和测试参数
    parser.add_argument('--test-mode', action='store_true',
                       help='测试模式：使用本地HTML文件，不访问真实网站')
    parser.add_argument('--show-image-capability', action='store_true',
                       help='显示图像处理能力信息')
    
    args = parser.parse_args()
    
    # 显示图像处理能力
    if args.show_image_capability:
        print("🖼️ 图像处理库支持情况:")
        print(f"   scikit-image: {'✅' if IMAGE_LIBS.skimage else '❌'}")
        print(f"   opencv-python: {'✅' if IMAGE_LIBS.cv2 else '❌'}")
        print(f"   PIL: {'✅' if IMAGE_LIBS.pil else '❌'}")
        print(f"   numpy: {'✅' if IMAGE_LIBS.numpy else '❌'}")
        return
    
    # 设置日志级别
    if args.verbose:
        logging.getLogger('healthcheck').setLevel(logging.DEBUG)
    
    # 处理headless参数
    headless = args.headless and not args.visible
    
    # 设置测试URL
    test_url = args.url
    if args.test_mode:
        # 测试模式：使用简单的HTML页面
        test_url = ("data:text/html,<html><body>"
                   "<a href='/login'>Log In</a>"
                   "<input type='email' id='email' placeholder='Email'/>"
                   "<button type='submit'>Continue</button>"
                   "<input type='password' id='password' placeholder='Password'/>"
                   "<button type='submit'>Log In</button>"
                   "</body></html>")
        print("🧪 测试模式：使用内置HTML页面")
    
    # 更新假凭据
    if args.fake_email != FAKE_CREDENTIALS['email']:
        FAKE_CREDENTIALS['email'] = args.fake_email
    if args.fake_password != FAKE_CREDENTIALS['password']:
        FAKE_CREDENTIALS['password'] = args.fake_password
    
    # 创建健康检查器
    checker = SelectorHealthChecker(
        headless=headless,
        screenshot_on_failure=not args.no_screenshots,
        enable_visual_comparison=not args.skip_visual,
        visual_threshold=args.visual_threshold,
        create_baselines=args.create_baselines,
        element_screenshots=not args.create_baselines and args.element_screenshots  # 创建基线时使用全页面
    )
    checker.timeout = args.timeout
    
    # 基线创建模式提示
    if args.create_baselines:
        print("📸 基线模板创建模式")
        print("   将为5个关键阶段创建基线模板截图")
        print("   请确保网站处于正常状态进行基线创建")
    
    # 运行健康检查
    try:
        exit_code = asyncio.run(checker.run_healthcheck(test_url))
        
        # 输出结果摘要
        summary = checker.result.get_summary()
        print(f"\n📊 假邮箱真流程检测结果:")
        print(f"   总检查数: {summary['total_checks']}")
        print(f"   成功数: {summary['success_count']}")
        print(f"   失败数: {summary['failure_count']}")
        print(f"   成功率: {summary['success_rate']}%")
        print(f"   总耗时: {summary['total_duration_seconds']}秒")
        
        # 保存结果到JSON文件
        json_file = project_root / "healthcheck_status.json"
        checker.result.save_to_json(str(json_file))
        print(f"💾 详细结果已保存到: {json_file}")
        
        # 生成视觉漂移报告
        if not args.skip_visual and not args.create_baselines:
            _generate_visual_drift_report(checker.result)
        
        # 返回退出码
        if exit_code == 0:
            print("✅ 假邮箱真流程检测通过")
        else:
            print("❌ 假邮箱真流程检测失败")
        
        sys.exit(exit_code)
        
    except KeyboardInterrupt:
        print("\n🛑 用户中断健康检查")
        sys.exit(130)  # SIGINT退出码
        
    except Exception as e:
        print(f"\n💥 健康检查异常终止: {e}")
        sys.exit(1)

def _generate_visual_drift_report(result: HealthCheckResult):
    """生成视觉漂移检测报告"""
    try:
        print("\n🔍 视觉模板漂移报告:")
        
        drift_found = False
        for r in result.results:
            if r.get('visual_analysis'):
                visual = r['visual_analysis']
                phase = r['phase']
                similarity = visual.get('similarity_score')
                status = visual.get('drift_status')
                
                if similarity is not None:
                    status_emoji = {
                        'normal': '✅',
                        'warning': '⚠️', 
                        'critical': '🚨'
                    }.get(status, '❓')
                    
                    print(f"   {status_emoji} {phase}: 相似度={similarity:.3f} ({status})")
                    
                    if status in ['warning', 'critical']:
                        drift_found = True
                        if status == 'critical':
                            print(f"      💡 建议: 运行 --create-baselines 更新基线")
        
        if not drift_found:
            print("   🎉 所有模板均正常，无显著漂移")
    
    except Exception as e:
        print(f"⚠️ 视觉漂移报告生成失败: {e}")


if __name__ == "__main__":
    main()