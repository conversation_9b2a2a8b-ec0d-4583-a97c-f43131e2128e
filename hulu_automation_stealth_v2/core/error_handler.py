"""
统一错误处理系统

提供分层异常架构、可取消重试机制和结构化错误上下文。
支持jitter、指数退避和智能取消响应。
"""

import asyncio
import random
import functools
import logging
from typing import Callable, Any, Dict, Optional, Type, Union
from datetime import datetime

logger = logging.getLogger(__name__)


class HuluAutomationError(Exception):
    """基础异常类"""
    
    def __init__(self, message: str, error_code: str = None, context: Dict[str, Any] = None):
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.context = context or {}
        self.timestamp = datetime.now().isoformat()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式，便于日志记录"""
        return {
            "error_type": self.__class__.__name__,
            "message": self.message,
            "error_code": self.error_code,
            "context": self.context,
            "timestamp": self.timestamp
        }


class LoginFailedError(HuluAutomationError):
    """登录失败异常"""
    pass


class VerificationError(HuluAutomationError):
    """验证码相关异常"""
    pass


class VerificationFailedError(HuluAutomationError):
    """验证失败错误"""
    pass


class ElementNotFoundError(HuluAutomationError):
    """元素未找到错误"""
    pass


class ElementInteractionError(HuluAutomationError):
    """元素交互错误"""
    pass


class FormInteractionError(HuluAutomationError):
    """表单交互错误"""
    pass


class NavigationError(HuluAutomationError):
    """导航错误"""
    pass


class StateManagementError(HuluAutomationError):
    """状态管理错误"""
    pass


class SelectorError(HuluAutomationError):
    """选择器错误"""
    pass


class ResourceError(HuluAutomationError):
    """资源管理异常"""
    pass


class NetworkError(HuluAutomationError):
    """网络相关异常"""
    pass


class TimeoutError(HuluAutomationError):
    """超时异常"""
    pass


def retry_on_failure(
    max_attempts: int = 3,
    base_delay: float = 1.0,
    max_delay: float = 60.0,
    exponential_base: float = 2.0,
    jitter: bool = True,
    jitter_range: float = 0.25,
    cancellable: bool = True,
    retryable_exceptions: tuple = (HuluAutomationError,),
    stop_on_exceptions: tuple = (asyncio.CancelledError, KeyboardInterrupt)
):
    """
    增强重试装饰器
    
    Args:
        max_attempts: 最大重试次数
        base_delay: 基础延迟时间(秒)
        max_delay: 最大延迟时间(秒)
        exponential_base: 指数退避基数
        jitter: 是否启用抖动
        jitter_range: 抖动范围 (±百分比)
        cancellable: 是否支持取消
        retryable_exceptions: 可重试的异常类型
        stop_on_exceptions: 立即停止的异常类型
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)  # 保留原函数元信息
        async def wrapper(*args, **kwargs) -> Any:
            last_exception = None
            
            for attempt in range(max_attempts):
                # 检查取消状态
                if cancellable:
                    try:
                        current_task = asyncio.current_task()
                        if current_task and current_task.cancelled():
                            logger.info(f"{func.__name__} 重试被取消", 
                                      attempt=attempt+1)
                            raise asyncio.CancelledError()
                    except RuntimeError:
                        pass  # 没有事件循环时忽略
                
                try:
                    result = await func(*args, **kwargs)
                    
                    # 重试成功日志
                    if attempt > 0:
                        logger.info(f"{func.__name__} 重试成功", 
                                  extra={
                                      "attempt": attempt+1, 
                                      "total_attempts": max_attempts,
                                      "success_after_retries": True
                                  })
                    
                    return result
                    
                except stop_on_exceptions as e:
                    # 立即停止的异常，不重试
                    logger.warning(f"{func.__name__} 遇到停止异常: {type(e).__name__}")
                    raise
                    
                except Exception as e:
                    last_exception = e
                    
                    # 检查是否为可重试异常
                    is_retryable = isinstance(e, retryable_exceptions)
                    
                    # 最后一次尝试或不可重试，直接抛出
                    if attempt == max_attempts - 1 or not is_retryable:
                        error_context = {
                            "function": func.__name__,
                            "attempts": attempt + 1,
                            "max_attempts": max_attempts,
                            "is_retryable": is_retryable,
                            "error_type": type(e).__name__
                        }
                        
                        if is_retryable:
                            logger.error(f"{func.__name__} 重试失败", extra=error_context)
                        else:
                            logger.error(f"{func.__name__} 不可重试异常", extra=error_context)
                        
                        raise
                    
                    # 计算延迟时间 (指数退避 + jitter)
                    delay = min(base_delay * (exponential_base ** attempt), max_delay)
                    
                    if jitter:
                        jitter_factor = 1.0 + random.uniform(-jitter_range, jitter_range)
                        delay *= jitter_factor
                    
                    logger.warning(f"{func.__name__} 重试中",
                                 extra={
                                     "attempt": attempt+1,
                                     "max_attempts": max_attempts,
                                     "delay_seconds": round(delay, 2),
                                     "error": str(e),
                                     "error_type": type(e).__name__
                                 })
                    
                    # 可取消的延迟
                    if cancellable:
                        try:
                            await asyncio.sleep(delay)
                        except asyncio.CancelledError:
                            logger.info(f"{func.__name__} 延迟期间被取消")
                            raise
                    else:
                        await asyncio.sleep(delay)
            
            # 理论上不会到这里，但为了类型安全
            if last_exception:
                raise last_exception
            
        return wrapper
    return decorator


def handle_errors_gracefully(
    default_return: Any = None,
    log_errors: bool = True,
    suppress_exceptions: tuple = (Exception,)
):
    """
    优雅错误处理装饰器 - 用于非关键操作
    
    Args:
        default_return: 出错时的默认返回值
        log_errors: 是否记录错误日志
        suppress_exceptions: 要抑制的异常类型
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs) -> Any:
            try:
                return await func(*args, **kwargs)
            except suppress_exceptions as e:
                if log_errors:
                    logger.warning(f"{func.__name__} 优雅处理异常: {str(e)}", 
                                 error_type=type(e).__name__,
                                 function=func.__name__)
                return default_return
                
        return wrapper
    return decorator


class ErrorContext:
    """错误上下文管理器 - 收集和传递错误信息"""
    
    def __init__(self):
        self.context = {}
        self.operation_stack = []
    
    def add_context(self, key: str, value: Any):
        """添加上下文信息"""
        self.context[key] = value
    
    def push_operation(self, operation: str):
        """推入操作栈"""
        self.operation_stack.append(operation)
    
    def pop_operation(self):
        """弹出操作栈"""
        if self.operation_stack:
            return self.operation_stack.pop()
        return None
    
    def get_full_context(self) -> Dict[str, Any]:
        """获取完整上下文"""
        return {
            "context": self.context.copy(),
            "operation_stack": self.operation_stack.copy(),
            "timestamp": datetime.now().isoformat()
        }
    
    def clear(self):
        """清空上下文"""
        self.context.clear()
        self.operation_stack.clear()


# 全局错误上下文实例
error_context = ErrorContext()


def with_error_context(**context_data):
    """添加错误上下文的装饰器"""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs) -> Any:
            # 添加上下文
            for key, value in context_data.items():
                error_context.add_context(key, value)
            
            # 推入操作
            error_context.push_operation(func.__name__)
            
            try:
                return await func(*args, **kwargs)
            except Exception as e:
                # 增强异常信息
                if isinstance(e, HuluAutomationError):
                    e.context.update(error_context.get_full_context())
                raise
            finally:
                # 清理
                error_context.pop_operation()
                
        return wrapper
    return decorator