"""
自动化引擎 - 核心协调器

统一协调所有组件，提供高级API接口。
负责组件间的依赖注入、生命周期管理和错误协调。
"""

import os
import asyncio
import logging
from typing import Dict, Any, Optional
from datetime import datetime
from playwright.async_api import async_playwright, Playwright, Browser, BrowserContext, Page

from ..utils.protocols import (
    CDPManagerProtocol, StealthServiceProtocol, LoginServiceProtocol,
    VerificationServiceProtocol, StateManagerProtocol, ResourceManagerProtocol
)
from .resource_manager import ResourceManager
from .cdp_manager import CDPChromeManager
from .stealth_service import StealthAntiDetectionService
from .error_handler import (
    <PERSON><PERSON><PERSON><PERSON>mationError, LoginFailedError, ResourceError,
    retry_on_failure, with_error_context
)

logger = logging.getLogger(__name__)


class AutomationEngine:
    """
    自动化引擎 - 企业级协调器
    
    特性:
    - 依赖注入架构
    - 统一资源生命周期管理
    - 组件间协调和通信
    - 增强错误处理和恢复
    - 完整的执行日志和监控
    """
    
    def __init__(self, 
                 debug_port: int = 9222,
                 auto_launch: bool = True,
                 persistent_session: bool = True,
                 account_index: int = 0,
                 account_email: str = None,
                 # 依赖注入接口
                 cdp_manager: Optional[CDPManagerProtocol] = None,
                 stealth_service: Optional[StealthServiceProtocol] = None,
                 login_service: Optional[LoginServiceProtocol] = None,
                 verification_service: Optional[VerificationServiceProtocol] = None,
                 state_manager: Optional[StateManagerProtocol] = None,
                 resource_manager: Optional[ResourceManagerProtocol] = None):
        """
        初始化自动化引擎
        
        Args:
            debug_port: CDP调试端口
            auto_launch: 是否自动启动Chrome
            persistent_session: 是否启用登录持久化
            account_index: 使用的账户索引（从0开始）
            account_email: 指定使用的账户邮箱（优先级高于account_index）
            cdp_manager: CDP管理器 (可注入)
            stealth_service: 反检测服务 (可注入)
            login_service: 登录服务 (可注入)
            verification_service: 验证服务 (可注入)
            state_manager: 状态管理器 (可注入)
            resource_manager: 资源管理器 (可注入)
        """
        # 基础配置
        self.debug_port = debug_port
        self.auto_launch = auto_launch
        self.persistent_session = persistent_session
        self.account_index = account_index
        self.account_email = account_email
        
        # 依赖注入 - 优先使用注入的实例，否则创建默认实例
        self.resource_manager = resource_manager or ResourceManager()
        
        self.cdp_manager = cdp_manager or CDPChromeManager(
            debug_port=debug_port,
            persistent_session=persistent_session,
            resource_manager=self.resource_manager
        )
        
        # Playwright相关资源
        self.playwright: Optional[Playwright] = None
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None
        self.page: Optional[Page] = None
        
        # 延迟初始化的服务 (需要playwright实例)
        self.stealth_service: Optional[StealthServiceProtocol] = stealth_service
        self.login_service: Optional[LoginServiceProtocol] = login_service
        self.verification_service: Optional[VerificationServiceProtocol] = verification_service
        self.state_manager: Optional[StateManagerProtocol] = state_manager
        
        # 执行状态
        self._initialized = False
        self._execution_stats = {
            "total_executions": 0,
            "successful_executions": 0,
            "failed_executions": 0,
            "last_execution": None,
            "average_execution_time": 0.0
        }
        
        # 创建截图目录
        self.screenshots_dir = "screenshots"
        os.makedirs(self.screenshots_dir, exist_ok=True)
    
    @with_error_context(operation="engine_initialization")
    async def _initialize_if_needed(self) -> None:
        """延迟初始化 - 确保所有组件就绪"""
        if self._initialized:
            return
        
        try:
            logger.info("🔧 初始化自动化引擎...")
            
            # 初始化Playwright
            if not self.playwright:
                self.playwright = await async_playwright().start()
                self.resource_manager.register_browser_resources(playwright=self.playwright)
                logger.debug("✅ Playwright已初始化")
            
            # 初始化延迟加载的服务
            if not self.stealth_service:
                self.stealth_service = StealthAntiDetectionService(
                    self.playwright, 
                    self.resource_manager
                )
                logger.debug("✅ 反检测服务已初始化")
            
            # TODO: 初始化其他服务 (将在后续步骤中实现)
            # if not self.login_service:
            #     self.login_service = LoginService(...)
            # if not self.verification_service:
            #     self.verification_service = VerificationService(...)
            # if not self.state_manager:
            #     self.state_manager = StateManager(...)
            
            self._initialized = True
            logger.info("✅ 自动化引擎初始化完成")
            
        except Exception as e:
            error_msg = f"自动化引擎初始化失败: {str(e)}"
            logger.error(error_msg)
            raise ResourceError(error_msg, error_code="ENGINE_INIT_FAILED")
    
    @retry_on_failure(max_attempts=3, base_delay=2.0)
    @with_error_context(operation="setup_browser_environment")
    async def _setup_browser_environment(self) -> None:
        """设置浏览器环境"""
        try:
            logger.info("🌐 设置浏览器环境...")
            
            # 启动Chrome (如果需要)
            if self.auto_launch and not self.cdp_manager.is_chrome_running():
                chrome_process = self.cdp_manager.auto_launch_chrome()
                logger.info(f"✅ Chrome已启动 (PID: {chrome_process.pid})")
                
                # 等待Chrome启动完成
                await asyncio.sleep(3.0)
            
            # 连接到浏览器
            self.browser = await self.cdp_manager.connect_to_browser(self.playwright)
            logger.info("✅ 浏览器连接成功")
            
            # 创建隐身上下文
            self.context = await self.cdp_manager.create_stealth_context(self.browser)
            logger.info("✅ 隐身上下文创建成功")
            
            # 创建反检测页面
            self.page = await self.stealth_service.create_stealth_page(self.context)
            logger.info("✅ 反检测页面创建成功")
            
            logger.info("🎯 浏览器环境设置完成")
            
        except Exception as e:
            error_msg = f"浏览器环境设置失败: {str(e)}"
            logger.error(error_msg)
            raise ResourceError(error_msg, error_code="BROWSER_SETUP_FAILED")
    
    @with_error_context(operation="execute_stealth_login")
    async def execute_stealth_login(self) -> Dict[str, Any]:
        """
        执行隐身登录流程 - 主要入口点
        
        Returns:
            Dict[str, Any]: 登录结果
        """
        execution_start = datetime.now()
        self._execution_stats["total_executions"] += 1
        
        logger.info("🚀 开始执行隐身登录流程...")
        
        try:
            # 使用资源管理器确保清理
            async with self.resource_manager as rm:
                # 初始化引擎
                await self._initialize_if_needed()
                
                # 设置浏览器环境
                await self._setup_browser_environment()
                
                # 执行登录流程 (临时简化实现)
                result = await self._execute_login_flow()
                
                # 记录成功
                self._execution_stats["successful_executions"] += 1
                execution_time = (datetime.now() - execution_start).total_seconds()
                self._update_execution_stats(execution_time, True)
                
                logger.info(f"✅ 登录流程执行完成 (耗时: {execution_time:.2f}s)")
                
                return {
                    "success": True,
                    "data": result,
                    "message": "登录执行完成",
                    "execution_time": execution_time,
                    "engine_version": "2.0.0",
                    "timestamp": execution_start.isoformat()
                }
                
        except Exception as e:
            # 记录失败
            self._execution_stats["failed_executions"] += 1
            execution_time = (datetime.now() - execution_start).total_seconds()
            self._update_execution_stats(execution_time, False)
            
            error_msg = f"登录流程执行失败: {str(e)}"
            logger.error(error_msg)
            
            return {
                "success": False,
                "data": {},
                "message": error_msg,
                "execution_time": execution_time,
                "error_type": type(e).__name__,
                "engine_version": "2.0.0",
                "timestamp": execution_start.isoformat()
            }
    
    async def _execute_login_flow(self) -> Dict[str, Any]:
        """
        执行登录流程的具体实现
        
        TODO: 这是一个临时的简化实现，将在后续步骤中完善
        
        Returns:
            Dict[str, Any]: 登录流程结果
        """
        logger.info("🔐 执行登录流程...")
        
        try:
            # 导航到登录页面
            await self.page.goto("https://hulu.com/login", wait_until="networkidle")
            logger.info("✅ 已导航到登录页面")
            
            # 等待页面稳定
            await asyncio.sleep(2.0)
            
            # 检查页面状态
            title = await self.page.title()
            logger.info(f"📄 页面标题: {title}")
            
            # 截图记录
            screenshot_path = os.path.join(self.screenshots_dir, 
                                         f"login_page_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png")
            await self.page.screenshot(path=screenshot_path)
            logger.info(f"📸 已保存截图: {screenshot_path}")
            
            # TODO: 实现实际的登录逻辑
            # - 检查现有登录状态
            # - 填写用户名密码
            # - 处理验证码
            # - 等待登录完成
            
            return {
                "page_title": title,
                "screenshot": screenshot_path,
                "status": "demo_completed"
            }
            
        except Exception as e:
            error_msg = f"登录流程执行出错: {str(e)}"
            logger.error(error_msg)
            raise LoginFailedError(error_msg, error_code="LOGIN_FLOW_FAILED")
    
    def _update_execution_stats(self, execution_time: float, success: bool) -> None:
        """更新执行统计"""
        self._execution_stats["last_execution"] = datetime.now().isoformat()
        
        # 更新平均执行时间
        total = self._execution_stats["total_executions"]
        current_avg = self._execution_stats["average_execution_time"]
        new_avg = ((current_avg * (total - 1)) + execution_time) / total
        self._execution_stats["average_execution_time"] = new_avg
    
    def get_execution_stats(self) -> Dict[str, Any]:
        """获取执行统计信息"""
        stats = self._execution_stats.copy()
        stats.update({
            "success_rate": (
                stats["successful_executions"] / max(stats["total_executions"], 1)
            ),
            "failure_rate": (
                stats["failed_executions"] / max(stats["total_executions"], 1)
            ),
            "initialized": self._initialized,
            "has_active_session": self.page is not None
        })
        return stats
    
    def get_component_status(self) -> Dict[str, Any]:
        """获取组件状态信息"""
        return {
            "engine": {
                "initialized": self._initialized,
                "version": "2.0.0"
            },
            "cdp_manager": {
                "chrome_running": self.cdp_manager.is_chrome_running(),
                "debug_port": self.debug_port
            },
            "stealth_service": (
                self.stealth_service.get_stealth_info() 
                if hasattr(self.stealth_service, 'get_stealth_info') else {"status": "available"}
            ),
            "resource_manager": (
                self.resource_manager.get_resource_stats()
                if hasattr(self.resource_manager, 'get_resource_stats') else {"status": "available"}
            ),
            "browser_resources": {
                "playwright": self.playwright is not None,
                "browser": self.browser is not None,
                "context": self.context is not None,
                "page": self.page is not None
            }
        }
    
    async def health_check(self) -> Dict[str, Any]:
        """系统健康检查"""
        try:
            health_status = "healthy"
            issues = []
            
            # 检查核心组件
            if not self._initialized:
                issues.append("Engine not initialized")
                health_status = "degraded"
            
            # 检查资源管理器
            if hasattr(self.resource_manager, 'is_healthy'):
                if not self.resource_manager.is_healthy():
                    issues.append("Resource manager unhealthy")
                    health_status = "degraded"
            
            # 检查Chrome进程
            if self.auto_launch and not self.cdp_manager.is_chrome_running():
                issues.append("Chrome process not running")
                health_status = "degraded"
            
            # 检查浏览器连接
            if self._initialized and not self.browser:
                issues.append("Browser not connected")
                health_status = "unhealthy"
            
            return {
                "status": health_status,
                "timestamp": datetime.now().isoformat(),
                "issues": issues,
                "component_status": self.get_component_status(),
                "execution_stats": self.get_execution_stats()
            }
            
        except Exception as e:
            return {
                "status": "unhealthy",
                "timestamp": datetime.now().isoformat(),
                "error": str(e),
                "issues": [f"Health check failed: {str(e)}"]
            }