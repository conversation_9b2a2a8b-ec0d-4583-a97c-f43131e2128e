"""
CDP Chrome管理器 - 重构版本

负责Chrome浏览器的启动、连接和生命周期管理。
集成资源管理器和增强错误处理。
"""

import os
import platform
import subprocess
import tempfile
import logging
from typing import Optional, List
from playwright.async_api import Play<PERSON>, <PERSON><PERSON>er, BrowserContext

from ..utils.protocols import CDPManagerProtocol
from .error_handler import ResourceError, retry_on_failure, with_error_context
from .resource_manager import ResourceManager

logger = logging.getLogger(__name__)


class CDPChromeManager(CDPManagerProtocol):
    """
    CDP Chrome管理器 - 企业级重构版本
    
    特性:
    - 跨平台Chrome路径检测
    - 持久化会话支持
    - 资源生命周期集成
    - 增强错误处理和重试
    - 优雅进程终止
    """
    
    def __init__(self, debug_port: int = 9222, persistent_session: bool = True, 
                 resource_manager: Optional[ResourceManager] = None):
        """
        初始化CDP管理器
        
        Args:
            debug_port: CDP调试端口
            persistent_session: 是否启用登录持久化
            resource_manager: 资源管理器实例 (可选)
        """
        self.debug_port = debug_port
        self.persistent_session = persistent_session
        self.resource_manager = resource_manager
        self.chrome_process: Optional[subprocess.Popen] = None
        
        # 持久化用户数据目录配置
        if persistent_session:
            self.user_data_dir = os.path.join(os.getcwd(), "hulu_session_data")
            os.makedirs(self.user_data_dir, exist_ok=True)
            logger.info(f"📁 持久化用户数据目录: {self.user_data_dir}")
        else:
            self.user_data_dir = None
            
        # Chrome路径缓存
        self._chrome_path_cache = None
    
    def _detect_chrome_path(self) -> str:
        """检测系统中的Chrome路径"""
        if self._chrome_path_cache:
            return self._chrome_path_cache
            
        system = platform.system()
        
        # 平台特定的Chrome路径
        chrome_paths = {
            "Darwin": [  # macOS
                "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome",
                "/Applications/Chromium.app/Contents/MacOS/Chromium"
            ],
            "Windows": [  # Windows
                "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe",
                "C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe",
                "C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\Application\\chrome.exe"
            ],
            "Linux": [  # Linux
                "/usr/bin/google-chrome",
                "/usr/bin/google-chrome-stable", 
                "/usr/bin/chromium-browser",
                "/usr/bin/chromium"
            ]
        }
        
        if system not in chrome_paths:
            raise ResourceError(f"不支持的操作系统: {system}", 
                              error_code="UNSUPPORTED_OS",
                              context={"system": system})
        
        # 尝试找到可用的Chrome路径
        for path in chrome_paths[system]:
            if system == "Windows":
                path = os.path.expandvars(path)  # 展开环境变量
                
            if os.path.exists(path):
                self._chrome_path_cache = path
                logger.debug(f"检测到Chrome路径: {path}")
                return path
        
        raise ResourceError(f"未找到Chrome浏览器", 
                          error_code="CHROME_NOT_FOUND",
                          context={"system": system, "searched_paths": chrome_paths[system]})
    
    def get_chrome_command(self, user_data_dir: Optional[str] = None) -> List[str]:
        """
        获取Chrome启动命令
        
        Args:
            user_data_dir: 用户数据目录路径
            
        Returns:
            List[str]: Chrome启动命令参数列表
        """
        chrome_path = self._detect_chrome_path()
        
        # 基础启动参数 (优化反检测)
        args = [
            chrome_path,
            f"--remote-debugging-port={self.debug_port}",
            "--remote-debugging-address=127.0.0.1",
            
            # 基础设置
            "--no-first-run",
            "--no-default-browser-check",
            "--disable-sync",
            "--disable-extensions",
            "--disable-plugins",
            
            # 性能优化
            "--disable-background-timer-throttling",
            "--disable-backgrounding-occluded-windows", 
            "--disable-renderer-backgrounding",
            "--disable-hang-monitor",
            
            # 反检测增强
            "--disable-features=TranslateUI,VizDisplayCompositor",
            "--disable-ipc-flooding-protection",
            "--disable-prompt-on-repost",
            "--disable-web-security",
            "--disable-dev-shm-usage",
            "--no-sandbox"
        ]
        
        # 添加用户数据目录
        if user_data_dir:
            args.append(f"--user-data-dir={user_data_dir}")
            
        return args
    
    @retry_on_failure(max_attempts=3, base_delay=2.0)
    @with_error_context(operation="launch_chrome")
    def auto_launch_chrome(self, user_data_dir: Optional[str] = None) -> subprocess.Popen:
        """
        自动启动Chrome浏览器
        
        Args:
            user_data_dir: 用户数据目录路径
            
        Returns:
            subprocess.Popen: Chrome进程对象
        """
        try:
            # 确定用户数据目录
            if not user_data_dir:
                if self.persistent_session and self.user_data_dir:
                    user_data_dir = self.user_data_dir
                else:
                    user_data_dir = tempfile.mkdtemp(prefix="chrome-debug-")
                    
            logger.info(f"📁 使用数据目录: {user_data_dir}")
            
            # 构建启动命令
            cmd = self.get_chrome_command(user_data_dir)
            
            # 记录启动信息
            chrome_path = cmd[0]
            logger.info(f"🚀 启动Chrome: {os.path.basename(chrome_path)} "
                       f"--remote-debugging-port={self.debug_port}")
            
            # 启动Chrome进程
            self.chrome_process = subprocess.Popen(
                cmd,
                stdout=subprocess.DEVNULL,
                stderr=subprocess.DEVNULL,
                start_new_session=True  # 创建新的进程组
            )
            
            # 注册到资源管理器
            if self.resource_manager:
                self.resource_manager.register_process(self.chrome_process, is_cdp=True)
            
            logger.info(f"✅ Chrome启动成功, PID: {self.chrome_process.pid}")
            return self.chrome_process
            
        except Exception as e:
            error_msg = f"Chrome启动失败: {str(e)}"
            logger.error(error_msg)
            raise ResourceError(error_msg, 
                              error_code="CHROME_LAUNCH_FAILED",
                              context={"debug_port": self.debug_port, "user_data_dir": user_data_dir})
    
    @retry_on_failure(max_attempts=5, base_delay=1.0, exponential_base=1.5)
    @with_error_context(operation="connect_browser")
    async def connect_to_browser(self, playwright: Playwright, cdp_url: str = None) -> Browser:
        """
        连接到CDP浏览器
        
        Args:
            playwright: Playwright实例
            cdp_url: CDP连接URL
            
        Returns:
            Browser: 浏览器实例
        """
        if not cdp_url:
            # 使用IPv4地址避免IPv6问题
            cdp_url = f"http://127.0.0.1:{self.debug_port}"
        
        logger.info(f"🔗 连接CDP端点: {cdp_url}")
        
        try:
            browser = await playwright.chromium.connect_over_cdp(cdp_url)
            
            # 注册到资源管理器
            if self.resource_manager:
                self.resource_manager.register_browser_resources(browser=browser)
            
            logger.info("✅ CDP连接成功")
            return browser
            
        except Exception as e:
            error_msg = f"CDP连接失败: {str(e)}"
            logger.error(error_msg)
            raise ResourceError(error_msg,
                              error_code="CDP_CONNECTION_FAILED", 
                              context={"cdp_url": cdp_url, "debug_port": self.debug_port})
    
    async def create_stealth_context(self, browser: Browser) -> BrowserContext:
        """
        创建隐身浏览器上下文
        
        Args:
            browser: 浏览器实例
            
        Returns:
            BrowserContext: 浏览器上下文
        """
        try:
            # 使用反检测配置创建上下文
            context = await browser.new_context(
                viewport={"width": 1920, "height": 1080},
                user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                java_script_enabled=True,
                accept_downloads=False,
                ignore_https_errors=True
            )
            
            # 注册到资源管理器
            if self.resource_manager:
                self.resource_manager.register_browser_resources(context=context)
            
            logger.debug("✅ 隐身上下文创建成功")
            return context
            
        except Exception as e:
            error_msg = f"创建浏览器上下文失败: {str(e)}"
            logger.error(error_msg)
            raise ResourceError(error_msg, 
                              error_code="CONTEXT_CREATION_FAILED")
    
    async def cleanup(self) -> None:
        """
        清理资源 (由资源管理器调用)
        
        Note: 此方法通常由ResourceManager调用，不需要手动调用
        """
        if self.chrome_process:
            try:
                if self.chrome_process.poll() is None:  # 进程还在运行
                    logger.debug(f"终止Chrome进程 PID: {self.chrome_process.pid}")
                    self.chrome_process.terminate()
                    
                    try:
                        self.chrome_process.wait(timeout=5.0)
                        logger.debug("Chrome进程优雅退出")
                    except subprocess.TimeoutExpired:
                        logger.warning("Chrome进程未在5秒内退出，强制终止")
                        self.chrome_process.kill()
                        self.chrome_process.wait()
                        
            except Exception as e:
                logger.warning(f"清理Chrome进程时出错: {e}")
            finally:
                self.chrome_process = None
    
    def is_chrome_running(self) -> bool:
        """检查Chrome进程是否正在运行"""
        if not self.chrome_process:
            return False
        return self.chrome_process.poll() is None
    
    def get_debug_info(self) -> dict:
        """获取调试信息"""
        return {
            "debug_port": self.debug_port,
            "persistent_session": self.persistent_session,
            "user_data_dir": self.user_data_dir,
            "chrome_running": self.is_chrome_running(),
            "chrome_pid": self.chrome_process.pid if self.chrome_process else None,
            "chrome_path": self._chrome_path_cache
        }