"""
统一资源生命周期管理器

提供异常隔离的资源清理、可取消的任务管理和优雅的进程终止。
确保在任何情况下都能正确清理资源，防止泄漏。
"""

import asyncio
import signal
import subprocess
import psutil
import logging
from typing import List, Optional, Dict, Any
from playwright.async_api import Browser, BrowserContext, Page
from datetime import datetime

from .error_handler import ResourceError, handle_errors_gracefully

logger = logging.getLogger(__name__)


class ResourceManager:
    """
    统一资源生命周期管理器
    
    特性:
    - 异步上下文管理器
    - 并行资源清理 + 异常隔离
    - 优雅进程终止 (terminate → wait → kill)
    - 任务取消和超时控制
    - 资源使用统计和监控
    """
    
    def __init__(self, cleanup_timeout: float = 10.0, process_kill_timeout: float = 5.0):
        """
        初始化资源管理器
        
        Args:
            cleanup_timeout: 清理操作超时时间(秒)
            process_kill_timeout: 进程终止等待时间(秒)
        """
        # 浏览器资源
        self.playwright = None
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None
        self.page: Optional[Page] = None
        
        # 后台任务
        self.background_tasks: List[asyncio.Task] = []
        
        # 进程管理
        self.cdp_process: Optional[subprocess.Popen] = None
        self.managed_processes: List[subprocess.Popen] = []
        
        # 配置
        self.cleanup_timeout = cleanup_timeout
        self.process_kill_timeout = process_kill_timeout
        
        # 统计信息
        self.stats = {
            "created_at": datetime.now().isoformat(),
            "resources_registered": 0,
            "tasks_registered": 0,
            "cleanup_attempts": 0,
            "cleanup_errors": 0
        }
        
        # 清理标记
        self._cleanup_in_progress = False
        self._cleanup_completed = False
    
    async def __aenter__(self):
        """进入资源管理上下文"""
        logger.debug("ResourceManager 上下文已启动")
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """退出资源管理上下文 - 保证清理执行"""
        await self.cleanup_all_resources()
        
        if exc_type:
            logger.warning(f"ResourceManager 上下文异常退出: {exc_type.__name__}")
        else:
            logger.debug("ResourceManager 上下文正常退出")
    
    def register_browser_resources(self, playwright=None, browser: Browser = None, 
                                 context: BrowserContext = None, page: Page = None):
        """注册浏览器资源"""
        if playwright:
            self.playwright = playwright
        if browser:
            self.browser = browser
            self.stats["resources_registered"] += 1
        if context:
            self.context = context
            self.stats["resources_registered"] += 1
        if page:
            self.page = page
            self.stats["resources_registered"] += 1
            
        logger.debug(f"浏览器资源已注册: browser={browser is not None}, "
                    f"context={context is not None}, page={page is not None}")
    
    def register_process(self, process: subprocess.Popen, is_cdp: bool = False):
        """注册需要管理的进程"""
        if is_cdp:
            self.cdp_process = process
        else:
            self.managed_processes.append(process)
        
        self.stats["resources_registered"] += 1
        logger.debug(f"进程已注册: PID={process.pid}, is_cdp={is_cdp}")
    
    async def register_task(self, task: asyncio.Task, description: str = ""):
        """注册需要管理的后台任务"""
        self.background_tasks.append(task)
        self.stats["tasks_registered"] += 1
        
        logger.debug(f"任务已注册: {description}, 总任务数: {len(self.background_tasks)}")
    
    async def cleanup_all_resources(self):
        """
        并行清理所有资源 + 异常隔离
        
        清理顺序:
        1. 后台任务 (优雅取消)
        2. 浏览器资源 (页面 → 上下文 → 浏览器)
        3. 进程资源 (terminate → wait → kill)
        """
        if self._cleanup_in_progress:
            logger.warning("资源清理已在进行中，跳过重复调用")
            return
        
        if self._cleanup_completed:
            logger.debug("资源清理已完成，跳过重复调用")
            return
        
        self._cleanup_in_progress = True
        self.stats["cleanup_attempts"] += 1
        
        logger.info("开始清理所有资源")
        start_time = datetime.now()
        
        # 并行执行清理任务，互不影响
        cleanup_tasks = [
            self._cleanup_background_tasks(),
            self._cleanup_browser_resources(),
            self._cleanup_process_resources()
        ]
        
        # 使用 gather 确保所有清理任务都执行，即使某些失败
        results = await asyncio.gather(*cleanup_tasks, return_exceptions=True)
        
        # 记录清理结果
        success_count = 0
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                self.stats["cleanup_errors"] += 1
                logger.warning(f"清理任务 {i} 异常: {type(result).__name__}: {result}")
            else:
                success_count += 1
        
        cleanup_time = (datetime.now() - start_time).total_seconds()
        logger.info(f"资源清理完成: 成功 {success_count}/{len(cleanup_tasks)}, "
                   f"耗时 {cleanup_time:.2f}s")
        
        self._cleanup_completed = True
        self._cleanup_in_progress = False
    
    @handle_errors_gracefully(log_errors=True)
    async def _cleanup_background_tasks(self):
        """清理后台任务"""
        if not self.background_tasks:
            logger.debug("无后台任务需要清理")
            return
        
        logger.debug(f"开始清理 {len(self.background_tasks)} 个后台任务")
        
        # 1. 优雅取消所有任务
        cancelled_tasks = []
        for task in self.background_tasks:
            if not task.done():
                task.cancel()
                cancelled_tasks.append(task)
        
        if not cancelled_tasks:
            logger.debug("所有后台任务已完成，无需取消")
            return
        
        # 2. 等待任务完成 (有超时)
        try:
            await asyncio.wait_for(
                asyncio.gather(*cancelled_tasks, return_exceptions=True),
                timeout=self.cleanup_timeout
            )
            logger.debug(f"成功清理 {len(cancelled_tasks)} 个后台任务")
            
        except asyncio.TimeoutError:
            logger.warning(f"后台任务清理超时 ({self.cleanup_timeout}s)，"
                         f"强制完成清理")
            
            # 统计未完成的任务
            unfinished = [task for task in cancelled_tasks if not task.done()]
            if unfinished:
                logger.warning(f"{len(unfinished)} 个任务未能优雅完成")
    
    @handle_errors_gracefully(log_errors=True)
    async def _cleanup_browser_resources(self):
        """清理浏览器资源"""
        logger.debug("开始清理浏览器资源")
        
        # 按顺序清理: 页面 → 上下文 → 浏览器 → Playwright
        cleanup_steps = [
            ("page", self.page),
            ("context", self.context), 
            ("browser", self.browser),
            ("playwright", self.playwright)
        ]
        
        for resource_name, resource in cleanup_steps:
            if resource is None:
                continue
                
            try:
                if resource_name == "page" and hasattr(resource, 'is_closed'):
                    if not resource.is_closed():
                        await resource.close()
                        logger.debug(f"页面已关闭")
                elif resource_name in ["context", "browser"] and hasattr(resource, 'close'):
                    await resource.close()
                    logger.debug(f"{resource_name} 已关闭")
                elif resource_name == "playwright" and hasattr(resource, 'stop'):
                    await resource.stop()
                    logger.debug("Playwright 已停止")
                    
            except Exception as e:
                logger.warning(f"{resource_name} 关闭异常: {type(e).__name__}: {e}")
        
        logger.debug("浏览器资源清理完成")
    
    @handle_errors_gracefully(log_errors=True)
    async def _cleanup_process_resources(self):
        """清理进程资源 - terminate → wait → kill"""
        all_processes = []
        
        if self.cdp_process:
            all_processes.append(("CDP", self.cdp_process))
        
        for i, process in enumerate(self.managed_processes):
            all_processes.append((f"Process-{i}", process))
        
        if not all_processes:
            logger.debug("无进程需要清理")
            return
        
        logger.debug(f"开始清理 {len(all_processes)} 个进程")
        
        for name, process in all_processes:
            await self._terminate_process_gracefully(name, process)
        
        logger.debug("进程资源清理完成")
    
    async def _terminate_process_gracefully(self, name: str, process: subprocess.Popen):
        """
        优雅终止进程: terminate → wait → kill
        
        Args:
            name: 进程名称(用于日志)
            process: 要终止的进程
        """
        if process.poll() is not None:
            logger.debug(f"{name} 进程已结束 (PID: {process.pid})")
            return
        
        try:
            pid = process.pid
            logger.debug(f"开始终止 {name} 进程 (PID: {pid})")
            
            # 1. 尝试优雅终止
            process.terminate()
            
            try:
                # 2. 等待进程退出 (有超时)
                await asyncio.wait_for(
                    asyncio.to_thread(process.wait),
                    timeout=self.process_kill_timeout
                )
                logger.debug(f"{name} 进程优雅退出 (PID: {pid})")
                return
                
            except asyncio.TimeoutError:
                logger.warning(f"{name} 进程 {self.process_kill_timeout}s 内未退出，强制终止")
                
                # 3. 强制杀死进程
                try:
                    process.kill()
                    await asyncio.to_thread(process.wait)
                    logger.debug(f"{name} 进程已强制终止 (PID: {pid})")
                except Exception as kill_error:
                    logger.warning(f"{name} 进程强制终止失败: {kill_error}")
                    
        except Exception as e:
            logger.warning(f"{name} 进程清理异常: {type(e).__name__}: {e}")
    
    def get_resource_stats(self) -> Dict[str, Any]:
        """获取资源使用统计"""
        current_stats = self.stats.copy()
        current_stats.update({
            "current_tasks": len(self.background_tasks),
            "active_tasks": len([t for t in self.background_tasks if not t.done()]),
            "has_browser": self.browser is not None,
            "has_page": self.page is not None,
            "has_cdp_process": self.cdp_process is not None,
            "managed_processes": len(self.managed_processes),
            "cleanup_completed": self._cleanup_completed
        })
        return current_stats
    
    def is_healthy(self) -> bool:
        """检查资源管理器健康状态"""
        if self._cleanup_completed:
            return True
            
        # 检查是否有异常状态
        unhealthy_conditions = [
            len(self.background_tasks) > 50,  # 任务过多
            self.stats["cleanup_errors"] > 3,  # 清理错误过多
            self._cleanup_in_progress and not self._cleanup_completed  # 清理卡住
        ]
        
        return not any(unhealthy_conditions)
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        return {
            "service": "ResourceManager",
            "status": "healthy" if self.is_healthy() else "degraded",
            "stats": self.get_resource_stats(),
            "timestamp": datetime.now().isoformat()
        }