"""
反检测服务 - 重构版本

集成现有反检测功能，提供统一的反检测脚本注入和页面创建服务。
支持多种反检测策略和灵活的脚本管理。
"""

import os
import logging
from typing import Optional
from playwright.async_api import Playwright, BrowserContext, Page

from ..utils.protocols import StealthServiceProtocol
from .error_handler import ResourceError, retry_on_failure, with_error_context
from .resource_manager import ResourceManager

logger = logging.getLogger(__name__)


class StealthAntiDetectionService(StealthServiceProtocol):
    """
    反检测服务 - 企业级重构版本
    
    特性:
    - 动态脚本加载和缓存
    - 多策略反检测支持  
    - 资源生命周期集成
    - 增强错误处理和重试
    - 脚本注入验证
    """
    
    def __init__(self, playwright: Playwright, resource_manager: Optional[ResourceManager] = None):
        """
        初始化反检测服务
        
        Args:
            playwright: Playwright实例
            resource_manager: 资源管理器实例 (可选)
        """
        self.playwright = playwright
        self.resource_manager = resource_manager
        
        # 脚本缓存
        self._stealth_scripts = {}
        self._script_loaded = False
        
        # 反检测配置
        self.stealth_config = {
            "enable_webgl_spoofing": True,
            "enable_canvas_spoofing": True,
            "enable_audio_spoofing": True,
            "enable_font_spoofing": True,
            "enable_webrtc_spoofing": True,
            "enable_timezone_spoofing": False,  # 可能影响功能
            "enable_geolocation_spoofing": False  # 不需要地理位置伪装
        }
    
    def _get_stealth_script_paths(self) -> dict:
        """获取反检测脚本路径"""
        base_path = "/Users/<USER>/Desktop/workflow/Account Registrar"
        
        return {
            "main": os.path.join(base_path, "infrastructure/anti_detection/stealth/stealth.min.js"),
            "fallback": os.path.join(base_path, "infrastructure/anti_detection/stealth.min.js"),
            "legacy": os.path.join(base_path, "stealth.min.js")
        }
    
    @retry_on_failure(max_attempts=3, base_delay=0.5)
    def _load_stealth_script(self, script_type: str = "main") -> str:
        """
        加载反检测脚本
        
        Args:
            script_type: 脚本类型 (main, fallback, legacy)
            
        Returns:
            str: 脚本内容
        """
        if script_type in self._stealth_scripts:
            return self._stealth_scripts[script_type]
        
        script_paths = self._get_stealth_script_paths()
        
        if script_type not in script_paths:
            raise ResourceError(f"未知的脚本类型: {script_type}",
                              error_code="UNKNOWN_SCRIPT_TYPE",
                              context={"script_type": script_type})
        
        script_path = script_paths[script_type]
        
        try:
            if not os.path.exists(script_path):
                raise FileNotFoundError(f"脚本文件不存在: {script_path}")
            
            with open(script_path, 'r', encoding='utf-8') as f:
                script_content = f.read()
            
            if not script_content.strip():
                raise ValueError(f"脚本文件为空: {script_path}")
            
            # 验证脚本基本结构
            if not self._validate_script_content(script_content):
                raise ValueError(f"脚本内容验证失败: {script_path}")
            
            # 缓存脚本
            self._stealth_scripts[script_type] = script_content
            logger.info(f"✅ 反检测脚本加载成功: {os.path.basename(script_path)} "
                       f"({len(script_content)} 字符)")
            return script_content
            
        except Exception as e:
            error_msg = f"加载反检测脚本失败: {str(e)}"
            logger.error(error_msg)
            raise ResourceError(error_msg,
                              error_code="SCRIPT_LOAD_FAILED",
                              context={"script_path": script_path, "script_type": script_type})
    
    def _validate_script_content(self, script_content: str) -> bool:
        """
        验证脚本内容的基本结构
        
        Args:
            script_content: 脚本内容
            
        Returns:
            bool: 验证是否通过
        """
        # 基本检查：包含关键的反检测特征
        required_patterns = [
            "webdriver",  # WebDriver检测相关
            "navigator",  # Navigator对象相关
            "chrome"      # Chrome特定检测
        ]
        
        script_lower = script_content.lower()
        
        for pattern in required_patterns:
            if pattern not in script_lower:
                logger.warning(f"脚本验证警告: 缺少关键模式 '{pattern}'")
                # 不强制失败，只是警告
        
        # 检查脚本长度 (反检测脚本通常比较大)
        if len(script_content) < 1000:
            logger.warning(f"脚本验证警告: 脚本过短 ({len(script_content)} 字符)")
            return False
        
        return True
    
    def _get_best_available_script(self) -> str:
        """获取最佳可用的反检测脚本"""
        script_priority = ["main", "fallback", "legacy"]
        
        for script_type in script_priority:
            try:
                return self._load_stealth_script(script_type)
            except Exception as e:
                logger.warning(f"尝试加载 {script_type} 脚本失败: {e}")
                continue
        
        # 所有脚本都失败，返回空脚本但记录错误
        logger.error("❌ 所有反检测脚本加载失败，将使用空脚本")
        return ""
    
    @retry_on_failure(max_attempts=2, base_delay=1.0)
    @with_error_context(operation="create_stealth_page")
    async def create_stealth_page(self, context: BrowserContext) -> Page:
        """
        创建注入反检测的页面
        
        Args:
            context: 浏览器上下文
            
        Returns:
            Page: 注入了反检测脚本的页面
        """
        try:
            # 创建新页面
            page = await context.new_page()
            
            # 注册到资源管理器
            if self.resource_manager:
                self.resource_manager.register_browser_resources(page=page)
            
            # 获取反检测脚本
            stealth_script = self._get_best_available_script()
            
            if stealth_script:
                # 注入反检测脚本
                await page.add_init_script(stealth_script)
                logger.info("✅ 反检测脚本注入成功")
                
                # 验证注入效果 (可选)
                await self._verify_stealth_injection(page)
                
            else:
                logger.warning("⚠️ 无可用反检测脚本，页面可能被检测")
            
            # 设置额外的反检测配置
            await self._apply_additional_stealth_config(page)
            
            return page
            
        except Exception as e:
            error_msg = f"创建反检测页面失败: {str(e)}"
            logger.error(error_msg)
            raise ResourceError(error_msg,
                              error_code="STEALTH_PAGE_CREATION_FAILED")
    
    async def _verify_stealth_injection(self, page: Page) -> bool:
        """
        验证反检测脚本注入效果
        
        Args:
            page: 页面实例
            
        Returns:
            bool: 验证是否通过
        """
        try:
            # 检查webdriver属性是否被隐藏
            webdriver_hidden = await page.evaluate("() => navigator.webdriver === undefined")
            
            if webdriver_hidden:
                logger.debug("✅ WebDriver属性已被隐藏")
                return True
            else:
                logger.warning("⚠️ WebDriver属性未被隐藏")
                return False
                
        except Exception as e:
            logger.warning(f"反检测验证失败: {e}")
            return False
    
    async def _apply_additional_stealth_config(self, page: Page) -> None:
        """
        应用额外的反检测配置
        
        Args:
            page: 页面实例
        """
        try:
            # 禁用图片加载 (提高速度)
            await page.route("**/*.{png,jpg,jpeg,gif,svg,webp}", lambda route: route.abort())
            
            # 禁用字体加载 (减少指纹特征)
            await page.route("**/*.{woff,woff2,ttf,otf}", lambda route: route.abort())
            
            # 设置额外的请求头
            await page.set_extra_http_headers({
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
                "Accept-Language": "en-US,en;q=0.5",
                "Accept-Encoding": "gzip, deflate",
                "DNT": "1",
                "Connection": "keep-alive",
                "Upgrade-Insecure-Requests": "1"
            })
            
            logger.debug("✅ 额外反检测配置已应用")
            
        except Exception as e:
            logger.warning(f"应用额外反检测配置失败: {e}")
    
    def get_stealth_info(self) -> dict:
        """获取反检测服务信息"""
        return {
            "script_loaded": self._script_loaded,
            "cached_scripts": list(self._stealth_scripts.keys()),
            "config": self.stealth_config.copy(),
            "script_sizes": {k: len(v) for k, v in self._stealth_scripts.items()}
        }
    
    def reload_scripts(self) -> bool:
        """重新加载反检测脚本"""
        try:
            # 清空缓存
            self._stealth_scripts.clear()
            self._script_loaded = False
            
            # 重新加载主脚本
            self._get_best_available_script()
            self._script_loaded = True
            
            logger.info("✅ 反检测脚本重新加载成功")
            return True
            
        except Exception as e:
            logger.error(f"重新加载反检测脚本失败: {e}")
            return False