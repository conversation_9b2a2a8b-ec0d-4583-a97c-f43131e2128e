"""
核心模块 - 企业级自动化引擎

提供核心的自动化协调、资源管理和错误处理功能。
"""

from .automation_engine import AutomationEngine
from .resource_manager import ResourceManager
from .cdp_manager import CDPChromeManager
from .stealth_service import StealthAntiDetectionService
from .error_handler import (
    HuluAutomationError,
    LoginFailedError,
    VerificationError,
    ResourceError,
    SelectorError,
    NetworkError,
    TimeoutError,
    retry_on_failure,
    handle_errors_gracefully,
    with_error_context,
    ErrorContext,
    error_context
)

__all__ = [
    # 主要组件
    "AutomationEngine",
    "ResourceManager", 
    "CDPChromeManager",
    "StealthAntiDetectionService",
    
    # 异常类
    "HuluAutomationError",
    "LoginFailedError",
    "VerificationError",
    "ResourceError", 
    "SelectorError",
    "NetworkError",
    "TimeoutError",
    
    # 装饰器和工具
    "retry_on_failure",
    "handle_errors_gracefully", 
    "with_error_context",
    "ErrorContext",
    "error_context"
]