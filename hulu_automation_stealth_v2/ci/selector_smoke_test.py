#!/usr/bin/env python3
"""
选择器冒烟测试 - CI集成

快速验证关键选择器在目标页面上的有效性。
用于CI/CD流水线中的自动化验证，防止页面改版导致的选择器失效。
"""

import asyncio
import sys
import os
import logging
import json
import time
from typing import Dict, List, Any, Optional
from datetime import datetime
from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, Page, TimeoutError as PlaywrightTimeoutError

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from hulu_automation_stealth_v2.config import ConfigManager
from hulu_automation_stealth_v2.core.error_handler import SelectorError

logger = logging.getLogger(__name__)


class SelectorSmokeTest:
    """
    选择器冒烟测试器
    
    特性:
    - 快速选择器验证 (短超时)
    - 多环境支持
    - 详细的失败报告
    - CI友好的退出码
    - 性能基准测试
    """
    
    def __init__(self, environment: str = "production", headless: bool = True):
        """
        初始化冒烟测试器
        
        Args:
            environment: 测试环境
            headless: 是否无头模式
        """
        self.environment = environment
        self.headless = headless
        self.config_manager = ConfigManager()
        
        # 测试结果
        self.test_results = {
            "start_time": datetime.now().isoformat(),
            "environment": environment,
            "selectors_tested": 0,
            "selectors_passed": 0,
            "selectors_failed": 0,
            "total_time_ms": 0,
            "failures": [],
            "performance_stats": {}
        }
        
        # 关键选择器列表 (优先测试)
        self.critical_selectors = [
            "login_button",
            "email_input",
            "password_input",
            "continue_button"
        ]
        
        # 可选选择器列表
        self.optional_selectors = [
            "verification_input",
            "get_both_button",
            "profile_icon"
        ]
    
    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
    
    async def run_smoke_tests(self) -> Dict[str, Any]:
        """
        运行完整的冒烟测试
        
        Returns:
            Dict[str, Any]: 测试结果报告
        """
        self.setup_logging()
        logger.info(f"🚀 开始选择器冒烟测试 - 环境: {self.environment}")
        
        start_time = time.time()
        
        try:
            # 加载配置
            selectors_config = self.config_manager.load_selectors(self.environment)
            logger.info(f"✅ 配置加载成功: version={selectors_config['metadata']['version']}")
            
            # 启动浏览器
            async with async_playwright() as p:
                browser = await p.chromium.launch(headless=self.headless)
                
                try:
                    # 测试关键选择器
                    await self._test_critical_selectors(browser, selectors_config)
                    
                    # 测试可选选择器
                    await self._test_optional_selectors(browser, selectors_config)
                    
                    # 生成性能统计
                    self._generate_performance_stats()
                    
                finally:
                    await browser.close()
        
        except Exception as e:
            logger.error(f"❌ 冒烟测试执行失败: {e}")
            self.test_results["execution_error"] = str(e)
        
        # 完成测试
        total_time = time.time() - start_time
        self.test_results["total_time_ms"] = int(total_time * 1000)
        self.test_results["end_time"] = datetime.now().isoformat()
        
        # 计算成功率
        if self.test_results["selectors_tested"] > 0:
            success_rate = self.test_results["selectors_passed"] / self.test_results["selectors_tested"]
            self.test_results["success_rate"] = round(success_rate, 3)
        else:
            self.test_results["success_rate"] = 0.0
        
        logger.info(f"🎯 冒烟测试完成: 成功率 {self.test_results['success_rate']:.1%}, "
                   f"耗时 {total_time:.2f}s")
        
        return self.test_results
    
    async def _test_critical_selectors(self, browser: Browser, config: Dict[str, Any]) -> None:
        """测试关键选择器"""
        logger.info("🔍 测试关键选择器...")
        
        page = await browser.new_page()
        
        try:
            # 导航到登录页面
            await page.goto("https://hulu.com/login", 
                          wait_until="networkidle", 
                          timeout=15000)
            
            # 等待页面稳定
            await asyncio.sleep(2.0)
            
            # 测试每个关键选择器
            for selector_name in self.critical_selectors:
                await self._test_single_selector(page, selector_name, config, critical=True)
                
        except Exception as e:
            logger.error(f"❌ 关键选择器测试失败: {e}")
            self.test_results["failures"].append({
                "type": "critical_test_setup",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            })
        finally:
            await page.close()
    
    async def _test_optional_selectors(self, browser: Browser, config: Dict[str, Any]) -> None:
        """测试可选选择器"""
        logger.info("🔍 测试可选选择器...")
        
        page = await browser.new_page()
        
        try:
            # 对于可选选择器，可能需要不同的页面
            await page.goto("https://hulu.com/login", 
                          wait_until="networkidle", 
                          timeout=15000)
            
            # 测试每个可选选择器
            for selector_name in self.optional_selectors:
                await self._test_single_selector(page, selector_name, config, critical=False)
                
        except Exception as e:
            logger.warning(f"⚠️ 可选选择器测试失败: {e}")
        finally:
            await page.close()
    
    async def _test_single_selector(self, page: Page, selector_name: str, 
                                  config: Dict[str, Any], critical: bool = True) -> None:
        """
        测试单个选择器
        
        Args:
            page: 页面实例
            selector_name: 选择器名称
            config: 配置
            critical: 是否为关键选择器
        """
        self.test_results["selectors_tested"] += 1
        test_start = time.time()
        
        try:
            # 获取选择器配置
            if selector_name not in config['selectors']:
                raise SelectorError(f"选择器 {selector_name} 不存在于配置中")
            
            selector_config = config['selectors'][selector_name]
            primary_selector = selector_config['primary']
            fallback_selectors = selector_config.get('fallback', [])
            contract = selector_config.get('contract', {})
            
            # 测试主选择器
            element_found = False
            used_selector = None
            
            # 首先尝试主选择器
            try:
                timeout = min(contract.get('timeout_ms', 3000), 5000)  # 冒烟测试使用较短超时
                
                await page.wait_for_selector(
                    primary_selector, 
                    timeout=timeout,
                    state='visible' if contract.get('must_be_visible', True) else 'attached'
                )
                
                element_found = True
                used_selector = primary_selector
                logger.debug(f"✅ {selector_name}: 主选择器成功")
                
            except PlaywrightTimeoutError:
                # 尝试回退选择器
                for fallback_selector in fallback_selectors:
                    try:
                        await page.wait_for_selector(
                            fallback_selector,
                            timeout=2000,  # 回退选择器使用更短超时
                            state='visible' if contract.get('must_be_visible', True) else 'attached'
                        )
                        
                        element_found = True
                        used_selector = fallback_selector
                        logger.debug(f"✅ {selector_name}: 回退选择器成功 ({fallback_selector})")
                        break
                        
                    except PlaywrightTimeoutError:
                        continue
            
            # 记录结果
            test_time = time.time() - test_start
            
            if element_found:
                self.test_results["selectors_passed"] += 1
                
                # 记录性能统计
                self.test_results["performance_stats"][selector_name] = {
                    "success": True,
                    "time_ms": int(test_time * 1000),
                    "used_selector": used_selector,
                    "used_fallback": used_selector != primary_selector
                }
                
                logger.info(f"✅ {selector_name}: 通过 ({test_time:.2f}s)")
                
            else:
                self.test_results["selectors_failed"] += 1
                
                failure_info = {
                    "selector_name": selector_name,
                    "primary_selector": primary_selector,
                    "fallback_selectors": fallback_selectors,
                    "critical": critical,
                    "error": "未找到匹配元素",
                    "time_ms": int(test_time * 1000),
                    "timestamp": datetime.now().isoformat()
                }
                
                self.test_results["failures"].append(failure_info)
                
                if critical:
                    logger.error(f"❌ {selector_name}: 关键选择器失败")
                else:
                    logger.warning(f"⚠️ {selector_name}: 可选选择器失败")
        
        except Exception as e:
            self.test_results["selectors_failed"] += 1
            test_time = time.time() - test_start
            
            failure_info = {
                "selector_name": selector_name,
                "critical": critical,
                "error": str(e),
                "time_ms": int(test_time * 1000),
                "timestamp": datetime.now().isoformat()
            }
            
            self.test_results["failures"].append(failure_info)
            
            if critical:
                logger.error(f"❌ {selector_name}: 测试异常 - {e}")
            else:
                logger.warning(f"⚠️ {selector_name}: 测试异常 - {e}")
    
    def _generate_performance_stats(self) -> None:
        """生成性能统计"""
        performance_stats = self.test_results.get("performance_stats", {})
        
        if not performance_stats:
            return
        
        # 计算统计指标
        times = [stats["time_ms"] for stats in performance_stats.values() if stats.get("success")]
        
        if times:
            self.test_results["performance_summary"] = {
                "avg_time_ms": int(sum(times) / len(times)),
                "min_time_ms": min(times),
                "max_time_ms": max(times),
                "fallback_usage_rate": sum(1 for stats in performance_stats.values() 
                                         if stats.get("used_fallback")) / len(performance_stats)
            }
    
    def generate_report(self, output_file: str = None) -> str:
        """
        生成测试报告
        
        Args:
            output_file: 输出文件路径
            
        Returns:
            str: 报告文件路径
        """
        if output_file is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"selector_smoke_test_report_{timestamp}.json"
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(self.test_results, f, indent=2, ensure_ascii=False)
            
            logger.info(f"📊 测试报告已生成: {output_file}")
            return output_file
            
        except Exception as e:
            logger.error(f"❌ 生成测试报告失败: {e}")
            return ""
    
    def get_exit_code(self) -> int:
        """
        获取CI友好的退出码
        
        Returns:
            int: 0=成功, 1=部分失败, 2=严重失败
        """
        if "execution_error" in self.test_results:
            return 2  # 执行错误
        
        # 检查关键选择器是否都通过
        critical_failures = [
            f for f in self.test_results["failures"] 
            if f.get("critical", False)
        ]
        
        if critical_failures:
            return 2  # 关键选择器失败
        
        # 检查总体成功率
        success_rate = self.test_results.get("success_rate", 0.0)
        
        if success_rate >= 0.8:  # 80%以上成功率
            return 0  # 成功
        elif success_rate >= 0.5:  # 50-80%成功率
            return 1  # 部分失败
        else:
            return 2  # 严重失败


async def main():
    """主函数 - CLI入口"""
    import argparse
    
    parser = argparse.ArgumentParser(description="选择器冒烟测试")
    parser.add_argument("--environment", "-e", default="production", 
                       help="测试环境 (默认: production)")
    parser.add_argument("--headless", action="store_true", default=True,
                       help="无头模式运行 (默认: True)")
    parser.add_argument("--report", "-r", 
                       help="测试报告输出文件路径")
    parser.add_argument("--verbose", "-v", action="store_true",
                       help="详细输出")
    
    args = parser.parse_args()
    
    # 设置日志级别
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # 创建测试器
    tester = SelectorSmokeTest(
        environment=args.environment,
        headless=args.headless
    )
    
    # 运行测试
    try:
        results = await tester.run_smoke_tests()
        
        # 生成报告
        report_file = tester.generate_report(args.report)
        
        # 输出摘要
        print(f"\n📊 测试摘要:")
        print(f"   环境: {results['environment']}")
        print(f"   测试选择器: {results['selectors_tested']}")
        print(f"   通过: {results['selectors_passed']}")
        print(f"   失败: {results['selectors_failed']}")
        print(f"   成功率: {results.get('success_rate', 0):.1%}")
        print(f"   总耗时: {results['total_time_ms']}ms")
        
        if report_file:
            print(f"   报告文件: {report_file}")
        
        # 输出失败详情
        if results['failures']:
            print(f"\n❌ 失败详情:")
            for failure in results['failures']:
                print(f"   - {failure.get('selector_name', 'unknown')}: {failure.get('error', 'unknown error')}")
        
        # 返回适当的退出码
        exit_code = tester.get_exit_code()
        sys.exit(exit_code)
        
    except Exception as e:
        logger.error(f"❌ 测试执行失败: {e}")
        sys.exit(2)


if __name__ == "__main__":
    asyncio.run(main())