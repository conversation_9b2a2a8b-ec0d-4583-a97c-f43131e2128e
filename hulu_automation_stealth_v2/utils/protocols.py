"""
协议接口定义 - 支持依赖注入和测试替换

基于PEP 544的Protocol接口，提供清晰的抽象边界和类型安全的依赖注入。
"""

from typing import Protocol, Dict, Any, Optional, List
from playwright.async_api import Browser, BrowserContext, Page
import subprocess


class CDPManagerProtocol(Protocol):
    """CDP管理器接口"""
    
    async def connect_to_browser(self, cdp_url: str = None) -> Browser:
        """连接到CDP浏览器"""
        ...
    
    async def cleanup(self) -> None:
        """清理资源"""
        ...
    
    def get_chrome_command(self, user_data_dir: Optional[str] = None) -> List[str]:
        """获取Chrome启动命令"""
        ...
    
    def auto_launch_chrome(self, user_data_dir: Optional[str] = None) -> subprocess.Popen:
        """自动启动Chrome"""
        ...


class StealthServiceProtocol(Protocol):
    """反检测服务接口"""
    
    async def create_stealth_page(self, context: BrowserContext) -> Page:
        """创建隐身页面"""
        ...


class LoginServiceProtocol(Protocol):
    """登录服务接口"""
    
    async def perform_login(self) -> Dict[str, Any]:
        """执行登录流程"""
        ...
    
    async def detect_login_state(self) -> str:
        """检测登录状态"""
        ...
    
    async def restore_login_state(self) -> bool:
        """恢复登录状态"""
        ...
    
    async def save_login_state(self) -> bool:
        """保存登录状态"""
        ...


class VerificationServiceProtocol(Protocol):
    """验证服务接口"""
    
    async def handle_verification_code(self) -> bool:
        """处理验证码"""
        ...
    
    async def get_verification_code(self, email: str) -> Optional[str]:
        """获取验证码"""
        ...
    
    async def fill_verification_code(self, code: str) -> bool:
        """填写验证码"""
        ...


class ElementHandlerProtocol(Protocol):
    """元素处理器接口"""
    
    async def safe_click(self, selectors: List[str], timeout: int = 2000, **kwargs) -> str:
        """安全点击元素"""
        ...
    
    async def safe_fill(self, selectors: List[str], value: str, timeout: int = 2000, **kwargs) -> str:
        """安全填充元素"""
        ...
    
    async def safe_type(self, selectors: List[str], value: str, delay: int = 100, **kwargs) -> str:
        """安全输入文本"""
        ...


class StateManagerProtocol(Protocol):
    """状态管理器接口"""
    
    async def load_session_state(self, account_email: str) -> Dict[str, Any]:
        """加载会话状态"""
        ...
    
    async def save_session_state(self, account_email: str, state: Dict[str, Any]) -> bool:
        """保存会话状态"""
        ...
    
    async def clear_session_state(self, account_email: str) -> bool:
        """清除会话状态"""
        ...


class ResourceManagerProtocol(Protocol):
    """资源管理器接口"""
    
    async def __aenter__(self):
        """进入资源管理上下文"""
        ...
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """退出资源管理上下文"""
        ...
    
    async def register_task(self, task) -> None:
        """注册需要管理的任务"""
        ...
    
    async def cleanup_all_resources(self) -> None:
        """清理所有资源"""
        ...


class ConfigManagerProtocol(Protocol):
    """配置管理器接口"""
    
    def load_selectors(self, environment: str = "production") -> Dict[str, Any]:
        """加载选择器配置"""
        ...
    
    def get_selector(self, key: str, environment: str = "production") -> Dict[str, Any]:
        """获取特定选择器"""
        ...
    
    def validate_selector_contracts(self, environment: str = "production") -> Dict[str, bool]:
        """验证选择器契约"""
        ...