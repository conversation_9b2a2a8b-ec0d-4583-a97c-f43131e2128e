"""
工具模块 - 协议接口和辅助功能

提供依赖注入接口定义、常量配置和辅助函数。
"""

from .protocols import (
    CDPManagerProtocol,
    StealthServiceProtocol,
    LoginServiceProtocol,
    VerificationServiceProtocol,
    ElementHandlerProtocol,
    StateManagerProtocol,
    ResourceManagerProtocol,
    ConfigManagerProtocol
)

__all__ = [
    # 协议接口
    "CDPManagerProtocol",
    "StealthServiceProtocol", 
    "LoginServiceProtocol",
    "VerificationServiceProtocol",
    "ElementHandlerProtocol",
    "StateManagerProtocol",
    "ResourceManagerProtocol",
    "ConfigManagerProtocol"
]