"""
登录服务 - 登录流程业务逻辑

负责完整的登录流程协调，包括凭据处理、步骤执行和状态跟踪。
"""

import asyncio
import logging
from typing import Dict, Any, Optional, Protocol
from datetime import datetime

from ..core.error_handler import (
    LoginFailedError, retry_on_failure, with_error_context
)

logger = logging.getLogger(__name__)


class CDPManagerProtocol(Protocol):
    """CDP管理器协议"""
    async def navigate_to_login(self) -> Dict[str, Any]: ...
    async def wait_for_page_load(self) -> bool: ...
    async def get_page_info(self) -> Dict[str, Any]: ...


class ElementHandlerProtocol(Protocol):
    """元素处理器协议"""
    async def fill_email(self, email: str) -> bool: ...
    async def fill_password(self, password: str) -> bool: ...
    async def click_login_button(self) -> bool: ...
    async def wait_for_element(self, selector: str, timeout: float = 10.0) -> bool: ...


class StateManagerProtocol(Protocol):
    """状态管理器协议"""
    async def save_login_state(self, state: Dict[str, Any]) -> bool: ...
    async def load_login_state(self) -> Optional[Dict[str, Any]]: ...
    async def clear_login_state(self) -> bool: ...


class LoginService:
    """
    登录服务
    
    特性:
    - 完整登录流程协调
    - 凭据安全处理
    - 登录状态持久化
    - 重试和错误恢复
    - 性能监控
    """
    
    def __init__(
        self,
        cdp_manager: CDPManagerProtocol,
        element_handler: ElementHandlerProtocol,
        state_manager: StateManagerProtocol
    ):
        """
        初始化登录服务
        
        Args:
            cdp_manager: CDP管理器
            element_handler: 元素处理器
            state_manager: 状态管理器
        """
        self.cdp_manager = cdp_manager
        self.element_handler = element_handler
        self.state_manager = state_manager
        
        # 服务状态
        self.login_attempts = 0
        self.max_attempts = 3
        self.last_login_time = None
        
        # 性能统计
        self.execution_stats = {
            "total_logins": 0,
            "successful_logins": 0,
            "failed_logins": 0,
            "avg_login_time": 0.0,
            "last_execution_time": 0.0
        }
    
    @retry_on_failure(max_attempts=3, base_delay=2.0, jitter=True)
    @with_error_context(operation="execute_login")
    async def execute_login(
        self,
        email: str,
        password: str,
        account_index: int = 0,
        persistent_session: bool = True
    ) -> Dict[str, Any]:
        """
        执行完整登录流程
        
        Args:
            email: 邮箱地址
            password: 密码
            account_index: 账户索引
            persistent_session: 是否持久化会话
            
        Returns:
            Dict[str, Any]: 登录结果
            
        Raises:
            LoginFailedError: 登录失败
        """
        start_time = datetime.now()
        self.login_attempts += 1
        
        try:
            logger.info(f"🔐 开始登录流程 - 账户#{account_index}")
            
            # 步骤1: 导航到登录页面
            await self._navigate_to_login_page()
            
            # 步骤2: 填写登录凭据
            await self._fill_login_credentials(email, password)
            
            # 步骤3: 提交登录表单
            login_result = await self._submit_login_form()
            
            # 步骤4: 验证登录状态
            verification_result = await self._verify_login_success()
            
            # 步骤5: 保存登录状态
            if persistent_session:
                await self._save_login_session(
                    email, account_index, verification_result
                )
            
            # 更新统计信息
            execution_time = (datetime.now() - start_time).total_seconds()
            await self._update_success_stats(execution_time)
            
            logger.info(f"✅ 登录成功 - 耗时 {execution_time:.2f}s")
            
            return {
                "success": True,
                "account_index": account_index,
                "email": email,
                "execution_time": execution_time,
                "session_persistent": persistent_session,
                "verification_result": verification_result,
                "timestamp": start_time.isoformat()
            }
            
        except Exception as e:
            execution_time = (datetime.now() - start_time).total_seconds()
            await self._update_failure_stats(execution_time)
            
            logger.error(f"❌ 登录失败: {e}")
            
            # 转换为标准化登录错误
            if isinstance(e, LoginFailedError):
                raise
            else:
                raise LoginFailedError(
                    f"登录流程执行失败: {e}",
                    error_code="LOGIN_EXECUTION_FAILED",
                    context={
                        "account_index": account_index,
                        "email": email,
                        "attempt": self.login_attempts,
                        "execution_time": execution_time
                    }
                ) from e
    
    async def _navigate_to_login_page(self) -> None:
        """导航到登录页面"""
        logger.debug("📍 导航到登录页面")
        
        nav_result = await self.cdp_manager.navigate_to_login()
        if not nav_result.get("success", False):
            raise LoginFailedError(
                "无法导航到登录页面",
                error_code="NAVIGATION_FAILED",
                context=nav_result
            )
        
        # 等待页面加载完成
        page_loaded = await self.cdp_manager.wait_for_page_load()
        if not page_loaded:
            raise LoginFailedError(
                "登录页面加载超时",
                error_code="PAGE_LOAD_TIMEOUT"
            )
    
    async def _fill_login_credentials(self, email: str, password: str) -> None:
        """填写登录凭据"""
        logger.debug("✏️ 填写登录凭据")
        
        # 填写邮箱
        email_filled = await self.element_handler.fill_email(email)
        if not email_filled:
            raise LoginFailedError(
                "无法填写邮箱地址",
                error_code="EMAIL_FILL_FAILED",
                context={"email": email[:5] + "***"}  # 安全日志
            )
        
        # 短暂延迟模拟人类行为
        await asyncio.sleep(0.5)
        
        # 填写密码
        password_filled = await self.element_handler.fill_password(password)
        if not password_filled:
            raise LoginFailedError(
                "无法填写密码",
                error_code="PASSWORD_FILL_FAILED"
            )
    
    async def _submit_login_form(self) -> Dict[str, Any]:
        """提交登录表单"""
        logger.debug("🔘 提交登录表单")
        
        # 点击登录按钮
        button_clicked = await self.element_handler.click_login_button()
        if not button_clicked:
            raise LoginFailedError(
                "无法点击登录按钮",
                error_code="LOGIN_BUTTON_CLICK_FAILED"
            )
        
        # 等待提交响应
        await asyncio.sleep(2.0)
        
        return {
            "form_submitted": True,
            "timestamp": datetime.now().isoformat()
        }
    
    async def _verify_login_success(self) -> Dict[str, Any]:
        """验证登录是否成功"""
        logger.debug("🔍 验证登录状态")
        
        # 等待登录结果页面
        await asyncio.sleep(3.0)
        
        # 获取页面信息判断登录状态
        page_info = await self.cdp_manager.get_page_info()
        current_url = page_info.get("url", "")
        
        # 简单的URL判断逻辑 (实际应用中需要更复杂的检测)
        if "login" in current_url.lower():
            # 仍在登录页面，可能失败
            raise LoginFailedError(
                "登录验证失败，仍在登录页面",
                error_code="LOGIN_VERIFICATION_FAILED",
                context={"current_url": current_url}
            )
        
        return {
            "login_verified": True,
            "redirect_url": current_url,
            "verification_method": "url_check",
            "timestamp": datetime.now().isoformat()
        }
    
    async def _save_login_session(
        self,
        email: str,
        account_index: int,
        verification_result: Dict[str, Any]
    ) -> None:
        """保存登录会话状态"""
        logger.debug("💾 保存登录会话")
        
        session_state = {
            "email": email,
            "account_index": account_index,
            "login_time": datetime.now().isoformat(),
            "verification_result": verification_result,
            "session_id": f"session_{account_index}_{int(datetime.now().timestamp())}"
        }
        
        saved = await self.state_manager.save_login_state(session_state)
        if not saved:
            logger.warning("⚠️ 登录状态保存失败")
    
    async def _update_success_stats(self, execution_time: float) -> None:
        """更新成功统计"""
        self.execution_stats["total_logins"] += 1
        self.execution_stats["successful_logins"] += 1
        self.execution_stats["last_execution_time"] = execution_time
        
        # 更新平均执行时间
        if self.execution_stats["total_logins"] > 1:
            current_avg = self.execution_stats["avg_login_time"]
            new_avg = (current_avg + execution_time) / 2
            self.execution_stats["avg_login_time"] = new_avg
        else:
            self.execution_stats["avg_login_time"] = execution_time
        
        self.last_login_time = datetime.now()
    
    async def _update_failure_stats(self, execution_time: float) -> None:
        """更新失败统计"""
        self.execution_stats["total_logins"] += 1
        self.execution_stats["failed_logins"] += 1
        self.execution_stats["last_execution_time"] = execution_time
    
    def get_service_stats(self) -> Dict[str, Any]:
        """获取服务统计信息"""
        return {
            "service_name": "LoginService",
            "login_attempts": self.login_attempts,
            "max_attempts": self.max_attempts,
            "last_login_time": self.last_login_time.isoformat() if self.last_login_time else None,
            "execution_stats": self.execution_stats.copy(),
            "success_rate": (
                self.execution_stats["successful_logins"] / 
                max(self.execution_stats["total_logins"], 1)
            )
        }
    
    async def health_check(self) -> Dict[str, Any]:
        """服务健康检查"""
        return {
            "service": "LoginService", 
            "status": "healthy",
            "dependencies": {
                "cdp_manager": "connected",
                "element_handler": "ready", 
                "state_manager": "available"
            },
            "stats": self.get_service_stats(),
            "timestamp": datetime.now().isoformat()
        }