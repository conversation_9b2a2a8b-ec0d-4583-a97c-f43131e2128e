"""
验证服务 - 邮件验证码处理

负责邮件验证码的获取、处理和提交逻辑。
"""

import asyncio
import logging
import re
from typing import Dict, Any, Optional, List, Protocol
from datetime import datetime, timedelta

from ..core.error_handler import (
    VerificationFailedError, retry_on_failure, with_error_context
)

logger = logging.getLogger(__name__)


class EmailClientProtocol(Protocol):
    """邮件客户端协议"""
    async def get_latest_emails(self, count: int = 5) -> List[Dict[str, Any]]: ...
    async def extract_verification_code(self, email_content: str) -> Optional[str]: ...


class ElementHandlerProtocol(Protocol):
    """元素处理器协议"""
    async def fill_verification_code(self, code: str) -> bool: ...
    async def click_verify_button(self) -> bool: ...
    async def wait_for_verification_form(self, timeout: float = 10.0) -> bool: ...


class VerificationService:
    """
    验证服务
    
    特性:
    - 自动邮件验证码获取
    - 智能验证码提取
    - 验证流程自动化
    - 超时和重试机制
    - 验证状态跟踪
    """
    
    def __init__(
        self,
        email_client: EmailClientProtocol,
        element_handler: ElementHandlerProtocol
    ):
        """
        初始化验证服务
        
        Args:
            email_client: 邮件客户端
            element_handler: 元素处理器
        """
        self.email_client = email_client
        self.element_handler = element_handler
        
        # 验证配置
        self.verification_timeout = 120.0  # 2分钟
        self.code_check_interval = 5.0     # 5秒检查间隔
        self.max_code_attempts = 3
        
        # 服务状态
        self.verification_attempts = 0
        self.last_verification_time = None
        
        # 性能统计
        self.execution_stats = {
            "total_verifications": 0,
            "successful_verifications": 0,
            "failed_verifications": 0,
            "avg_verification_time": 0.0,
            "code_extraction_success_rate": 0.0
        }
    
    @retry_on_failure(max_attempts=2, base_delay=5.0)
    @with_error_context(operation="execute_verification")
    async def execute_verification(self, email: str) -> Dict[str, Any]:
        """
        执行完整的邮件验证流程
        
        Args:
            email: 邮箱地址
            
        Returns:
            Dict[str, Any]: 验证结果
            
        Raises:
            VerificationFailedError: 验证失败
        """
        start_time = datetime.now()
        self.verification_attempts += 1
        
        try:
            logger.info(f"📧 开始邮件验证流程 - {email}")
            
            # 步骤1: 等待验证表单出现
            await self._wait_for_verification_form()
            
            # 步骤2: 获取验证码
            verification_code = await self._get_verification_code(email)
            
            # 步骤3: 填写并提交验证码
            verification_result = await self._submit_verification_code(verification_code)
            
            # 更新成功统计
            execution_time = (datetime.now() - start_time).total_seconds()
            await self._update_success_stats(execution_time)
            
            logger.info(f"✅ 邮件验证成功 - 耗时 {execution_time:.2f}s")
            
            return {
                "success": True,
                "email": email,
                "verification_code": verification_code[:2] + "***",  # 安全日志
                "execution_time": execution_time,
                "verification_result": verification_result,
                "timestamp": start_time.isoformat()
            }
            
        except Exception as e:
            execution_time = (datetime.now() - start_time).total_seconds()
            await self._update_failure_stats(execution_time)
            
            logger.error(f"❌ 邮件验证失败: {e}")
            
            # 转换为标准化验证错误
            if isinstance(e, VerificationFailedError):
                raise
            else:
                raise VerificationFailedError(
                    f"验证流程执行失败: {e}",
                    error_code="VERIFICATION_EXECUTION_FAILED",
                    context={
                        "email": email,
                        "attempt": self.verification_attempts,
                        "execution_time": execution_time
                    }
                ) from e
    
    async def _wait_for_verification_form(self) -> None:
        """等待验证表单出现"""
        logger.debug("⏳ 等待验证表单出现")
        
        form_appeared = await self.element_handler.wait_for_verification_form(
            timeout=30.0
        )
        
        if not form_appeared:
            raise VerificationFailedError(
                "验证表单未出现",
                error_code="VERIFICATION_FORM_NOT_FOUND"
            )
    
    @retry_on_failure(max_attempts=3, base_delay=5.0)
    async def _get_verification_code(self, email: str) -> str:
        """
        获取验证码
        
        Args:
            email: 邮箱地址
            
        Returns:
            str: 验证码
            
        Raises:
            VerificationFailedError: 获取失败
        """
        logger.debug(f"📬 获取验证码 - {email}")
        
        end_time = datetime.now() + timedelta(seconds=self.verification_timeout)
        
        while datetime.now() < end_time:
            try:
                # 获取最新邮件
                emails = await self.email_client.get_latest_emails(count=5)
                
                # 查找验证邮件
                for email_data in emails:
                    if self._is_verification_email(email_data):
                        # 提取验证码
                        code = await self._extract_verification_code(email_data)
                        if code:
                            logger.info(f"✅ 验证码获取成功: {code[:2]}***")
                            return code
                
                # 等待下次检查
                logger.debug(f"⏳ 未找到验证码，{self.code_check_interval}s后重试...")
                await asyncio.sleep(self.code_check_interval)
                
            except Exception as e:
                logger.warning(f"⚠️ 检查邮件时出错: {e}")
                await asyncio.sleep(self.code_check_interval)
        
        # 超时未获取到验证码
        raise VerificationFailedError(
            f"验证码获取超时 ({self.verification_timeout}s)",
            error_code="VERIFICATION_CODE_TIMEOUT",
            context={"email": email, "timeout": self.verification_timeout}
        )
    
    def _is_verification_email(self, email_data: Dict[str, Any]) -> bool:
        """
        判断是否为验证邮件
        
        Args:
            email_data: 邮件数据
            
        Returns:
            bool: 是否为验证邮件
        """
        subject = email_data.get("subject", "").lower()
        from_address = email_data.get("from", "").lower()
        
        # 验证邮件的特征
        verification_keywords = [
            "verification", "verify", "code", "confirmation",
            "验证", "确认", "验证码"
        ]
        
        hulu_keywords = ["hulu", "disney"]
        
        # 检查主题是否包含验证关键词
        has_verification_keyword = any(
            keyword in subject for keyword in verification_keywords
        )
        
        # 检查发件人是否来自Hulu相关域名
        has_hulu_sender = any(
            keyword in from_address for keyword in hulu_keywords
        )
        
        # 检查邮件时间 (最近10分钟内)
        email_time = email_data.get("timestamp")
        if email_time:
            try:
                email_datetime = datetime.fromisoformat(email_time.replace('Z', '+00:00'))
                is_recent = (datetime.now() - email_datetime.replace(tzinfo=None)) < timedelta(minutes=10)
            except:
                is_recent = True  # 解析失败时假设是最近的
        else:
            is_recent = True
        
        return has_verification_keyword and (has_hulu_sender or is_recent)
    
    async def _extract_verification_code(self, email_data: Dict[str, Any]) -> Optional[str]:
        """
        从邮件中提取验证码
        
        Args:
            email_data: 邮件数据
            
        Returns:
            Optional[str]: 验证码
        """
        content = email_data.get("content", "")
        subject = email_data.get("subject", "")
        
        # 尝试使用邮件客户端的提取方法
        try:
            code = await self.email_client.extract_verification_code(content)
            if code:
                return code
        except Exception as e:
            logger.debug(f"邮件客户端提取失败: {e}")
        
        # 备用正则表达式提取
        patterns = [
            r'\b(\d{6})\b',           # 6位数字
            r'\b(\d{4})\b',           # 4位数字  
            r'code[:\s]+(\d{4,6})',   # code: 123456
            r'verification[:\s]+(\d{4,6})',  # verification: 123456
            r'验证码[：:\s]+(\d{4,6})', # 验证码：123456
        ]
        
        # 在邮件内容和主题中搜索
        full_text = f"{subject} {content}"
        
        for pattern in patterns:
            matches = re.findall(pattern, full_text, re.IGNORECASE)
            if matches:
                # 返回第一个匹配的验证码
                code = matches[0]
                if len(code) >= 4:  # 验证码至少4位
                    return code
        
        return None
    
    async def _submit_verification_code(self, code: str) -> Dict[str, Any]:
        """
        提交验证码
        
        Args:
            code: 验证码
            
        Returns:
            Dict[str, Any]: 提交结果
        """
        logger.debug(f"📝 提交验证码: {code[:2]}***")
        
        # 填写验证码
        code_filled = await self.element_handler.fill_verification_code(code)
        if not code_filled:
            raise VerificationFailedError(
                "无法填写验证码",
                error_code="VERIFICATION_CODE_FILL_FAILED"
            )
        
        # 短暂延迟模拟人类行为
        await asyncio.sleep(0.5)
        
        # 点击验证按钮
        button_clicked = await self.element_handler.click_verify_button()
        if not button_clicked:
            raise VerificationFailedError(
                "无法点击验证按钮",
                error_code="VERIFICATION_BUTTON_CLICK_FAILED"
            )
        
        # 等待验证响应
        await asyncio.sleep(3.0)
        
        return {
            "code_submitted": True,
            "verification_code": code[:2] + "***",
            "timestamp": datetime.now().isoformat()
        }
    
    async def _update_success_stats(self, execution_time: float) -> None:
        """更新成功统计"""
        self.execution_stats["total_verifications"] += 1
        self.execution_stats["successful_verifications"] += 1
        
        # 更新平均执行时间
        if self.execution_stats["total_verifications"] > 1:
            current_avg = self.execution_stats["avg_verification_time"]
            new_avg = (current_avg + execution_time) / 2
            self.execution_stats["avg_verification_time"] = new_avg
        else:
            self.execution_stats["avg_verification_time"] = execution_time
        
        # 更新成功率
        success_rate = (
            self.execution_stats["successful_verifications"] / 
            self.execution_stats["total_verifications"]
        )
        self.execution_stats["code_extraction_success_rate"] = success_rate
        
        self.last_verification_time = datetime.now()
    
    async def _update_failure_stats(self, execution_time: float) -> None:
        """更新失败统计"""
        self.execution_stats["total_verifications"] += 1
        self.execution_stats["failed_verifications"] += 1
    
    def get_service_stats(self) -> Dict[str, Any]:
        """获取服务统计信息"""
        return {
            "service_name": "VerificationService",
            "verification_attempts": self.verification_attempts,
            "max_code_attempts": self.max_code_attempts,
            "verification_timeout": self.verification_timeout,
            "last_verification_time": (
                self.last_verification_time.isoformat() 
                if self.last_verification_time else None
            ),
            "execution_stats": self.execution_stats.copy(),
            "success_rate": (
                self.execution_stats["successful_verifications"] / 
                max(self.execution_stats["total_verifications"], 1)
            )
        }
    
    async def health_check(self) -> Dict[str, Any]:
        """服务健康检查"""
        return {
            "service": "VerificationService",
            "status": "healthy",
            "dependencies": {
                "email_client": "connected",
                "element_handler": "ready"
            },
            "config": {
                "verification_timeout": self.verification_timeout,
                "code_check_interval": self.code_check_interval,
                "max_code_attempts": self.max_code_attempts
            },
            "stats": self.get_service_stats(),
            "timestamp": datetime.now().isoformat()
        }