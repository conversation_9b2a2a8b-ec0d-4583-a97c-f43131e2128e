"""
状态管理器 - 应用状态持久化

负责会话状态、配置缓存和临时数据的管理。
"""

import asyncio
import json
import logging
import os
from pathlib import Path
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
# import aiofiles  # 可选依赖，回退到同步文件操作

from ..core.error_handler import StateManagementError, with_error_context

logger = logging.getLogger(__name__)


class StateManager:
    """
    状态管理器
    
    特性:
    - 会话状态持久化
    - 配置缓存管理
    - 临时数据存储
    - 状态过期清理
    - 并发访问控制
    """
    
    def __init__(self, data_dir: str = "data/sessions"):
        """
        初始化状态管理器
        
        Args:
            data_dir: 数据存储目录
        """
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(parents=True, exist_ok=True)
        
        # 文件路径
        self.login_state_file = self.data_dir / "login_states.json"
        self.config_cache_file = self.data_dir / "config_cache.json"
        self.temp_data_file = self.data_dir / "temp_data.json"
        
        # 状态配置
        self.state_expiry_hours = 24  # 状态保留24小时
        self.max_states = 100         # 最多保留100个状态
        self.cleanup_interval = 3600  # 每小时清理一次
        
        # 内存缓存
        self._login_states = {}
        self._config_cache = {}
        self._temp_data = {}
        
        # 并发控制
        self._lock = asyncio.Lock()
        self._last_cleanup = datetime.now()
        
        # 统计信息
        self.stats = {
            "states_saved": 0,
            "states_loaded": 0,
            "states_expired": 0,
            "cache_hits": 0,
            "cache_misses": 0
        }
    
    async def initialize(self) -> None:
        """初始化状态管理器"""
        async with self._lock:
            try:
                # 加载现有状态
                await self._load_all_states()
                
                # 执行初始清理
                await self._cleanup_expired_states()
                
                logger.info("✅ 状态管理器初始化完成")
                
            except Exception as e:
                logger.error(f"❌ 状态管理器初始化失败: {e}")
                raise StateManagementError(
                    f"状态管理器初始化失败: {e}",
                    error_code="STATE_MANAGER_INIT_FAILED"
                ) from e
    
    @with_error_context(operation="save_login_state")
    async def save_login_state(self, state: Dict[str, Any]) -> bool:
        """
        保存登录状态
        
        Args:
            state: 登录状态数据
            
        Returns:
            bool: 保存是否成功
        """
        async with self._lock:
            try:
                # 添加时间戳和过期时间
                state_with_meta = {
                    **state,
                    "saved_at": datetime.now().isoformat(),
                    "expires_at": (datetime.now() + timedelta(hours=self.state_expiry_hours)).isoformat()
                }
                
                # 生成状态ID
                state_id = self._generate_state_id(state)
                
                # 保存到内存缓存
                self._login_states[state_id] = state_with_meta
                
                # 持久化到文件
                await self._persist_login_states()
                
                # 定期清理
                await self._periodic_cleanup()
                
                self.stats["states_saved"] += 1
                logger.debug(f"✅ 登录状态已保存: {state_id}")
                
                return True
                
            except Exception as e:
                logger.error(f"❌ 保存登录状态失败: {e}")
                return False
    
    @with_error_context(operation="load_login_state")
    async def load_login_state(self, account_index: int = None, email: str = None) -> Optional[Dict[str, Any]]:
        """
        加载登录状态
        
        Args:
            account_index: 账户索引
            email: 邮箱地址
            
        Returns:
            Optional[Dict[str, Any]]: 登录状态，未找到返回None
        """
        async with self._lock:
            try:
                # 查找匹配的状态  
                for state_id, state in self._login_states.items():
                    # 检查是否过期
                    if self._is_state_expired(state):
                        continue
                    
                    # 匹配条件
                    if account_index is not None and state.get("account_index") == account_index:
                        self.stats["states_loaded"] += 1
                        logger.debug(f"✅ 找到账户#{account_index}的登录状态")
                        return state
                    
                    if email is not None and state.get("email") == email:
                        self.stats["states_loaded"] += 1
                        logger.debug(f"✅ 找到邮箱{email}的登录状态")
                        return state
                
                logger.debug("❌ 未找到匹配的登录状态")
                return None
                
            except Exception as e:
                logger.error(f"❌ 加载登录状态失败: {e}")
                return None
    
    @with_error_context(operation="clear_login_state")
    async def clear_login_state(self, account_index: int = None, email: str = None) -> bool:
        """
        清除登录状态
        
        Args:
            account_index: 账户索引
            email: 邮箱地址
            
        Returns:
            bool: 清除是否成功
        """
        async with self._lock:
            try:
                states_to_remove = []
                
                # 查找要清除的状态
                for state_id, state in self._login_states.items():
                    if account_index is not None and state.get("account_index") == account_index:
                        states_to_remove.append(state_id)
                    elif email is not None and state.get("email") == email:
                        states_to_remove.append(state_id)
                
                # 删除状态
                for state_id in states_to_remove:
                    del self._login_states[state_id]
                
                # 持久化更改
                if states_to_remove:
                    await self._persist_login_states()
                    logger.info(f"✅ 已清除 {len(states_to_remove)} 个登录状态")
                
                return len(states_to_remove) > 0
                
            except Exception as e:
                logger.error(f"❌ 清除登录状态失败: {e}")
                return False
    
    async def save_config_cache(self, key: str, data: Dict[str, Any], ttl_hours: int = 24) -> bool:
        """
        保存配置缓存
        
        Args:
            key: 缓存键
            data: 缓存数据
            ttl_hours: 生存时间(小时)
            
        Returns:
            bool: 保存是否成功
        """
        async with self._lock:
            try:
                cache_entry = {
                    "data": data,
                    "cached_at": datetime.now().isoformat(),
                    "expires_at": (datetime.now() + timedelta(hours=ttl_hours)).isoformat()
                }
                
                self._config_cache[key] = cache_entry
                await self._persist_config_cache()
                
                logger.debug(f"✅ 配置缓存已保存: {key}")
                return True
                
            except Exception as e:
                logger.error(f"❌ 保存配置缓存失败: {e}")
                return False
    
    async def load_config_cache(self, key: str) -> Optional[Dict[str, Any]]:
        """
        加载配置缓存
        
        Args:
            key: 缓存键
            
        Returns:
            Optional[Dict[str, Any]]: 缓存数据，过期或不存在返回None
        """
        async with self._lock:
            try:
                if key not in self._config_cache:
                    self.stats["cache_misses"] += 1
                    return None
                
                cache_entry = self._config_cache[key]
                
                # 检查是否过期
                if self._is_cache_expired(cache_entry):
                    del self._config_cache[key]
                    self.stats["cache_misses"] += 1
                    return None
                
                self.stats["cache_hits"] += 1
                return cache_entry["data"]
                
            except Exception as e:
                logger.error(f"❌ 加载配置缓存失败: {e}")
                return None
    
    async def save_temp_data(self, key: str, data: Any) -> bool:
        """
        保存临时数据
        
        Args:
            key: 数据键
            data: 数据内容
            
        Returns:
            bool: 保存是否成功
        """
        async with self._lock:
            try:
                self._temp_data[key] = {
                    "data": data,
                    "timestamp": datetime.now().isoformat()
                }
                
                await self._persist_temp_data()
                return True
                
            except Exception as e:
                logger.error(f"❌ 保存临时数据失败: {e}")
                return False
    
    async def load_temp_data(self, key: str) -> Any:
        """
        加载临时数据
        
        Args:
            key: 数据键
            
        Returns:
            Any: 数据内容，不存在返回None
        """
        return self._temp_data.get(key, {}).get("data")
    
    def _generate_state_id(self, state: Dict[str, Any]) -> str:
        """生成状态ID"""
        account_index = state.get("account_index", 0)
        email = state.get("email", "unknown")
        timestamp = int(datetime.now().timestamp())
        return f"state_{account_index}_{hash(email)}_{timestamp}"
    
    def _is_state_expired(self, state: Dict[str, Any]) -> bool:
        """检查状态是否过期"""
        expires_at = state.get("expires_at")
        if not expires_at:
            return False
        
        try:
            expire_time = datetime.fromisoformat(expires_at)
            return datetime.now() > expire_time
        except:
            return True  # 解析失败视为过期
    
    def _is_cache_expired(self, cache_entry: Dict[str, Any]) -> bool:
        """检查缓存是否过期"""
        expires_at = cache_entry.get("expires_at")
        if not expires_at:
            return False
        
        try:
            expire_time = datetime.fromisoformat(expires_at)
            return datetime.now() > expire_time
        except:
            return True
    
    async def _load_all_states(self) -> None:
        """从文件加载所有状态"""
        try:
            if self.login_state_file.exists():
                with open(self.login_state_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    self._login_states = json.loads(content) if content.strip() else {}
            
            if self.config_cache_file.exists():
                with open(self.config_cache_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    self._config_cache = json.loads(content) if content.strip() else {}
            
            if self.temp_data_file.exists():
                with open(self.temp_data_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    self._temp_data = json.loads(content) if content.strip() else {}
                    
        except Exception as e:
            logger.warning(f"⚠️ 加载状态文件时出错: {e}")
    
    async def _persist_login_states(self) -> None:
        """持久化登录状态到文件"""
        try:
            with open(self.login_state_file, 'w', encoding='utf-8') as f:
                f.write(json.dumps(self._login_states, indent=2, ensure_ascii=False))
        except Exception as e:
            logger.error(f"❌ 持久化登录状态失败: {e}")
    
    async def _persist_config_cache(self) -> None:
        """持久化配置缓存到文件"""
        try:
            with open(self.config_cache_file, 'w', encoding='utf-8') as f:
                f.write(json.dumps(self._config_cache, indent=2, ensure_ascii=False))
        except Exception as e:
            logger.error(f"❌ 持久化配置缓存失败: {e}")
    
    async def _persist_temp_data(self) -> None:
        """持久化临时数据到文件"""
        try:
            with open(self.temp_data_file, 'w', encoding='utf-8') as f:
                f.write(json.dumps(self._temp_data, indent=2, ensure_ascii=False))
        except Exception as e:
            logger.error(f"❌ 持久化临时数据失败: {e}")
    
    async def _cleanup_expired_states(self) -> None:
        """清理过期状态"""
        try:
            # 清理过期登录状态
            expired_states = [
                state_id for state_id, state in self._login_states.items()
                if self._is_state_expired(state)
            ]
            
            for state_id in expired_states:
                del self._login_states[state_id]
                self.stats["states_expired"] += 1
            
            # 清理过期配置缓存
            expired_cache = [
                key for key, entry in self._config_cache.items()
                if self._is_cache_expired(entry)
            ]
            
            for key in expired_cache:
                del self._config_cache[key]
            
            if expired_states or expired_cache:
                logger.info(f"🧹 清理完成: {len(expired_states)} 个状态, {len(expired_cache)} 个缓存")
                
        except Exception as e:
            logger.error(f"❌ 清理过期状态失败: {e}")
    
    async def _periodic_cleanup(self) -> None:
        """定期清理"""
        now = datetime.now()
        if (now - self._last_cleanup).total_seconds() > self.cleanup_interval:
            await self._cleanup_expired_states()
            self._last_cleanup = now
    
    def get_state_stats(self) -> Dict[str, Any]:
        """获取状态统计信息"""
        return {
            "service_name": "StateManager",
            "data_dir": str(self.data_dir),
            "login_states_count": len(self._login_states),
            "config_cache_count": len(self._config_cache),
            "temp_data_count": len(self._temp_data),
            "stats": self.stats.copy(),
            "config": {
                "state_expiry_hours": self.state_expiry_hours,
                "max_states": self.max_states,
                "cleanup_interval": self.cleanup_interval
            }
        }
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        try:
            # 检查数据目录
            data_dir_writable = os.access(self.data_dir, os.W_OK)
            
            # 检查内存使用
            memory_usage = {
                "login_states": len(self._login_states),
                "config_cache": len(self._config_cache),
                "temp_data": len(self._temp_data)
            }
            
            return {
                "service": "StateManager",
                "status": "healthy" if data_dir_writable else "degraded",
                "data_dir_writable": data_dir_writable,
                "memory_usage": memory_usage,
                "stats": self.get_state_stats(),
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                "service": "StateManager",
                "status": "unhealthy",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }