"""
元素处理器 - 浏览器元素操作

负责查找、操作和验证浏览器页面元素。
"""

import asyncio
import logging
import random
from typing import Dict, Any, Optional, List, Protocol
from datetime import datetime

from ..core.error_handler import (
    ElementNotFoundError, ElementInteractionError, retry_on_failure, with_error_context
)

logger = logging.getLogger(__name__)


class CDPManagerProtocol(Protocol):
    """CDP管理器协议"""
    async def evaluate_js(self, script: str) -> Any: ...
    async def wait_for_selector(self, selector: str, timeout: float = 10.0) -> bool: ...
    async def click_element(self, selector: str) -> bool: ...
    async def type_text(self, selector: str, text: str, delay: float = 0.1) -> bool: ...
    async def get_element_info(self, selector: str) -> Dict[str, Any]: ...


class ConfigManagerProtocol(Protocol):
    """配置管理器协议"""
    def get_selector(self, name: str) -> Dict[str, Any]: ...
    def get_typing_config(self) -> Dict[str, Any]: ...


class ElementHandler:
    """
    元素处理器
    
    特性:
    - 智能元素查找 (主选择器 + 回退)
    - 人类化交互模拟
    - 元素状态验证
    - 交互性能监控
    - 错误恢复机制
    """
    
    def __init__(
        self,
        cdp_manager: CDPManagerProtocol,
        config_manager: ConfigManagerProtocol
    ):
        """
        初始化元素处理器
        
        Args:
            cdp_manager: CDP管理器
            config_manager: 配置管理器
        """
        self.cdp_manager = cdp_manager
        self.config_manager = config_manager
        
        # 交互配置
        self.default_timeout = 10.0
        self.retry_delay = 1.0
        self.typing_delay_range = (0.05, 0.15)  # 打字延迟范围
        
        # 性能统计
        self.interaction_stats = {
            "total_interactions": 0,
            "successful_interactions": 0,
            "failed_interactions": 0,
            "element_find_time": 0.0,
            "interaction_time": 0.0
        }
    
    @retry_on_failure(max_attempts=3, base_delay=1.0)
    @with_error_context(operation="fill_email")
    async def fill_email(self, email: str) -> bool:
        """
        填写邮箱输入框
        
        Args:
            email: 邮箱地址
            
        Returns:
            bool: 填写是否成功
        """
        try:
            logger.debug(f"✏️ 填写邮箱: {email[:5]}***")
            
            # 获取邮箱输入框选择器
            selector_config = self.config_manager.get_selector("email_input")
            
            # 查找并填写元素
            filled = await self._fill_input_element(
                selector_config, 
                email,
                element_name="邮箱输入框"
            )
            
            if filled:
                logger.info("✅ 邮箱填写成功")
                return True
            else:
                logger.error("❌ 邮箱填写失败")
                return False
                
        except Exception as e:
            logger.error(f"❌ 填写邮箱时出错: {e}")
            return False
    
    @retry_on_failure(max_attempts=3, base_delay=1.0)
    @with_error_context(operation="fill_password")
    async def fill_password(self, password: str) -> bool:
        """
        填写密码输入框
        
        Args:
            password: 密码
            
        Returns:
            bool: 填写是否成功
        """
        try:
            logger.debug("🔑 填写密码")
            
            # 获取密码输入框选择器
            selector_config = self.config_manager.get_selector("password_input")
            
            # 查找并填写元素
            filled = await self._fill_input_element(
                selector_config,
                password,
                element_name="密码输入框",
                secure=True  # 安全模式，不记录实际密码
            )
            
            if filled:
                logger.info("✅ 密码填写成功")
                return True
            else:
                logger.error("❌ 密码填写失败")
                return False
                
        except Exception as e:
            logger.error(f"❌ 填写密码时出错: {e}")
            return False
    
    @retry_on_failure(max_attempts=3, base_delay=1.0)
    @with_error_context(operation="fill_verification_code")
    async def fill_verification_code(self, code: str) -> bool:
        """
        填写验证码输入框
        
        Args:
            code: 验证码
            
        Returns:
            bool: 填写是否成功
        """
        try:
            logger.debug(f"🔢 填写验证码: {code[:2]}***")
            
            # 获取验证码输入框选择器
            selector_config = self.config_manager.get_selector("verification_input")
            
            # 查找并填写元素
            filled = await self._fill_input_element(
                selector_config,
                code,
                element_name="验证码输入框"
            )
            
            if filled:
                logger.info("✅ 验证码填写成功")
                return True
            else:
                logger.error("❌ 验证码填写失败")
                return False
                
        except Exception as e:
            logger.error(f"❌ 填写验证码时出错: {e}")
            return False
    
    @retry_on_failure(max_attempts=3, base_delay=1.0)
    @with_error_context(operation="click_login_button")
    async def click_login_button(self) -> bool:
        """
        点击登录按钮
        
        Returns:
            bool: 点击是否成功
        """
        try:
            logger.debug("🔘 点击登录按钮")
            
            # 获取登录按钮选择器
            selector_config = self.config_manager.get_selector("login_button")
            
            # 查找并点击元素
            clicked = await self._click_element(
                selector_config,
                element_name="登录按钮"
            )
            
            if clicked:
                logger.info("✅ 登录按钮点击成功")
                return True
            else:
                logger.error("❌ 登录按钮点击失败")
                return False
                
        except Exception as e:
            logger.error(f"❌ 点击登录按钮时出错: {e}")
            return False
    
    @retry_on_failure(max_attempts=3, base_delay=1.0)
    @with_error_context(operation="click_verify_button")
    async def click_verify_button(self) -> bool:
        """
        点击验证按钮
        
        Returns:
            bool: 点击是否成功
        """
        try:
            logger.debug("🔘 点击验证按钮")
            
            # 获取验证按钮选择器 (可能是continue_button)
            selector_config = self.config_manager.get_selector("continue_button")
            
            # 查找并点击元素
            clicked = await self._click_element(
                selector_config,
                element_name="验证按钮"
            )
            
            if clicked:
                logger.info("✅ 验证按钮点击成功")
                return True
            else:
                logger.error("❌ 验证按钮点击失败")
                return False
                
        except Exception as e:
            logger.error(f"❌ 点击验证按钮时出错: {e}")
            return False
    
    async def wait_for_element(self, selector: str, timeout: float = 10.0) -> bool:
        """
        等待元素出现
        
        Args:
            selector: 元素选择器
            timeout: 超时时间
            
        Returns:
            bool: 元素是否出现
        """
        try:
            return await self.cdp_manager.wait_for_selector(selector, timeout)
        except Exception as e:
            logger.error(f"❌ 等待元素失败: {e}")
            return False
    
    async def wait_for_verification_form(self, timeout: float = 10.0) -> bool:
        """
        等待验证表单出现
        
        Args:
            timeout: 超时时间
            
        Returns:
            bool: 验证表单是否出现
        """
        try:
            # 获取验证输入框选择器
            selector_config = self.config_manager.get_selector("verification_input")
            primary_selector = selector_config["primary"]
            
            return await self.wait_for_element(primary_selector, timeout)
            
        except Exception as e:
            logger.error(f"❌ 等待验证表单失败: {e}")
            return False
    
    async def _fill_input_element(
        self,
        selector_config: Dict[str, Any],
        text: str,
        element_name: str,
        secure: bool = False
    ) -> bool:
        """
        填写输入框元素
        
        Args:
            selector_config: 选择器配置
            text: 要填写的文本
            element_name: 元素名称(用于日志)
            secure: 是否为敏感信息
            
        Returns:
            bool: 填写是否成功
        """
        start_time = datetime.now()
        
        try:
            # 查找元素
            selector = await self._find_element(selector_config, element_name)
            if not selector:
                return False
            
            # 清空输入框
            await self._clear_input(selector)
            
            # 人类化打字
            await self._human_type(selector, text, secure)
            
            # 更新统计
            interaction_time = (datetime.now() - start_time).total_seconds()
            self._update_success_stats(interaction_time)
            
            return True
            
        except Exception as e:
            interaction_time = (datetime.now() - start_time).total_seconds()
            self._update_failure_stats(interaction_time)
            
            raise ElementInteractionError(
                f"填写{element_name}失败: {e}",
                error_code="INPUT_FILL_FAILED",
                context={
                    "element_name": element_name,
                    "selector_config": selector_config,
                    "secure": secure
                }
            ) from e
    
    async def _click_element(
        self,
        selector_config: Dict[str, Any],
        element_name: str
    ) -> bool:
        """
        点击元素
        
        Args:
            selector_config: 选择器配置
            element_name: 元素名称
            
        Returns:
            bool: 点击是否成功
        """
        start_time = datetime.now()
        
        try:
            # 查找元素
            selector = await self._find_element(selector_config, element_name)
            if not selector:
                return False
            
            # 人类化点击
            await self._human_click(selector)
            
            # 更新统计
            interaction_time = (datetime.now() - start_time).total_seconds()
            self._update_success_stats(interaction_time)
            
            return True
            
        except Exception as e:
            interaction_time = (datetime.now() - start_time).total_seconds()
            self._update_failure_stats(interaction_time)
            
            raise ElementInteractionError(
                f"点击{element_name}失败: {e}",
                error_code="ELEMENT_CLICK_FAILED",
                context={
                    "element_name": element_name,
                    "selector_config": selector_config
                }
            ) from e
    
    async def _find_element(
        self,
        selector_config: Dict[str, Any],
        element_name: str
    ) -> Optional[str]:
        """
        查找元素
        
        Args:
            selector_config: 选择器配置
            element_name: 元素名称
            
        Returns:
            Optional[str]: 找到的选择器，未找到返回None
        """
        primary_selector = selector_config["primary"]
        fallback_selectors = selector_config.get("fallback", [])
        contract = selector_config.get("contract", {})
        timeout = contract.get("timeout_ms", self.default_timeout * 1000) / 1000
        
        # 尝试主选择器
        if await self.cdp_manager.wait_for_selector(primary_selector, timeout):
            logger.debug(f"✅ 找到{element_name}: {primary_selector}")
            return primary_selector
        
        # 尝试回退选择器
        for fallback_selector in fallback_selectors:
            if await self.cdp_manager.wait_for_selector(fallback_selector, timeout / 2):
                logger.debug(f"✅ 找到{element_name}(回退): {fallback_selector}")
                return fallback_selector
        
        # 未找到元素
        logger.error(f"❌ 未找到{element_name}")
        raise ElementNotFoundError(
            f"未找到{element_name}",
            error_code="ELEMENT_NOT_FOUND",
            context={
                "element_name": element_name,
                "primary_selector": primary_selector,
                "fallback_selectors": fallback_selectors
            }
        )
    
    async def _clear_input(self, selector: str) -> None:
        """清空输入框"""
        # 全选并删除
        await self.cdp_manager.evaluate_js(f'''
            document.querySelector("{selector}").select();
        ''')
        await asyncio.sleep(0.1)
        
        await self.cdp_manager.evaluate_js(f'''
            document.querySelector("{selector}").value = "";
        ''')
    
    async def _human_type(self, selector: str, text: str, secure: bool = False) -> None:
        """
        人类化打字
        
        Args:
            selector: 元素选择器
            text: 要输入的文本
            secure: 是否为敏感信息
        """
        typing_config = self.config_manager.get_typing_config()
        base_delay = typing_config.get("base_delay", 0.1)
        delay_variation = typing_config.get("delay_variation", 0.05)
        
        for char in text:
            # 计算随机延迟
            delay = base_delay + random.uniform(-delay_variation, delay_variation)
            
            # 输入字符
            await self.cdp_manager.type_text(selector, char, delay=0)
            
            # 人类化延迟
            await asyncio.sleep(delay)
        
        # 记录日志
        if secure:
            logger.debug(f"🔤 人类化输入完成: ***")
        else:
            logger.debug(f"🔤 人类化输入完成: {text[:10]}{'...' if len(text) > 10 else ''}")
    
    async def _human_click(self, selector: str) -> None:
        """
        人类化点击
        
        Args:
            selector: 元素选择器
        """
        # 短暂延迟模拟鼠标移动
        await asyncio.sleep(random.uniform(0.1, 0.3))
        
        # 执行点击
        clicked = await self.cdp_manager.click_element(selector)
        if not clicked:
            raise ElementInteractionError(
                f"点击元素失败: {selector}",
                error_code="CLICK_FAILED"
            )
        
        # 点击后延迟
        await asyncio.sleep(random.uniform(0.2, 0.5))
    
    def _update_success_stats(self, interaction_time: float) -> None:
        """更新成功统计"""
        self.interaction_stats["total_interactions"] += 1
        self.interaction_stats["successful_interactions"] += 1
        self.interaction_stats["interaction_time"] = interaction_time
    
    def _update_failure_stats(self, interaction_time: float) -> None:
        """更新失败统计"""
        self.interaction_stats["total_interactions"] += 1
        self.interaction_stats["failed_interactions"] += 1
        self.interaction_stats["interaction_time"] = interaction_time
    
    def get_handler_stats(self) -> Dict[str, Any]:
        """获取处理器统计信息"""
        total = max(self.interaction_stats["total_interactions"], 1)
        
        return {
            "handler_name": "ElementHandler",
            "interaction_stats": self.interaction_stats.copy(),
            "success_rate": self.interaction_stats["successful_interactions"] / total,
            "config": {
                "default_timeout": self.default_timeout,
                "retry_delay": self.retry_delay,
                "typing_delay_range": self.typing_delay_range
            }
        }
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        return {
            "handler": "ElementHandler",
            "status": "healthy",
            "dependencies": {
                "cdp_manager": "connected",
                "config_manager": "ready"
            },
            "stats": self.get_handler_stats(),
            "timestamp": datetime.now().isoformat()
        }