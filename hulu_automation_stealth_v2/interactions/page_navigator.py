"""
页面导航器 - 页面导航和状态管理

负责页面跳转、状态检测和导航流程控制。
"""

import asyncio
import logging
from typing import Dict, Any, Optional, List, Protocol
from datetime import datetime
from urllib.parse import urlparse

from ..core.error_handler import (
    NavigationError, retry_on_failure, with_error_context
)

logger = logging.getLogger(__name__)


class CDPManagerProtocol(Protocol):
    """CDP管理器协议"""
    async def navigate_to_url(self, url: str) -> Dict[str, Any]: ...
    async def wait_for_page_load(self) -> bool: ...
    async def get_page_info(self) -> Dict[str, Any]: ...
    async def evaluate_js(self, script: str) -> Any: ...
    async def go_back(self) -> bool: ...
    async def refresh_page(self) -> bool: ...


class PageNavigator:
    """
    页面导航器
    
    特性:
    - 智能页面导航
    - 页面状态检测
    - 导航历史管理
    - 页面加载优化
    - 导航错误恢复
    """
    
    def __init__(self, cdp_manager: CDPManagerProtocol):
        """
        初始化页面导航器
        
        Args:
            cdp_manager: CDP管理器
        """
        self.cdp_manager = cdp_manager
        
        # 导航配置
        self.page_load_timeout = 30.0
        self.navigation_timeout = 15.0
        self.retry_delay = 2.0
        
        # 页面状态
        self.current_page = None
        self.navigation_history = []
        self.page_states = {}
        
        # Hulu相关URL
        self.hulu_urls = {
            "home": "https://www.hulu.com",
            "login": "https://www.hulu.com/login",
            "welcome": "https://www.hulu.com/welcome",
            "profiles": "https://www.hulu.com/profiles",
            "hub": "https://www.hulu.com/hub"
        }
        
        # 性能统计
        self.navigation_stats = {
            "total_navigations": 0,
            "successful_navigations": 0,
            "failed_navigations": 0,
            "avg_navigation_time": 0.0,
            "page_load_failures": 0
        }
    
    @retry_on_failure(max_attempts=3, base_delay=2.0)
    @with_error_context(operation="navigate_to_login")
    async def navigate_to_login(self) -> Dict[str, Any]:
        """
        导航到登录页面
        
        Returns:
            Dict[str, Any]: 导航结果
            
        Raises:
            NavigationError: 导航失败
        """
        start_time = datetime.now()
        
        try:
            logger.info("🧭 导航到登录页面")
            
            # 执行导航
            nav_result = await self._navigate_to_page("login", self.hulu_urls["login"])
            
            # 验证登录页面
            page_verification = await self._verify_login_page()
            
            # 更新导航历史
            self._update_navigation_history("login", nav_result)
            
            # 更新成功统计
            execution_time = (datetime.now() - start_time).total_seconds()
            await self._update_success_stats(execution_time)
            
            logger.info(f"✅ 登录页面导航成功 - 耗时 {execution_time:.2f}s")
            
            return {
                "success": True,
                "page_type": "login",
                "url": self.hulu_urls["login"],
                "navigation_result": nav_result,
                "page_verification": page_verification,
                "execution_time": execution_time,
                "timestamp": start_time.isoformat()
            }
            
        except Exception as e:
            execution_time = (datetime.now() - start_time).total_seconds()
            await self._update_failure_stats(execution_time)
            
            logger.error(f"❌ 登录页面导航失败: {e}")
            
            raise NavigationError(
                f"导航到登录页面失败: {e}",
                error_code="LOGIN_NAVIGATION_FAILED",
                context={
                    "target_url": self.hulu_urls["login"],
                    "execution_time": execution_time
                }
            ) from e
    
    @retry_on_failure(max_attempts=2, base_delay=3.0)
    @with_error_context(operation="check_login_success")
    async def check_login_success(self) -> Dict[str, Any]:
        """
        检查登录是否成功
        
        Returns:
            Dict[str, Any]: 登录状态检查结果
            
        Raises:
            NavigationError: 检查失败
        """
        try:
            logger.info("🔍 检查登录状态")
            
            # 等待页面稳定
            await asyncio.sleep(3.0)
            
            # 获取当前页面信息
            page_info = await self.cdp_manager.get_page_info()
            current_url = page_info.get("url", "")
            
            # 分析URL判断登录状态
            login_status = await self._analyze_login_status(current_url)
            
            # 额外的页面内容检查
            content_check = await self._check_page_content()
            
            # 综合判断
            is_logged_in = login_status["is_logged_in"] and content_check["login_indicators"]
            
            result = {
                "success": True,
                "is_logged_in": is_logged_in,
                "current_url": current_url,
                "login_status": login_status,
                "content_check": content_check,
                "timestamp": datetime.now().isoformat()
            }
            
            if is_logged_in:
                logger.info(f"✅ 登录成功确认 - 当前页面: {login_status['page_type']}")
                self.current_page = login_status["page_type"]
            else:
                logger.warning(f"⚠️ 登录状态异常 - 当前URL: {current_url}")
            
            return result
            
        except Exception as e:
            logger.error(f"❌ 登录状态检查失败: {e}")
            
            raise NavigationError(
                f"登录状态检查失败: {e}",
                error_code="LOGIN_STATUS_CHECK_FAILED"
            ) from e
    
    async def wait_for_page_transition(self, expected_page: str, timeout: float = 15.0) -> bool:
        """
        等待页面跳转完成
        
        Args:
            expected_page: 期望的页面类型
            timeout: 超时时间
            
        Returns:
            bool: 是否成功跳转到期望页面
        """
        start_time = datetime.now()
        
        while (datetime.now() - start_time).total_seconds() < timeout:
            try:
                page_info = await self.cdp_manager.get_page_info()
                current_url = page_info.get("url", "")
                
                # 检查是否到达期望页面
                if self._is_expected_page(current_url, expected_page):
                    logger.info(f"✅ 页面跳转成功: {expected_page}")
                    self.current_page = expected_page
                    return True
                
                await asyncio.sleep(1.0)
                
            except Exception as e:
                logger.debug(f"页面检查时出错: {e}")
                await asyncio.sleep(1.0)
        
        logger.warning(f"⚠️ 页面跳转超时: {expected_page}")
        return False
    
    async def _navigate_to_page(self, page_type: str, url: str) -> Dict[str, Any]:
        """
        导航到指定页面
        
        Args:
            page_type: 页面类型
            url: 目标URL
            
        Returns:
            Dict[str, Any]: 导航结果
        """
        logger.debug(f"🌐 导航到 {page_type}: {url}")
        
        # 执行导航
        nav_result = await self.cdp_manager.navigate_to_url(url)
        if not nav_result.get("success", False):
            raise NavigationError(
                f"导航请求失败: {url}",
                error_code="NAVIGATION_REQUEST_FAILED",
                context=nav_result
            )
        
        # 等待页面加载
        page_loaded = await self.cdp_manager.wait_for_page_load()
        if not page_loaded:
            raise NavigationError(
                f"页面加载超时: {url}",
                error_code="PAGE_LOAD_TIMEOUT",
                context={"url": url, "timeout": self.page_load_timeout}
            )
        
        # 额外稳定时间
        await asyncio.sleep(2.0)
        
        return {
            "navigation_success": True,
            "page_loaded": True,
            "page_type": page_type,
            "url": url
        }
    
    async def _verify_login_page(self) -> Dict[str, Any]:
        """验证登录页面"""
        logger.debug("✅ 验证登录页面")
        
        try:
            page_info = await self.cdp_manager.get_page_info()
            current_url = page_info.get("url", "")
            page_title = page_info.get("title", "")
            
            # URL验证
            is_login_url = "login" in current_url.lower()
            
            # 标题验证
            is_login_title = any(keyword in page_title.lower() for keyword in ["login", "sign in", "hulu"])
            
            # 页面元素验证
            has_login_elements = await self._check_login_elements()
            
            verification_result = {
                "is_login_page": is_login_url and (is_login_title or has_login_elements),
                "url_check": is_login_url,
                "title_check": is_login_title,
                "elements_check": has_login_elements,
                "current_url": current_url,
                "page_title": page_title
            }
            
            if not verification_result["is_login_page"]:
                logger.warning(f"⚠️ 登录页面验证失败: {verification_result}")
            
            return verification_result
            
        except Exception as e:
            logger.error(f"❌ 登录页面验证出错: {e}")
            return {"is_login_page": False, "error": str(e)}
    
    async def _check_login_elements(self) -> bool:
        """检查登录页面元素"""
        try:
            # 检查登录表单元素
            elements_check = await self.cdp_manager.evaluate_js('''
                // 检查关键登录元素
                const emailInput = document.querySelector('input[type="email"], input[name*="email"], input[id*="email"]');
                const passwordInput = document.querySelector('input[type="password"], input[name*="password"], input[id*="password"]');
                const loginButton = document.querySelector('button[type="submit"], button:contains("Log In"), button:contains("Sign In")');
                
                return {
                    hasEmailInput: !!emailInput,
                    hasPasswordInput: !!passwordInput,
                    hasLoginButton: !!loginButton
                };
            ''')
            
            has_required_elements = (
                elements_check.get("hasEmailInput", False) and
                elements_check.get("hasPasswordInput", False)
            )
            
            return has_required_elements
            
        except Exception as e:
            logger.debug(f"登录元素检查失败: {e}")
            return False
    
    async def _analyze_login_status(self, current_url: str) -> Dict[str, Any]:
        """
        分析登录状态
        
        Args:
            current_url: 当前URL
            
        Returns:
            Dict[str, Any]: 登录状态分析结果
        """
        parsed_url = urlparse(current_url.lower())
        path = parsed_url.path
        
        # 页面类型判断
        if "login" in path:
            page_type = "login"
            is_logged_in = False
        elif any(keyword in path for keyword in ["welcome", "profiles", "hub", "home"]):
            if "welcome" in path:
                page_type = "welcome"
            elif "profiles" in path:
                page_type = "profiles"
            elif "hub" in path:
                page_type = "hub"
            else:
                page_type = "home"
            is_logged_in = True
        else:
            page_type = "unknown"
            is_logged_in = False
        
        return {
            "is_logged_in": is_logged_in,
            "page_type": page_type,
            "current_path": path,
            "analysis_method": "url_path"
        }
    
    async def _check_page_content(self) -> Dict[str, Any]:
        """检查页面内容指标"""
        try:
            # 检查登录指标
            content_indicators = await self.cdp_manager.evaluate_js('''
                // 检查页面内容指标
                const loginForm = document.querySelector('form[action*="login"], form:has(input[type="password"])');
                const userMenu = document.querySelector('[data-testid="user-menu"], .user-menu, [class*="profile"]');
                const welcomeMessage = document.querySelector('[class*="welcome"], [data-testid="welcome"]');
                const errorMessage = document.querySelector('.error, [class*="error"], [aria-live="assertive"]');
                
                return {
                    hasLoginForm: !!loginForm,
                    hasUserMenu: !!userMenu,
                    hasWelcomeMessage: !!welcomeMessage,
                    hasErrorMessage: !!errorMessage,
                    pageText: document.body.innerText.toLowerCase().slice(0, 500)
                };
            ''')
            
            # 基于内容判断登录状态
            login_indicators = (
                content_indicators.get("hasUserMenu", False) or
                content_indicators.get("hasWelcomeMessage", False) or
                (not content_indicators.get("hasLoginForm", True))
            )
            
            return {
                "login_indicators": login_indicators,
                "content_check": content_indicators,
                "has_errors": content_indicators.get("hasErrorMessage", False)
            }
            
        except Exception as e:
            logger.debug(f"页面内容检查失败: {e}")
            return {"login_indicators": False, "error": str(e)}
    
    def _is_expected_page(self, current_url: str, expected_page: str) -> bool:
        """检查是否为期望页面"""
        url_lower = current_url.lower()
        
        page_patterns = {
            "login": ["login"],
            "welcome": ["welcome"],
            "profiles": ["profiles", "profile"],
            "hub": ["hub", "home"],
            "home": ["hulu.com", "home"]
        }
        
        patterns = page_patterns.get(expected_page, [expected_page])
        return any(pattern in url_lower for pattern in patterns)
    
    def _update_navigation_history(self, page_type: str, nav_result: Dict[str, Any]) -> None:
        """更新导航历史"""
        history_entry = {
            "page_type": page_type,
            "url": nav_result.get("url"),
            "timestamp": datetime.now().isoformat(),
            "success": nav_result.get("navigation_success", False)
        }
        
        self.navigation_history.append(history_entry)
        
        # 保持历史记录长度
        if len(self.navigation_history) > 10:
            self.navigation_history.pop(0)
        
        self.current_page = page_type
    
    async def _update_success_stats(self, execution_time: float) -> None:
        """更新成功统计"""
        self.navigation_stats["total_navigations"] += 1
        self.navigation_stats["successful_navigations"] += 1
        
        # 更新平均时间
        if self.navigation_stats["total_navigations"] > 1:
            current_avg = self.navigation_stats["avg_navigation_time"]
            new_avg = (current_avg + execution_time) / 2
            self.navigation_stats["avg_navigation_time"] = new_avg
        else:
            self.navigation_stats["avg_navigation_time"] = execution_time
    
    async def _update_failure_stats(self, execution_time: float) -> None:
        """更新失败统计"""
        self.navigation_stats["total_navigations"] += 1
        self.navigation_stats["failed_navigations"] += 1
    
    def get_navigator_stats(self) -> Dict[str, Any]:
        """获取导航器统计信息"""
        total = max(self.navigation_stats["total_navigations"], 1)
        
        return {
            "navigator_name": "PageNavigator",
            "current_page": self.current_page,
            "navigation_history": self.navigation_history[-5:],  # 最近5次导航
            "navigation_stats": self.navigation_stats.copy(),
            "success_rate": self.navigation_stats["successful_navigations"] / total,
            "config": {
                "page_load_timeout": self.page_load_timeout,
                "navigation_timeout": self.navigation_timeout,
                "retry_delay": self.retry_delay
            }
        }
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        return {
            "navigator": "PageNavigator",
            "status": "healthy",
            "dependencies": {
                "cdp_manager": "connected"
            },
            "stats": self.get_navigator_stats(),
            "timestamp": datetime.now().isoformat()
        }