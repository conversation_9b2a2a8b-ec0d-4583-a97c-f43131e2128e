"""
表单处理器 - 表单操作协调

负责复杂表单的填写、验证和提交逻辑。
"""

import asyncio
import logging
from typing import Dict, Any, List, Optional, Protocol
from datetime import datetime

from ..core.error_handler import (
    FormInteractionError, retry_on_failure, with_error_context
)

logger = logging.getLogger(__name__)


class ElementHandlerProtocol(Protocol):
    """元素处理器协议"""
    async def fill_email(self, email: str) -> bool: ...
    async def fill_password(self, password: str) -> bool: ...
    async def fill_verification_code(self, code: str) -> bool: ...
    async def click_login_button(self) -> bool: ...
    async def click_verify_button(self) -> bool: ...
    async def wait_for_element(self, selector: str, timeout: float = 10.0) -> bool: ...


class CDPManagerProtocol(Protocol):
    """CDP管理器协议"""
    async def get_page_info(self) -> Dict[str, Any]: ...
    async def evaluate_js(self, script: str) -> Any: ...
    async def take_screenshot(self, filename: str = None) -> str: ...


class FormHandler:
    """
    表单处理器
    
    特性:
    - 复杂表单工作流协调
    - 表单验证和错误检测
    - 自适应表单处理
    - 表单状态跟踪
    - 截图证据收集
    """
    
    def __init__(
        self,
        element_handler: ElementHandlerProtocol,
        cdp_manager: CDPManagerProtocol
    ):
        """
        初始化表单处理器
        
        Args:
            element_handler: 元素处理器
            cdp_manager: CDP管理器
        """
        self.element_handler = element_handler
        self.cdp_manager = cdp_manager
        
        # 表单配置
        self.form_timeout = 30.0
        self.step_delay = 1.0
        self.validation_delay = 2.0
        
        # 表单状态
        self.current_form_step = None
        self.form_errors = []
        
        # 性能统计
        self.form_stats = {
            "total_forms": 0,
            "successful_forms": 0,
            "failed_forms": 0,
            "avg_form_time": 0.0,
            "validation_errors": 0
        }
    
    @retry_on_failure(max_attempts=2, base_delay=2.0)
    @with_error_context(operation="process_login_form")
    async def process_login_form(
        self,
        email: str,
        password: str,
        screenshot_path: str = None
    ) -> Dict[str, Any]:
        """
        处理登录表单
        
        Args:
            email: 邮箱地址
            password: 密码
            screenshot_path: 截图保存路径
            
        Returns:
            Dict[str, Any]: 表单处理结果
            
        Raises:
            FormInteractionError: 表单处理失败
        """
        start_time = datetime.now()
        self.current_form_step = "login_form"
        
        try:
            logger.info("📝 开始处理登录表单")
            
            # 步骤1: 表单预检查
            await self._validate_login_form()
            
            # 步骤2: 填写表单字段
            form_data = await self._fill_login_form(email, password)
            
            # 步骤3: 表单验证
            validation_result = await self._validate_form_data()
            
            # 步骤4: 提交表单
            submission_result = await self._submit_login_form()
            
            # 步骤5: 收集证据
            evidence = await self._collect_form_evidence(screenshot_path)
            
            # 更新成功统计
            execution_time = (datetime.now() - start_time).total_seconds()
            await self._update_success_stats(execution_time)
            
            logger.info(f"✅ 登录表单处理成功 - 耗时 {execution_time:.2f}s")
            
            return {
                "success": True,
                "form_type": "login_form",
                "form_data": form_data,
                "validation_result": validation_result,
                "submission_result": submission_result,
                "evidence": evidence,
                "execution_time": execution_time,
                "timestamp": start_time.isoformat()
            }
            
        except Exception as e:
            execution_time = (datetime.now() - start_time).total_seconds()
            await self._update_failure_stats(execution_time)
            
            logger.error(f"❌ 登录表单处理失败: {e}")
            
            # 收集错误证据
            error_evidence = await self._collect_error_evidence(screenshot_path)
            
            raise FormInteractionError(
                f"登录表单处理失败: {e}",
                error_code="LOGIN_FORM_FAILED",
                context={
                    "form_step": self.current_form_step,
                    "form_errors": self.form_errors,
                    "execution_time": execution_time,
                    "error_evidence": error_evidence
                }
            ) from e
        
        finally:
            self.current_form_step = None
            self.form_errors.clear()
    
    @retry_on_failure(max_attempts=2, base_delay=2.0)
    @with_error_context(operation="process_verification_form")
    async def process_verification_form(
        self,
        verification_code: str,
        screenshot_path: str = None
    ) -> Dict[str, Any]:
        """
        处理验证表单
        
        Args:
            verification_code: 验证码
            screenshot_path: 截图保存路径
            
        Returns:
            Dict[str, Any]: 表单处理结果
            
        Raises:
            FormInteractionError: 表单处理失败
        """
        start_time = datetime.now()
        self.current_form_step = "verification_form"
        
        try:
            logger.info("📝 开始处理验证表单")
            
            # 步骤1: 等待验证表单出现
            await self._wait_for_verification_form()
            
            # 步骤2: 填写验证码
            form_data = await self._fill_verification_form(verification_code)
            
            # 步骤3: 验证输入
            validation_result = await self._validate_verification_input()
            
            # 步骤4: 提交验证
            submission_result = await self._submit_verification_form()
            
            # 步骤5: 收集证据
            evidence = await self._collect_form_evidence(screenshot_path)
            
            # 更新成功统计
            execution_time = (datetime.now() - start_time).total_seconds()
            await self._update_success_stats(execution_time)
            
            logger.info(f"✅ 验证表单处理成功 - 耗时 {execution_time:.2f}s")
            
            return {
                "success": True,
                "form_type": "verification_form",
                "form_data": form_data,
                "validation_result": validation_result,
                "submission_result": submission_result,
                "evidence": evidence,
                "execution_time": execution_time,
                "timestamp": start_time.isoformat()
            }
            
        except Exception as e:
            execution_time = (datetime.now() - start_time).total_seconds()
            await self._update_failure_stats(execution_time)
            
            logger.error(f"❌ 验证表单处理失败: {e}")
            
            # 收集错误证据
            error_evidence = await self._collect_error_evidence(screenshot_path)
            
            raise FormInteractionError(
                f"验证表单处理失败: {e}",
                error_code="VERIFICATION_FORM_FAILED",
                context={
                    "form_step": self.current_form_step,
                    "form_errors": self.form_errors,
                    "execution_time": execution_time,
                    "error_evidence": error_evidence
                }
            ) from e
        
        finally:
            self.current_form_step = None
            self.form_errors.clear()
    
    async def _validate_login_form(self) -> None:
        """验证登录表单存在"""
        logger.debug("🔍 验证登录表单")
        
        page_info = await self.cdp_manager.get_page_info()
        current_url = page_info.get("url", "")
        
        if "login" not in current_url.lower():
            raise FormInteractionError(
                "当前页面不是登录页面",
                error_code="NOT_LOGIN_PAGE",
                context={"current_url": current_url}
            )
    
    async def _fill_login_form(self, email: str, password: str) -> Dict[str, Any]:
        """填写登录表单"""
        logger.debug("✏️ 填写登录表单字段")
        
        form_data = {"fields_filled": []}
        
        # 填写邮箱
        self.current_form_step = "fill_email"
        email_filled = await self.element_handler.fill_email(email)
        if not email_filled:
            self.form_errors.append("邮箱填写失败")
            raise FormInteractionError("邮箱填写失败", error_code="EMAIL_FILL_FAILED")
        
        form_data["fields_filled"].append("email")
        await asyncio.sleep(self.step_delay)
        
        # 填写密码
        self.current_form_step = "fill_password"
        password_filled = await self.element_handler.fill_password(password)
        if not password_filled:
            self.form_errors.append("密码填写失败")
            raise FormInteractionError("密码填写失败", error_code="PASSWORD_FILL_FAILED")
        
        form_data["fields_filled"].append("password")
        
        return form_data
    
    async def _validate_form_data(self) -> Dict[str, Any]:
        """验证表单数据"""
        logger.debug("✅ 验证表单数据")
        
        # 简单的前端验证检查
        validation_result = {
            "client_side_validation": "passed",
            "validation_errors": [],
            "timestamp": datetime.now().isoformat()
        }
        
        # 检查是否有客户端验证错误
        try:
            js_errors = await self.cdp_manager.evaluate_js('''
                // 检查表单验证错误
                const errors = [];
                const errorElements = document.querySelectorAll('.error, .invalid, [aria-invalid="true"]');
                errorElements.forEach(el => {
                    errors.push(el.textContent.trim());
                });
                return errors;
            ''')
            
            if js_errors:
                validation_result["validation_errors"] = js_errors
                validation_result["client_side_validation"] = "failed"
                self.form_stats["validation_errors"] += len(js_errors)
                
        except Exception as e:
            logger.debug(f"无法检查客户端验证: {e}")
        
        await asyncio.sleep(self.validation_delay)
        return validation_result
    
    async def _submit_login_form(self) -> Dict[str, Any]:
        """提交登录表单"""
        logger.debug("🚀 提交登录表单")
        
        self.current_form_step = "submit_form"
        
        # 点击登录按钮
        button_clicked = await self.element_handler.click_login_button()
        if not button_clicked:
            self.form_errors.append("登录按钮点击失败")
            raise FormInteractionError("登录按钮点击失败", error_code="LOGIN_BUTTON_FAILED")
        
        # 等待表单提交响应
        await asyncio.sleep(3.0)
        
        return {
            "form_submitted": True,
            "timestamp": datetime.now().isoformat()
        }
    
    async def _wait_for_verification_form(self) -> None:
        """等待验证表单出现"""
        logger.debug("⏳ 等待验证表单")
        
        form_appeared = await self.element_handler.wait_for_verification_form(
            timeout=self.form_timeout
        )
        
        if not form_appeared:
            raise FormInteractionError(
                "验证表单未出现",
                error_code="VERIFICATION_FORM_NOT_FOUND"
            )
    
    async def _fill_verification_form(self, verification_code: str) -> Dict[str, Any]:
        """填写验证表单"""
        logger.debug("✏️ 填写验证码")
        
        self.current_form_step = "fill_verification_code"
        
        code_filled = await self.element_handler.fill_verification_code(verification_code)
        if not code_filled:
            self.form_errors.append("验证码填写失败")
            raise FormInteractionError("验证码填写失败", error_code="VERIFICATION_CODE_FILL_FAILED")
        
        return {
            "fields_filled": ["verification_code"],
            "code_length": len(verification_code)
        }
    
    async def _validate_verification_input(self) -> Dict[str, Any]:
        """验证验证码输入"""
        logger.debug("✅ 验证验证码输入")
        
        await asyncio.sleep(self.validation_delay)
        
        return {
            "input_validation": "passed",
            "timestamp": datetime.now().isoformat()
        }
    
    async def _submit_verification_form(self) -> Dict[str, Any]:
        """提交验证表单"""
        logger.debug("🚀 提交验证表单")
        
        self.current_form_step = "submit_verification"
        
        # 点击验证按钮
        button_clicked = await self.element_handler.click_verify_button()
        if not button_clicked:
            self.form_errors.append("验证按钮点击失败")
            raise FormInteractionError("验证按钮点击失败", error_code="VERIFY_BUTTON_FAILED")
        
        # 等待验证响应
        await asyncio.sleep(3.0)
        
        return {
            "verification_submitted": True,
            "timestamp": datetime.now().isoformat()
        }
    
    async def _collect_form_evidence(self, screenshot_path: str = None) -> Dict[str, Any]:
        """收集表单处理证据"""
        evidence = {
            "page_info": {},
            "screenshot": None,
            "timestamp": datetime.now().isoformat()
        }
        
        try:
            # 获取页面信息
            evidence["page_info"] = await self.cdp_manager.get_page_info()
            
            # 截图
            if screenshot_path:
                screenshot_file = await self.cdp_manager.take_screenshot(screenshot_path)
                evidence["screenshot"] = screenshot_file
                
        except Exception as e:
            logger.warning(f"⚠️ 收集证据时出错: {e}")
        
        return evidence
    
    async def _collect_error_evidence(self, screenshot_path: str = None) -> Dict[str, Any]:
        """收集错误证据"""
        error_evidence = {
            "page_info": {},
            "form_errors": self.form_errors.copy(),
            "current_step": self.current_form_step,
            "screenshot": None,
            "timestamp": datetime.now().isoformat()
        }
        
        try:
            # 获取页面信息
            error_evidence["page_info"] = await self.cdp_manager.get_page_info()
            
            # 错误截图
            if screenshot_path:
                error_screenshot = screenshot_path.replace(".png", "_error.png")
                screenshot_file = await self.cdp_manager.take_screenshot(error_screenshot)
                error_evidence["screenshot"] = screenshot_file
                
        except Exception as e:
            logger.warning(f"⚠️ 收集错误证据时出错: {e}")
        
        return error_evidence
    
    async def _update_success_stats(self, execution_time: float) -> None:
        """更新成功统计"""
        self.form_stats["total_forms"] += 1
        self.form_stats["successful_forms"] += 1
        
        # 更新平均时间
        if self.form_stats["total_forms"] > 1:
            current_avg = self.form_stats["avg_form_time"]
            new_avg = (current_avg + execution_time) / 2
            self.form_stats["avg_form_time"] = new_avg
        else:
            self.form_stats["avg_form_time"] = execution_time
    
    async def _update_failure_stats(self, execution_time: float) -> None:
        """更新失败统计"""
        self.form_stats["total_forms"] += 1
        self.form_stats["failed_forms"] += 1
    
    def get_handler_stats(self) -> Dict[str, Any]:
        """获取处理器统计信息"""
        total = max(self.form_stats["total_forms"], 1)
        
        return {
            "handler_name": "FormHandler",
            "form_stats": self.form_stats.copy(),
            "success_rate": self.form_stats["successful_forms"] / total,
            "current_form_step": self.current_form_step,
            "config": {
                "form_timeout": self.form_timeout,
                "step_delay": self.step_delay,
                "validation_delay": self.validation_delay
            }
        }
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        return {
            "handler": "FormHandler",
            "status": "healthy",
            "dependencies": {
                "element_handler": "ready",
                "cdp_manager": "connected"
            },
            "stats": self.get_handler_stats(),
            "timestamp": datetime.now().isoformat()
        }