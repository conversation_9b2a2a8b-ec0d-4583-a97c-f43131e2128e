# 契约化选择器配置 - 版本化和环境分层
# 
# 版本控制：每次更新需要更新版本号和变更记录
# 环境分层：支持production、staging、development环境
# 契约验证：包含选择器的预期行为和验证规则

schema_version: "1.0.0"
version: "2.1.0"
target_domain: "hulu.com"
domain_signature: "sha256:f8e7d6c5b4a3928176eca0b9d8c7f6e5"  # 页面特征校验码
last_updated: "2025-01-15"
environment_compatibility:
  production: true
  staging: true
  development: true

# 生产环境选择器配置
environments:
  production:
    # 登录按钮选择器
    login_button:
      primary: '[class*="hx-core-auth"] button:has-text("Log In")'
      fallback:
        - '[data-testid="login-button"]'
        - 'button:has-text("Log In")'
        - 'input[type="submit"][value*="Log"]'
        - '.login-btn, .signin-btn'
      contract:
        timeout_ms: 5000
        must_be_visible: true
        must_be_enabled: true
        should_contain_text: ["Log In", "Sign In"]
      validation:
        min_elements: 1
        max_elements: 3
        element_type: "button"
    
    # 邮箱输入框选择器
    email_input:
      primary: '[data-testid="email-input"]'
      fallback:
        - 'input[type="email"]'
        - '[placeholder*="email" i]'
        - '[name="email"], [name="username"]'
        - '#email, #username'
      contract:
        timeout_ms: 3000
        must_be_visible: true
        must_be_editable: true
        input_type: "email"
      validation:
        min_elements: 1
        max_elements: 1
        element_type: "input"
        required_attributes: ["type", "name"]
    
    # 密码输入框选择器
    password_input:
      primary: '[data-testid="password-input"]'
      fallback:
        - 'input[type="password"]'
        - '[name="password"]'
        - '#password'
      contract:
        timeout_ms: 3000
        must_be_visible: true
        must_be_editable: true
        input_type: "password"
      validation:
        min_elements: 1
        max_elements: 1
        element_type: "input"
        required_attributes: ["type"]
    
    # 验证码输入框选择器
    verification_input:
      primary: '[data-testid="verification-code"]'
      fallback:
        - 'input[placeholder*="code" i]'
        - 'input[placeholder*="verification" i]'
        - '[name*="code"], [name*="verification"]'
        - '.verification-input input'
      contract:
        timeout_ms: 8000  # 验证码页面加载较慢
        must_be_visible: true
        must_be_editable: true
        input_type: "text"
      validation:
        min_elements: 1
        max_elements: 1
        element_type: "input"
    
    # 继续按钮选择器
    continue_button:
      primary: '[class*="hx-core-auth"] button:has-text("Continue")'
      fallback:
        - 'button:has-text("Continue")'
        - 'input[type="submit"][value*="Continue"]'
        - '.continue-btn, .next-btn'
      contract:
        timeout_ms: 5000
        must_be_visible: true
        must_be_enabled: true
        should_contain_text: ["Continue", "Next"]
      validation:
        min_elements: 1
        max_elements: 2
        element_type: "button"
    
    # 获取两者按钮选择器 (Hulu特定)
    get_both_button:
      primary: '[class*="button"]:has-text("GET THEM BOTH")'
      fallback:
        - '[class*="cta"]:has-text("GET THEM BOTH")'
        - 'button:has-text("GET THEM BOTH")'
        - '[data-testid="get-both-cta"]'
      contract:
        timeout_ms: 10000
        must_be_visible: true
        must_be_enabled: true
        should_contain_text: ["GET THEM BOTH"]
      validation:
        min_elements: 0  # 可选元素
        max_elements: 1
        element_type: "button"
        optional: true
    
    # 用户档案图标选择器
    profile_icon:
      primary: '[data-testid="profile-icon"]'
      fallback:
        - '.profile-icon, .user-icon'
        - '[class*="profile"], [class*="avatar"]'
        - 'img[alt*="profile" i]'
      contract:
        timeout_ms: 8000
        must_be_visible: true
        element_type: ["button", "img", "div"]
      validation:
        min_elements: 0
        max_elements: 1
        optional: true

  # 测试环境配置 (可能有不同的选择器)
  staging:
    login_button:
      primary: '[data-testid="staging-login-btn"]'
      fallback:
        - '[class*="hx-core-auth"] button:has-text("Log In")'
        - 'button:has-text("Log In")'
      contract:
        timeout_ms: 5000
        must_be_visible: true
        must_be_enabled: true
      validation:
        min_elements: 1
        max_elements: 2
        element_type: "button"

  # 开发环境配置 (可能包含调试选择器)
  development:
    login_button:
      primary: '[data-testid="dev-login-btn"]'
      fallback:
        - '[class*="hx-core-auth"] button:has-text("Log In")'
        - 'button:has-text("Log In")'
      contract:
        timeout_ms: 10000  # 开发环境通常较慢
        must_be_visible: true
        must_be_enabled: true
      validation:
        min_elements: 1
        max_elements: 3
        element_type: "button"

# 全局回退策略配置
fallback_strategy:
  max_selector_attempts: 3
  selector_timeout_ms: 5000
  retry_delay_ms: 1000
  enable_fuzzy_matching: true
  fuzzy_similarity_threshold: 0.8
  
  # 智能回退规则
  smart_fallback:
    # 如果主选择器失败，是否尝试相似元素
    try_similar_elements: true
    # 相似度计算权重
    text_similarity_weight: 0.4
    class_similarity_weight: 0.3
    position_similarity_weight: 0.3

# 性能优化配置
performance:
  enable_selector_caching: true
  cache_ttl_seconds: 300
  parallel_selector_search: true
  max_parallel_searches: 3
  
  # 预加载策略
  preload_selectors:
    - "login_button"
    - "email_input"
    - "password_input"

# 契约验证规则
validation_rules:
  # 全局验证规则
  global:
    max_search_time_ms: 15000
    require_visible_elements: true
    allow_hidden_fallbacks: false
    validate_element_type: true
    
  # 特定类型验证
  input_elements:
    must_be_interactable: true
    check_readonly_attribute: true
    validate_input_type: true
    
  button_elements:
    must_be_clickable: true
    check_disabled_attribute: true
    validate_button_text: true

# 变更记录 (用于追踪选择器演进)
change_log:
  - version: "2.1.0"
    date: "2025-01-15"
    author: "refactor-team"
    changes: 
      - "添加契约验证和domain_signature"
      - "增强fallback策略和智能匹配"
      - "添加性能优化配置"
    breaking_changes: false
    affected_selectors: ["all"]
    
  - version: "2.0.0"
    date: "2025-01-10"
    author: "refactor-team"
    changes:
      - "重构为YAML格式配置"
      - "添加环境分层支持"
      - "增加验证规则和回退策略"
    breaking_changes: true
    affected_selectors: ["all"]
    migration_notes: "需要更新配置加载逻辑"

# 监控和告警配置
monitoring:
  # 选择器健康检查
  health_check:
    enabled: true
    check_interval_minutes: 30
    failure_threshold: 3
    alert_on_failure: true
    
  # 性能监控
  performance_monitoring:
    enabled: true
    slow_selector_threshold_ms: 5000
    track_success_rate: true
    generate_usage_reports: true
    
  # 自动修复
  auto_repair:
    enabled: false  # 暂时禁用自动修复
    max_repair_attempts: 2
    repair_strategies: ["fuzzy_match", "dom_analysis"]