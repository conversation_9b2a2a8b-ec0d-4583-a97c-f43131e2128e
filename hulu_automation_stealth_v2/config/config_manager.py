"""
配置管理器 - 契约化选择器配置

支持版本化配置、环境分层、契约验证和智能回退。
提供完整的配置加载、验证和监控功能。
"""

import os
import yaml
import hashlib
import logging
from typing import Dict, Any, List, Optional, Union
from datetime import datetime, timedelta
from dataclasses import dataclass

from ..utils.protocols import ConfigManagerProtocol
from ..core.error_handler import ResourceError, SelectorError, retry_on_failure

logger = logging.getLogger(__name__)


@dataclass
class SelectorContract:
    """选择器契约定义"""
    timeout_ms: int = 5000
    must_be_visible: bool = True
    must_be_enabled: bool = True
    must_be_editable: bool = False
    input_type: Optional[str] = None
    element_type: Union[str, List[str]] = "any"
    should_contain_text: Optional[List[str]] = None
    min_elements: int = 1
    max_elements: int = 1
    optional: bool = False
    required_attributes: Optional[List[str]] = None


@dataclass
class SelectorConfig:
    """选择器配置"""
    primary: str
    fallback: List[str]
    contract: SelectorContract
    validation: Dict[str, Any]


class ConfigManager(ConfigManagerProtocol):
    """
    配置管理器 - 企业级选择器配置管理
    
    特性:
    - YAML配置文件支持
    - 版本化和变更追踪
    - 环境分层配置
    - 契约验证和智能回退
    - 配置缓存和性能优化
    - 健康检查和监控
    """
    
    def __init__(self, config_dir: str = None):
        """
        初始化配置管理器
        
        Args:
            config_dir: 配置文件目录路径
        """
        # 配置目录
        if config_dir is None:
            config_dir = os.path.join(
                os.path.dirname(__file__), ""
            )
        self.config_dir = config_dir
        
        # 配置缓存
        self._config_cache = {}
        self._cache_timestamps = {}
        self._cache_ttl = 300  # 5分钟缓存
        
        # 配置文件路径
        self.selectors_file = os.path.join(config_dir, "selectors.yml")
        self.environments_file = os.path.join(config_dir, "environments.yml")
        
        # 加载主配置
        self._main_config = None
        self._load_main_config()
    
    @retry_on_failure(max_attempts=3, base_delay=0.5)
    def _load_main_config(self) -> None:
        """加载主配置文件"""
        try:
            if not os.path.exists(self.selectors_file):
                raise ResourceError(f"选择器配置文件不存在: {self.selectors_file}",
                                  error_code="CONFIG_FILE_NOT_FOUND")
            
            with open(self.selectors_file, 'r', encoding='utf-8') as f:
                self._main_config = yaml.safe_load(f)
            
            # 验证配置格式
            self._validate_config_format(self._main_config)
            
            logger.info(f"✅ 配置加载成功: version={self._main_config.get('version')}, "
                       f"schema={self._main_config.get('schema_version')}")
                       
        except yaml.YAMLError as e:
            error_msg = f"YAML配置解析失败: {str(e)}"
            logger.error(error_msg)
            raise ResourceError(error_msg, error_code="CONFIG_PARSE_ERROR")
        except Exception as e:
            error_msg = f"配置加载失败: {str(e)}"
            logger.error(error_msg)
            raise ResourceError(error_msg, error_code="CONFIG_LOAD_ERROR")
    
    def _validate_config_format(self, config: Dict[str, Any]) -> None:
        """验证配置文件格式"""
        required_fields = ['schema_version', 'version', 'environments']
        
        for field in required_fields:
            if field not in config:
                raise ResourceError(f"配置缺少必要字段: {field}",
                                  error_code="CONFIG_INVALID_FORMAT")
        
        # 验证环境配置
        if 'production' not in config['environments']:
            raise ResourceError("配置必须包含production环境",
                              error_code="CONFIG_MISSING_PRODUCTION")
        
        logger.debug("✅ 配置格式验证通过")
    
    def _is_cache_valid(self, cache_key: str) -> bool:
        """检查缓存是否有效"""
        if cache_key not in self._cache_timestamps:
            return False
        
        cache_time = self._cache_timestamps[cache_key]
        return (datetime.now() - cache_time).total_seconds() < self._cache_ttl
    
    def _get_from_cache(self, cache_key: str) -> Optional[Any]:
        """从缓存获取数据"""
        if self._is_cache_valid(cache_key):
            return self._config_cache.get(cache_key)
        return None
    
    def _set_cache(self, cache_key: str, value: Any) -> None:
        """设置缓存"""
        self._config_cache[cache_key] = value
        self._cache_timestamps[cache_key] = datetime.now()
    
    def load_selectors(self, environment: str = "production") -> Dict[str, Any]:
        """
        加载选择器配置
        
        Args:
            environment: 环境名称
            
        Returns:
            Dict[str, Any]: 选择器配置
        """
        cache_key = f"selectors_{environment}"
        
        # 尝试从缓存获取
        cached_config = self._get_from_cache(cache_key)
        if cached_config:
            logger.debug(f"✅ 从缓存加载选择器配置: {environment}")
            return cached_config
        
        # 重新加载主配置 (如果文件更新)
        if self._should_reload_config():
            self._load_main_config()
        
        # 获取环境配置
        if environment not in self._main_config['environments']:
            logger.warning(f"环境 {environment} 不存在，使用production环境")
            environment = 'production'
        
        env_config = self._main_config['environments'][environment]
        
        # 合并全局配置
        full_config = {
            'selectors': env_config,
            'fallback_strategy': self._main_config.get('fallback_strategy', {}),
            'performance': self._main_config.get('performance', {}),
            'validation_rules': self._main_config.get('validation_rules', {}),
            'metadata': {
                'version': self._main_config.get('version'),
                'schema_version': self._main_config.get('schema_version'),
                'environment': environment,
                'loaded_at': datetime.now().isoformat()
            }
        }
        
        # 缓存配置
        self._set_cache(cache_key, full_config)
        
        logger.info(f"✅ 选择器配置加载完成: {environment}")
        return full_config
    
    def get_selector(self, key: str, environment: str = "production") -> Dict[str, Any]:
        """
        获取特定选择器配置
        
        Args:
            key: 选择器名称
            environment: 环境名称
            
        Returns:
            Dict[str, Any]: 选择器配置
        """
        selectors_config = self.load_selectors(environment)
        
        if key not in selectors_config['selectors']:
            raise SelectorError(f"选择器 '{key}' 在环境 '{environment}' 中不存在",
                              error_code="SELECTOR_NOT_FOUND",
                              context={"key": key, "environment": environment})
        
        selector_data = selectors_config['selectors'][key]
        
        # 构建选择器配置对象
        return {
            'name': key,
            'primary': selector_data['primary'],
            'fallback': selector_data.get('fallback', []),
            'contract': self._build_selector_contract(selector_data.get('contract', {})),
            'validation': selector_data.get('validation', {}),
            'environment': environment,
            'fallback_strategy': selectors_config['fallback_strategy']
        }
    
    def _build_selector_contract(self, contract_data: Dict[str, Any]) -> SelectorContract:
        """构建选择器契约对象"""
        return SelectorContract(
            timeout_ms=contract_data.get('timeout_ms', 5000),
            must_be_visible=contract_data.get('must_be_visible', True),
            must_be_enabled=contract_data.get('must_be_enabled', True),
            must_be_editable=contract_data.get('must_be_editable', False),
            input_type=contract_data.get('input_type'),
            element_type=contract_data.get('element_type', 'any'),
            should_contain_text=contract_data.get('should_contain_text'),
            min_elements=contract_data.get('min_elements', 1),
            max_elements=contract_data.get('max_elements', 1),
            optional=contract_data.get('optional', False),
            required_attributes=contract_data.get('required_attributes')
        )
    
    def validate_selector_contracts(self, environment: str = "production") -> Dict[str, bool]:
        """
        验证选择器契约
        
        Args:
            environment: 环境名称
            
        Returns:
            Dict[str, bool]: 验证结果
        """
        selectors_config = self.load_selectors(environment)
        validation_results = {}
        
        for selector_name, selector_data in selectors_config['selectors'].items():
            try:
                # 验证基本格式
                if not selector_data.get('primary'):
                    validation_results[selector_name] = False
                    logger.warning(f"选择器 {selector_name} 缺少primary配置")
                    continue
                
                # 验证契约配置
                contract = selector_data.get('contract', {})
                if not isinstance(contract, dict):
                    validation_results[selector_name] = False
                    logger.warning(f"选择器 {selector_name} 契约配置格式错误")
                    continue
                
                # 验证验证规则
                validation = selector_data.get('validation', {})
                if not isinstance(validation, dict):
                    validation_results[selector_name] = False
                    logger.warning(f"选择器 {selector_name} 验证规则格式错误")
                    continue
                
                validation_results[selector_name] = True
                
            except Exception as e:
                validation_results[selector_name] = False
                logger.error(f"选择器 {selector_name} 验证失败: {e}")
        
        logger.info(f"✅ 选择器契约验证完成: {sum(validation_results.values())}/{len(validation_results)} 通过")
        return validation_results
    
    def _should_reload_config(self) -> bool:
        """检查是否应该重新加载配置"""
        try:
            if not os.path.exists(self.selectors_file):
                return False
            
            current_mtime = os.path.getmtime(self.selectors_file)
            if not hasattr(self, '_last_config_mtime'):
                self._last_config_mtime = current_mtime
                return False
            
            if current_mtime > self._last_config_mtime:
                self._last_config_mtime = current_mtime
                logger.info("📄 检测到配置文件更新，重新加载")
                return True
            
            return False
            
        except Exception as e:
            logger.warning(f"检查配置文件更新状态失败: {e}")
            return False
    
    def get_config_info(self) -> Dict[str, Any]:
        """获取配置信息"""
        if not self._main_config:
            return {"status": "not_loaded"}
        
        return {
            "status": "loaded",
            "version": self._main_config.get('version'),
            "schema_version": self._main_config.get('schema_version'),
            "last_updated": self._main_config.get('last_updated'),
            "environments": list(self._main_config.get('environments', {}).keys()),
            "cache_size": len(self._config_cache),
            "cache_hit_rate": self._calculate_cache_hit_rate(),
            "selectors_file": self.selectors_file,
            "file_exists": os.path.exists(self.selectors_file)
        }
    
    def _calculate_cache_hit_rate(self) -> float:
        """计算缓存命中率"""
        if not hasattr(self, '_cache_hits'):
            return 0.0
        
        total_requests = getattr(self, '_cache_requests', 0)
        if total_requests == 0:
            return 0.0
        
        return self._cache_hits / total_requests
    
    def clear_cache(self) -> None:
        """清空配置缓存"""
        self._config_cache.clear()
        self._cache_timestamps.clear()
        logger.info("✅ 配置缓存已清空")
    
    def reload_config(self) -> bool:
        """强制重新加载配置"""
        try:
            self.clear_cache()
            self._load_main_config()
            logger.info("✅ 配置重新加载成功")
            return True
        except Exception as e:
            logger.error(f"配置重新加载失败: {e}")
            return False
    
    def get_selector_usage_stats(self) -> Dict[str, Any]:
        """获取选择器使用统计"""
        # TODO: 实现选择器使用统计
        return {
            "total_requests": getattr(self, '_total_selector_requests', 0),
            "most_used_selectors": [],
            "performance_stats": {},
            "error_rates": {}
        }