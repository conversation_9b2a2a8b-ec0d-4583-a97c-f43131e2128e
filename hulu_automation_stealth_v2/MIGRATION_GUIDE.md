# 迁移指南 - hulu_automation_stealth v2.0

## 📋 概述

本指南帮助您从原始的 `hulu_automation_stealth.py` 迁移到新的模块化架构 `hulu_automation_stealth_v2`。

**重要提醒**: 原API将在v3.0中移除，请在2025年Q3前完成迁移。

## 🚀 快速迁移

### 方案1: Facade适配器 (推荐渐进迁移)

无需更改现有代码，获得新架构的稳定性优势：

```python
# 使用Facade适配器 - 代码无需更改
from hulu_automation_stealth_facade import HuluStealthAutomation

automation = HuluStealthAutomation(debug_port=9222)
result = await automation.execute_stealth_login()
```

### 方案2: 新API直接迁移 (推荐新项目)

迁移到现代化的模块化架构：

```python
# 旧代码
from hulu_automation_stealth import HuluStealthAutomation
automation = HuluStealthAutomation(debug_port=9222)
result = await automation.execute_stealth_login()

# 新代码 
from hulu_automation_stealth_v2.core import AutomationEngine
engine = AutomationEngine(debug_port=9222)
result = await engine.execute_stealth_login()
```

## 📊 API对比表

| 功能 | 原API | 新API | 兼容性 |
|------|-------|--------|---------|
| 构造函数 | `HuluStealthAutomation()` | `AutomationEngine()` | ✅ 参数完全兼容 |
| 主要方法 | `execute_stealth_login()` | `execute_stealth_login()` | ✅ 签名和返回值一致 |
| 错误处理 | 基础异常 | 增强异常层次 | ✅ 向下兼容 |
| 资源管理 | 手动管理 | 自动生命周期 | ✅ 自动改进 |
| 统计功能 | 无 | `get_execution_stats()` | ➕ 新增功能 |
| 健康检查 | 无 | `health_check()` | ➕ 新增功能 |

## 🛠️ 详细迁移步骤

### 步骤1: 评估当前使用

```bash
# 检查Facade使用统计
python3 -c "
from hulu_automation_stealth_facade import HuluStealthAutomation
stats = HuluStealthAutomation.get_usage_stats()
print('使用统计:', stats['health_check'])
"
```

### 步骤2: 测试新API

```python
# 测试新API兼容性
from hulu_automation_stealth_v2.core import AutomationEngine

# 创建引擎实例
engine = AutomationEngine(
    debug_port=9222,
    auto_launch=True,
    persistent_session=True
)

# 检查健康状态
health = await engine.health_check()
print(f"健康状态: {health['status']}")

# 获取组件状态
status = engine.get_component_status()
print(f"组件状态: {status}")
```

### 步骤3: 渐进迁移

**3.1 开发环境迁移**
```python
# 在开发环境先使用新API
if os.getenv('ENVIRONMENT') == 'development':
    from hulu_automation_stealth_v2.core import AutomationEngine as Engine
else:
    from hulu_automation_stealth_facade import HuluStealthAutomation as Engine

automation = Engine()
```

**3.2 功能验证**
```python
# 运行契约测试验证兼容性
python3 -m pytest hulu_automation_stealth_v2/tests/contract/ -v
```

**3.3 性能对比**
```python
# 对比执行时间和资源使用
import time

# 测试原实现
start = time.time()
# ... 执行原代码
old_time = time.time() - start

# 测试新实现  
start = time.time()
# ... 执行新代码
new_time = time.time() - start

print(f"性能对比: 原版 {old_time:.2f}s, 新版 {new_time:.2f}s")
```

### 步骤4: 生产环境迁移

**4.1 Feature Flag控制**
```python
import os

# 使用环境变量控制迁移
USE_V2_ENGINE = os.getenv('USE_V2_ENGINE', 'false').lower() == 'true'

if USE_V2_ENGINE:
    from hulu_automation_stealth_v2.core import AutomationEngine
    engine = AutomationEngine(debug_port=9222)
else:
    from hulu_automation_stealth_facade import HuluStealthAutomation
    engine = HuluStealthAutomation(debug_port=9222)
```

**4.2 金丝雀发布**
```python
import random

# 5%流量使用新版本
use_new_version = random.random() < 0.05

if use_new_version:
    from hulu_automation_stealth_v2.core import AutomationEngine as Engine
else:
    from hulu_automation_stealth_facade import HuluStealthAutomation as Engine
```

## 🔧 高级迁移选项

### 依赖注入迁移

充分利用新架构的依赖注入能力：

```python
from hulu_automation_stealth_v2.core import (
    AutomationEngine, ResourceManager, CDPChromeManager
)

# 自定义资源管理器
custom_rm = ResourceManager(cleanup_timeout=15.0)

# 自定义CDP管理器
custom_cdp = CDPChromeManager(debug_port=9223, persistent_session=False)

# 注入自定义组件
engine = AutomationEngine(
    resource_manager=custom_rm,
    cdp_manager=custom_cdp
)
```

### 错误处理增强

利用新的错误处理能力：

```python
from hulu_automation_stealth_v2.core.error_handler import (
    retry_on_failure, LoginFailedError, with_error_context
)

@retry_on_failure(max_attempts=3, jitter=True)
@with_error_context(operation="custom_login")
async def custom_login_flow():
    engine = AutomationEngine()
    try:
        return await engine.execute_stealth_login()
    except LoginFailedError as e:
        logger.error(f"登录失败: {e.to_dict()}")
        raise
```

### 配置系统迁移

使用新的契约化配置：

```python
from hulu_automation_stealth_v2.config import ConfigManager

# 加载配置
config_manager = ConfigManager()
selectors = config_manager.load_selectors('production')

# 获取特定选择器
login_button = config_manager.get_selector('login_button')
print(f"登录按钮配置: {login_button}")
```

## 🧪 验证迁移

### 契约测试

```bash
# 运行完整的契约测试
python3 -m pytest hulu_automation_stealth_v2/tests/contract/ -v --tb=short

# 运行集成测试
python3 -m pytest hulu_automation_stealth_v2/tests/integration/ -v
```

### 选择器冒烟测试

```bash
# 验证选择器仍然有效
python3 hulu_automation_stealth_v2/ci/selector_smoke_test.py \
    --environment production \
    --report smoke_test_report.json
```

### 性能基准测试

```python
# 运行性能对比测试
python3 -c "
import asyncio
from hulu_automation_stealth_v2.core import AutomationEngine

async def performance_test():
    engine = AutomationEngine(auto_launch=False)  # 避免启动浏览器
    
    # 测试初始化性能
    health = await engine.health_check()
    stats = engine.get_execution_stats()
    
    print(f'健康检查: {health[\"status\"]}')
    print(f'组件状态: OK')

asyncio.run(performance_test())
"
```

## 📈 迁移监控

### 使用统计监控

```python
# 定期检查Facade使用统计
from hulu_automation_stealth_facade import HuluStealthAutomation

stats = HuluStealthAutomation.get_usage_stats()
print(f"总调用次数: {stats['stats']['total_calls']}")
print(f"成功率: {stats['health_check']['success_rate']:.1%}")

# 导出迁移报告
report_path = HuluStealthAutomation.export_migration_report()
print(f"迁移报告: {report_path}")
```

### 错误率监控

```python
# 监控新旧版本的错误率
def monitor_error_rates():
    v1_errors = get_v1_error_count()  # 实现获取v1错误
    v2_errors = get_v2_error_count()  # 实现获取v2错误
    
    if v2_errors > v1_errors * 1.1:  # 新版本错误率高10%以上
        print("⚠️ 新版本错误率过高，考虑回滚")
        return False
    
    return True
```

## 🚨 故障排查

### 常见问题

**Q1: 导入错误**
```python
# 错误: ModuleNotFoundError: No module named 'hulu_automation_stealth_v2'
# 解决: 检查Python路径
import sys, os
sys.path.insert(0, os.path.dirname(__file__))
from hulu_automation_stealth_v2.core import AutomationEngine
```

**Q2: 性能下降**
```python
# 如果新版本性能较差，可以调整配置
engine = AutomationEngine(auto_launch=False)  # 禁用自动启动
# 或使用自定义资源管理器
custom_rm = ResourceManager(cleanup_timeout=5.0)  # 减少清理超时
```

**Q3: 兼容性问题**
```python
# 如果遇到兼容性问题，回退到Facade
from hulu_automation_stealth_facade import HuluStealthAutomation
automation = HuluStealthAutomation()  # 使用完全兼容的适配器
```

### 回滚计划

**方案1: 代码级回滚**
```python
# 简单切换导入
USE_LEGACY = True  # 设置为True回滚

if USE_LEGACY:
    from hulu_automation_stealth import HuluStealthAutomation as Engine
else:
    from hulu_automation_stealth_v2.core import AutomationEngine as Engine
```

**方案2: 环境变量回滚**
```bash
# 设置环境变量回滚
export HULU_USE_LEGACY=true
```

**方案3: Git回滚**
```bash
# Git级别回滚
git revert <commit-hash>
git push origin main
```

## 📞 获得帮助

- **文档**: 查看 `hulu_automation_stealth_v2/` 目录下的文档
- **测试**: 运行 `python3 -m pytest hulu_automation_stealth_v2/tests/ -v`
- **健康检查**: 使用 `engine.health_check()` 诊断问题
- **迁移报告**: 使用 `HuluStealthAutomation.export_migration_report()` 获取详细报告

## 📅 迁移时间表

- **2025年1月**: v2.0发布，开始迁移准备
- **2025年2月**: 开发环境迁移，功能验证
- **2025年Q1**: 生产环境渐进迁移 (5% → 25% → 50% → 100%)
- **2025年Q2**: 完成迁移，移除Facade适配器
- **2025年Q3**: 移除原始API (v3.0发布)

立即开始迁移，确保充足的测试和验证时间！