#!/usr/bin/env python3
"""
演示脚本 - hulu_automation_stealth_v2

展示新架构的核心功能和API使用方式。
验证模块化组件的集成和基本功能。
"""

import asyncio
import logging
import sys
import os

# 添加项目路径
sys.path.insert(0, os.path.dirname(__file__))

from hulu_automation_stealth_v2.core import AutomationEngine
from hulu_automation_stealth_v2.config import ConfigManager
from hulu_automation_stealth_v2.core.error_handler import retry_on_failure


def setup_demo_logging():
    """设置演示日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )


async def demo_basic_functionality():
    """演示基本功能"""
    print("🎯 演示1: 基本功能测试")
    print("=" * 50)
    
    # 创建自动化引擎 (不启动浏览器避免环境问题)
    engine = AutomationEngine(auto_launch=False)
    
    # 获取组件状态
    print("📊 组件状态:")
    status = engine.get_component_status()
    for component, info in status.items():
        print(f"   {component}: {info}")
    
    # 获取执行统计
    print("\n📈 执行统计:")
    stats = engine.get_execution_stats()
    for key, value in stats.items():
        print(f"   {key}: {value}")
    
    # 健康检查
    print("\n🏥 健康检查:")
    health = await engine.health_check()
    print(f"   状态: {health['status']}")
    print(f"   时间戳: {health['timestamp']}")
    if 'issues' in health and health['issues']:
        print(f"   问题: {health['issues']}")
    
    print("✅ 基本功能演示完成\n")


async def demo_config_management():
    """演示配置管理"""
    print("🎯 演示2: 配置管理系统")
    print("=" * 50)
    
    try:
        # 创建配置管理器
        config_manager = ConfigManager()
        
        # 获取配置信息
        print("📋 配置信息:")
        config_info = config_manager.get_config_info()
        for key, value in config_info.items():
            print(f"   {key}: {value}")
        
        # 加载选择器配置
        print("\n🎯 加载选择器配置:")
        selectors_config = config_manager.load_selectors('production')
        print(f"   版本: {selectors_config['metadata']['version']}")
        print(f"   环境: {selectors_config['metadata']['environment']}")
        print(f"   选择器数量: {len(selectors_config['selectors'])}")
        
        # 获取特定选择器
        print("\n🔍 特定选择器示例:")
        login_button = config_manager.get_selector('login_button')
        print(f"   名称: {login_button['name']}")
        print(f"   主选择器: {login_button['primary']}")
        print(f"   回退选择器数量: {len(login_button['fallback'])}")
        
        # 验证选择器契约
        print("\n✅ 契约验证:")
        validation_results = config_manager.validate_selector_contracts()
        passed = sum(validation_results.values())
        total = len(validation_results)
        print(f"   验证结果: {passed}/{total} 通过")
        
        print("✅ 配置管理演示完成\n")
        
    except Exception as e:
        print(f"❌ 配置管理演示失败: {e}\n")


async def demo_error_handling():
    """演示错误处理"""
    print("🎯 演示3: 增强错误处理")
    print("=" * 50)
    
    from hulu_automation_stealth_v2.core.error_handler import (
        HuluAutomationError, LoginFailedError, retry_on_failure, with_error_context
    )
    
    # 基础异常测试
    print("🔧 异常层次结构:")
    try:
        error = LoginFailedError("演示登录失败", error_code="DEMO_ERROR", 
                               context={"attempt": 1, "demo": True})
        print(f"   异常类型: {type(error).__name__}")
        print(f"   错误码: {error.error_code}")
        print(f"   上下文: {error.context}")
        
        # 转换为字典
        error_dict = error.to_dict()
        print(f"   字典格式: {error_dict['error_type']}")
        
    except Exception as e:
        print(f"   异常创建失败: {e}")
    
    # 重试装饰器测试
    print("\n🔄 重试机制演示:")
    
    attempt_count = 0
    
    @retry_on_failure(max_attempts=3, base_delay=0.1, jitter=False)
    async def demo_retry_function():
        nonlocal attempt_count
        attempt_count += 1
        print(f"   尝试 #{attempt_count}")
        
        if attempt_count < 3:
            raise HuluAutomationError("演示失败")
        return "成功"
    
    try:
        result = await demo_retry_function()
        print(f"   最终结果: {result}")
        print(f"   总尝试次数: {attempt_count}")
    except Exception as e:
        print(f"   重试失败: {e}")
    
    # 错误上下文测试
    print("\n📊 错误上下文演示:")
    
    @with_error_context(demo_stage="context_test", operation="demo")
    async def demo_context_function():
        print("   上下文已添加")
        return "上下文测试完成"
    
    try:
        result = await demo_context_function()
        print(f"   结果: {result}")
    except Exception as e:
        print(f"   上下文测试失败: {e}")
    
    print("✅ 错误处理演示完成\n")


async def demo_resource_management():
    """演示资源管理"""
    print("🎯 演示4: 资源生命周期管理")
    print("=" * 50)
    
    from hulu_automation_stealth_v2.core import ResourceManager
    
    # 创建资源管理器
    print("🔧 创建资源管理器:")
    async with ResourceManager() as rm:
        print("   ✅ 资源管理器已启动")
        
        # 获取资源统计
        stats = rm.get_resource_stats()
        print(f"   创建时间: {stats['created_at']}")
        print(f"   注册资源数: {stats['resources_registered']}")
        print(f"   注册任务数: {stats['tasks_registered']}")
        
        # 健康检查
        is_healthy = rm.is_healthy()
        print(f"   健康状态: {'健康' if is_healthy else '不健康'}")
        
        # 模拟注册一些资源
        import asyncio
        dummy_task = asyncio.create_task(asyncio.sleep(0.1))
        await rm.register_task(dummy_task, "演示任务")
        
        print("   ✅ 演示任务已注册")
        
        # 等待任务完成
        await dummy_task
        
    print("   ✅ 资源管理器已清理")
    print("✅ 资源管理演示完成\n")


async def demo_facade_compatibility():
    """演示Facade兼容性"""
    print("🎯 演示5: Facade适配器兼容性")
    print("=" * 50)
    
    try:
        # 尝试导入Facade适配器
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))
        from hulu_automation_stealth_facade import HuluStealthAutomation
        
        print("📦 Facade适配器:")
        # 创建实例 (会显示弃用警告)
        facade = HuluStealthAutomation(debug_port=9224, auto_launch=False)
        print("   ✅ Facade实例创建成功")
        
        # 获取使用统计
        print("\n📊 使用统计:")
        stats = HuluStealthAutomation.get_usage_stats()
        health_check = stats.get('health_check', {})
        migration_info = stats.get('migration_info', {})
        
        print(f"   总调用次数: {health_check.get('total_calls', 0)}")
        print(f"   迁移截止日期: {migration_info.get('deadline', 'N/A')}")
        print(f"   新API导入: {migration_info.get('new_api_import', 'N/A')}")
        
        # 导出迁移报告
        print("\n📋 导出迁移报告:")
        report_path = HuluStealthAutomation.export_migration_report("demo_migration_report.json")
        print(f"   报告路径: {report_path}")
        
        print("✅ Facade兼容性演示完成\n")
        
    except ImportError as e:
        print(f"❌ Facade适配器不可用: {e}\n")


async def main():
    """主演示函数"""
    setup_demo_logging()
    
    print("🚀 hulu_automation_stealth_v2 架构演示")
    print("=" * 60)
    print("版本: 2.0.0")
    print("架构: 企业级模块化重构")
    print("特性: 依赖注入 + 资源管理 + 契约测试")
    print("=" * 60)
    print()
    
    try:
        # 运行各种演示
        await demo_basic_functionality()
        await demo_config_management()
        await demo_error_handling()
        await demo_resource_management()
        await demo_facade_compatibility()
        
        print("🎉 所有演示完成!")
        print("\n📚 下一步:")
        print("   1. 运行契约测试: python3 -m pytest hulu_automation_stealth_v2/tests/contract/ -v")
        print("   2. 运行集成测试: python3 -m pytest hulu_automation_stealth_v2/tests/integration/ -v")
        print("   3. 运行选择器冒烟测试: python3 hulu_automation_stealth_v2/ci/selector_smoke_test.py")
        print("   4. 查看迁移指南: hulu_automation_stealth_v2/MIGRATION_GUIDE.md")
        
    except KeyboardInterrupt:
        print("\n⏹️ 演示被用户中断")
    except Exception as e:
        print(f"\n❌ 演示执行失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())