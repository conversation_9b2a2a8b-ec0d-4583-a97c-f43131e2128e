"""
hulu_automation_stealth_v2 - 企业级浏览器自动化重构版本

现代化的模块化架构，提供企业级的稳定性和可维护性。

主要特性:
- 向后兼容的Facade适配器
- 统一的资源生命周期管理
- 增强的错误处理和重试机制
- 契约化的选择器配置
- 完整的测试覆盖和CI集成

使用示例:
    # 推荐: 使用新的模块化API
    from hulu_automation_stealth_v2.core import AutomationEngine
    engine = AutomationEngine()
    result = await engine.execute_stealth_login()
    
    # 兼容: 保持原API (将在v3.0移除)
    from hulu_automation_stealth import HuluStealthAutomation
    automation = HuluStealthAutomation()
    result = await automation.execute_stealth_login()
"""

__version__ = "2.0.0"
__author__ = "Account Registrar Team"

# 公共API导出
from .core.automation_engine import AutomationEngine
from .core.resource_manager import ResourceManager
from .core.error_handler import (
    HuluAutomationError,
    LoginFailedError, 
    VerificationError,
    ResourceError,
    retry_on_failure
)

__all__ = [
    "AutomationEngine",
    "ResourceManager", 
    "HuluAutomationError",
    "LoginFailedError",
    "VerificationError", 
    "ResourceError",
    "retry_on_failure"
]