{"statistics": {"total_definitions": 1307, "total_references": 47654, "confirmed_dead": 138, "suspicious": 14, "preserved": 1155, "duplicates": 243, "dead_code_ratio": 0.10558530986993114, "files_analyzed": 88}, "confirmed_dead": [{"name": "visit_FunctionDef", "type": "method", "file_path": "dead_code_detector.py", "line_number": 70, "module_name": "dead_code_detector"}, {"name": "visit_AsyncFunctionDef", "type": "method", "file_path": "dead_code_detector.py", "line_number": 87, "module_name": "dead_code_detector"}, {"name": "visit_ClassDef", "type": "method", "file_path": "dead_code_detector.py", "line_number": 104, "module_name": "dead_code_detector"}, {"name": "visit_Call", "type": "method", "file_path": "dead_code_detector.py", "line_number": 132, "module_name": "dead_code_detector"}, {"name": "visit_Attribute", "type": "method", "file_path": "dead_code_detector.py", "line_number": 157, "module_name": "dead_code_detector"}, {"name": "visit_Name", "type": "method", "file_path": "dead_code_detector.py", "line_number": 169, "module_name": "dead_code_detector"}, {"name": "visit_Import", "type": "method", "file_path": "dead_code_detector.py", "line_number": 182, "module_name": "dead_code_detector"}, {"name": "visit_ImportFrom", "type": "method", "file_path": "dead_code_detector.py", "line_number": 196, "module_name": "dead_code_detector"}, {"name": "stop_background_sync", "type": "method", "file_path": "metrics.py", "line_number": 466, "module_name": "metrics"}, {"name": "start_metrics_server", "type": "function", "file_path": "metrics.py", "line_number": 536, "module_name": "metrics"}, {"name": "get_network_stats", "type": "method", "file_path": "intelligent_waiting_manager.py", "line_number": 84, "module_name": "intelligent_waiting_manager"}, {"name": "wait_for_elements_stable", "type": "async_method", "file_path": "intelligent_waiting_manager.py", "line_number": 214, "module_name": "intelligent_waiting_manager"}, {"name": "create_intelligent_waiting_manager", "type": "function", "file_path": "intelligent_waiting_manager.py", "line_number": 635, "module_name": "intelligent_waiting_manager"}, {"name": "urls_match", "type": "method", "file_path": "advanced_caching_system.py", "line_number": 235, "module_name": "advanced_caching_system"}, {"name": "clear_page_cache", "type": "method", "file_path": "advanced_caching_system.py", "line_number": 720, "module_name": "advanced_caching_system"}, {"name": "restore_page_state", "type": "async_method", "file_path": "advanced_caching_system.py", "line_number": 849, "module_name": "advanced_caching_system"}, {"name": "validate_required_elements", "type": "async_method", "file_path": "advanced_caching_system.py", "line_number": 923, "module_name": "advanced_caching_system"}, {"name": "clear_all_cache", "type": "method", "file_path": "advanced_caching_system.py", "line_number": 991, "module_name": "advanced_caching_system"}, {"name": "create_advanced_caching_system", "type": "function", "file_path": "advanced_caching_system.py", "line_number": 1033, "module_name": "advanced_caching_system"}, {"name": "get_caching_performance_report", "type": "async_method", "file_path": "hulu_automation_stealth.py", "line_number": 2869, "module_name": "hulu_automation_stealth"}, {"name": "get_performance_report", "type": "async_method", "file_path": "hulu_automation_stealth.py", "line_number": 3231, "module_name": "hulu_automation_stealth"}, {"name": "debug_exit_intent_modal", "type": "async_method", "file_path": "hulu_automation_stealth.py", "line_number": 4093, "module_name": "hulu_automation_stealth"}, {"name": "create_quick_start_guide", "type": "function", "file_path": "anti_detection/config_manager.py", "line_number": 311, "module_name": "anti_detection.config_manager"}, {"name": "get_enabled_features_for_session", "type": "method", "file_path": "anti_detection/feature_flags.py", "line_number": 378, "module_name": "anti_detection.feature_flags"}, {"name": "simulate_human_click", "type": "async_method", "file_path": "anti_detection/behavior_simulator.py", "line_number": 435, "module_name": "anti_detection.behavior_simulator"}, {"name": "select_random_template", "type": "method", "file_path": "anti_detection/device_templates.py", "line_number": 184, "module_name": "anti_detection.device_templates"}, {"name": "get_dashboard_data", "type": "method", "file_path": "anti_detection/analytics.py", "line_number": 544, "module_name": "anti_detection.analytics"}, {"name": "simulate_page_interaction", "type": "async_method", "file_path": "anti_detection/manager.py", "line_number": 266, "module_name": "anti_detection.manager"}, {"name": "generate_optimization_report", "type": "method", "file_path": "anti_detection/manager.py", "line_number": 407, "module_name": "anti_detection.manager"}, {"name": "parse_csv_line", "type": "method", "file_path": "credit_card_validator/data_processor.py", "line_number": 203, "module_name": "credit_card_validator.data_processor"}, {"name": "decrypt_processed_data", "type": "method", "file_path": "credit_card_validator/data_processor.py", "line_number": 496, "module_name": "credit_card_validator.data_processor"}, {"name": "to_json", "type": "method", "file_path": "credit_card_validator/core/validator.py", "line_number": 35, "module_name": "credit_card_validator.core.validator"}, {"name": "encrypt_file_content", "type": "method", "file_path": "credit_card_validator/core/encryption.py", "line_number": 208, "module_name": "credit_card_validator.core.encryption"}, {"name": "secure_delete_file", "type": "method", "file_path": "credit_card_validator/core/encryption.py", "line_number": 262, "module_name": "credit_card_validator.core.encryption"}, {"name": "reload_config", "type": "method", "file_path": "utils/locators.py", "line_number": 224, "module_name": "utils.locators"}, {"name": "LOGIN_BUTTONS", "type": "method", "file_path": "utils/locators.py", "line_number": 247, "module_name": "utils.locators"}, {"name": "EMAIL_INPUTS", "type": "method", "file_path": "utils/locators.py", "line_number": 251, "module_name": "utils.locators"}, {"name": "PASSWORD_INPUTS", "type": "method", "file_path": "utils/locators.py", "line_number": 255, "module_name": "utils.locators"}, {"name": "verification", "type": "method", "file_path": "utils/locators.py", "line_number": 266, "module_name": "utils.locators"}, {"name": "log_phase_failure", "type": "method", "file_path": "utils/logger.py", "line_number": 301, "module_name": "utils.logger"}, {"name": "log_selector_failed", "type": "method", "file_path": "utils/logger.py", "line_number": 323, "module_name": "utils.logger"}, {"name": "get_csrf_token", "type": "method", "file_path": "utils/utils.py", "line_number": 99, "module_name": "utils.utils"}, {"name": "get_api_status", "type": "function", "file_path": "utils/utils.py", "line_number": 463, "module_name": "utils.utils"}, {"name": "load_all_accounts", "type": "method", "file_path": "utils/account_loader.py", "line_number": 109, "module_name": "utils.account_loader"}, {"name": "get_account_count", "type": "method", "file_path": "utils/account_loader.py", "line_number": 151, "module_name": "utils.account_loader"}, {"name": "reload_accounts", "type": "method", "file_path": "utils/account_loader.py", "line_number": 160, "module_name": "utils.account_loader"}, {"name": "update_cookies_status", "type": "method", "file_path": "utils/account_loader.py", "line_number": 165, "module_name": "utils.account_loader"}, {"name": "load_account_by_index", "type": "function", "file_path": "utils/account_loader.py", "line_number": 257, "module_name": "utils.account_loader"}, {"name": "load_account_by_email", "type": "function", "file_path": "utils/account_loader.py", "line_number": 272, "module_name": "utils.account_loader"}, {"name": "thought_not_empty", "type": "method", "file_path": "mcp-sequential-thinking/mcp_sequential_thinking/models.py", "line_number": 63, "module_name": "mcp-sequential-thinking.mcp_sequential_thinking.models"}, {"name": "thought_number_positive", "type": "method", "file_path": "mcp-sequential-thinking/mcp_sequential_thinking/models.py", "line_number": 70, "module_name": "mcp-sequential-thinking.mcp_sequential_thinking.models"}, {"name": "total_thoughts_valid", "type": "method", "file_path": "mcp-sequential-thinking/mcp_sequential_thinking/models.py", "line_number": 77, "module_name": "mcp-sequential-thinking.mcp_sequential_thinking.models"}, {"name": "set_storage_state_path", "type": "method", "file_path": "hulu_automation/business/login_service.py", "line_number": 72, "module_name": "hulu_automation.business.login_service"}, {"name": "coordinate_verification_flow", "type": "method", "file_path": "hulu_automation/business/workflow_orchestrator.py", "line_number": 260, "module_name": "hulu_automation.business.workflow_orchestrator"}, {"name": "check_system_status", "type": "method", "file_path": "hulu_automation/business/workflow_orchestrator.py", "line_number": 328, "module_name": "hulu_automation.business.workflow_orchestrator"}, {"name": "clear_otp_input_humanized", "type": "method", "file_path": "hulu_automation/business/verification_service.py", "line_number": 692, "module_name": "hulu_automation.business.verification_service"}, {"name": "Selectors", "type": "class", "file_path": "hulu_automation/infrastructure/page_selectors.py", "line_number": 266, "module_name": "hulu_automation.infrastructure.page_selectors"}, {"name": "verification", "type": "method", "file_path": "hulu_automation/infrastructure/page_selectors.py", "line_number": 281, "module_name": "hulu_automation.infrastructure.page_selectors"}, {"name": "fill_input", "type": "method", "file_path": "hulu_automation/infrastructure/humanized_element_locator.py", "line_number": 73, "module_name": "hulu_automation.infrastructure.humanized_element_locator"}, {"name": "click_element_with_focus", "type": "method", "file_path": "hulu_automation/infrastructure/humanized_element_locator.py", "line_number": 151, "module_name": "hulu_automation.infrastructure.humanized_element_locator"}, {"name": "click_element_debug_style", "type": "method", "file_path": "hulu_automation/infrastructure/humanized_element_locator.py", "line_number": 640, "module_name": "hulu_automation.infrastructure.humanized_element_locator"}, {"name": "find_element", "type": "method", "file_path": "hulu_automation/infrastructure/element_locator.py", "line_number": 26, "module_name": "hulu_automation.infrastructure.element_locator"}, {"name": "fill_input", "type": "method", "file_path": "hulu_automation/infrastructure/element_locator.py", "line_number": 74, "module_name": "hulu_automation.infrastructure.element_locator"}, {"name": "wait_for_element", "type": "method", "file_path": "hulu_automation/infrastructure/element_locator.py", "line_number": 105, "module_name": "hulu_automation.infrastructure.element_locator"}, {"name": "retry_operation", "type": "method", "file_path": "hulu_automation/infrastructure/error_handler.py", "line_number": 165, "module_name": "hulu_automation.infrastructure.error_handler"}, {"name": "log_error_context", "type": "method", "file_path": "hulu_automation/infrastructure/error_handler.py", "line_number": 212, "module_name": "hulu_automation.infrastructure.error_handler"}, {"name": "get_error_statistics", "type": "method", "file_path": "hulu_automation/infrastructure/error_handler.py", "line_number": 295, "module_name": "hulu_automation.infrastructure.error_handler"}, {"name": "reset_statistics", "type": "method", "file_path": "hulu_automation/infrastructure/error_handler.py", "line_number": 307, "module_name": "hulu_automation.infrastructure.error_handler"}, {"name": "human_typing", "type": "method", "file_path": "hulu_automation/infrastructure/human_behavior.py", "line_number": 433, "module_name": "hulu_automation.infrastructure.human_behavior"}, {"name": "update_fatigue", "type": "method", "file_path": "hulu_automation/infrastructure/human_behavior.py", "line_number": 555, "module_name": "hulu_automation.infrastructure.human_behavior"}, {"name": "update_stress", "type": "method", "file_path": "hulu_automation/infrastructure/human_behavior.py", "line_number": 565, "module_name": "hulu_automation.infrastructure.human_behavior"}, {"name": "generate_mouse_jitter", "type": "method", "file_path": "hulu_automation/infrastructure/human_behavior.py", "line_number": 575, "module_name": "hulu_automation.infrastructure.human_behavior"}, {"name": "simulate_hesitation", "type": "method", "file_path": "hulu_automation/infrastructure/human_behavior.py", "line_number": 626, "module_name": "hulu_automation.infrastructure.human_behavior"}, {"name": "generate_typing_rhythm", "type": "method", "file_path": "hulu_automation/infrastructure/human_behavior.py", "line_number": 649, "module_name": "hulu_automation.infrastructure.human_behavior"}, {"name": "SmartDelay", "type": "class", "file_path": "hulu_automation/infrastructure/human_behavior.py", "line_number": 689, "module_name": "hulu_automation.infrastructure.human_behavior"}, {"name": "wait_for_page_load", "type": "method", "file_path": "hulu_automation/infrastructure/human_behavior.py", "line_number": 697, "module_name": "hulu_automation.infrastructure.human_behavior"}, {"name": "wait_before_action", "type": "method", "file_path": "hulu_automation/infrastructure/human_behavior.py", "line_number": 715, "module_name": "hulu_automation.infrastructure.human_behavior"}, {"name": "adaptive_delay", "type": "method", "file_path": "hulu_automation/infrastructure/human_behavior.py", "line_number": 748, "module_name": "hulu_automation.infrastructure.human_behavior"}, {"name": "navigate_to", "type": "method", "file_path": "hulu_automation/infrastructure/browser_engine.py", "line_number": 113, "module_name": "hulu_automation.infrastructure.browser_engine"}, {"name": "apply_fingerprint", "type": "method", "file_path": "hulu_automation/infrastructure/browser_engine.py", "line_number": 130, "module_name": "hulu_automation.infrastructure.browser_engine"}, {"name": "get_current_url", "type": "method", "file_path": "hulu_automation/infrastructure/browser_engine.py", "line_number": 259, "module_name": "hulu_automation.infrastructure.browser_engine"}, {"name": "is_initialized", "type": "method", "file_path": "hulu_automation/infrastructure/browser_engine.py", "line_number": 265, "module_name": "hulu_automation.infrastructure.browser_engine"}, {"name": "randomize_user_agent", "type": "method", "file_path": "hulu_automation/infrastructure/anti_detection_service.py", "line_number": 163, "module_name": "hulu_automation.infrastructure.anti_detection_service"}, {"name": "get_session_fingerprints", "type": "method", "file_path": "hulu_automation/infrastructure/anti_detection_service.py", "line_number": 247, "module_name": "hulu_automation.infrastructure.anti_detection_service"}, {"name": "clear_state", "type": "method", "file_path": "hulu_automation/data/state_manager.py", "line_number": 102, "module_name": "hulu_automation.data.state_manager"}, {"name": "get_storage_path", "type": "method", "file_path": "hulu_automation/data/state_manager.py", "line_number": 179, "module_name": "hulu_automation.data.state_manager"}, {"name": "log_info", "type": "method", "file_path": "hulu_automation/data/logging_service.py", "line_number": 59, "module_name": "hulu_automation.data.logging_service"}, {"name": "log_error", "type": "method", "file_path": "hulu_automation/data/logging_service.py", "line_number": 63, "module_name": "hulu_automation.data.logging_service"}, {"name": "log_debug", "type": "method", "file_path": "hulu_automation/data/logging_service.py", "line_number": 70, "module_name": "hulu_automation.data.logging_service"}, {"name": "log_warning", "type": "method", "file_path": "hulu_automation/data/logging_service.py", "line_number": 74, "module_name": "hulu_automation.data.logging_service"}, {"name": "get_browser_config", "type": "method", "file_path": "hulu_automation/data/configuration_provider.py", "line_number": 71, "module_name": "hulu_automation.data.configuration_provider"}, {"name": "get_api_config", "type": "method", "file_path": "hulu_automation/data/configuration_provider.py", "line_number": 79, "module_name": "hulu_automation.data.configuration_provider"}, {"name": "validate_config", "type": "method", "file_path": "hulu_automation/data/configuration_provider.py", "line_number": 96, "module_name": "hulu_automation.data.configuration_provider"}, {"name": "get_config", "type": "method", "file_path": "hulu_automation/data/configuration_provider.py", "line_number": 112, "module_name": "hulu_automation.data.configuration_provider"}, {"name": "get_all_config", "type": "method", "file_path": "hulu_automation/data/configuration_provider.py", "line_number": 116, "module_name": "hulu_automation.data.configuration_provider"}, {"name": "enable_debug_mode", "type": "method", "file_path": "hulu_automation/presentation/hulu_bot_facade.py", "line_number": 292, "module_name": "hulu_automation.presentation.hulu_bot_facade"}, {"name": "apply_anti_detection_config", "type": "method", "file_path": "hulu_automation/presentation/hulu_bot_facade.py", "line_number": 311, "module_name": "hulu_automation.presentation.hulu_bot_facade"}, {"name": "register_account", "type": "method", "file_path": "hulu_automation/presentation/hulu_bot_facade.py", "line_number": 331, "module_name": "hulu_automation.presentation.hulu_bot_facade"}, {"name": "save_storage_state", "type": "method", "file_path": "hulu_automation/presentation/hulu_bot_facade.py", "line_number": 496, "module_name": "hulu_automation.presentation.hulu_bot_facade"}, {"name": "get_session_summary", "type": "method", "file_path": "hulu_automation/presentation/hulu_bot_facade.py", "line_number": 557, "module_name": "hulu_automation.presentation.hulu_bot_facade"}, {"name": "with_account_index", "type": "method", "file_path": "hulu_automation/presentation/hulu_bot_facade.py", "line_number": 594, "module_name": "hulu_automation.presentation.hulu_bot_facade"}, {"name": "with_account_email", "type": "method", "file_path": "hulu_automation/presentation/hulu_bot_facade.py", "line_number": 608, "module_name": "hulu_automation.presentation.hulu_bot_facade"}, {"name": "expect_popup", "type": "async_method", "file_path": "infrastructure/intelligent_waiting_manager.py", "line_number": 278, "module_name": "infrastructure.intelligent_waiting_manager"}, {"name": "add_wait_context", "type": "method", "file_path": "infrastructure/intelligent_waiting_manager.py", "line_number": 304, "module_name": "infrastructure.intelligent_waiting_manager"}, {"name": "update_wait_context", "type": "method", "file_path": "infrastructure/intelligent_waiting_manager.py", "line_number": 309, "module_name": "infrastructure.intelligent_waiting_manager"}, {"name": "replace_sleep", "type": "async_method", "file_path": "infrastructure/intelligent_waiting_manager.py", "line_number": 318, "module_name": "infrastructure.intelligent_waiting_manager"}, {"name": "get_context_info", "type": "method", "file_path": "infrastructure/intelligent_waiting_manager.py", "line_number": 359, "module_name": "infrastructure.intelligent_waiting_manager"}, {"name": "create_baseline_templates", "type": "async_method", "file_path": "infrastructure/visual_fallback_system.py", "line_number": 335, "module_name": "infrastructure.visual_fallback_system"}, {"name": "list_templates", "type": "method", "file_path": "infrastructure/visual_fallback_system.py", "line_number": 408, "module_name": "infrastructure.visual_fallback_system"}, {"name": "remove_template", "type": "method", "file_path": "infrastructure/visual_fallback_system.py", "line_number": 422, "module_name": "infrastructure.visual_fallback_system"}, {"name": "debug_match", "type": "async_method", "file_path": "infrastructure/visual_fallback_system.py", "line_number": 445, "module_name": "infrastructure.visual_fallback_system"}, {"name": "get_page_strategy", "type": "method", "file_path": "infrastructure/smart_interaction_controller.py", "line_number": 433, "module_name": "infrastructure.smart_interaction_controller"}, {"name": "add_page_config", "type": "method", "file_path": "infrastructure/smart_interaction_controller.py", "line_number": 438, "module_name": "infrastructure.smart_interaction_controller"}, {"name": "update_page_config", "type": "method", "file_path": "infrastructure/smart_interaction_controller.py", "line_number": 443, "module_name": "infrastructure.smart_interaction_controller"}, {"name": "clear_records", "type": "method", "file_path": "infrastructure/simple_monitor.py", "line_number": 201, "module_name": "infrastructure.simple_monitor"}, {"name": "simulate_human_mouse_movement", "type": "async_method", "file_path": "infrastructure/anti_detection/service.py", "line_number": 137, "module_name": "infrastructure.anti_detection.service"}, {"name": "is_active", "type": "method", "file_path": "infrastructure/anti_detection/service.py", "line_number": 222, "module_name": "infrastructure.anti_detection.service"}, {"name": "get_config", "type": "method", "file_path": "infrastructure/anti_detection/service.py", "line_number": 226, "module_name": "infrastructure.anti_detection.service"}, {"name": "get_status", "type": "method", "file_path": "infrastructure/anti_detection/service.py", "line_number": 230, "module_name": "infrastructure.anti_detection.service"}, {"name": "get_anti_detection_service", "type": "function", "file_path": "infrastructure/anti_detection/service.py", "line_number": 258, "module_name": "infrastructure.anti_detection.service"}, {"name": "reset_anti_detection_service", "type": "function", "file_path": "infrastructure/anti_detection/service.py", "line_number": 276, "module_name": "infrastructure.anti_detection.service"}, {"name": "from_env", "type": "method", "file_path": "infrastructure/anti_detection/config.py", "line_number": 153, "module_name": "infrastructure.anti_detection.config"}, {"name": "get_typing_config", "type": "method", "file_path": "infrastructure/anti_detection/config.py", "line_number": 190, "module_name": "infrastructure.anti_detection.config"}, {"name": "get_viewport_config", "type": "method", "file_path": "infrastructure/anti_detection/config.py", "line_number": 198, "module_name": "infrastructure.anti_detection.config"}, {"name": "balanced_performance", "type": "method", "file_path": "infrastructure/anti_detection/config.py", "line_number": 261, "module_name": "infrastructure.anti_detection.config"}, {"name": "minimal_footprint", "type": "method", "file_path": "infrastructure/anti_detection/config.py", "line_number": 274, "module_name": "infrastructure.anti_detection.config"}, {"name": "debugging_mode", "type": "method", "file_path": "infrastructure/anti_detection/config.py", "line_number": 287, "module_name": "infrastructure.anti_detection.config"}, {"name": "ease_out_quart", "type": "method", "file_path": "infrastructure/anti_detection/behavior_simulator.py", "line_number": 34, "module_name": "infrastructure.anti_detection.behavior_simulator"}, {"name": "ease_out_bounce", "type": "method", "file_path": "infrastructure/anti_detection/behavior_simulator.py", "line_number": 39, "module_name": "infrastructure.anti_detection.behavior_simulator"}, {"name": "ease_in_out_circ", "type": "method", "file_path": "infrastructure/anti_detection/behavior_simulator.py", "line_number": 70, "module_name": "infrastructure.anti_detection.behavior_simulator"}, {"name": "get_typing_stats", "type": "method", "file_path": "infrastructure/anti_detection/behavior_simulator.py", "line_number": 572, "module_name": "infrastructure.anti_detection.behavior_simulator"}, {"name": "inject_to_context", "type": "async_method", "file_path": "infrastructure/anti_detection/stealth_injector.py", "line_number": 430, "module_name": "infrastructure.anti_detection.stealth_injector"}, {"name": "get_stealth_status", "type": "method", "file_path": "infrastructure/anti_detection/stealth_injector.py", "line_number": 490, "module_name": "infrastructure.anti_detection.stealth_injector"}, {"name": "get_strategy_effectiveness", "type": "method", "file_path": "infrastructure/anti_detection/fallback_handler.py", "line_number": 427, "module_name": "infrastructure.anti_detection.fallback_handler"}, {"name": "get_detection_pattern", "type": "method", "file_path": "infrastructure/anti_detection/fallback_handler.py", "line_number": 437, "module_name": "infrastructure.anti_detection.fallback_handler"}, {"name": "reset_stats", "type": "method", "file_path": "infrastructure/anti_detection/fallback_handler.py", "line_number": 467, "module_name": "infrastructure.anti_detection.fallback_handler"}, {"name": "get_fallback_handler", "type": "function", "file_path": "infrastructure/anti_detection/fallback_handler.py", "line_number": 485, "module_name": "infrastructure.anti_detection.fallback_handler"}, {"name": "reset_fallback_handler", "type": "function", "file_path": "infrastructure/anti_detection/fallback_handler.py", "line_number": 503, "module_name": "infrastructure.anti_detection.fallback_handler"}], "suspicious": [{"name": "_restore_login_state", "type": "async_method", "file_path": "hulu_automation_stealth.py", "line_number": 1242, "module_name": "hulu_automation_stealth"}, {"name": "_detect_password_error", "type": "async_method", "file_path": "hulu_automation_stealth.py", "line_number": 1988, "module_name": "hulu_automation_stealth"}, {"name": "_verify_config_applied_legacy", "type": "method", "file_path": "hulu_automation_stealth.py", "line_number": 3253, "module_name": "hulu_automation_stealth"}, {"name": "_debug_billing_page_structure", "type": "async_method", "file_path": "hulu_automation_stealth.py", "line_number": 3650, "module_name": "hulu_automation_stealth"}, {"name": "_simulate_interaction", "type": "async_method", "file_path": "ci/selector_healthcheck.py", "line_number": 1442, "module_name": "ci.selector_healthcheck"}, {"name": "_check_and_handle_continue_step", "type": "method", "file_path": "hulu_automation/business/login_service.py", "line_number": 368, "module_name": "hulu_automation.business.login_service"}, {"name": "_is_still_on_login_page", "type": "method", "file_path": "hulu_automation/business/login_service.py", "line_number": 410, "module_name": "hulu_automation.business.login_service"}, {"name": "_attempt_form_submission", "type": "method", "file_path": "hulu_automation/business/login_service.py", "line_number": 438, "module_name": "hulu_automation.business.login_service"}, {"name": "_handle_login_verification", "type": "method", "file_path": "hulu_automation/business/login_service.py", "line_number": 454, "module_name": "hulu_automation.business.login_service"}, {"name": "_get_session_info", "type": "method", "file_path": "hulu_automation/business/login_service.py", "line_number": 588, "module_name": "hulu_automation.business.login_service"}, {"name": "_save_storage_state", "type": "method", "file_path": "hulu_automation/business/login_service.py", "line_number": 610, "module_name": "hulu_automation.business.login_service"}, {"name": "_wait_for_form_validation", "type": "method", "file_path": "hulu_automation/infrastructure/humanized_element_locator.py", "line_number": 377, "module_name": "hulu_automation.infrastructure.humanized_element_locator"}, {"name": "_verify_click_effect", "type": "method", "file_path": "hulu_automation/infrastructure/humanized_element_locator.py", "line_number": 431, "module_name": "hulu_automation.infrastructure.humanized_element_locator"}, {"name": "_apply_fingerprint_safe", "type": "method", "file_path": "hulu_automation/infrastructure/anti_detection_service.py", "line_number": 53, "module_name": "hulu_automation.infrastructure.anti_detection_service"}], "duplicates": [{"name": "main", "locations": [{"file": "dead_code_detector.py", "line": 497}, {"file": "verify_page_state.py", "line": 19}]}, {"name": "main", "locations": [{"file": "dead_code_detector.py", "line": 497}, {"file": "credit_card.py", "line": 236}]}, {"name": "main", "locations": [{"file": "dead_code_detector.py", "line": 497}, {"file": "manual_cleanup.py", "line": 221}]}, {"name": "main", "locations": [{"file": "dead_code_detector.py", "line": 497}, {"file": "hulu_automation_stealth.py", "line": 4606}]}, {"name": "main", "locations": [{"file": "dead_code_detector.py", "line": 497}, {"file": "ci/selector_healthcheck.py", "line": 1511}]}, {"name": "main", "locations": [{"file": "dead_code_detector.py", "line": 497}, {"file": "tests/test_monitor_integration_simple.py", "line": 176}]}, {"name": "main", "locations": [{"file": "dead_code_detector.py", "line": 497}, {"file": "tests/test_element_search_monitoring_integration.py", "line": 200}]}, {"name": "main", "locations": [{"file": "dead_code_detector.py", "line": 497}, {"file": "tests/monitoring_system_final_report.py", "line": 271}]}, {"name": "main", "locations": [{"file": "dead_code_detector.py", "line": 497}, {"file": "tests/test_email_api.py", "line": 49}]}, {"name": "main", "locations": [{"file": "dead_code_detector.py", "line": 497}, {"file": "tests/test_element_location_monitoring.py", "line": 189}]}, {"name": "main", "locations": [{"file": "dead_code_detector.py", "line": 497}, {"file": "tests/test_quick_wins_integration.py", "line": 255}]}, {"name": "main", "locations": [{"file": "dead_code_detector.py", "line": 497}, {"file": "tests/debug/debug_get_them_both_button.py", "line": 650}]}, {"name": "main", "locations": [{"file": "dead_code_detector.py", "line": 497}, {"file": "tests/debug/debug_email_input.py", "line": 173}]}, {"name": "main", "locations": [{"file": "dead_code_detector.py", "line": 497}, {"file": "tests/debug/debug_test_api.py", "line": 105}]}, {"name": "main", "locations": [{"file": "dead_code_detector.py", "line": 497}, {"file": "mcp-sequential-thinking/mcp_sequential_thinking/server.py", "line": 219}]}, {"name": "main", "locations": [{"file": "dead_code_detector.py", "line": 497}, {"file": "examples/hulu_anti_detection_quick_start.py", "line": 166}]}, {"name": "main", "locations": [{"file": "dead_code_detector.py", "line": 497}, {"file": "examples/hulu_recaptcha_v3_fixed.py", "line": 816}]}, {"name": "main", "locations": [{"file": "dead_code_detector.py", "line": 497}, {"file": "examples/quick_wins_integration_demo.py", "line": 251}]}, {"name": "main", "locations": [{"file": "dead_code_detector.py", "line": 497}, {"file": "examples/hulu_recaptcha_v3_killer.py", "line": 404}]}, {"name": "main", "locations": [{"file": "dead_code_detector.py", "line": 497}, {"file": "examples/login_with_state_persistence.py", "line": 123}]}, {"name": "main", "locations": [{"file": "dead_code_detector.py", "line": 497}, {"file": "examples/hulu_login_automation.py", "line": 593}]}, {"name": "main", "locations": [{"file": "verify_page_state.py", "line": 19}, {"file": "credit_card.py", "line": 236}]}, {"name": "main", "locations": [{"file": "verify_page_state.py", "line": 19}, {"file": "manual_cleanup.py", "line": 221}]}, {"name": "main", "locations": [{"file": "verify_page_state.py", "line": 19}, {"file": "hulu_automation_stealth.py", "line": 4606}]}, {"name": "main", "locations": [{"file": "verify_page_state.py", "line": 19}, {"file": "ci/selector_healthcheck.py", "line": 1511}]}, {"name": "main", "locations": [{"file": "verify_page_state.py", "line": 19}, {"file": "tests/test_monitor_integration_simple.py", "line": 176}]}, {"name": "main", "locations": [{"file": "verify_page_state.py", "line": 19}, {"file": "tests/test_element_search_monitoring_integration.py", "line": 200}]}, {"name": "main", "locations": [{"file": "verify_page_state.py", "line": 19}, {"file": "tests/monitoring_system_final_report.py", "line": 271}]}, {"name": "main", "locations": [{"file": "verify_page_state.py", "line": 19}, {"file": "tests/test_email_api.py", "line": 49}]}, {"name": "main", "locations": [{"file": "verify_page_state.py", "line": 19}, {"file": "tests/test_element_location_monitoring.py", "line": 189}]}, {"name": "main", "locations": [{"file": "verify_page_state.py", "line": 19}, {"file": "tests/test_quick_wins_integration.py", "line": 255}]}, {"name": "main", "locations": [{"file": "verify_page_state.py", "line": 19}, {"file": "tests/debug/debug_get_them_both_button.py", "line": 650}]}, {"name": "main", "locations": [{"file": "verify_page_state.py", "line": 19}, {"file": "tests/debug/debug_email_input.py", "line": 173}]}, {"name": "main", "locations": [{"file": "verify_page_state.py", "line": 19}, {"file": "tests/debug/debug_test_api.py", "line": 105}]}, {"name": "main", "locations": [{"file": "verify_page_state.py", "line": 19}, {"file": "mcp-sequential-thinking/mcp_sequential_thinking/server.py", "line": 219}]}, {"name": "main", "locations": [{"file": "verify_page_state.py", "line": 19}, {"file": "examples/hulu_anti_detection_quick_start.py", "line": 166}]}, {"name": "main", "locations": [{"file": "verify_page_state.py", "line": 19}, {"file": "examples/hulu_recaptcha_v3_fixed.py", "line": 816}]}, {"name": "main", "locations": [{"file": "verify_page_state.py", "line": 19}, {"file": "examples/quick_wins_integration_demo.py", "line": 251}]}, {"name": "main", "locations": [{"file": "verify_page_state.py", "line": 19}, {"file": "examples/hulu_recaptcha_v3_killer.py", "line": 404}]}, {"name": "main", "locations": [{"file": "verify_page_state.py", "line": 19}, {"file": "examples/login_with_state_persistence.py", "line": 123}]}, {"name": "main", "locations": [{"file": "verify_page_state.py", "line": 19}, {"file": "examples/hulu_login_automation.py", "line": 593}]}, {"name": "main", "locations": [{"file": "credit_card.py", "line": 236}, {"file": "manual_cleanup.py", "line": 221}]}, {"name": "main", "locations": [{"file": "credit_card.py", "line": 236}, {"file": "hulu_automation_stealth.py", "line": 4606}]}, {"name": "main", "locations": [{"file": "credit_card.py", "line": 236}, {"file": "ci/selector_healthcheck.py", "line": 1511}]}, {"name": "main", "locations": [{"file": "credit_card.py", "line": 236}, {"file": "tests/test_monitor_integration_simple.py", "line": 176}]}, {"name": "main", "locations": [{"file": "credit_card.py", "line": 236}, {"file": "tests/test_element_search_monitoring_integration.py", "line": 200}]}, {"name": "main", "locations": [{"file": "credit_card.py", "line": 236}, {"file": "tests/monitoring_system_final_report.py", "line": 271}]}, {"name": "main", "locations": [{"file": "credit_card.py", "line": 236}, {"file": "tests/test_email_api.py", "line": 49}]}, {"name": "main", "locations": [{"file": "credit_card.py", "line": 236}, {"file": "tests/test_element_location_monitoring.py", "line": 189}]}, {"name": "main", "locations": [{"file": "credit_card.py", "line": 236}, {"file": "tests/test_quick_wins_integration.py", "line": 255}]}, {"name": "main", "locations": [{"file": "credit_card.py", "line": 236}, {"file": "tests/debug/debug_get_them_both_button.py", "line": 650}]}, {"name": "main", "locations": [{"file": "credit_card.py", "line": 236}, {"file": "tests/debug/debug_email_input.py", "line": 173}]}, {"name": "main", "locations": [{"file": "credit_card.py", "line": 236}, {"file": "tests/debug/debug_test_api.py", "line": 105}]}, {"name": "main", "locations": [{"file": "credit_card.py", "line": 236}, {"file": "mcp-sequential-thinking/mcp_sequential_thinking/server.py", "line": 219}]}, {"name": "main", "locations": [{"file": "credit_card.py", "line": 236}, {"file": "examples/hulu_anti_detection_quick_start.py", "line": 166}]}, {"name": "main", "locations": [{"file": "credit_card.py", "line": 236}, {"file": "examples/hulu_recaptcha_v3_fixed.py", "line": 816}]}, {"name": "main", "locations": [{"file": "credit_card.py", "line": 236}, {"file": "examples/quick_wins_integration_demo.py", "line": 251}]}, {"name": "main", "locations": [{"file": "credit_card.py", "line": 236}, {"file": "examples/hulu_recaptcha_v3_killer.py", "line": 404}]}, {"name": "main", "locations": [{"file": "credit_card.py", "line": 236}, {"file": "examples/login_with_state_persistence.py", "line": 123}]}, {"name": "main", "locations": [{"file": "credit_card.py", "line": 236}, {"file": "examples/hulu_login_automation.py", "line": 593}]}, {"name": "main", "locations": [{"file": "manual_cleanup.py", "line": 221}, {"file": "hulu_automation_stealth.py", "line": 4606}]}, {"name": "main", "locations": [{"file": "manual_cleanup.py", "line": 221}, {"file": "ci/selector_healthcheck.py", "line": 1511}]}, {"name": "main", "locations": [{"file": "manual_cleanup.py", "line": 221}, {"file": "tests/test_monitor_integration_simple.py", "line": 176}]}, {"name": "main", "locations": [{"file": "manual_cleanup.py", "line": 221}, {"file": "tests/test_element_search_monitoring_integration.py", "line": 200}]}, {"name": "main", "locations": [{"file": "manual_cleanup.py", "line": 221}, {"file": "tests/monitoring_system_final_report.py", "line": 271}]}, {"name": "main", "locations": [{"file": "manual_cleanup.py", "line": 221}, {"file": "tests/test_email_api.py", "line": 49}]}, {"name": "main", "locations": [{"file": "manual_cleanup.py", "line": 221}, {"file": "tests/test_element_location_monitoring.py", "line": 189}]}, {"name": "main", "locations": [{"file": "manual_cleanup.py", "line": 221}, {"file": "tests/test_quick_wins_integration.py", "line": 255}]}, {"name": "main", "locations": [{"file": "manual_cleanup.py", "line": 221}, {"file": "tests/debug/debug_get_them_both_button.py", "line": 650}]}, {"name": "main", "locations": [{"file": "manual_cleanup.py", "line": 221}, {"file": "tests/debug/debug_email_input.py", "line": 173}]}, {"name": "main", "locations": [{"file": "manual_cleanup.py", "line": 221}, {"file": "tests/debug/debug_test_api.py", "line": 105}]}, {"name": "main", "locations": [{"file": "manual_cleanup.py", "line": 221}, {"file": "mcp-sequential-thinking/mcp_sequential_thinking/server.py", "line": 219}]}, {"name": "main", "locations": [{"file": "manual_cleanup.py", "line": 221}, {"file": "examples/hulu_anti_detection_quick_start.py", "line": 166}]}, {"name": "main", "locations": [{"file": "manual_cleanup.py", "line": 221}, {"file": "examples/hulu_recaptcha_v3_fixed.py", "line": 816}]}, {"name": "main", "locations": [{"file": "manual_cleanup.py", "line": 221}, {"file": "examples/quick_wins_integration_demo.py", "line": 251}]}, {"name": "main", "locations": [{"file": "manual_cleanup.py", "line": 221}, {"file": "examples/hulu_recaptcha_v3_killer.py", "line": 404}]}, {"name": "main", "locations": [{"file": "manual_cleanup.py", "line": 221}, {"file": "examples/login_with_state_persistence.py", "line": 123}]}, {"name": "main", "locations": [{"file": "manual_cleanup.py", "line": 221}, {"file": "examples/hulu_login_automation.py", "line": 593}]}, {"name": "main", "locations": [{"file": "hulu_automation_stealth.py", "line": 4606}, {"file": "ci/selector_healthcheck.py", "line": 1511}]}, {"name": "main", "locations": [{"file": "hulu_automation_stealth.py", "line": 4606}, {"file": "tests/test_monitor_integration_simple.py", "line": 176}]}, {"name": "main", "locations": [{"file": "hulu_automation_stealth.py", "line": 4606}, {"file": "tests/test_element_search_monitoring_integration.py", "line": 200}]}, {"name": "main", "locations": [{"file": "hulu_automation_stealth.py", "line": 4606}, {"file": "tests/monitoring_system_final_report.py", "line": 271}]}, {"name": "main", "locations": [{"file": "hulu_automation_stealth.py", "line": 4606}, {"file": "tests/test_email_api.py", "line": 49}]}, {"name": "main", "locations": [{"file": "hulu_automation_stealth.py", "line": 4606}, {"file": "tests/test_element_location_monitoring.py", "line": 189}]}, {"name": "main", "locations": [{"file": "hulu_automation_stealth.py", "line": 4606}, {"file": "tests/test_quick_wins_integration.py", "line": 255}]}, {"name": "main", "locations": [{"file": "hulu_automation_stealth.py", "line": 4606}, {"file": "tests/debug/debug_get_them_both_button.py", "line": 650}]}, {"name": "main", "locations": [{"file": "hulu_automation_stealth.py", "line": 4606}, {"file": "tests/debug/debug_email_input.py", "line": 173}]}, {"name": "main", "locations": [{"file": "hulu_automation_stealth.py", "line": 4606}, {"file": "tests/debug/debug_test_api.py", "line": 105}]}, {"name": "main", "locations": [{"file": "hulu_automation_stealth.py", "line": 4606}, {"file": "mcp-sequential-thinking/mcp_sequential_thinking/server.py", "line": 219}]}, {"name": "main", "locations": [{"file": "hulu_automation_stealth.py", "line": 4606}, {"file": "examples/hulu_anti_detection_quick_start.py", "line": 166}]}, {"name": "main", "locations": [{"file": "hulu_automation_stealth.py", "line": 4606}, {"file": "examples/hulu_recaptcha_v3_fixed.py", "line": 816}]}, {"name": "main", "locations": [{"file": "hulu_automation_stealth.py", "line": 4606}, {"file": "examples/quick_wins_integration_demo.py", "line": 251}]}, {"name": "main", "locations": [{"file": "hulu_automation_stealth.py", "line": 4606}, {"file": "examples/hulu_recaptcha_v3_killer.py", "line": 404}]}, {"name": "main", "locations": [{"file": "hulu_automation_stealth.py", "line": 4606}, {"file": "examples/login_with_state_persistence.py", "line": 123}]}, {"name": "main", "locations": [{"file": "hulu_automation_stealth.py", "line": 4606}, {"file": "examples/hulu_login_automation.py", "line": 593}]}, {"name": "main", "locations": [{"file": "ci/selector_healthcheck.py", "line": 1511}, {"file": "tests/test_monitor_integration_simple.py", "line": 176}]}, {"name": "main", "locations": [{"file": "ci/selector_healthcheck.py", "line": 1511}, {"file": "tests/test_element_search_monitoring_integration.py", "line": 200}]}, {"name": "main", "locations": [{"file": "ci/selector_healthcheck.py", "line": 1511}, {"file": "tests/monitoring_system_final_report.py", "line": 271}]}, {"name": "main", "locations": [{"file": "ci/selector_healthcheck.py", "line": 1511}, {"file": "tests/test_email_api.py", "line": 49}]}, {"name": "main", "locations": [{"file": "ci/selector_healthcheck.py", "line": 1511}, {"file": "tests/test_element_location_monitoring.py", "line": 189}]}, {"name": "main", "locations": [{"file": "ci/selector_healthcheck.py", "line": 1511}, {"file": "tests/test_quick_wins_integration.py", "line": 255}]}, {"name": "main", "locations": [{"file": "ci/selector_healthcheck.py", "line": 1511}, {"file": "tests/debug/debug_get_them_both_button.py", "line": 650}]}, {"name": "main", "locations": [{"file": "ci/selector_healthcheck.py", "line": 1511}, {"file": "tests/debug/debug_email_input.py", "line": 173}]}, {"name": "main", "locations": [{"file": "ci/selector_healthcheck.py", "line": 1511}, {"file": "tests/debug/debug_test_api.py", "line": 105}]}, {"name": "main", "locations": [{"file": "ci/selector_healthcheck.py", "line": 1511}, {"file": "mcp-sequential-thinking/mcp_sequential_thinking/server.py", "line": 219}]}, {"name": "main", "locations": [{"file": "ci/selector_healthcheck.py", "line": 1511}, {"file": "examples/hulu_anti_detection_quick_start.py", "line": 166}]}, {"name": "main", "locations": [{"file": "ci/selector_healthcheck.py", "line": 1511}, {"file": "examples/hulu_recaptcha_v3_fixed.py", "line": 816}]}, {"name": "main", "locations": [{"file": "ci/selector_healthcheck.py", "line": 1511}, {"file": "examples/quick_wins_integration_demo.py", "line": 251}]}, {"name": "main", "locations": [{"file": "ci/selector_healthcheck.py", "line": 1511}, {"file": "examples/hulu_recaptcha_v3_killer.py", "line": 404}]}, {"name": "main", "locations": [{"file": "ci/selector_healthcheck.py", "line": 1511}, {"file": "examples/login_with_state_persistence.py", "line": 123}]}, {"name": "main", "locations": [{"file": "ci/selector_healthcheck.py", "line": 1511}, {"file": "examples/hulu_login_automation.py", "line": 593}]}, {"name": "main", "locations": [{"file": "tests/test_monitor_integration_simple.py", "line": 176}, {"file": "tests/test_element_search_monitoring_integration.py", "line": 200}]}, {"name": "main", "locations": [{"file": "tests/test_monitor_integration_simple.py", "line": 176}, {"file": "tests/monitoring_system_final_report.py", "line": 271}]}, {"name": "main", "locations": [{"file": "tests/test_monitor_integration_simple.py", "line": 176}, {"file": "tests/test_email_api.py", "line": 49}]}, {"name": "main", "locations": [{"file": "tests/test_monitor_integration_simple.py", "line": 176}, {"file": "tests/test_element_location_monitoring.py", "line": 189}]}, {"name": "main", "locations": [{"file": "tests/test_monitor_integration_simple.py", "line": 176}, {"file": "tests/test_quick_wins_integration.py", "line": 255}]}, {"name": "main", "locations": [{"file": "tests/test_monitor_integration_simple.py", "line": 176}, {"file": "tests/debug/debug_get_them_both_button.py", "line": 650}]}, {"name": "main", "locations": [{"file": "tests/test_monitor_integration_simple.py", "line": 176}, {"file": "tests/debug/debug_email_input.py", "line": 173}]}, {"name": "main", "locations": [{"file": "tests/test_monitor_integration_simple.py", "line": 176}, {"file": "tests/debug/debug_test_api.py", "line": 105}]}, {"name": "main", "locations": [{"file": "tests/test_monitor_integration_simple.py", "line": 176}, {"file": "mcp-sequential-thinking/mcp_sequential_thinking/server.py", "line": 219}]}, {"name": "main", "locations": [{"file": "tests/test_monitor_integration_simple.py", "line": 176}, {"file": "examples/hulu_anti_detection_quick_start.py", "line": 166}]}, {"name": "main", "locations": [{"file": "tests/test_monitor_integration_simple.py", "line": 176}, {"file": "examples/hulu_recaptcha_v3_fixed.py", "line": 816}]}, {"name": "main", "locations": [{"file": "tests/test_monitor_integration_simple.py", "line": 176}, {"file": "examples/quick_wins_integration_demo.py", "line": 251}]}, {"name": "main", "locations": [{"file": "tests/test_monitor_integration_simple.py", "line": 176}, {"file": "examples/hulu_recaptcha_v3_killer.py", "line": 404}]}, {"name": "main", "locations": [{"file": "tests/test_monitor_integration_simple.py", "line": 176}, {"file": "examples/login_with_state_persistence.py", "line": 123}]}, {"name": "main", "locations": [{"file": "tests/test_monitor_integration_simple.py", "line": 176}, {"file": "examples/hulu_login_automation.py", "line": 593}]}, {"name": "main", "locations": [{"file": "tests/test_element_search_monitoring_integration.py", "line": 200}, {"file": "tests/monitoring_system_final_report.py", "line": 271}]}, {"name": "main", "locations": [{"file": "tests/test_element_search_monitoring_integration.py", "line": 200}, {"file": "tests/test_email_api.py", "line": 49}]}, {"name": "main", "locations": [{"file": "tests/test_element_search_monitoring_integration.py", "line": 200}, {"file": "tests/test_element_location_monitoring.py", "line": 189}]}, {"name": "main", "locations": [{"file": "tests/test_element_search_monitoring_integration.py", "line": 200}, {"file": "tests/test_quick_wins_integration.py", "line": 255}]}, {"name": "main", "locations": [{"file": "tests/test_element_search_monitoring_integration.py", "line": 200}, {"file": "tests/debug/debug_get_them_both_button.py", "line": 650}]}, {"name": "main", "locations": [{"file": "tests/test_element_search_monitoring_integration.py", "line": 200}, {"file": "tests/debug/debug_email_input.py", "line": 173}]}, {"name": "main", "locations": [{"file": "tests/test_element_search_monitoring_integration.py", "line": 200}, {"file": "tests/debug/debug_test_api.py", "line": 105}]}, {"name": "main", "locations": [{"file": "tests/test_element_search_monitoring_integration.py", "line": 200}, {"file": "mcp-sequential-thinking/mcp_sequential_thinking/server.py", "line": 219}]}, {"name": "main", "locations": [{"file": "tests/test_element_search_monitoring_integration.py", "line": 200}, {"file": "examples/hulu_anti_detection_quick_start.py", "line": 166}]}, {"name": "main", "locations": [{"file": "tests/test_element_search_monitoring_integration.py", "line": 200}, {"file": "examples/hulu_recaptcha_v3_fixed.py", "line": 816}]}, {"name": "main", "locations": [{"file": "tests/test_element_search_monitoring_integration.py", "line": 200}, {"file": "examples/quick_wins_integration_demo.py", "line": 251}]}, {"name": "main", "locations": [{"file": "tests/test_element_search_monitoring_integration.py", "line": 200}, {"file": "examples/hulu_recaptcha_v3_killer.py", "line": 404}]}, {"name": "main", "locations": [{"file": "tests/test_element_search_monitoring_integration.py", "line": 200}, {"file": "examples/login_with_state_persistence.py", "line": 123}]}, {"name": "main", "locations": [{"file": "tests/test_element_search_monitoring_integration.py", "line": 200}, {"file": "examples/hulu_login_automation.py", "line": 593}]}, {"name": "main", "locations": [{"file": "tests/monitoring_system_final_report.py", "line": 271}, {"file": "tests/test_email_api.py", "line": 49}]}, {"name": "main", "locations": [{"file": "tests/monitoring_system_final_report.py", "line": 271}, {"file": "tests/test_element_location_monitoring.py", "line": 189}]}, {"name": "main", "locations": [{"file": "tests/monitoring_system_final_report.py", "line": 271}, {"file": "tests/test_quick_wins_integration.py", "line": 255}]}, {"name": "main", "locations": [{"file": "tests/monitoring_system_final_report.py", "line": 271}, {"file": "tests/debug/debug_get_them_both_button.py", "line": 650}]}, {"name": "main", "locations": [{"file": "tests/monitoring_system_final_report.py", "line": 271}, {"file": "tests/debug/debug_email_input.py", "line": 173}]}, {"name": "main", "locations": [{"file": "tests/monitoring_system_final_report.py", "line": 271}, {"file": "tests/debug/debug_test_api.py", "line": 105}]}, {"name": "main", "locations": [{"file": "tests/monitoring_system_final_report.py", "line": 271}, {"file": "mcp-sequential-thinking/mcp_sequential_thinking/server.py", "line": 219}]}, {"name": "main", "locations": [{"file": "tests/monitoring_system_final_report.py", "line": 271}, {"file": "examples/hulu_anti_detection_quick_start.py", "line": 166}]}, {"name": "main", "locations": [{"file": "tests/monitoring_system_final_report.py", "line": 271}, {"file": "examples/hulu_recaptcha_v3_fixed.py", "line": 816}]}, {"name": "main", "locations": [{"file": "tests/monitoring_system_final_report.py", "line": 271}, {"file": "examples/quick_wins_integration_demo.py", "line": 251}]}, {"name": "main", "locations": [{"file": "tests/monitoring_system_final_report.py", "line": 271}, {"file": "examples/hulu_recaptcha_v3_killer.py", "line": 404}]}, {"name": "main", "locations": [{"file": "tests/monitoring_system_final_report.py", "line": 271}, {"file": "examples/login_with_state_persistence.py", "line": 123}]}, {"name": "main", "locations": [{"file": "tests/monitoring_system_final_report.py", "line": 271}, {"file": "examples/hulu_login_automation.py", "line": 593}]}, {"name": "main", "locations": [{"file": "tests/test_email_api.py", "line": 49}, {"file": "tests/test_element_location_monitoring.py", "line": 189}]}, {"name": "main", "locations": [{"file": "tests/test_email_api.py", "line": 49}, {"file": "tests/test_quick_wins_integration.py", "line": 255}]}, {"name": "main", "locations": [{"file": "tests/test_email_api.py", "line": 49}, {"file": "tests/debug/debug_get_them_both_button.py", "line": 650}]}, {"name": "main", "locations": [{"file": "tests/test_email_api.py", "line": 49}, {"file": "tests/debug/debug_email_input.py", "line": 173}]}, {"name": "main", "locations": [{"file": "tests/test_email_api.py", "line": 49}, {"file": "tests/debug/debug_test_api.py", "line": 105}]}, {"name": "main", "locations": [{"file": "tests/test_email_api.py", "line": 49}, {"file": "mcp-sequential-thinking/mcp_sequential_thinking/server.py", "line": 219}]}, {"name": "main", "locations": [{"file": "tests/test_email_api.py", "line": 49}, {"file": "examples/hulu_anti_detection_quick_start.py", "line": 166}]}, {"name": "main", "locations": [{"file": "tests/test_email_api.py", "line": 49}, {"file": "examples/hulu_recaptcha_v3_fixed.py", "line": 816}]}, {"name": "main", "locations": [{"file": "tests/test_email_api.py", "line": 49}, {"file": "examples/quick_wins_integration_demo.py", "line": 251}]}, {"name": "main", "locations": [{"file": "tests/test_email_api.py", "line": 49}, {"file": "examples/hulu_recaptcha_v3_killer.py", "line": 404}]}, {"name": "main", "locations": [{"file": "tests/test_email_api.py", "line": 49}, {"file": "examples/login_with_state_persistence.py", "line": 123}]}, {"name": "main", "locations": [{"file": "tests/test_email_api.py", "line": 49}, {"file": "examples/hulu_login_automation.py", "line": 593}]}, {"name": "main", "locations": [{"file": "tests/test_element_location_monitoring.py", "line": 189}, {"file": "tests/test_quick_wins_integration.py", "line": 255}]}, {"name": "main", "locations": [{"file": "tests/test_element_location_monitoring.py", "line": 189}, {"file": "tests/debug/debug_get_them_both_button.py", "line": 650}]}, {"name": "main", "locations": [{"file": "tests/test_element_location_monitoring.py", "line": 189}, {"file": "tests/debug/debug_email_input.py", "line": 173}]}, {"name": "main", "locations": [{"file": "tests/test_element_location_monitoring.py", "line": 189}, {"file": "tests/debug/debug_test_api.py", "line": 105}]}, {"name": "main", "locations": [{"file": "tests/test_element_location_monitoring.py", "line": 189}, {"file": "mcp-sequential-thinking/mcp_sequential_thinking/server.py", "line": 219}]}, {"name": "main", "locations": [{"file": "tests/test_element_location_monitoring.py", "line": 189}, {"file": "examples/hulu_anti_detection_quick_start.py", "line": 166}]}, {"name": "main", "locations": [{"file": "tests/test_element_location_monitoring.py", "line": 189}, {"file": "examples/hulu_recaptcha_v3_fixed.py", "line": 816}]}, {"name": "main", "locations": [{"file": "tests/test_element_location_monitoring.py", "line": 189}, {"file": "examples/quick_wins_integration_demo.py", "line": 251}]}, {"name": "main", "locations": [{"file": "tests/test_element_location_monitoring.py", "line": 189}, {"file": "examples/hulu_recaptcha_v3_killer.py", "line": 404}]}, {"name": "main", "locations": [{"file": "tests/test_element_location_monitoring.py", "line": 189}, {"file": "examples/login_with_state_persistence.py", "line": 123}]}, {"name": "main", "locations": [{"file": "tests/test_element_location_monitoring.py", "line": 189}, {"file": "examples/hulu_login_automation.py", "line": 593}]}, {"name": "main", "locations": [{"file": "tests/test_quick_wins_integration.py", "line": 255}, {"file": "tests/debug/debug_get_them_both_button.py", "line": 650}]}, {"name": "main", "locations": [{"file": "tests/test_quick_wins_integration.py", "line": 255}, {"file": "tests/debug/debug_email_input.py", "line": 173}]}, {"name": "main", "locations": [{"file": "tests/test_quick_wins_integration.py", "line": 255}, {"file": "tests/debug/debug_test_api.py", "line": 105}]}, {"name": "main", "locations": [{"file": "tests/test_quick_wins_integration.py", "line": 255}, {"file": "mcp-sequential-thinking/mcp_sequential_thinking/server.py", "line": 219}]}, {"name": "main", "locations": [{"file": "tests/test_quick_wins_integration.py", "line": 255}, {"file": "examples/hulu_anti_detection_quick_start.py", "line": 166}]}, {"name": "main", "locations": [{"file": "tests/test_quick_wins_integration.py", "line": 255}, {"file": "examples/hulu_recaptcha_v3_fixed.py", "line": 816}]}, {"name": "main", "locations": [{"file": "tests/test_quick_wins_integration.py", "line": 255}, {"file": "examples/quick_wins_integration_demo.py", "line": 251}]}, {"name": "main", "locations": [{"file": "tests/test_quick_wins_integration.py", "line": 255}, {"file": "examples/hulu_recaptcha_v3_killer.py", "line": 404}]}, {"name": "main", "locations": [{"file": "tests/test_quick_wins_integration.py", "line": 255}, {"file": "examples/login_with_state_persistence.py", "line": 123}]}, {"name": "main", "locations": [{"file": "tests/test_quick_wins_integration.py", "line": 255}, {"file": "examples/hulu_login_automation.py", "line": 593}]}, {"name": "main", "locations": [{"file": "tests/debug/debug_get_them_both_button.py", "line": 650}, {"file": "tests/debug/debug_email_input.py", "line": 173}]}, {"name": "main", "locations": [{"file": "tests/debug/debug_get_them_both_button.py", "line": 650}, {"file": "tests/debug/debug_test_api.py", "line": 105}]}, {"name": "main", "locations": [{"file": "tests/debug/debug_get_them_both_button.py", "line": 650}, {"file": "mcp-sequential-thinking/mcp_sequential_thinking/server.py", "line": 219}]}, {"name": "main", "locations": [{"file": "tests/debug/debug_get_them_both_button.py", "line": 650}, {"file": "examples/hulu_anti_detection_quick_start.py", "line": 166}]}, {"name": "main", "locations": [{"file": "tests/debug/debug_get_them_both_button.py", "line": 650}, {"file": "examples/hulu_recaptcha_v3_fixed.py", "line": 816}]}, {"name": "main", "locations": [{"file": "tests/debug/debug_get_them_both_button.py", "line": 650}, {"file": "examples/quick_wins_integration_demo.py", "line": 251}]}, {"name": "main", "locations": [{"file": "tests/debug/debug_get_them_both_button.py", "line": 650}, {"file": "examples/hulu_recaptcha_v3_killer.py", "line": 404}]}, {"name": "main", "locations": [{"file": "tests/debug/debug_get_them_both_button.py", "line": 650}, {"file": "examples/login_with_state_persistence.py", "line": 123}]}, {"name": "main", "locations": [{"file": "tests/debug/debug_get_them_both_button.py", "line": 650}, {"file": "examples/hulu_login_automation.py", "line": 593}]}, {"name": "main", "locations": [{"file": "tests/debug/debug_email_input.py", "line": 173}, {"file": "tests/debug/debug_test_api.py", "line": 105}]}, {"name": "main", "locations": [{"file": "tests/debug/debug_email_input.py", "line": 173}, {"file": "mcp-sequential-thinking/mcp_sequential_thinking/server.py", "line": 219}]}, {"name": "main", "locations": [{"file": "tests/debug/debug_email_input.py", "line": 173}, {"file": "examples/hulu_anti_detection_quick_start.py", "line": 166}]}, {"name": "main", "locations": [{"file": "tests/debug/debug_email_input.py", "line": 173}, {"file": "examples/hulu_recaptcha_v3_fixed.py", "line": 816}]}, {"name": "main", "locations": [{"file": "tests/debug/debug_email_input.py", "line": 173}, {"file": "examples/quick_wins_integration_demo.py", "line": 251}]}, {"name": "main", "locations": [{"file": "tests/debug/debug_email_input.py", "line": 173}, {"file": "examples/hulu_recaptcha_v3_killer.py", "line": 404}]}, {"name": "main", "locations": [{"file": "tests/debug/debug_email_input.py", "line": 173}, {"file": "examples/login_with_state_persistence.py", "line": 123}]}, {"name": "main", "locations": [{"file": "tests/debug/debug_email_input.py", "line": 173}, {"file": "examples/hulu_login_automation.py", "line": 593}]}, {"name": "main", "locations": [{"file": "tests/debug/debug_test_api.py", "line": 105}, {"file": "mcp-sequential-thinking/mcp_sequential_thinking/server.py", "line": 219}]}, {"name": "main", "locations": [{"file": "tests/debug/debug_test_api.py", "line": 105}, {"file": "examples/hulu_anti_detection_quick_start.py", "line": 166}]}, {"name": "main", "locations": [{"file": "tests/debug/debug_test_api.py", "line": 105}, {"file": "examples/hulu_recaptcha_v3_fixed.py", "line": 816}]}, {"name": "main", "locations": [{"file": "tests/debug/debug_test_api.py", "line": 105}, {"file": "examples/quick_wins_integration_demo.py", "line": 251}]}, {"name": "main", "locations": [{"file": "tests/debug/debug_test_api.py", "line": 105}, {"file": "examples/hulu_recaptcha_v3_killer.py", "line": 404}]}, {"name": "main", "locations": [{"file": "tests/debug/debug_test_api.py", "line": 105}, {"file": "examples/login_with_state_persistence.py", "line": 123}]}, {"name": "main", "locations": [{"file": "tests/debug/debug_test_api.py", "line": 105}, {"file": "examples/hulu_login_automation.py", "line": 593}]}, {"name": "main", "locations": [{"file": "mcp-sequential-thinking/mcp_sequential_thinking/server.py", "line": 219}, {"file": "examples/hulu_anti_detection_quick_start.py", "line": 166}]}, {"name": "main", "locations": [{"file": "mcp-sequential-thinking/mcp_sequential_thinking/server.py", "line": 219}, {"file": "examples/hulu_recaptcha_v3_fixed.py", "line": 816}]}, {"name": "main", "locations": [{"file": "mcp-sequential-thinking/mcp_sequential_thinking/server.py", "line": 219}, {"file": "examples/quick_wins_integration_demo.py", "line": 251}]}, {"name": "main", "locations": [{"file": "mcp-sequential-thinking/mcp_sequential_thinking/server.py", "line": 219}, {"file": "examples/hulu_recaptcha_v3_killer.py", "line": 404}]}, {"name": "main", "locations": [{"file": "mcp-sequential-thinking/mcp_sequential_thinking/server.py", "line": 219}, {"file": "examples/login_with_state_persistence.py", "line": 123}]}, {"name": "main", "locations": [{"file": "mcp-sequential-thinking/mcp_sequential_thinking/server.py", "line": 219}, {"file": "examples/hulu_login_automation.py", "line": 593}]}, {"name": "main", "locations": [{"file": "examples/hulu_anti_detection_quick_start.py", "line": 166}, {"file": "examples/hulu_recaptcha_v3_fixed.py", "line": 816}]}, {"name": "main", "locations": [{"file": "examples/hulu_anti_detection_quick_start.py", "line": 166}, {"file": "examples/quick_wins_integration_demo.py", "line": 251}]}, {"name": "main", "locations": [{"file": "examples/hulu_anti_detection_quick_start.py", "line": 166}, {"file": "examples/hulu_recaptcha_v3_killer.py", "line": 404}]}, {"name": "main", "locations": [{"file": "examples/hulu_anti_detection_quick_start.py", "line": 166}, {"file": "examples/login_with_state_persistence.py", "line": 123}]}, {"name": "main", "locations": [{"file": "examples/hulu_anti_detection_quick_start.py", "line": 166}, {"file": "examples/hulu_login_automation.py", "line": 593}]}, {"name": "main", "locations": [{"file": "examples/hulu_recaptcha_v3_fixed.py", "line": 816}, {"file": "examples/quick_wins_integration_demo.py", "line": 251}]}, {"name": "main", "locations": [{"file": "examples/hulu_recaptcha_v3_fixed.py", "line": 816}, {"file": "examples/hulu_recaptcha_v3_killer.py", "line": 404}]}, {"name": "main", "locations": [{"file": "examples/hulu_recaptcha_v3_fixed.py", "line": 816}, {"file": "examples/login_with_state_persistence.py", "line": 123}]}, {"name": "main", "locations": [{"file": "examples/hulu_recaptcha_v3_fixed.py", "line": 816}, {"file": "examples/hulu_login_automation.py", "line": 593}]}, {"name": "main", "locations": [{"file": "examples/quick_wins_integration_demo.py", "line": 251}, {"file": "examples/hulu_recaptcha_v3_killer.py", "line": 404}]}, {"name": "main", "locations": [{"file": "examples/quick_wins_integration_demo.py", "line": 251}, {"file": "examples/login_with_state_persistence.py", "line": 123}]}, {"name": "main", "locations": [{"file": "examples/quick_wins_integration_demo.py", "line": 251}, {"file": "examples/hulu_login_automation.py", "line": 593}]}, {"name": "main", "locations": [{"file": "examples/hulu_recaptcha_v3_killer.py", "line": 404}, {"file": "examples/login_with_state_persistence.py", "line": 123}]}, {"name": "main", "locations": [{"file": "examples/hulu_recaptcha_v3_killer.py", "line": 404}, {"file": "examples/hulu_login_automation.py", "line": 593}]}, {"name": "main", "locations": [{"file": "examples/login_with_state_persistence.py", "line": 123}, {"file": "examples/hulu_login_automation.py", "line": 593}]}, {"name": "IntelligentWaitingManager", "locations": [{"file": "intelligent_waiting_manager.py", "line": 403}, {"file": "infrastructure/intelligent_waiting_manager.py", "line": 48}]}, {"name": "setup_logging", "locations": [{"file": "hulu_automation_stealth.py", "line": 109}, {"file": "utils/logger.py", "line": 402}]}, {"name": "setup_logging", "locations": [{"file": "hulu_automation_stealth.py", "line": 109}, {"file": "utils/utils.py", "line": 406}]}, {"name": "setup_logging", "locations": [{"file": "utils/logger.py", "line": 402}, {"file": "utils/utils.py", "line": 406}]}, {"name": "BehaviorSimulator", "locations": [{"file": "anti_detection/behavior_simulator.py", "line": 21}, {"file": "infrastructure/anti_detection/behavior_simulator.py", "line": 220}]}, {"name": "TestConfig", "locations": [{"file": "tests/test_element_search_monitoring_integration.py", "line": 35}, {"file": "tests/monitoring_system_final_report.py", "line": 59}]}, {"name": "HuluLoginBot", "locations": [{"file": "tests/debug/debug_test_api.py", "line": 27}, {"file": "examples/hulu_login_automation.py", "line": 266}]}, {"name": "configure_logging", "locations": [{"file": "utils/logger.py", "line": 351}, {"file": "mcp-sequential-thinking/mcp_sequential_thinking/logging_conf.py", "line": 5}]}, {"name": "HumanizedElementLocator", "locations": [{"file": "hulu_automation/infrastructure/humanized_element_locator.py", "line": 17}, {"file": "examples/hulu_login_automation.py", "line": 218}]}, {"name": "AntiDetectionService", "locations": [{"file": "hulu_automation/infrastructure/anti_detection_service.py", "line": 17}, {"file": "examples/hulu_login_automation.py", "line": 185}]}, {"name": "AntiDetectionService", "locations": [{"file": "hulu_automation/infrastructure/anti_detection_service.py", "line": 17}, {"file": "infrastructure/anti_detection/service.py", "line": 18}]}, {"name": "AntiDetectionService", "locations": [{"file": "examples/hulu_login_automation.py", "line": 185}, {"file": "infrastructure/anti_detection/service.py", "line": 18}]}]}