#!/usr/bin/env python3
"""
信用卡校验学习模块主程序
演示完整的信用卡数据处理和验证流程
"""

import os
import sys
import logging
from pathlib import Path
from datetime import datetime

# 添加模块路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from credit_card_validator import (
    SecureDataProcessor,
    CreditCardValidator
)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 默认路径配置
DEFAULT_INPUT_FILE = "data/data_input/fake_data.txt"
DEFAULT_OUTPUT_BASE_DIR = "data/data_output"

def ensure_directories():
    """确保必要的目录存在"""
    input_dir = Path("data/data_input")
    output_dir = Path(DEFAULT_OUTPUT_BASE_DIR)

    input_dir.mkdir(parents=True, exist_ok=True)
    output_dir.mkdir(parents=True, exist_ok=True)

    return input_dir, output_dir

def generate_output_dir_name(base_dir: str = None) -> str:
    """生成带时间戳的输出目录名称"""
    if base_dir is None:
        base_dir = DEFAULT_OUTPUT_BASE_DIR

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir_name = f"validation_results_{timestamp}"

    return os.path.join(base_dir, output_dir_name)


def demo_quick_validation():
    """演示快速验证功能"""
    print("\n" + "="*60)
    print("🔍 信用卡快速验证演示")
    print("="*60)
    
    # 测试用例（这些是算法生成的测试卡号，非真实卡号）
    test_cases = [
        {
            'card_number': '****************',
            'expiry_month': '12',
            'expiry_year': '25',
            'cvv': '123',
            'description': 'Valid Visa Card'
        },
        {
            'card_number': '****************',
            'expiry_month': '01',
            'expiry_year': '26',
            'cvv': '456',
            'description': 'Valid Mastercard'
        },
        {
            'card_number': '***************',
            'expiry_month': '03',
            'expiry_year': '24',
            'cvv': '1234',
            'description': 'Valid American Express'
        },
        {
            'card_number': '1234567890123456',
            'expiry_month': '12',
            'expiry_year': '25',
            'cvv': '123',
            'description': 'Invalid Card (Luhn Fail)'
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 测试用例 {i}: {test_case['description']}")
        print(f"   卡号: {test_case['card_number']}")
        print(f"   有效期: {test_case['expiry_month']}/{test_case['expiry_year']}")
        print(f"   CVV: {test_case['cvv']}")
        
        # 执行验证
        validator = CreditCardValidator()
        result = validator.validate_card(
            test_case['card_number'],
            test_case['expiry_month'],
            test_case['expiry_year'],
            test_case['cvv']
        )
        
        # 显示结果
        print(f"   ✅ 验证结果: {'通过' if result.is_valid else '失败'}")
        print(f"   🏷️  卡片品牌: {result.card_type}")
        print(f"   🔢 掩码卡号: {result.card_number_masked}")
        print(f"   📊 风险评分: {result.risk_score:.2f}")
        
        if result.errors:
            print(f"   ❌ 错误信息: {', '.join(result.errors)}")
        
        print(f"   🔍 详细验证:")
        print(f"      - Luhn算法: {'✅' if result.luhn_valid else '❌'}")
        print(f"      - 格式验证: {'✅' if result.format_valid else '❌'}")
        print(f"      - 有效期: {'✅' if result.expiry_valid else '❌'}")
        print(f"      - CVV验证: {'✅' if result.cvv_valid else '❌'}")


def demo_file_processing():
    """演示文件处理功能"""
    print("\n" + "="*60)
    print("📁 信用卡数据文件处理演示")
    print("="*60)

    # 确保目录存在
    ensure_directories()

    # 使用固定的默认路径
    input_file = DEFAULT_INPUT_FILE
    output_dir = generate_output_dir_name()

    if not os.path.exists(input_file):
        print(f"❌ 输入文件不存在: {input_file}")
        print(f"💡 请先创建示例数据: python credit_card_demo.py --create-sample")
        return

    print(f"📂 输入文件: {input_file}")
    print(f"📂 输出目录: {output_dir}")
    
    try:
        # 创建安全处理器
        print("\n🔐 初始化安全处理器...")
        processor = SecureDataProcessor("demo_master_password_2024")
        
        # 处理文件
        print("⚙️  开始处理数据文件...")
        stats = processor.process_txt_file(input_file)
        
        # 显示统计结果
        print("\n📊 处理统计:")
        print(f"   总记录数: {stats['total_records']}")
        print(f"   有效记录: {stats['valid_records']}")
        print(f"   无效记录: {stats['invalid_records']}")
        print(f"   解析错误: {stats['parsing_errors']}")
        
        # 显示输出文件
        print(f"\n📋 生成的文件:")
        output_path = Path(output_dir)
        if output_path.exists():
            for file_path in output_path.iterdir():
                if file_path.is_file():
                    size = file_path.stat().st_size
                    print(f"   📄 {file_path.name} ({size} bytes)")
        
        print(f"\n✅ 处理完成！结果保存在: {output_dir}")
        
    except Exception as e:
        logger.error(f"文件处理失败: {e}")
        print(f"❌ 处理失败: {e}")


def demo_security_features():
    """演示安全功能"""
    print("\n" + "="*60)
    print("🔒 安全功能演示")
    print("="*60)
    
    from credit_card_validator.core.encryption import SecureCardDataHandler
    
    # 创建加密处理器
    print("🔐 创建安全加密处理器...")
    handler = SecureCardDataHandler("demo_password_123")
    
    # 演示数据加密
    test_card = "****************"
    print(f"📝 原始卡号: {test_card}")
    
    # 加密卡号
    encrypted_info = handler.encrypt_card_number(test_card)
    print(f"🔒 加密结果:")
    print(f"   - 掩码显示: {encrypted_info['masked_display']}")
    print(f"   - BIN码: {encrypted_info['bin']}")
    print(f"   - 后四位: {encrypted_info['last4']}")
    print(f"   - 加密数据: {encrypted_info['encrypted_full'][:50]}...")
    
    # 解密验证
    decrypted = handler.decrypt_data(encrypted_info['encrypted_full'])
    print(f"🔓 解密验证: {'✅ 成功' if decrypted == test_card else '❌ 失败'}")
    
    # 演示哈希功能
    hash_value = handler.hash_sensitive_data(test_card)
    print(f"🔗 数据哈希: {hash_value[:16]}...")
    
    # 安全清理
    print("🧹 执行安全内存清理...")
    handler.secure_wipe_memory()
    print("✅ 安全功能演示完成")


def create_sample_data():
    """创建示例数据文件"""
    # 确保目录存在
    ensure_directories()
    file_path = DEFAULT_INPUT_FILE

    # 确保目标目录存在
    Path(file_path).parent.mkdir(parents=True, exist_ok=True)

    sample_data = [
        "**************** 12 25 123 90210 123 Main St Los Angeles CA US John Doe",
        "**************** 01 26 456 10001 456 Broadway New York NY US Jane Smith",
        "*************** 03 24 1234 60601 789 Oak Ave Chicago IL US Bob Johnson",
        "1234567890123456 12 25 999 12345 Invalid Card Data Test"
    ]

    with open(file_path, 'w', encoding='utf-8') as f:
        for line in sample_data:
            f.write(line + '\n')

    print(f"📝 示例数据文件已创建: {file_path}")


def main():
    """主函数"""
    # 显示欢迎信息
    print("🎓 信用卡校验学习模块演示程序")
    print("=" * 60)
    print("⚠️  注意: 本程序仅用于学习和演示目的")
    print("🔒 所有数据处理都采用加密保护")
    print("=" * 60)

    try:
        # 确保目录存在并创建示例文件
        ensure_directories()
        create_sample_data()

        # 自动执行完整的演示流程
        demo_quick_validation()
        demo_file_processing()
        demo_security_features()

    except KeyboardInterrupt:
        print("\n\n⏹️  程序被用户中断")
    except Exception as e:
        logger.error(f"程序执行错误: {e}")
        print(f"\n❌ 程序执行错误: {e}")
        sys.exit(1)

    print("\n🎉 演示程序执行完成！")


if __name__ == "__main__":
    main()
