#!/usr/bin/env python3
"""
Phase 2: 并行测试环境搭建

建立新旧架构的并行对比测试环境，包括:
- 性能对比分析
- 功能一致性验证
- 资源消耗监控
- 错误处理对比
"""

import asyncio
import logging
import time
import sys
import os
import json
import psutil
import tracemalloc
from datetime import datetime
from typing import Dict, Any, List, Tuple
from concurrent.futures import ThreadPoolExecutor

# 添加项目路径
sys.path.insert(0, os.path.dirname(__file__))

# 导入两套架构
from hulu_automation_stealth import HuluStealthAutomation
try:
    from hulu_automation_stealth_v2.core import AutomationEngine
    from hulu_automation_stealth_facade import HuluStealthAutomation as FacadeAutomation
    NEW_ARCHITECTURE_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ 新架构组件不可用: {e}")
    NEW_ARCHITECTURE_AVAILABLE = False


class ParallelTester:
    """
    并行测试环境管理器
    
    功能:
    - 新旧架构并行初始化
    - 性能指标实时监控
    - 功能一致性验证
    - 资源使用对比分析
    """
    
    def __init__(self):
        self.setup_logging()
        self.test_results = {
            "test_start_time": datetime.now().isoformat(),
            "original_architecture": {},
            "new_architecture": {},
            "facade_architecture": {},
            "performance_comparison": {},
            "resource_usage": {},
            "consistency_tests": {}
        }
        
        # 性能监控
        self.process = psutil.Process()
        
    def setup_logging(self):
        """设置测试日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(f'phase2_parallel_test_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    async def run_parallel_tests(self) -> Dict[str, Any]:
        """运行并行对比测试"""
        print("🚀 开始 Phase 2 并行测试环境验证")
        print("=" * 60)
        
        try:
            # 启动内存追踪
            tracemalloc.start()
            
            # 测试1: 并行初始化性能对比
            await self.test_parallel_initialization()
            
            # 测试2: 资源使用监控
            await self.test_resource_usage()
            
            # 测试3: 功能一致性验证
            if NEW_ARCHITECTURE_AVAILABLE:
                await self.test_functionality_consistency()
            
            # 测试4: 负载压力测试
            await self.test_load_performance()
            
            # 测试5: 错误处理对比
            await self.test_error_handling_comparison()
            
            # 生成对比报告
            self.generate_comparison_report()
            
            return self.test_results
            
        except Exception as e:
            self.logger.error(f"并行测试失败: {e}")
            import traceback
            traceback.print_exc()
            return {"error": str(e), "test_results": self.test_results}
        finally:
            # 停止内存追踪
            if tracemalloc.is_tracing():
                tracemalloc.stop()
    
    async def test_parallel_initialization(self):
        """并行初始化性能对比"""
        print("\n⚡ 测试1: 并行初始化性能对比")
        print("-" * 40)
        
        # 原始架构初始化测试
        print("  🔍 原始架构初始化测试...")
        original_metrics = await self._test_original_initialization()
        self.test_results["original_architecture"] = original_metrics
        print(f"  ✅ 原始架构: {original_metrics['avg_init_time']:.3f}s (平均)")
        
        if NEW_ARCHITECTURE_AVAILABLE:
            # 新架构初始化测试
            print("  🆕 新架构初始化测试...")
            new_metrics = await self._test_new_architecture_initialization()
            self.test_results["new_architecture"] = new_metrics
            print(f"  ✅ 新架构: {new_metrics['avg_init_time']:.3f}s (平均)")
            
            # Facade适配器测试
            print("  🎭 Facade适配器测试...")
            facade_metrics = await self._test_facade_initialization()
            self.test_results["facade_architecture"] = facade_metrics
            print(f"  ✅ Facade适配器: {facade_metrics['avg_init_time']:.3f}s (平均)")
            
            # 性能对比计算
            self._calculate_performance_comparison()
        
        print("  🎉 并行初始化测试完成")
    
    async def _test_original_initialization(self, iterations: int = 5) -> Dict[str, Any]:
        """测试原始架构初始化"""
        times = []
        memory_usage = []
        
        for i in range(iterations):
            # 记录初始内存
            initial_memory = self.process.memory_info().rss / 1024 / 1024  # MB
            
            start_time = time.time()
            try:
                automation = HuluStealthAutomation(
                    debug_port=9300 + i,
                    auto_launch=False,
                    persistent_session=True
                )
                init_time = time.time() - start_time
                times.append(init_time)
                
                # 记录峰值内存
                peak_memory = self.process.memory_info().rss / 1024 / 1024  # MB
                memory_usage.append(peak_memory - initial_memory)
                
                # 清理
                del automation
                
            except Exception as e:
                self.logger.warning(f"原始架构初始化 #{i+1} 失败: {e}")
                times.append(float('inf'))
                memory_usage.append(0)
        
        valid_times = [t for t in times if t != float('inf')]
        
        return {
            "iterations": iterations,
            "successful_inits": len(valid_times),
            "init_times": valid_times,
            "avg_init_time": sum(valid_times) / len(valid_times) if valid_times else 0,
            "min_init_time": min(valid_times) if valid_times else 0,
            "max_init_time": max(valid_times) if valid_times else 0,
            "memory_usage_mb": memory_usage,
            "avg_memory_mb": sum(memory_usage) / len(memory_usage) if memory_usage else 0
        }
    
    async def _test_new_architecture_initialization(self, iterations: int = 5) -> Dict[str, Any]:
        """测试新架构初始化"""
        times = []
        memory_usage = []
        
        for i in range(iterations):
            initial_memory = self.process.memory_info().rss / 1024 / 1024  # MB
            
            start_time = time.time()
            try:
                engine = AutomationEngine(auto_launch=False)
                init_time = time.time() - start_time
                times.append(init_time)
                
                peak_memory = self.process.memory_info().rss / 1024 / 1024  # MB
                memory_usage.append(peak_memory - initial_memory)
                
                # 清理
                del engine
                
            except Exception as e:
                self.logger.warning(f"新架构初始化 #{i+1} 失败: {e}")
                times.append(float('inf'))
                memory_usage.append(0)
        
        valid_times = [t for t in times if t != float('inf')]
        
        return {
            "iterations": iterations,
            "successful_inits": len(valid_times),
            "init_times": valid_times,
            "avg_init_time": sum(valid_times) / len(valid_times) if valid_times else 0,
            "min_init_time": min(valid_times) if valid_times else 0,
            "max_init_time": max(valid_times) if valid_times else 0,
            "memory_usage_mb": memory_usage,
            "avg_memory_mb": sum(memory_usage) / len(memory_usage) if memory_usage else 0
        }
    
    async def _test_facade_initialization(self, iterations: int = 5) -> Dict[str, Any]:
        """测试Facade适配器初始化"""
        times = []
        memory_usage = []
        
        for i in range(iterations):
            initial_memory = self.process.memory_info().rss / 1024 / 1024  # MB
            
            start_time = time.time()
            try:
                facade = FacadeAutomation(
                    debug_port=9400 + i,
                    auto_launch=False
                )
                init_time = time.time() - start_time
                times.append(init_time)
                
                peak_memory = self.process.memory_info().rss / 1024 / 1024  # MB
                memory_usage.append(peak_memory - initial_memory)
                
                # 清理
                del facade
                
            except Exception as e:
                self.logger.warning(f"Facade初始化 #{i+1} 失败: {e}")
                times.append(float('inf'))
                memory_usage.append(0)
        
        valid_times = [t for t in times if t != float('inf')]
        
        return {
            "iterations": iterations,
            "successful_inits": len(valid_times),
            "init_times": valid_times,
            "avg_init_time": sum(valid_times) / len(valid_times) if valid_times else 0,
            "min_init_time": min(valid_times) if valid_times else 0,
            "max_init_time": max(valid_times) if valid_times else 0,
            "memory_usage_mb": memory_usage,
            "avg_memory_mb": sum(memory_usage) / len(memory_usage) if memory_usage else 0
        }
    
    def _calculate_performance_comparison(self):
        """计算性能对比指标"""
        original = self.test_results["original_architecture"]
        new_arch = self.test_results["new_architecture"]
        facade = self.test_results["facade_architecture"]
        
        # 初始化时间对比
        orig_time = original.get("avg_init_time", 0)
        new_time = new_arch.get("avg_init_time", 0)
        facade_time = facade.get("avg_init_time", 0)
        
        improvements = {}
        if orig_time > 0:
            improvements["new_vs_original_percent"] = ((orig_time - new_time) / orig_time * 100) if new_time > 0 else 0
            improvements["facade_vs_original_percent"] = ((orig_time - facade_time) / orig_time * 100) if facade_time > 0 else 0
        
        # 内存使用对比
        orig_memory = original.get("avg_memory_mb", 0)
        new_memory = new_arch.get("avg_memory_mb", 0)
        facade_memory = facade.get("avg_memory_mb", 0)
        
        if orig_memory > 0:
            improvements["memory_new_vs_original_percent"] = ((orig_memory - new_memory) / orig_memory * 100) if new_memory > 0 else 0
            improvements["memory_facade_vs_original_percent"] = ((orig_memory - facade_memory) / orig_memory * 100) if facade_memory > 0 else 0
        
        # 成功率对比
        orig_success_rate = (original.get("successful_inits", 0) / original.get("iterations", 1)) * 100
        new_success_rate = (new_arch.get("successful_inits", 0) / new_arch.get("iterations", 1)) * 100
        facade_success_rate = (facade.get("successful_inits", 0) / facade.get("iterations", 1)) * 100
        
        self.test_results["performance_comparison"] = {
            "initialization_improvements": improvements,
            "success_rates": {
                "original": orig_success_rate,
                "new_architecture": new_success_rate,
                "facade": facade_success_rate
            },
            "memory_comparison": {
                "original_mb": orig_memory,
                "new_architecture_mb": new_memory,
                "facade_mb": facade_memory
            }
        }
    
    async def test_resource_usage(self):
        """资源使用监控"""
        print("\n📊 测试2: 资源使用监控")
        print("-" * 40)
        
        # 监控系统资源基线
        baseline_cpu = self.process.cpu_percent()
        baseline_memory = self.process.memory_info().rss / 1024 / 1024  # MB
        
        print(f"  📈 系统基线: CPU={baseline_cpu:.1f}%, Memory={baseline_memory:.1f}MB")
        
        resource_metrics = {
            "baseline_cpu_percent": baseline_cpu,
            "baseline_memory_mb": baseline_memory,
            "peak_cpu_percent": 0,
            "peak_memory_mb": 0,
            "monitoring_duration_seconds": 0
        }
        
        # 模拟负载测试
        print("  🔄 执行资源监控负载测试...")
        start_time = time.time()
        
        try:
            # 创建多个实例进行负载测试
            instances = []
            for i in range(3):
                try:
                    automation = HuluStealthAutomation(
                        debug_port=9500 + i,
                        auto_launch=False
                    )
                    instances.append(automation)
                    
                    # 监控资源使用
                    current_cpu = self.process.cpu_percent()
                    current_memory = self.process.memory_info().rss / 1024 / 1024
                    
                    resource_metrics["peak_cpu_percent"] = max(
                        resource_metrics["peak_cpu_percent"], current_cpu
                    )
                    resource_metrics["peak_memory_mb"] = max(
                        resource_metrics["peak_memory_mb"], current_memory
                    )
                    
                    await asyncio.sleep(0.5)  # 模拟操作延迟
                    
                except Exception as e:
                    self.logger.warning(f"负载测试实例 {i} 创建失败: {e}")
            
            # 清理资源
            for instance in instances:
                try:
                    del instance
                except:
                    pass
        
        except Exception as e:
            self.logger.error(f"资源监控测试失败: {e}")
        
        resource_metrics["monitoring_duration_seconds"] = time.time() - start_time
        self.test_results["resource_usage"] = resource_metrics
        
        print(f"  ✅ 峰值 CPU: {resource_metrics['peak_cpu_percent']:.1f}%")
        print(f"  ✅ 峰值内存: {resource_metrics['peak_memory_mb']:.1f}MB")
        print("  🎉 资源使用监控完成")
    
    async def test_functionality_consistency(self):
        """功能一致性验证"""
        print("\n🔄 测试3: 功能一致性验证")
        print("-" * 40)
        
        consistency_results = {
            "initialization_consistency": False,
            "configuration_consistency": False,
            "method_availability": {},
            "health_check_comparison": {}
        }
        
        try:
            # 创建测试实例
            print("  🔧 创建测试实例...")
            original = HuluStealthAutomation(debug_port=9600, auto_launch=False)
            new_engine = AutomationEngine(auto_launch=False)
            facade = FacadeAutomation(debug_port=9601, auto_launch=False)
            
            # 1. 初始化一致性检查
            print("  ✅ 检查初始化一致性...")
            original_attrs = [attr for attr in dir(original) if not attr.startswith('_')]
            facade_attrs = [attr for attr in dir(facade) if not attr.startswith('_')]
            consistency_results["initialization_consistency"] = len(set(original_attrs) & set(facade_attrs)) > 10
            
            # 2. 配置一致性检查
            print("  ⚙️ 检查配置一致性...")
            config_attrs = ['debug_port', 'auto_launch', 'persistent_session']
            config_consistent = True
            for attr in config_attrs:
                if hasattr(original, attr) != hasattr(facade, attr):
                    config_consistent = False
                    break
            consistency_results["configuration_consistency"] = config_consistent
            
            # 3. 方法可用性对比
            print("  📋 检查方法可用性...")
            important_methods = ['execute_stealth_login', 'get_execution_stats']
            for method in important_methods:
                consistency_results["method_availability"][method] = {
                    "original": hasattr(original, method),
                    "facade": hasattr(facade, method)
                }
            
            # 4. 健康检查对比
            print("  🏥 执行健康检查对比...")
            try:
                new_health = await new_engine.health_check()
                consistency_results["health_check_comparison"] = {
                    "new_architecture_health": new_health,
                    "health_check_available": True
                }
            except Exception as e:
                consistency_results["health_check_comparison"] = {
                    "error": str(e),
                    "health_check_available": False
                }
            
            print("  ✅ 功能一致性验证完成")
            
        except Exception as e:
            self.logger.error(f"功能一致性测试失败: {e}")
            consistency_results["error"] = str(e)
        
        self.test_results["consistency_tests"] = consistency_results
    
    async def test_load_performance(self):
        """负载压力测试"""
        print("\n⚡ 测试4: 负载压力测试")
        print("-" * 40)
        
        load_results = {
            "concurrent_initializations": 0,
            "successful_concurrent": 0,
            "avg_concurrent_time": 0,
            "load_test_completed": False
        }
        
        try:
            print("  🔄 执行并发初始化测试...")
            
            # 并发初始化测试
            concurrent_count = 5
            start_time = time.time()
            
            async def create_instance(index):
                try:
                    automation = HuluStealthAutomation(
                        debug_port=9700 + index,
                        auto_launch=False
                    )
                    await asyncio.sleep(0.1)  # 模拟一些操作
                    return True
                except Exception as e:
                    self.logger.warning(f"并发实例 {index} 失败: {e}")
                    return False
            
            # 并发执行
            tasks = [create_instance(i) for i in range(concurrent_count)]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            successful = sum(1 for r in results if r is True)
            total_time = time.time() - start_time
            
            load_results.update({
                "concurrent_initializations": concurrent_count,
                "successful_concurrent": successful,
                "avg_concurrent_time": total_time / concurrent_count,
                "load_test_completed": True
            })
            
            print(f"  ✅ 并发测试: {successful}/{concurrent_count} 成功")
            print(f"  ✅ 平均时间: {total_time/concurrent_count:.3f}s")
            
        except Exception as e:
            self.logger.error(f"负载测试失败: {e}")
            load_results["error"] = str(e)
        
        self.test_results["load_performance"] = load_results
        print("  🎉 负载压力测试完成")
    
    async def test_error_handling_comparison(self):
        """错误处理对比测试"""
        print("\n🚨 测试5: 错误处理对比")
        print("-" * 40)
        
        error_handling_results = {
            "original_error_handling": {},
            "new_architecture_error_handling": {},
            "error_recovery_comparison": {}
        }
        
        try:
            # 测试原始架构错误处理
            print("  🔍 测试原始架构错误处理...")
            original_errors = await self._test_error_scenarios("original")
            error_handling_results["original_error_handling"] = original_errors
            
            if NEW_ARCHITECTURE_AVAILABLE:
                # 测试新架构错误处理
                print("  🆕 测试新架构错误处理...")
                new_errors = await self._test_error_scenarios("new")
                error_handling_results["new_architecture_error_handling"] = new_errors
                
                # 对比分析
                error_handling_results["error_recovery_comparison"] = {
                    "original_recovery_rate": original_errors.get("recovery_rate", 0),
                    "new_recovery_rate": new_errors.get("recovery_rate", 0),
                    "improvement": new_errors.get("recovery_rate", 0) - original_errors.get("recovery_rate", 0)
                }
            
            print("  ✅ 错误处理对比完成")
            
        except Exception as e:
            self.logger.error(f"错误处理测试失败: {e}")
            error_handling_results["error"] = str(e)
        
        self.test_results["error_handling"] = error_handling_results
        print("  🎉 错误处理对比测试完成")
    
    async def _test_error_scenarios(self, architecture_type: str) -> Dict[str, Any]:
        """测试错误场景"""
        error_scenarios = [
            ("invalid_port", 99999),
            ("invalid_config", None),
            ("resource_exhaustion", "test")
        ]
        
        recovered_count = 0
        total_scenarios = len(error_scenarios)
        
        for scenario_name, test_param in error_scenarios:
            try:
                if architecture_type == "original":
                    # 测试原始架构
                    automation = HuluStealthAutomation(
                        debug_port=test_param if isinstance(test_param, int) else 9800,
                        auto_launch=False
                    )
                elif architecture_type == "new":
                    # 测试新架构
                    engine = AutomationEngine(auto_launch=False)
                
                # 如果没有抛出异常,说明有错误恢复能力
                recovered_count += 1
                
            except Exception:
                # 预期的异常，不算恢复成功
                pass
        
        return {
            "total_scenarios": total_scenarios,
            "recovered_scenarios": recovered_count,
            "recovery_rate": (recovered_count / total_scenarios * 100) if total_scenarios > 0 else 0
        }
    
    def generate_comparison_report(self):
        """生成对比报告"""
        print("\n📋 生成并行测试对比报告...")
        
        # 完成时间
        self.test_results["test_end_time"] = datetime.now().isoformat()
        
        # 生成总结
        summary = {
            "test_categories": len([k for k in self.test_results.keys() if not k.endswith("_time")]),
            "new_architecture_available": NEW_ARCHITECTURE_AVAILABLE,
            "performance_improvements_detected": False,
            "consistency_verified": False
        }
        
        # 检查性能改进
        if "performance_comparison" in self.test_results:
            improvements = self.test_results["performance_comparison"].get("initialization_improvements", {})
            if any(improvement > 0 for improvement in improvements.values()):
                summary["performance_improvements_detected"] = True
        
        # 检查一致性
        if "consistency_tests" in self.test_results:
            consistency = self.test_results["consistency_tests"]
            if consistency.get("initialization_consistency") and consistency.get("configuration_consistency"):
                summary["consistency_verified"] = True
        
        self.test_results["summary"] = summary
        
        # 保存详细报告
        report_filename = f"phase2_parallel_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        try:
            with open(report_filename, 'w', encoding='utf-8') as f:
                json.dump(self.test_results, f, indent=2, ensure_ascii=False)
            print(f"  ✅ 详细报告已保存: {report_filename}")
        except Exception as e:
            print(f"  ⚠️ 报告保存失败: {e}")
        
        print("  ✅ 对比报告生成完成")


async def main():
    """主测试函数"""
    print("🎯 Phase 2: 并行测试环境搭建与对比分析")
    print("=" * 60)
    print("目标: 建立新旧架构对比环境，分析性能差异和功能一致性")
    print()
    
    tester = ParallelTester()
    
    try:
        results = await tester.run_parallel_tests()
        
        print("\n" + "=" * 60)
        print("🎉 Phase 2 并行测试完成!")
        
        # 显示总结
        summary = results.get("summary", {})
        if summary:
            print(f"\n📊 测试总结:")
            print(f"  测试类别: {summary['test_categories']} 个")
            print(f"  新架构可用: {'是' if summary['new_architecture_available'] else '否'}")
            print(f"  性能改进: {'检测到' if summary['performance_improvements_detected'] else '未检测到'}")
            print(f"  功能一致性: {'已验证' if summary['consistency_verified'] else '待验证'}")
        
        # 显示关键指标
        if "performance_comparison" in results:
            perf = results["performance_comparison"]
            improvements = perf.get("initialization_improvements", {})
            if improvements:
                print(f"\n⚡ 性能改进指标:")
                for key, value in improvements.items():
                    if isinstance(value, (int, float)) and value != 0:
                        print(f"  {key}: {value:+.1f}%")
        
        print(f"\n🚀 Phase 2 状态: {'✅ 成功' if summary.get('new_architecture_available', False) else '⚠️ 部分成功'}")
        print("\n📋 下一步: 可以开始 Phase 2 配置系统优化")
        
        return 0 if summary.get('new_architecture_available', False) else 1
        
    except Exception as e:
        print(f"\n❌ Phase 2 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return 2


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)