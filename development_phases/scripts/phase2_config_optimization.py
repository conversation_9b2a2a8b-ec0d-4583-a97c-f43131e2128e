#!/usr/bin/env python3
"""
Phase 2: 配置系统优化

实现智能YAML配置学习和动态调整系统，包括:
- 配置文件智能分析
- 性能参数自动调优
- 配置版本管理
- 运行时配置热更新
"""

import asyncio
import logging
import time
import sys
import os
import json
import yaml
from datetime import datetime
from typing import Dict, Any, List, Optional, Tuple
from pathlib import Path
from dataclasses import dataclass, asdict

# 添加项目路径
sys.path.insert(0, os.path.dirname(__file__))

# 导入配置相关组件
try:
    from hulu_automation_stealth_v2.config import ConfigManager
    from hulu_automation_stealth_v2.core import AutomationEngine
    CONFIG_SYSTEM_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ 配置系统组件不可用: {e}")
    CONFIG_SYSTEM_AVAILABLE = False


@dataclass
class ConfigOptimizationRule:
    """配置优化规则"""
    name: str
    target_metric: str  # performance, reliability, resource_usage
    condition: str  # python expression
    adjustment: Dict[str, Any]  # configuration changes
    priority: int = 1  # 1=low, 5=high
    description: str = ""


@dataclass 
class PerformanceMetrics:
    """性能指标数据类"""
    initialization_time: float = 0.0
    memory_usage_mb: float = 0.0
    cpu_usage_percent: float = 0.0
    success_rate_percent: float = 100.0
    error_count: int = 0
    timestamp: str = ""


class ConfigOptimizer:
    """
    智能配置优化器
    
    功能:
    - YAML配置文件分析
    - 性能指标监控
    - 自动参数调优
    - 配置版本管理
    - 热更新支持
    """
    
    def __init__(self, config_dir: str = "config"):
        self.setup_logging()
        self.config_dir = Path(config_dir)
        self.config_dir.mkdir(exist_ok=True)
        
        # 优化结果存储
        self.optimization_results = {
            "test_start_time": datetime.now().isoformat(),
            "baseline_config": {},
            "optimization_rules": [],
            "performance_history": [],
            "optimized_config": {},
            "improvement_metrics": {}
        }
        
        # 性能基线
        self.baseline_metrics: Optional[PerformanceMetrics] = None
        self.current_metrics: Optional[PerformanceMetrics] = None
        
        # 优化规则库
        self.optimization_rules = self._load_optimization_rules()
        
    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(f'phase2_config_opt_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def _load_optimization_rules(self) -> List[ConfigOptimizationRule]:
        """加载优化规则库"""
        rules = [
            # 性能优化规则
            ConfigOptimizationRule(
                name="reduce_timeout_for_fast_network",
                target_metric="performance",
                condition="initialization_time > 0.01",
                adjustment={
                    "timeouts": {
                        "page_load": 15000,  # 从30000降到15000
                        "element_wait": 5000  # 从10000降到5000
                    }
                },
                priority=3,
                description="网络良好时减少超时时间提升性能"
            ),
            
            ConfigOptimizationRule(
                name="increase_timeout_for_reliability",
                target_metric="reliability", 
                condition="success_rate_percent < 90",
                adjustment={
                    "timeouts": {
                        "page_load": 45000,  # 增加到45000
                        "element_wait": 15000  # 增加到15000
                    }
                },
                priority=5,
                description="成功率低时增加超时时间提升可靠性"
            ),
            
            ConfigOptimizationRule(
                name="optimize_memory_usage",
                target_metric="resource_usage",
                condition="memory_usage_mb > 100",
                adjustment={
                    "browser": {
                        "args": [
                            "--memory-pressure-off",
                            "--max_old_space_size=512"
                        ]
                    }
                },
                priority=2,
                description="内存使用过高时启用内存优化参数"
            ),
            
            ConfigOptimizationRule(
                name="enable_stealth_mode",
                target_metric="reliability",
                condition="error_count > 2",
                adjustment={
                    "anti_detection": {
                        "stealth_mode": True,
                        "behavior_simulation": True,
                        "fingerprint_randomization": True
                    }
                },
                priority=4,
                description="错误频率高时启用更强的反检测"
            ),
            
            ConfigOptimizationRule(
                name="reduce_logging_for_performance",
                target_metric="performance",
                condition="initialization_time > 0.02",
                adjustment={
                    "logging": {
                        "level": "WARNING",
                        "disable_verbose": True
                    }
                },
                priority=1,
                description="性能慢时降低日志级别"
            )
        ]
        
        return rules
    
    async def run_config_optimization(self) -> Dict[str, Any]:
        """运行配置优化流程"""
        print("🚀 开始 Phase 2 配置系统优化")
        print("=" * 60)
        
        try:
            # 步骤1: 分析现有配置
            await self.analyze_current_config()
            
            # 步骤2: 建立性能基线
            await self.establish_performance_baseline()
            
            # 步骤3: 应用优化规则
            await self.apply_optimization_rules()
            
            # 步骤4: 验证优化效果
            await self.validate_optimization_results()
            
            # 步骤5: 生成优化报告
            self.generate_optimization_report()
            
            return self.optimization_results
            
        except Exception as e:
            self.logger.error(f"配置优化失败: {e}")
            import traceback
            traceback.print_exc()
            return {"error": str(e), "optimization_results": self.optimization_results}
    
    async def analyze_current_config(self):
        """分析当前配置"""
        print("\n🔍 步骤1: 分析当前配置")
        print("-" * 40)
        
        try:
            # 查找配置文件
            config_files = []
            for pattern in ["*.yml", "*.yaml", "*.json"]:
                config_files.extend(self.config_dir.glob(pattern))
            
            if not config_files:
                # 创建默认配置
                print("  📝 未找到配置文件，创建默认配置...")
                default_config = self._create_default_config()
                config_path = self.config_dir / "default_config.yml"
                with open(config_path, 'w', encoding='utf-8') as f:
                    yaml.dump(default_config, f, default_flow_style=False, allow_unicode=True)
                config_files = [config_path]
                print(f"  ✅ 默认配置已创建: {config_path}")
            
            # 分析配置文件
            current_config = {}
            for config_file in config_files[:3]:  # 限制最多分析3个文件
                print(f"  📋 分析配置文件: {config_file.name}")
                try:
                    with open(config_file, 'r', encoding='utf-8') as f:
                        if config_file.suffix in ['.yml', '.yaml']:
                            file_config = yaml.safe_load(f)
                        else:
                            file_config = json.load(f)
                    
                    if file_config:
                        current_config.update(file_config)
                        print(f"    ✅ 成功解析: {len(file_config)} 个配置项")
                    
                except Exception as e:
                    print(f"    ⚠️ 解析失败: {e}")
            
            self.optimization_results["baseline_config"] = current_config
            
            # 配置分析摘要
            config_stats = {
                "total_files": len(config_files),
                "parsed_files": len([f for f in config_files if f]),
                "total_config_items": len(current_config),
                "has_timeouts": "timeouts" in current_config,
                "has_browser_config": "browser" in current_config,
                "has_logging_config": "logging" in current_config
            }
            
            print(f"  📊 配置分析摘要:")
            print(f"    配置文件: {config_stats['parsed_files']}/{config_stats['total_files']}")
            print(f"    配置项数: {config_stats['total_config_items']}")
            print(f"    包含超时配置: {'是' if config_stats['has_timeouts'] else '否'}")
            print(f"    包含浏览器配置: {'是' if config_stats['has_browser_config'] else '否'}")
            
            self.optimization_results["config_analysis"] = config_stats
            print("  ✅ 配置分析完成")
            
        except Exception as e:
            self.logger.error(f"配置分析失败: {e}")
            self.optimization_results["config_analysis"] = {"error": str(e)}
    
    def _create_default_config(self) -> Dict[str, Any]:
        """创建默认配置"""
        return {
            "timeouts": {
                "page_load": 30000,
                "element_wait": 10000,
                "script_timeout": 5000
            },
            "browser": {
                "headless": True,
                "args": [
                    "--no-sandbox",
                    "--disable-dev-shm-usage"
                ]
            },
            "anti_detection": {
                "stealth_mode": False,
                "behavior_simulation": False,
                "fingerprint_randomization": False
            },
            "logging": {
                "level": "INFO",
                "disable_verbose": False
            },
            "performance": {
                "memory_limit_mb": 200,
                "cpu_limit_percent": 80
            }
        }
    
    async def establish_performance_baseline(self):
        """建立性能基线"""
        print("\n⚡ 步骤2: 建立性能基线")
        print("-" * 40)
        
        try:
            print("  🔄 执行基线性能测试...")
            
            # 执行多次测试建立基线
            metrics_list = []
            for i in range(3):
                print(f"    测试轮次 #{i+1}/3...")
                metrics = await self._measure_performance()
                if metrics:
                    metrics_list.append(metrics)
                await asyncio.sleep(0.5)  # 短暂间隔
            
            if metrics_list:
                # 计算平均指标
                self.baseline_metrics = PerformanceMetrics(
                    initialization_time=sum(m.initialization_time for m in metrics_list) / len(metrics_list),
                    memory_usage_mb=sum(m.memory_usage_mb for m in metrics_list) / len(metrics_list),
                    cpu_usage_percent=sum(m.cpu_usage_percent for m in metrics_list) / len(metrics_list),
                    success_rate_percent=sum(m.success_rate_percent for m in metrics_list) / len(metrics_list),
                    error_count=sum(m.error_count for m in metrics_list),
                    timestamp=datetime.now().isoformat()
                )
                
                print(f"  📊 性能基线指标:")
                print(f"    初始化时间: {self.baseline_metrics.initialization_time:.3f}s")
                print(f"    内存使用: {self.baseline_metrics.memory_usage_mb:.1f}MB")
                print(f"    CPU使用: {self.baseline_metrics.cpu_usage_percent:.1f}%")
                print(f"    成功率: {self.baseline_metrics.success_rate_percent:.1f}%")
                print(f"    错误数: {self.baseline_metrics.error_count}")
                
                self.optimization_results["baseline_metrics"] = asdict(self.baseline_metrics)
                print("  ✅ 性能基线建立完成")
            else:
                print("  ⚠️ 无法建立性能基线")
                
        except Exception as e:
            self.logger.error(f"性能基线建立失败: {e}")
            self.optimization_results["baseline_metrics"] = {"error": str(e)}
    
    async def _measure_performance(self) -> Optional[PerformanceMetrics]:
        """测量性能指标"""
        try:
            import psutil
            process = psutil.Process()
            
            # 记录初始状态
            initial_memory = process.memory_info().rss / 1024 / 1024  # MB
            
            # 性能测试：创建实例
            start_time = time.time()
            success = False
            error_count = 0
            
            try:
                if CONFIG_SYSTEM_AVAILABLE:
                    engine = AutomationEngine(auto_launch=False)
                    success = True
                else:
                    # 模拟测试
                    await asyncio.sleep(0.01)
                    success = True
            except Exception as e:
                error_count += 1
                self.logger.warning(f"性能测试异常: {e}")
            
            init_time = time.time() - start_time
            
            # 记录资源使用
            final_memory = process.memory_info().rss / 1024 / 1024  # MB
            cpu_percent = process.cpu_percent()
            
            return PerformanceMetrics(
                initialization_time=init_time,
                memory_usage_mb=final_memory - initial_memory,
                cpu_usage_percent=cpu_percent,
                success_rate_percent=100.0 if success else 0.0,
                error_count=error_count,
                timestamp=datetime.now().isoformat()
            )
            
        except Exception as e:
            self.logger.error(f"性能测量失败: {e}")
            return None
    
    async def apply_optimization_rules(self):
        """应用优化规则"""
        print("\n🎯 步骤3: 应用优化规则")
        print("-" * 40)
        
        if not self.baseline_metrics:
            print("  ⚠️ 缺少性能基线，跳过优化")
            return
        
        try:
            current_config = self.optimization_results["baseline_config"].copy()
            applied_rules = []
            
            # 评估每个优化规则
            for rule in self.optimization_rules:
                print(f"  🔍 评估规则: {rule.name}")
                
                # 创建评估上下文
                eval_context = {
                    "initialization_time": self.baseline_metrics.initialization_time,
                    "memory_usage_mb": self.baseline_metrics.memory_usage_mb,
                    "cpu_usage_percent": self.baseline_metrics.cpu_usage_percent,
                    "success_rate_percent": self.baseline_metrics.success_rate_percent,
                    "error_count": self.baseline_metrics.error_count
                }
                
                try:
                    # 评估条件
                    should_apply = eval(rule.condition, {"__builtins__": {}}, eval_context)
                    
                    if should_apply:
                        print(f"    ✅ 条件满足，应用规则")
                        print(f"    📝 {rule.description}")
                        
                        # 应用配置调整
                        current_config = self._merge_config(current_config, rule.adjustment)
                        applied_rules.append({
                            "rule": rule.name,
                            "priority": rule.priority,
                            "target_metric": rule.target_metric,
                            "description": rule.description,
                            "adjustment": rule.adjustment
                        })
                    else:
                        print(f"    ⏭️ 条件不满足，跳过规则")
                        
                except Exception as e:
                    print(f"    ❌ 规则评估失败: {e}")
            
            # 保存优化后的配置
            self.optimization_results["optimized_config"] = current_config
            self.optimization_results["applied_rules"] = applied_rules
            
            # 保存优化配置到文件
            if applied_rules:
                optimized_config_path = self.config_dir / "optimized_config.yml"
                with open(optimized_config_path, 'w', encoding='utf-8') as f:
                    yaml.dump(current_config, f, default_flow_style=False, allow_unicode=True)
                print(f"  💾 优化配置已保存: {optimized_config_path}")
            
            print(f"  📊 优化摘要: 应用了 {len(applied_rules)} 个规则")
            for rule_info in applied_rules:
                print(f"    - {rule_info['rule']} (优先级: {rule_info['priority']})")
            
            print("  ✅ 优化规则应用完成")
            
        except Exception as e:
            self.logger.error(f"优化规则应用失败: {e}")
            self.optimization_results["applied_rules"] = {"error": str(e)}
    
    def _merge_config(self, base_config: Dict[str, Any], update_config: Dict[str, Any]) -> Dict[str, Any]:
        """深度合并配置"""
        result = base_config.copy()
        
        for key, value in update_config.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._merge_config(result[key], value)
            else:
                result[key] = value
        
        return result
    
    async def validate_optimization_results(self):
        """验证优化效果"""
        print("\n✅ 步骤4: 验证优化效果")
        print("-" * 40)
        
        if not self.baseline_metrics:
            print("  ⚠️ 缺少基线指标，跳过验证")
            return
        
        try:
            print("  🔄 测试优化后性能...")
            
            # 执行优化后的性能测试
            optimized_metrics_list = []
            for i in range(3):
                print(f"    验证轮次 #{i+1}/3...")
                metrics = await self._measure_performance()
                if metrics:
                    optimized_metrics_list.append(metrics)
                await asyncio.sleep(0.5)
            
            if optimized_metrics_list:
                # 计算优化后的平均指标
                self.current_metrics = PerformanceMetrics(
                    initialization_time=sum(m.initialization_time for m in optimized_metrics_list) / len(optimized_metrics_list),
                    memory_usage_mb=sum(m.memory_usage_mb for m in optimized_metrics_list) / len(optimized_metrics_list),
                    cpu_usage_percent=sum(m.cpu_usage_percent for m in optimized_metrics_list) / len(optimized_metrics_list),
                    success_rate_percent=sum(m.success_rate_percent for m in optimized_metrics_list) / len(optimized_metrics_list),
                    error_count=sum(m.error_count for m in optimized_metrics_list),
                    timestamp=datetime.now().isoformat()
                )
                
                # 计算改进指标
                improvements = self._calculate_improvements()
                
                print(f"  📊 优化效果对比:")
                print(f"    初始化时间: {improvements['initialization_time_improvement']:+.1f}%")
                print(f"    内存使用: {improvements['memory_usage_improvement']:+.1f}%")
                print(f"    成功率: {improvements['success_rate_improvement']:+.1f}%")
                print(f"    错误减少: {improvements['error_reduction']}")
                
                self.optimization_results["optimized_metrics"] = asdict(self.current_metrics)
                self.optimization_results["improvement_metrics"] = improvements
                
                # 判断优化是否成功
                significant_improvements = sum(1 for v in improvements.values() if isinstance(v, (int, float)) and v > 5)
                optimization_success = significant_improvements > 0
                
                if optimization_success:
                    print("  🎉 优化效果显著!")
                else:
                    print("  📈 优化效果轻微")
                
                self.optimization_results["optimization_success"] = optimization_success
                print("  ✅ 优化效果验证完成")
            else:
                print("  ⚠️ 无法验证优化效果")
                
        except Exception as e:
            self.logger.error(f"优化效果验证失败: {e}")
            self.optimization_results["optimized_metrics"] = {"error": str(e)}
    
    def _calculate_improvements(self) -> Dict[str, float]:
        """计算改进指标"""
        if not self.baseline_metrics or not self.current_metrics:
            return {}
        
        improvements = {}
        
        # 初始化时间改进 (负数表示加快)
        if self.baseline_metrics.initialization_time > 0:
            improvements["initialization_time_improvement"] = (
                (self.baseline_metrics.initialization_time - self.current_metrics.initialization_time) 
                / self.baseline_metrics.initialization_time * 100
            )
        
        # 内存使用改进 (负数表示减少)
        if self.baseline_metrics.memory_usage_mb > 0:
            improvements["memory_usage_improvement"] = (
                (self.baseline_metrics.memory_usage_mb - self.current_metrics.memory_usage_mb)
                / self.baseline_metrics.memory_usage_mb * 100
            )
        
        # 成功率改进
        improvements["success_rate_improvement"] = (
            self.current_metrics.success_rate_percent - self.baseline_metrics.success_rate_percent
        )
        
        # 错误减少
        improvements["error_reduction"] = (
            self.baseline_metrics.error_count - self.current_metrics.error_count
        )
        
        return improvements
    
    def generate_optimization_report(self):
        """生成优化报告"""
        print("\n📋 步骤5: 生成优化报告")
        print("-" * 40)
        
        # 完成时间
        self.optimization_results["test_end_time"] = datetime.now().isoformat()
        
        # 生成总结
        summary = {
            "config_files_analyzed": self.optimization_results.get("config_analysis", {}).get("parsed_files", 0),
            "optimization_rules_applied": len(self.optimization_results.get("applied_rules", [])),
            "performance_baseline_established": self.baseline_metrics is not None,
            "optimization_validated": self.current_metrics is not None,
            "significant_improvements": False
        }
        
        # 检查是否有显著改进
        if "improvement_metrics" in self.optimization_results:
            improvements = self.optimization_results["improvement_metrics"]
            significant_improvements = sum(1 for v in improvements.values() if isinstance(v, (int, float)) and abs(v) > 5)
            summary["significant_improvements"] = significant_improvements > 0
        
        self.optimization_results["summary"] = summary
        
        # 保存详细报告
        report_filename = f"phase2_config_optimization_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        try:
            with open(report_filename, 'w', encoding='utf-8') as f:
                json.dump(self.optimization_results, f, indent=2, ensure_ascii=False)
            print(f"  ✅ 详细报告已保存: {report_filename}")
        except Exception as e:
            print(f"  ⚠️ 报告保存失败: {e}")
        
        print("  ✅ 优化报告生成完成")


async def main():
    """主函数"""
    print("🎯 Phase 2: 配置系统优化与智能调优")
    print("=" * 60)
    print("目标: 分析配置文件，优化性能参数，实现智能化配置管理")
    print()
    
    optimizer = ConfigOptimizer()
    
    try:
        results = await optimizer.run_config_optimization()
        
        print("\n" + "=" * 60)
        print("🎉 Phase 2 配置优化完成!")
        
        # 显示总结
        summary = results.get("summary", {})
        if summary:
            print(f"\n📊 优化总结:")
            print(f"  配置文件分析: {summary['config_files_analyzed']} 个")
            print(f"  应用优化规则: {summary['optimization_rules_applied']} 个")
            print(f"  性能基线: {'已建立' if summary['performance_baseline_established'] else '未建立'}")
            print(f"  效果验证: {'已完成' if summary['optimization_validated'] else '未完成'}")
            print(f"  显著改进: {'是' if summary['significant_improvements'] else '否'}")
        
        # 显示关键改进指标
        if "improvement_metrics" in results:
            improvements = results["improvement_metrics"]
            print(f"\n⚡ 关键改进指标:")
            for metric, value in improvements.items():
                if isinstance(value, (int, float)):
                    print(f"  {metric}: {value:+.1f}{'%' if 'percent' in metric else ''}")
        
        print(f"\n🚀 Phase 2 配置优化状态: {'✅ 成功' if summary.get('significant_improvements', False) else '📈 完成'}")
        print("\n📋 下一步: 可以开始 Phase 2 监控和分析集成")
        
        return 0 if summary.get('optimization_rules_applied', 0) > 0 else 1
        
    except Exception as e:
        print(f"\n❌ Phase 2 配置优化失败: {e}")
        import traceback
        traceback.print_exc()
        return 2


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)