service_name,status,response_time_ms,memory_usage_mb,cpu_usage_percent,error_count,success_rate_percent,timestamp,details
AutomationEngine,degraded,0.008821487426757812,0.0,0.0,0,100.0,2025-08-01T23:13:01.447121,"{'status': 'degraded', 'timestamp': '2025-08-01T23:13:01.447111', 'issues': ['Engine not initialized'], 'component_status': {'engine': {'initialized': False, 'version': '2.0.0'}, 'cdp_manager': {'chrome_running': False, 'debug_port': 9222}, 'stealth_service': {'status': 'available'}, 'resource_manager': {'created_at': '2025-08-01T23:13:01.446974', 'resources_registered': 0, 'tasks_registered': 0, 'cleanup_attempts': 0, 'cleanup_errors': 0, 'current_tasks': 0, 'active_tasks': 0, 'has_browser': False, 'has_page': False, 'has_cdp_process': False, 'managed_processes': 0, 'cleanup_completed': False}, 'browser_resources': {'playwright': False, 'browser': False, 'context': False, 'page': False}}, 'execution_stats': {'total_executions': 0, 'successful_executions': 0, 'failed_executions': 0, 'last_execution': None, 'average_execution_time': 0.0, 'success_rate': 0.0, 'failure_rate': 0.0, 'initialized': False, 'has_active_session': False}}"
ResourceManager,healthy,0.005245208740234375,0.0,0.0,0,100.0,2025-08-01T23:13:01.447334,"{'service': 'ResourceManager', 'status': 'healthy', 'stats': {'created_at': '2025-08-01T23:13:01.447320', 'resources_registered': 0, 'tasks_registered': 0, 'cleanup_attempts': 0, 'cleanup_errors': 0, 'current_tasks': 0, 'active_tasks': 0, 'has_browser': False, 'has_page': False, 'has_cdp_process': False, 'managed_processes': 0, 'cleanup_completed': False}, 'timestamp': '2025-08-01T23:13:01.447330'}"
