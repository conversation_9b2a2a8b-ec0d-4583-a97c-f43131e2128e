{"test_start_time": "2025-08-01T23:09:11.299989", "baseline_config": {"anti_detection": {"behavior_simulation": false, "fingerprint_randomization": false, "stealth_mode": false}, "browser": {"args": ["--no-sandbox", "--disable-dev-shm-usage"], "headless": true}, "logging": {"disable_verbose": false, "level": "INFO"}, "performance": {"cpu_limit_percent": 80, "memory_limit_mb": 200}, "timeouts": {"element_wait": 10000, "page_load": 30000, "script_timeout": 5000}}, "optimization_rules": [], "performance_history": [], "optimized_config": {"anti_detection": {"behavior_simulation": false, "fingerprint_randomization": false, "stealth_mode": false}, "browser": {"args": ["--no-sandbox", "--disable-dev-shm-usage"], "headless": true}, "logging": {"disable_verbose": false, "level": "INFO"}, "performance": {"cpu_limit_percent": 80, "memory_limit_mb": 200}, "timeouts": {"element_wait": 10000, "page_load": 30000, "script_timeout": 5000}}, "improvement_metrics": {"initialization_time_improvement": 4.452619561080807, "memory_usage_improvement": 11.111111111111116, "success_rate_improvement": 0.0, "error_reduction": 0}, "config_analysis": {"total_files": 1, "parsed_files": 1, "total_config_items": 5, "has_timeouts": true, "has_browser_config": true, "has_logging_config": true}, "baseline_metrics": {"initialization_time": 0.0006264845530192057, "memory_usage_mb": 0.046875, "cpu_usage_percent": 0.0, "success_rate_percent": 100.0, "error_count": 0, "timestamp": "2025-08-01T23:09:12.807658"}, "applied_rules": [], "optimized_metrics": {"initialization_time": 0.0005985895792643229, "memory_usage_mb": 0.041666666666666664, "cpu_usage_percent": 0.0, "success_rate_percent": 100.0, "error_count": 0, "timestamp": "2025-08-01T23:09:14.313666"}, "optimization_success": true, "test_end_time": "2025-08-01T23:09:14.313859", "summary": {"config_files_analyzed": 1, "optimization_rules_applied": 0, "performance_baseline_established": true, "optimization_validated": true, "significant_improvements": true}}