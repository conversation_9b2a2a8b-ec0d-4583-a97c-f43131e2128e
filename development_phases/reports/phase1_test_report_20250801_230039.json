{"test_start_time": "2025-08-01T23:00:39.373658", "original_tests": {"initialization_time": 0.0030999183654785156, "configuration_valid": false, "component_status": {"debug_port": null, "auto_launch": false, "persistent_session": true, "initialized": true, "class_name": "HuluStealthAutomation"}, "test_passed": true, "test_time": 0.003106832504272461}, "new_architecture_tests": {"facade_init_time": 0.0008859634399414062, "engine_init_time": 5.984306335449219e-05, "health_check": {"status": "degraded", "timestamp": "2025-08-01T23:00:39.377738", "issues": ["Engine not initialized"], "component_status": {"engine": {"initialized": false, "version": "2.0.0"}, "cdp_manager": {"chrome_running": false, "debug_port": 9222}, "stealth_service": {"status": "available"}, "resource_manager": {"created_at": "2025-08-01T23:00:39.377678", "resources_registered": 0, "tasks_registered": 0, "cleanup_attempts": 0, "cleanup_errors": 0, "current_tasks": 0, "active_tasks": 0, "has_browser": false, "has_page": false, "has_cdp_process": false, "managed_processes": 0, "cleanup_completed": false}, "browser_resources": {"playwright": false, "browser": false, "context": false, "page": false}}, "execution_stats": {"total_executions": 0, "successful_executions": 0, "failed_executions": 0, "last_execution": null, "average_execution_time": 0.0, "success_rate": 0.0, "failure_rate": 0.0, "initialized": false, "has_active_session": false}}, "component_status": {"engine": {"initialized": false, "version": "2.0.0"}, "cdp_manager": {"chrome_running": false, "debug_port": 9222}, "stealth_service": {"status": "available"}, "resource_manager": {"created_at": "2025-08-01T23:00:39.377678", "resources_registered": 0, "tasks_registered": 0, "cleanup_attempts": 0, "cleanup_errors": 0, "current_tasks": 0, "active_tasks": 0, "has_browser": false, "has_page": false, "has_cdp_process": false, "managed_processes": 0, "cleanup_completed": false}, "browser_resources": {"playwright": false, "browser": false, "context": false, "page": false}}, "execution_stats": {"total_executions": 0, "successful_executions": 0, "failed_executions": 0, "last_execution": null, "average_execution_time": 0.0, "success_rate": 0.0, "failure_rate": 0.0, "initialized": false, "has_active_session": false}, "test_passed": true, "test_time": 0.0009639263153076172}, "performance_baseline": {"initialization_times": [0.0006451606750488281, 0.0007119178771972656, 0.0022301673889160156, 0.0006549358367919922, 0.0008518695831298828], "average_time": 0.0010188102722167968, "min_time": 0.0006451606750488281, "max_time": 0.0022301673889160156, "stability_variance_percent": 155.57427688851448, "baseline_established": true}, "error_recovery_tests": {"tests_run": {"invalid_port": {"handled": true, "error": null}, "config_error": {"handled": true, "error": null}}, "total_tests": 2, "passed_tests": 2, "success_rate": 100.0, "recovery_testing_completed": true}, "comparison_results": {"initialization_improvement_percent": 98.06952776495925, "original_components": 5, "new_components": 5, "new_features": ["health_check", "execution_stats", "resource_management"], "comparison_completed": true}, "test_end_time": "2025-08-01T23:00:39.384565", "summary": {"total_test_categories": 3, "passed_test_categories": 3, "overall_success_rate": 100.0, "new_architecture_available": true, "performance_baseline_established": true}}