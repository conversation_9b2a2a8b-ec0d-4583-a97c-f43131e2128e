{"session_start_time": "2025-08-01T23:13:01.446960", "health_checks": [{"service_name": "AutomationEngine", "status": "degraded", "response_time_ms": 0.008821487426757812, "memory_usage_mb": 0.0, "cpu_usage_percent": 0.0, "error_count": 0, "success_rate_percent": 100.0, "timestamp": "2025-08-01T23:13:01.447121", "details": {"status": "degraded", "timestamp": "2025-08-01T23:13:01.447111", "issues": ["Engine not initialized"], "component_status": {"engine": {"initialized": false, "version": "2.0.0"}, "cdp_manager": {"chrome_running": false, "debug_port": 9222}, "stealth_service": {"status": "available"}, "resource_manager": {"created_at": "2025-08-01T23:13:01.446974", "resources_registered": 0, "tasks_registered": 0, "cleanup_attempts": 0, "cleanup_errors": 0, "current_tasks": 0, "active_tasks": 0, "has_browser": false, "has_page": false, "has_cdp_process": false, "managed_processes": 0, "cleanup_completed": false}, "browser_resources": {"playwright": false, "browser": false, "context": false, "page": false}}, "execution_stats": {"total_executions": 0, "successful_executions": 0, "failed_executions": 0, "last_execution": null, "average_execution_time": 0.0, "success_rate": 0.0, "failure_rate": 0.0, "initialized": false, "has_active_session": false}}}, {"service_name": "ResourceManager", "status": "healthy", "response_time_ms": 0.005245208740234375, "memory_usage_mb": 0.0, "cpu_usage_percent": 0.0, "error_count": 0, "success_rate_percent": 100.0, "timestamp": "2025-08-01T23:13:01.447334", "details": {"service": "ResourceManager", "status": "healthy", "stats": {"created_at": "2025-08-01T23:13:01.447320", "resources_registered": 0, "tasks_registered": 0, "cleanup_attempts": 0, "cleanup_errors": 0, "current_tasks": 0, "active_tasks": 0, "has_browser": false, "has_page": false, "has_cdp_process": false, "managed_processes": 0, "cleanup_completed": false}, "timestamp": "2025-08-01T23:13:01.447330"}}], "performance_metrics": [{"service_name": "Round-1", "status": "healthy", "response_time_ms": 101.19009017944336, "memory_usage_mb": 0.0, "cpu_usage_percent": 0.0, "error_count": 0, "success_rate_percent": 100.0, "timestamp": "2025-08-01T23:13:01.548892", "details": {}}, {"service_name": "Round-2", "status": "healthy", "response_time_ms": 101.59993171691895, "memory_usage_mb": 0.078125, "cpu_usage_percent": 0.0, "error_count": 0, "success_rate_percent": 100.0, "timestamp": "2025-08-01T23:13:02.652004", "details": {}}, {"service_name": "Round-3", "status": "healthy", "response_time_ms": 101.86314582824707, "memory_usage_mb": 0.0, "cpu_usage_percent": 0.0, "error_count": 0, "success_rate_percent": 100.0, "timestamp": "2025-08-01T23:13:03.754847", "details": {}}, {"service_name": "Round-4", "status": "healthy", "response_time_ms": 101.6688346862793, "memory_usage_mb": 0.0, "cpu_usage_percent": 0.0, "error_count": 0, "success_rate_percent": 100.0, "timestamp": "2025-08-01T23:13:04.857754", "details": {}}, {"service_name": "Round-5", "status": "healthy", "response_time_ms": 101.82309150695801, "memory_usage_mb": 0.046875, "cpu_usage_percent": 0.0, "error_count": 0, "success_rate_percent": 100.0, "timestamp": "2025-08-01T23:13:05.961198", "details": {}}], "alert_history": [{"scenario": "高内存使用", "alert_name": "high_memory_usage", "severity": "medium", "description": "内存使用量超过200MB", "timestamp": "2025-08-01T23:13:06.962851", "metrics": {"service_name": "TestService", "status": "degraded", "response_time_ms": 200.0, "memory_usage_mb": 250.0, "cpu_usage_percent": 15.0, "error_count": 1, "success_rate_percent": 95.0, "timestamp": "2025-08-01T23:13:06.962566", "details": {}}}, {"scenario": "高错误率", "alert_name": "high_error_rate", "severity": "high", "description": "错误数量超过3个", "timestamp": "2025-08-01T23:13:06.962992", "metrics": {"service_name": "TestService", "status": "unhealthy", "response_time_ms": 300.0, "memory_usage_mb": 80.0, "cpu_usage_percent": 20.0, "error_count": 5, "success_rate_percent": 70.0, "timestamp": "2025-08-01T23:13:06.962572", "details": {}}}, {"scenario": "高错误率", "alert_name": "low_success_rate", "severity": "high", "description": "成功率低于80%", "timestamp": "2025-08-01T23:13:06.963026", "metrics": {"service_name": "TestService", "status": "unhealthy", "response_time_ms": 300.0, "memory_usage_mb": 80.0, "cpu_usage_percent": 20.0, "error_count": 5, "success_rate_percent": 70.0, "timestamp": "2025-08-01T23:13:06.962572", "details": {}}}, {"scenario": "高错误率", "alert_name": "system_unhealthy", "severity": "critical", "description": "系统状态不健康", "timestamp": "2025-08-01T23:13:06.963052", "metrics": {"service_name": "TestService", "status": "unhealthy", "response_time_ms": 300.0, "memory_usage_mb": 80.0, "cpu_usage_percent": 20.0, "error_count": 5, "success_rate_percent": 70.0, "timestamp": "2025-08-01T23:13:06.962572", "details": {}}}, {"scenario": "系统故障", "alert_name": "slow_response_time", "severity": "medium", "description": "响应时间超过5秒", "timestamp": "2025-08-01T23:13:06.963124", "metrics": {"service_name": "TestService", "status": "unhealthy", "response_time_ms": 8000.0, "memory_usage_mb": 300.0, "cpu_usage_percent": 50.0, "error_count": 10, "success_rate_percent": 30.0, "timestamp": "2025-08-01T23:13:06.962577", "details": {}}}], "system_statistics": {"health_statistics": {"total_health_checks": 2, "healthy_checks": 1, "healthy_percentage": 50.0, "avg_response_time_ms": 0.007033348083496094, "max_response_time_ms": 0.008821487426757812}, "performance_statistics": {"total_data_points": 5, "avg_memory_mb": 0.025, "max_memory_mb": 0.078125, "avg_cpu_percent": 0.0, "max_cpu_percent": 0.0, "avg_response_time_ms": 101.62901878356934}, "alert_statistics": {"total_alerts": 5, "critical_alerts": 1, "high_alerts": 2, "medium_alerts": 2, "low_alerts": 0, "severity_distribution": {"medium": 2, "high": 2, "critical": 1}}, "system_overview": {"monitoring_session_duration": "0:00:05.516253", "total_components_monitored": 2, "monitoring_system_status": "active", "data_collection_points": 12}}, "data_persistence": {"exported_files": ["monitoring_data/health_checks.csv", "monitoring_data/performance_metrics.csv", "monitoring_data/alert_history.csv", "monitoring_data/monitoring_complete_data.json"], "export_timestamp": "2025-08-01T23:13:06.966758", "export_success": true}, "real_time_demo": {"demo_duration_seconds": 10, "data_points_collected": 5, "demo_data": [{"service_name": "RealTimeMonitor", "status": "healthy", "response_time_ms": 11.096954345703125, "memory_usage_mb": 28.828125, "cpu_usage_percent": 0.0, "error_count": 0, "success_rate_percent": 100.0, "timestamp": "2025-08-01T23:13:06.978181", "details": {}}, {"service_name": "RealTimeMonitor", "status": "healthy", "response_time_ms": 11.16800308227539, "memory_usage_mb": 28.84375, "cpu_usage_percent": 0.0, "error_count": 0, "success_rate_percent": 100.0, "timestamp": "2025-08-01T23:13:08.991404", "details": {}}, {"service_name": "RealTimeMonitor", "status": "healthy", "response_time_ms": 11.149883270263672, "memory_usage_mb": 28.84375, "cpu_usage_percent": 0.0, "error_count": 0, "success_rate_percent": 100.0, "timestamp": "2025-08-01T23:13:11.004475", "details": {}}, {"service_name": "RealTimeMonitor", "status": "healthy", "response_time_ms": 11.076211929321289, "memory_usage_mb": 28.84375, "cpu_usage_percent": 0.0, "error_count": 0, "success_rate_percent": 100.0, "timestamp": "2025-08-01T23:13:13.017187", "details": {}}, {"service_name": "RealTimeMonitor", "status": "healthy", "response_time_ms": 11.183977127075195, "memory_usage_mb": 28.84375, "cpu_usage_percent": 0.0, "error_count": 0, "success_rate_percent": 100.0, "timestamp": "2025-08-01T23:13:15.030181", "details": {}}], "demo_completed": true}, "session_end_time": "2025-08-01T23:13:17.031725", "summary": {"health_checks_completed": true, "performance_monitoring_active": true, "alert_system_functional": true, "statistics_generated": true, "data_persistence_working": true, "real_time_monitoring_demo": true, "monitoring_system_ready": true, "integration_success": true}}