{"pipeline_start": "2025-08-01T23:24:26.540777", "ci_cd_results": {"total_steps": 8, "passed_steps": 7, "warning_steps": 1, "failed_steps": 0, "total_duration_seconds": 15.210410594940186, "pipeline_status": "success", "step_results": [{"step_name": "source_checkout", "status": "passed", "duration_seconds": 0.20114398002624512, "expected_duration": 2, "timestamp": "2025-08-01T23:24:26.741968"}, {"step_name": "dependency_install", "status": "passed", "duration_seconds": 1.5011680126190186, "expected_duration": 15, "timestamp": "2025-08-01T23:24:28.243161"}, {"step_name": "lint_check", "status": "passed", "duration_seconds": 0.5012521743774414, "expected_duration": 5, "timestamp": "2025-08-01T23:24:28.744472"}, {"step_name": "unit_tests", "status": "passed", "duration_seconds": 3.0014188289642334, "expected_duration": 30, "timestamp": "2025-08-01T23:24:31.745971"}, {"step_name": "integration_tests", "status": "passed", "duration_seconds": 4.50199294090271, "expected_duration": 45, "timestamp": "2025-08-01T23:24:36.248210"}, {"step_name": "security_scan", "status": "warning", "duration_seconds": 2.0014517307281494, "expected_duration": 20, "timestamp": "2025-08-01T23:24:38.249893", "warning_message": "发现2个低级安全警告"}, {"step_name": "build_artifacts", "status": "passed", "duration_seconds": 1.0007150173187256, "expected_duration": 10, "timestamp": "2025-08-01T23:24:39.250795"}, {"step_name": "deploy_staging", "status": "passed", "duration_seconds": 2.501267910003662, "expected_duration": 25, "timestamp": "2025-08-01T23:24:41.752155"}]}, "quality_gates": [{"name": "test_coverage", "threshold": 80.0, "actual_value": 85.5, "unit": "%", "status": "passed", "description": "测试覆盖率: 85.5% (要求: ≥80%)"}, {"name": "performance_regression", "threshold": 10.0, "actual_value": 3.2, "unit": "%", "status": "passed", "description": "性能回归: 3.2% (要求: ≤10%)"}, {"name": "error_rate", "threshold": 5.0, "actual_value": 1.8, "unit": "%", "status": "passed", "description": "错误率: 1.8% (要求: ≤5%)"}, {"name": "response_time", "threshold": 2000.0, "actual_value": 1250.0, "unit": "ms", "status": "passed", "description": "平均响应时间: 1250ms (要求: ≤2000ms)"}, {"name": "memory_usage", "threshold": 200.0, "actual_value": 180.5, "unit": "MB", "status": "passed", "description": "内存使用: 180.5MB (要求: ≤200MB)"}], "test_execution": {"total_test_suites": 5, "total_tests": 90, "total_passed": 85, "total_failed": 5, "overall_success_rate": 94.44444444444444, "total_duration_seconds": 7.806300401687622, "suite_results": {"unit_tests": {"suite_name": "unit_tests", "total_tests": 45, "passed_tests": 45, "failed_tests": 0, "skipped_tests": 0, "duration_seconds": 0.8012268543243408, "success_rate": 100.0, "timestamp": "2025-08-01T23:24:44.061138"}, "integration_tests": {"suite_name": "integration_tests", "total_tests": 12, "passed_tests": 11, "failed_tests": 1, "skipped_tests": 0, "duration_seconds": 1.5011792182922363, "success_rate": 91.66666666666666, "timestamp": "2025-08-01T23:24:45.562399", "failed_test_details": ["test_integration_tests_0"]}, "performance_tests": {"suite_name": "performance_tests", "total_tests": 5, "passed_tests": 3, "failed_tests": 2, "skipped_tests": 0, "duration_seconds": 2.5012660026550293, "success_rate": 60.0, "timestamp": "2025-08-01T23:24:48.063742", "failed_test_details": ["test_performance_tests_0", "test_performance_tests_1"]}, "security_tests": {"suite_name": "security_tests", "total_tests": 8, "passed_tests": 8, "failed_tests": 0, "skipped_tests": 0, "duration_seconds": 1.2012882232666016, "success_rate": 100.0, "timestamp": "2025-08-01T23:24:49.265147"}, "regression_tests": {"suite_name": "regression_tests", "total_tests": 20, "passed_tests": 18, "failed_tests": 2, "skipped_tests": 0, "duration_seconds": 1.801340103149414, "success_rate": 90.0, "timestamp": "2025-08-01T23:24:51.066595", "failed_test_details": ["test_regression_tests_0", "test_regression_tests_1"]}}}, "reports_generated": ["automated_testing_report_20250801_232451.json", "test_summary_20250801_232451.txt"], "regression_tests": {"total_regression_suites": 2, "passed_regression_suites": 2, "failed_regression_suites": 0, "regression_success_rate": 100.0, "regression_details": {"phase1_basic": {"status": "passed", "initialization_time": 0.014972925186157227, "components_verified": 3}, "phase2_architecture": {"status": "passed", "health_check_time": 0.0009059906005859375, "health_status": "degraded"}}}, "comprehensive_report": {"test_session_summary": {"session_start": "2025-08-01T23:24:26.540777", "session_end": "2025-08-01T23:24:51.086386", "total_duration_minutes": 0.4090935666666667}, "cicd_report": {"report_type": "CI/CD Pipeline Report", "pipeline_status": "success", "total_duration": 15.210410594940186, "steps_summary": {"passed": 7, "warning": 1, "failed": 0}}, "quality_report": {"report_type": "Quality Gates Report", "gates_checked": 5, "gates_passed": 5, "critical_failures": 0, "quality_score": 85.2}, "test_report": {"report_type": "Test Execution Report", "total_tests": 90, "success_rate": 94.44444444444444, "test_suites": 5, "execution_time": 7.806300401687622}, "regression_report": {"report_type": "Regression Test Report", "regression_suites": 2, "regression_success_rate": 100.0, "critical_regressions": 0}, "overall_assessment": {"pipeline_health": "good", "quality_compliance": "compliant", "test_reliability": "high", "deployment_readiness": "ready"}}, "pipeline_end": "2025-08-01T23:24:51.087557", "summary": {"cicd_pipeline_success": true, "quality_gates_passed": true, "automated_tests_executed": true, "regression_tests_passed": true, "test_reports_generated": true, "deployment_ready": true, "phase3_testing_success": true}}