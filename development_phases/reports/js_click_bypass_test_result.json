{"timestamp": 1753451398.2630248, "test_type": "javascript_bypass_exitintentmodal", "recaptcha": {"grecaptcha_exists": false, "recaptcha_scripts": [], "recaptcha_elements": [], "recaptcha_ready": false}, "modal": {"wrapper_exists": true, "wrapper_visible": false, "wrapper_classes": "ExitIntentModal__wrapper --show", "has_show_class": true, "backdrop_exists": true, "backdrop_intercepts": true, "computed_display": "flex", "computed_visibility": "visible", "z_index": "1000", "intercepts_clicks": true, "other_modals": [{"tagName": "A", "className": "Ribbon__legal_modal", "id": ""}, {"tagName": "BUTTON", "className": "Billboard__modalLink", "id": ""}, {"tagName": "BUTTON", "className": "modal--close", "id": ""}, {"tagName": "SPAN", "className": "footer-modals", "id": ""}, {"tagName": "DIV", "className": "", "id": "modal"}]}, "js_click_test": {"results": {"strategies": [{"name": "DOM直接点击", "success": true, "time_ms": "0.00", "selector_used": "a[href*=\"login\"]", "element_tag": "A", "element_text": "Log In"}, {"name": "事件模拟点击", "success": true, "time_ms": "0.00", "selector_used": "a[href*=\"login\"]", "element_tag": "A", "element_text": "Log In"}, {"name": "强制焦点点击", "success": true, "time_ms": "0.00", "selector_used": "a[href*=\"login\"]", "element_tag": "A", "element_text": "Log In"}, {"name": "表单提交触发", "success": true, "time_ms": "0.00", "selector_used": "a[href*=\"login\"]", "element_tag": "A", "element_text": "Log In"}], "best_strategy": "DOM直接点击", "total_found": 1}, "playwright_comparison": [{"selector": "a:has-text(\"Log In\")", "success": true, "time_ms": 39.2918586730957, "found": true}, {"selector": "button:has-text(\"Log In\")", "success": false, "error": "Locator.element_handle: Timeout 1000ms exceeded.\nC", "time_ms": 0}, {"selector": "text=\"LOG IN\"", "success": false, "error": "Locator.element_handle: Timeout 1000ms exceeded.\nC", "time_ms": 0}, {"selector": "text=\"Log In\"", "success": true, "time_ms": 15.433073043823242, "found": true}, {"selector": "a[href*=\"login\"]", "success": true, "time_ms": 7.**************, "found": true}], "performance_analysis": {"js_avg_time_ms": 0.0, "playwright_avg_time_ms": 20.***************, "js_speed_advantage": 0}}, "login_test": {"success": true, "verification_needed": {"has_captcha": false, "has_challenge": false, "has_error": false, "page_blocked": false, "has_email_field": true, "has_password_field": false, "page_title": "<PERSON><PERSON> | Hulu", "body_text_sample": "Enter your email to continueLog in to Hulu with your MyDisney account. If you don't have one, you will be prompted to create one.EmailContinueHulu is part of The Walt Disney Family of CompaniesMyDisne"}, "url_after_click": "https://auth.hulu.com/web/login/enter-email", "actual_js_click_performed": true, "page_stability_achieved": true}}