{"test_start_time": "2025-08-01T23:06:06.266141", "original_architecture": {"iterations": 5, "successful_inits": 5, "init_times": [0.006819725036621094, 0.00815892219543457, 0.005802154541015625, 0.002090930938720703, 0.0018498897552490234], "avg_init_time": 0.004944324493408203, "min_init_time": 0.0018498897552490234, "max_init_time": 0.00815892219543457, "memory_usage_mb": [0.28125, 0.0625, 0.109375, 0.015625, 0.015625], "avg_memory_mb": 0.096875}, "new_architecture": {"iterations": 5, "successful_inits": 5, "init_times": [0.00012493133544921875, 0.00010371208190917969, 0.00010085105895996094, 9.918212890625e-05, 9.918212890625e-05], "avg_init_time": 0.00010557174682617188, "min_init_time": 9.918212890625e-05, "max_init_time": 0.00012493133544921875, "memory_usage_mb": [0.0, 0.0, 0.0, 0.0, 0.0], "avg_memory_mb": 0.0}, "facade_architecture": {"iterations": 5, "successful_inits": 5, "init_times": [0.006808757781982422, 0.005516767501831055, 0.0022127628326416016, 0.001950979232788086, 0.0018939971923828125], "avg_init_time": 0.0036766529083251953, "min_init_time": 0.0018939971923828125, "max_init_time": 0.006808757781982422, "memory_usage_mb": [0.203125, 0.0, 0.0, 0.0, 0.0], "avg_memory_mb": 0.040625}, "performance_comparison": {"initialization_improvements": {"new_vs_original_percent": 97.86478927572573, "facade_vs_original_percent": 25.63892371491947, "memory_new_vs_original_percent": 0, "memory_facade_vs_original_percent": 58.06451612903226}, "success_rates": {"original": 100.0, "new_architecture": 100.0, "facade": 100.0}, "memory_comparison": {"original_mb": 0.096875, "new_architecture_mb": 0.0, "facade_mb": 0.040625}}, "resource_usage": {"baseline_cpu_percent": 0.0, "baseline_memory_mb": 63.03125, "peak_cpu_percent": 475.1, "peak_memory_mb": 63.25, "monitoring_duration_seconds": 1.5215327739715576}, "consistency_tests": {"initialization_consistency": false, "configuration_consistency": true, "method_availability": {"execute_stealth_login": {"original": true, "facade": true}, "get_execution_stats": {"original": false, "facade": false}}, "health_check_comparison": {"new_architecture_health": {"status": "degraded", "timestamp": "2025-08-01T23:06:07.841890", "issues": ["Engine not initialized"], "component_status": {"engine": {"initialized": false, "version": "2.0.0"}, "cdp_manager": {"chrome_running": false, "debug_port": 9222}, "stealth_service": {"status": "available"}, "resource_manager": {"created_at": "2025-08-01T23:06:07.837851", "resources_registered": 0, "tasks_registered": 0, "cleanup_attempts": 0, "cleanup_errors": 0, "current_tasks": 0, "active_tasks": 0, "has_browser": false, "has_page": false, "has_cdp_process": false, "managed_processes": 0, "cleanup_completed": false}, "browser_resources": {"playwright": false, "browser": false, "context": false, "page": false}}, "execution_stats": {"total_executions": 0, "successful_executions": 0, "failed_executions": 0, "last_execution": null, "average_execution_time": 0.0, "success_rate": 0.0, "failure_rate": 0.0, "initialized": false, "has_active_session": false}}, "health_check_available": true}}, "load_performance": {"concurrent_initializations": 5, "successful_concurrent": 5, "avg_concurrent_time": 0.02356257438659668, "load_test_completed": true}, "error_handling": {"original_error_handling": {"total_scenarios": 3, "recovered_scenarios": 3, "recovery_rate": 100.0}, "new_architecture_error_handling": {"total_scenarios": 3, "recovered_scenarios": 3, "recovery_rate": 100.0}, "error_recovery_comparison": {"original_recovery_rate": 100.0, "new_recovery_rate": 100.0, "improvement": 0.0}}, "test_end_time": "2025-08-01T23:06:07.971636", "summary": {"test_categories": 8, "new_architecture_available": true, "performance_improvements_detected": true, "consistency_verified": false}}