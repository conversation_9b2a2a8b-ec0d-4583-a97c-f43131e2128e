# Sessions目录

此目录用于存储各个账户的独立session文件，实现多账户cookies隔离。

## 文件命名规则

每个账户的session文件按以下规则命名：
- 格式：`{safe_email}_session.json`
- safe_email：将email中的特殊字符替换为下划线
- 示例：
  - `<EMAIL>` → `3c63b55bf45e_testkuroneko_xyz_session.json`
  - `<EMAIL>` → `6250e8982ad1_testkuroneko_xyz_session.json`

## 文件内容

每个session文件包含Playwright的storage state信息：
- cookies
- localStorage
- sessionStorage
- origins

## 自动管理

- 目录会在首次使用时自动创建
- session文件会在登录成功后自动生成
- 每个账户的session完全独立，避免相互影响