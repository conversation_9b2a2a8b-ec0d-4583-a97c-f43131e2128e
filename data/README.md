# 📁 Data Directory Structure

This directory contains the data management structure for the Credit Card Validation Learning Module.

## 📂 Directory Overview

```
data/
├── data_input/          # Input data directory
│   ├── .gitignore      # Ignores all input files for security
│   └── (input files)   # Credit card data files (ignored by git)
├── data_output/         # Output results directory  
│   ├── .gitignore      # Ignores all output files for security
│   └── validation_results_*/  # Timestamped result directories (ignored by git)
└── README.md           # This file
```

## 🔒 Security Notice

**⚠️ IMPORTANT SECURITY INFORMATION**

All files in the `data_input/` and `data_output/` directories are automatically ignored by Git for security reasons:

- **Input files** may contain sensitive credit card data (even if fake/test data)
- **Output files** contain encrypted data and validation reports
- **Git ignore** prevents accidental commits of sensitive information

## 📥 Input Directory (`data_input/`)

**Purpose**: Store input data files for credit card validation

**Supported formats**:
- `.txt` files (space-separated format)
- `.csv` files 
- `.json` files

**Example usage**:
```bash
# Create sample data
python credit_card_demo.py --create-sample

# Process input file
python credit_card_demo.py --file-demo data/data_input/fake_data.txt
```

**Security**: All files in this directory are ignored by Git via `.gitignore`

## 📤 Output Directory (`data_output/`)

**Purpose**: Store validation results and reports

**Generated directories**:
- `validation_results_YYYYMMDD_HHMMSS/` - Timestamped result folders
- `card_validation_report_YYYYMMDD_HHMMSS/` - Alternative naming format

**Generated files**:
- `processed_data_encrypted.json` - Encrypted complete processing data
- `validation_report.json` - Sanitized validation report (JSON)
- `validation_report.csv` - Sanitized validation report (CSV)
- `processing_summary.json` - Processing statistics summary

**Security**: All files and directories are ignored by Git via `.gitignore`

## 🛡️ Git Ignore Configuration

### Root `.gitignore` Rules
```gitignore
# Credit Card Validator specific
data/data_input/*
!data/data_input/.gitignore

data/data_output/*
!data/data_output/.gitignore

credit_card_validator.log
```

### Local `.gitignore` Files
- `data_input/.gitignore` - Ignores all input files
- `data_output/.gitignore` - Ignores all output files

## 🚀 Quick Start

1. **Create sample data**:
   ```bash
   python credit_card_demo.py --create-sample
   ```

2. **Process data**:
   ```bash
   python credit_card_demo.py --file-demo data/data_input/fake_data.txt
   ```

3. **View results**:
   ```bash
   ls data/data_output/validation_results_*/
   ```

## 📋 File Management Best Practices

1. **Regular cleanup**: Periodically clean old output directories
2. **Backup important results**: Save important validation reports elsewhere
3. **Monitor disk space**: Output directories can grow over time
4. **Secure deletion**: Use the module's secure deletion features

## ⚠️ Important Notes

- **Never commit sensitive data**: The `.gitignore` files prevent this
- **Test data only**: Only use fake/test credit card data
- **Local processing**: All processing happens locally for security
- **Encrypted storage**: Sensitive data is always encrypted in outputs

## 🔧 Troubleshooting

**Issue**: Files showing up in git status
**Solution**: Check `.gitignore` files are properly configured

**Issue**: Permission denied errors
**Solution**: Ensure proper file system permissions

**Issue**: Disk space issues
**Solution**: Clean old output directories regularly

---

**🔒 Security Reminder**: This module is for educational purposes only. Never process real credit card data.
