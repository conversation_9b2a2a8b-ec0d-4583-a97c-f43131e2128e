#!/usr/bin/env python3
"""
智能等待管理器 (Intelligent Waiting Manager)
替换传统的5秒循环等待和固定延迟，使用现代Playwright API

功能:
- expect_page上下文管理器用于新标签页处理
- 智能元素等待策略
- 动态超时调整
- 页面状态感知等待
- 错误恢复机制

优化重点:
- 移除5秒循环等待逻辑
- 使用page.wait_for_load_state()替换固定延迟
- 实现智能等待决策
"""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Any, Callable, Union
from enum import Enum
from dataclasses import dataclass
from playwright.async_api import Page, expect, Locator
from contextlib import asynccontextmanager

class WaitStrategy(Enum):
    """等待策略枚举"""
    LOAD_STATE = "load_state"           # 页面加载状态等待
    NETWORK_IDLE = "network_idle"       # 网络空闲等待
    ELEMENT_VISIBLE = "element_visible" # 元素可见等待
    ELEMENT_STABLE = "element_stable"   # 元素稳定等待
    DYNAMIC_CONTENT = "dynamic_content" # 动态内容等待
    USER_ACTION = "user_action"         # 用户交互等待

@dataclass
class WaitContext:
    """等待上下文"""
    operation_type: str                 # 操作类型
    expected_outcome: str               # 期望结果
    timeout_ms: int = 5000             # 超时时间
    strategy: WaitStrategy = WaitStrategy.LOAD_STATE
    fallback_strategy: Optional[WaitStrategy] = None
    selectors: List[str] = None        # 相关选择器
    custom_condition: Optional[Callable] = None  # 自定义条件

class IntelligentWaitingManager:
    """
    智能等待管理器
    
    替换传统的固定延迟和循环等待，使用现代Playwright API
    提供上下文感知的智能等待策略
    """
    
    def __init__(self, page: Page, logger: Optional[logging.Logger] = None):
        self.page = page
        self.logger = logger or logging.getLogger(__name__)
        
        # 预定义等待上下文
        self.wait_contexts = {
            # 页面导航等待
            "page_navigation": WaitContext(
                operation_type="navigation",
                expected_outcome="page_loaded",
                timeout_ms=10000,
                strategy=WaitStrategy.LOAD_STATE
            ),
            
            # 表单提交等待
            "form_submission": WaitContext(
                operation_type="form_submit",
                expected_outcome="response_received",
                timeout_ms=8000,
                strategy=WaitStrategy.NETWORK_IDLE,
                fallback_strategy=WaitStrategy.LOAD_STATE
            ),
            
            # 按钮点击等待
            "button_click": WaitContext(
                operation_type="click",
                expected_outcome="ui_response",
                timeout_ms=3000,
                strategy=WaitStrategy.ELEMENT_STABLE
            ),
            
            # 动态内容加载等待
            "dynamic_content": WaitContext(
                operation_type="content_load",
                expected_outcome="content_ready",
                timeout_ms=6000,
                strategy=WaitStrategy.DYNAMIC_CONTENT
            ),
            
            # 模态框处理等待
            "modal_handling": WaitContext(
                operation_type="modal",
                expected_outcome="modal_processed",
                timeout_ms=4000,
                strategy=WaitStrategy.ELEMENT_VISIBLE
            )
        }
        
        self.logger.info("⚡ IntelligentWaitingManager初始化完成")

    async def smart_wait(self, 
                        context_name: str, 
                        custom_timeout: Optional[int] = None,
                        custom_selectors: Optional[List[str]] = None) -> bool:
        """
        智能等待 - 根据上下文选择最佳等待策略
        
        Args:
            context_name: 预定义的等待上下文名称
            custom_timeout: 自定义超时时间(ms)
            custom_selectors: 自定义选择器列表
            
        Returns:
            bool: 等待是否成功
        """
        if context_name not in self.wait_contexts:
            self.logger.warning(f"⚠️ 未知等待上下文: {context_name}, 使用默认策略")
            return await self._default_wait(custom_timeout or 3000)
        
        context = self.wait_contexts[context_name]
        timeout = custom_timeout or context.timeout_ms
        selectors = custom_selectors or context.selectors
        
        self.logger.info(f"🧠 智能等待: {context_name} ({context.strategy.value})")
        
        try:
            # 根据策略执行等待
            if context.strategy == WaitStrategy.LOAD_STATE:
                return await self._wait_for_load_state(timeout)
            elif context.strategy == WaitStrategy.NETWORK_IDLE:
                return await self._wait_for_network_idle(timeout)
            elif context.strategy == WaitStrategy.ELEMENT_VISIBLE:
                return await self._wait_for_element_visible(selectors, timeout)
            elif context.strategy == WaitStrategy.ELEMENT_STABLE:
                return await self._wait_for_element_stable(selectors, timeout)
            elif context.strategy == WaitStrategy.DYNAMIC_CONTENT:
                return await self._wait_for_dynamic_content(timeout)
            elif context.strategy == WaitStrategy.USER_ACTION:
                return await self._wait_for_user_action(timeout)
            else:
                return await self._default_wait(timeout)
                
        except Exception as e:
            self.logger.debug(f"⚠️ 主要策略失败: {e}")
            
            # 尝试降级策略
            if context.fallback_strategy:
                self.logger.info(f"🔄 尝试降级策略: {context.fallback_strategy.value}")
                try:
                    if context.fallback_strategy == WaitStrategy.LOAD_STATE:
                        return await self._wait_for_load_state(timeout // 2)
                    elif context.fallback_strategy == WaitStrategy.NETWORK_IDLE:
                        return await self._wait_for_network_idle(timeout // 2)
                    else:
                        return await self._default_wait(timeout // 2)
                except Exception as fallback_error:
                    self.logger.debug(f"⚠️ 降级策略也失败: {fallback_error}")
            
            # 最后的兜底策略
            return await self._default_wait(min(2000, timeout // 3))

    async def _wait_for_load_state(self, timeout_ms: int) -> bool:
        """等待页面加载状态"""
        try:
            await self.page.wait_for_load_state('domcontentloaded', timeout=timeout_ms)
            self.logger.debug("✅ DOM内容加载完成")
            return True
        except Exception as e:
            self.logger.debug(f"⚠️ DOM加载等待超时: {e}")
            return False

    async def _wait_for_network_idle(self, timeout_ms: int) -> bool:
        """等待网络空闲"""
        try:
            await self.page.wait_for_load_state('networkidle', timeout=timeout_ms)
            self.logger.debug("✅ 网络活动已空闲")
            return True
        except Exception as e:
            self.logger.debug(f"⚠️ 网络空闲等待超时: {e}")
            return False

    async def _wait_for_element_visible(self, selectors: Optional[List[str]], timeout_ms: int) -> bool:
        """等待元素可见"""
        if not selectors:
            return await self._default_wait(timeout_ms)
        
        for selector in selectors:
            try:
                locator = self.page.locator(selector).first()
                await locator.wait_for(state='visible', timeout=timeout_ms)
                self.logger.debug(f"✅ 元素可见: {selector}")
                return True
            except Exception as e:
                self.logger.debug(f"⚠️ 元素可见等待失败: {selector} - {e}")
                continue
        
        return False

    async def _wait_for_element_stable(self, selectors: Optional[List[str]], timeout_ms: int) -> bool:
        """等待元素稳定（可点击）"""
        if not selectors:
            return await self._default_wait(timeout_ms)
        
        for selector in selectors:
            try:
                locator = self.page.locator(selector).first()
                await locator.wait_for(state='visible', timeout=timeout_ms // 2)
                
                # 等待元素稳定（没有动画等）
                await asyncio.sleep(0.3)  # 短暂等待确保稳定
                
                # 验证元素仍然可见且可交互
                if await locator.is_visible():
                    self.logger.debug(f"✅ 元素稳定: {selector}")
                    return True
                    
            except Exception as e:
                self.logger.debug(f"⚠️ 元素稳定等待失败: {selector} - {e}")
                continue
        
        return False

    async def _wait_for_dynamic_content(self, timeout_ms: int) -> bool:
        """等待动态内容加载完成"""
        try:
            # 等待网络空闲
            await self.page.wait_for_load_state('networkidle', timeout=timeout_ms // 2)
            
            # 等待JavaScript执行完成
            await asyncio.sleep(0.5)
            
            # 再次检查网络状态
            await self.page.wait_for_load_state('domcontentloaded', timeout=timeout_ms // 4)
            
            self.logger.debug("✅ 动态内容加载完成")
            return True
            
        except Exception as e:
            self.logger.debug(f"⚠️ 动态内容等待超时: {e}")
            return False

    async def _wait_for_user_action(self, timeout_ms: int) -> bool:
        """等待用户交互响应"""
        try:
            # 等待页面稳定
            await asyncio.sleep(0.2)
            
            # 等待可能的页面跳转
            await self.page.wait_for_load_state('domcontentloaded', timeout=timeout_ms)
            
            self.logger.debug("✅ 用户交互响应完成")
            return True
            
        except Exception as e:
            self.logger.debug(f"⚠️ 用户交互等待超时: {e}")
            return False

    async def _default_wait(self, timeout_ms: int) -> bool:
        """默认等待策略"""
        try:
            # 使用较短的智能延迟替代固定5秒等待
            wait_time = min(timeout_ms / 1000, 2.0)  # 最多2秒
            await asyncio.sleep(wait_time)
            
            self.logger.debug(f"✅ 默认等待完成: {wait_time}s")
            return True
            
        except Exception as e:
            self.logger.debug(f"⚠️ 默认等待失败: {e}")
            return False

    @asynccontextmanager
    async def expect_popup(self, trigger_action: Callable):
        """
        新标签页/弹窗处理上下文管理器
        
        Usage:
            async with waiting_manager.expect_popup(lambda: page.click("a[target='_blank']")) as popup:
                # 处理新打开的页面
                await popup.wait_for_load_state()
        """
        try:
            # 设置弹窗监听
            popup_promise = self.page.context.wait_for_page()
            
            # 执行触发动作
            await trigger_action()
            
            # 等待弹窗
            popup = await popup_promise
            
            self.logger.info("✅ 新页面已打开")
            yield popup
            
        except Exception as e:
            self.logger.error(f"❌ 弹窗处理失败: {e}")
            yield None

    def add_wait_context(self, name: str, context: WaitContext):
        """添加自定义等待上下文"""
        self.wait_contexts[name] = context
        self.logger.info(f"➕ 添加等待上下文: {name}")

    def update_wait_context(self, name: str, **kwargs):
        """更新等待上下文"""
        if name in self.wait_contexts:
            context = self.wait_contexts[name]
            for key, value in kwargs.items():
                if hasattr(context, key):
                    setattr(context, key, value)
            self.logger.info(f"🔄 更新等待上下文: {name}")

    async def replace_sleep(self, seconds: float, context: str = "default") -> bool:
        """
        替换传统的asyncio.sleep()调用
        
        Args:
            seconds: 原始睡眠秒数
            context: 上下文描述
            
        Returns:
            bool: 等待是否成功
        """
        if seconds >= 5.0:
            # 长时间睡眠改为智能等待
            self.logger.info(f"🔄 替换{seconds}秒睡眠为智能等待: {context}")
            return await self.smart_wait("dynamic_content", int(seconds * 800))
        elif seconds >= 2.0:
            # 中等睡眠改为页面稳定等待
            return await self.smart_wait("page_navigation", int(seconds * 1000))
        else:
            # 短睡眠保留但优化
            optimized_time = min(seconds, 1.0)  # 最多1秒
            await asyncio.sleep(optimized_time)
            return True

    async def intelligent_wait(self, 
                              context_name: str, 
                              custom_timeout: Optional[int] = None,
                              custom_selectors: Optional[List[str]] = None) -> bool:
        """
        智能等待方法 - smart_wait()的别名，用于向后兼容
        
        Args:
            context_name: 预定义的等待上下文名称
            custom_timeout: 自定义超时时间(ms)
            custom_selectors: 自定义选择器列表
            
        Returns:
            bool: 等待是否成功
        """
        return await self.smart_wait(context_name, custom_timeout, custom_selectors)

    def get_context_info(self, context_name: str) -> Dict[str, Any]:
        """获取等待上下文信息 (用于调试)"""
        if context_name in self.wait_contexts:
            context = self.wait_contexts[context_name]
            return {
                "operation_type": context.operation_type,
                "expected_outcome": context.expected_outcome,
                "timeout_ms": context.timeout_ms,
                "strategy": context.strategy.value,
                "fallback_strategy": context.fallback_strategy.value if context.fallback_strategy else None
            }
        return {"error": f"Unknown context: {context_name}"}