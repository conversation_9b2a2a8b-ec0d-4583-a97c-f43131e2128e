#!/usr/bin/env python3
"""
智能交互控制器 (Smart Interaction Controller)
页面特定的交互策略路由系统

功能:
- 页面URL识别和路由
- Welcome页面JS点击保护机制
- 智能交互方式选择
- 现代Locator API集成
- 向后兼容性保证

保护的关键页面:
- hulu.com/welcome: 保持JS点击以维持反检测能力
- 其他页面: 逐步升级到现代Playwright API
"""

import asyncio
import logging
import re
from typing import Dict, List, Optional, Any, Union, Tuple
from enum import Enum
from dataclasses import dataclass
from playwright.async_api import Page, Locator

# 可选的视觉兜底系统导入
try:
    from .visual_fallback_system import VisualFallbackSystem, visual_fallback_click
    VISUAL_FALLBACK_AVAILABLE = True
except ImportError:
    VISUAL_FALLBACK_AVAILABLE = False

class InteractionStrategy(Enum):
    """交互策略枚举"""
    JS_CLICK_ONLY = "js_click_only"           # 强制JS点击 (Welcome页面)
    JS_CLICK_PREFERRED = "js_click_preferred" # 优先JS点击
    LOCATOR_PREFERRED = "locator_preferred"   # 优先现代Locator
    LOCATOR_ONLY = "locator_only"             # 仅现代Locator
    HYBRID_FALLBACK = "hybrid_fallback"       # 混合降级策略

@dataclass
class PageConfig:
    """页面配置"""
    url_pattern: str
    strategy: InteractionStrategy
    special_selectors: Dict[str, List[str]] = None
    timeout_ms: int = 5000
    description: str = ""

class SmartInteractionController:
    """
    智能交互控制器
    
    根据页面URL和元素类型智能选择最佳交互方式:
    - Welcome页面: 保持JS点击以维持98%成功率
    - 表单页面: 现代Locator API提升性能  
    - 其他页面: 混合策略保证兼容性
    """
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        self.logger = logger or logging.getLogger(__name__)
        
        # 页面特定配置
        self.page_configs = {
            "welcome": PageConfig(
                url_pattern=r"hulu\.com/welcome",
                strategy=InteractionStrategy.JS_CLICK_ONLY,
                special_selectors={
                    "login_button": [
                        'a[href*="login"]',
                        'button[data-automation-id="login"]', 
                        '.login-button',
                        'a[data-testid="login-button"]'
                    ]
                },
                timeout_ms=8000,
                description="Welcome页面 - 保持JS点击反检测优势"
            ),
            
            "signup": PageConfig(
                url_pattern=r"hulu\.com/signup",
                strategy=InteractionStrategy.JS_CLICK_PREFERRED,
                special_selectors={
                    "submit_button": [
                        'button[type="submit"]',
                        'input[type="submit"]',
                        '.signup-submit'
                    ]
                },
                description="注册页面 - 优先JS点击"
            ),
            
            "login_form": PageConfig(
                url_pattern=r"auth\.hulu\.com|login",
                strategy=InteractionStrategy.LOCATOR_PREFERRED,
                special_selectors={
                    "email_input": [
                        'input[type="email"]',
                        'input[name="email"]',
                        '#email'
                    ],
                    "password_input": [
                        'input[type="password"]',
                        'input[name="password"]',
                        '#password'
                    ]
                },
                description="登录表单页面 - 现代Locator API"
            ),
            
            "default": PageConfig(
                url_pattern=r".*",
                strategy=InteractionStrategy.HYBRID_FALLBACK,
                description="默认页面 - 混合降级策略"
            )
        }
        
        # 可选的视觉兜底系统
        self.visual_fallback: Optional[VisualFallbackSystem] = None
        if VISUAL_FALLBACK_AVAILABLE:
            try:
                self.visual_fallback = VisualFallbackSystem(logger=self.logger)
                self.logger.info("🖼️ 视觉兜底系统已启用")
            except Exception as e:
                self.logger.warning(f"⚠️ 视觉兜底系统初始化失败: {e}")
        
        self.logger.info("✅ SmartInteractionController初始化完成")

    def _identify_page_type(self, url: str) -> str:
        """识别页面类型"""
        for page_type, config in self.page_configs.items():
            if page_type != "default" and re.search(config.url_pattern, url, re.IGNORECASE):
                self.logger.debug(f"🎯 识别页面类型: {page_type} ({url})")
                return page_type
        
        self.logger.debug(f"🔄 使用默认页面策略: {url}")
        return "default"

    def _get_page_config(self, url: str) -> PageConfig:
        """获取页面配置"""
        page_type = self._identify_page_type(url)
        return self.page_configs[page_type]

    async def smart_click(self, 
                         page: Page, 
                         selectors: Union[str, List[str]], 
                         element_type: str = "button",
                         timeout_ms: Optional[int] = None) -> bool:
        """
        智能点击 - 根据页面类型选择最佳策略
        
        Args:
            page: Playwright页面对象
            selectors: 选择器字符串或列表
            element_type: 元素类型 (用于特殊选择器匹配)
            timeout_ms: 超时时间
            
        Returns:
            bool: 点击是否成功
        """
        if isinstance(selectors, str):
            selectors = [selectors]
            
        current_url = page.url
        config = self._get_page_config(current_url)
        timeout_ms = timeout_ms or config.timeout_ms
        
        self.logger.info(f"🎯 智能点击策略: {config.strategy.value} ({current_url})")
        
        # 检查是否有特殊选择器
        if config.special_selectors and element_type in config.special_selectors:
            special_selectors = config.special_selectors[element_type]
            self.logger.debug(f"🔍 使用特殊选择器: {element_type}")
            selectors = special_selectors + selectors  # 特殊选择器优先
        
        # 根据策略选择交互方式
        if config.strategy == InteractionStrategy.JS_CLICK_ONLY:
            return await self._js_click_strategy(page, selectors, timeout_ms)
        elif config.strategy == InteractionStrategy.JS_CLICK_PREFERRED:
            return await self._js_preferred_strategy(page, selectors, timeout_ms)
        elif config.strategy == InteractionStrategy.LOCATOR_PREFERRED:
            return await self._locator_preferred_strategy(page, selectors, timeout_ms)
        elif config.strategy == InteractionStrategy.LOCATOR_ONLY:
            return await self._locator_only_strategy(page, selectors, timeout_ms)
        else:  # HYBRID_FALLBACK
            return await self._hybrid_fallback_strategy(page, selectors, timeout_ms)

    async def _js_click_strategy(self, page: Page, selectors: List[str], timeout_ms: int) -> bool:
        """JS点击策略 - Welcome页面专用"""
        self.logger.info("🛡️ 使用JS点击策略 (反检测保护)")
        
        for selector in selectors:
            try:
                # 等待元素存在
                await page.wait_for_selector(selector, timeout=timeout_ms)
                
                # 使用JS点击
                result = await page.evaluate(f'''
                    (function() {{
                        const element = document.querySelector("{selector}");
                        if (element && element.offsetParent !== null) {{
                            element.click();
                            return true;
                        }}
                        return false;
                    }})()
                ''')
                
                if result:
                    self.logger.info(f"✅ JS点击成功: {selector}")
                    return True
                else:
                    self.logger.debug(f"⚠️ JS点击失败 (元素不可见): {selector}")
                    
            except Exception as e:
                self.logger.debug(f"⚠️ JS点击失败: {selector} - {e}")
                continue
        
        self.logger.warning("❌ 所有JS点击策略均失败")
        return False

    async def _locator_preferred_strategy(self, page: Page, selectors: List[str], timeout_ms: int) -> bool:
        """现代Locator优先策略"""
        self.logger.info("⚡ 使用现代Locator优先策略")
        
        for selector in selectors:
            try:
                # 尝试现代Locator API
                locator = page.locator(selector).first()
                await locator.wait_for(state='visible', timeout=timeout_ms)
                await locator.click(timeout=timeout_ms)
                
                self.logger.info(f"✅ Locator点击成功: {selector}")
                return True
                
            except Exception as e:
                self.logger.debug(f"⚠️ Locator点击失败: {selector} - {e}")
                
                # 降级到传统方式
                try:
                    element = await page.wait_for_selector(selector, timeout=1000)
                    if element and await element.is_visible():
                        await element.click()
                        self.logger.info(f"✅ 传统点击成功 (降级): {selector}")
                        return True
                except Exception as fallback_error:
                    self.logger.debug(f"⚠️ 降级点击也失败: {selector} - {fallback_error}")
                    continue
        
        self.logger.warning("❌ Locator优先策略失败")
        return False

    async def _js_preferred_strategy(self, page: Page, selectors: List[str], timeout_ms: int) -> bool:
        """JS优先策略"""
        self.logger.info("🔧 使用JS优先策略")
        
        # 先尝试JS点击
        if await self._js_click_strategy(page, selectors, timeout_ms):
            return True
        
        # 降级到Locator
        self.logger.info("🔄 降级到Locator策略")
        return await self._locator_preferred_strategy(page, selectors, timeout_ms)

    async def _locator_only_strategy(self, page: Page, selectors: List[str], timeout_ms: int) -> bool:
        """仅现代Locator策略"""
        self.logger.info("🆕 使用纯Locator策略")
        
        for selector in selectors:
            try:
                locator = page.locator(selector).first()
                await locator.wait_for(state='visible', timeout=timeout_ms)
                await locator.click(timeout=timeout_ms)
                
                self.logger.info(f"✅ 纯Locator点击成功: {selector}")
                return True
                
            except Exception as e:
                self.logger.debug(f"⚠️ 纯Locator点击失败: {selector} - {e}")
                continue
        
        self.logger.warning("❌ 纯Locator策略失败")
        return False

    async def _hybrid_fallback_strategy(self, page: Page, selectors: List[str], timeout_ms: int) -> bool:
        """混合降级策略"""
        self.logger.info("🔀 使用混合降级策略")
        
        strategies = [
            ("Locator优先", self._locator_preferred_strategy),
            ("JS降级", self._js_click_strategy)
        ]
        
        for strategy_name, strategy_func in strategies:
            self.logger.debug(f"🔄 尝试策略: {strategy_name}")
            if await strategy_func(page, selectors, timeout_ms):
                return True
        
        # 最后尝试视觉兜底策略
        if self.visual_fallback:
            self.logger.info("🖼️ 尝试视觉兜底策略")
            try:
                # 根据选择器推断可能的模板名称
                template_candidates = self._infer_template_names_from_selectors(selectors)
                
                for template_name in template_candidates:
                    success = await self.visual_fallback.smart_click_by_template(
                        page, template_name, confidence_override=0.70  # 稍微降低阈值
                    )
                    if success:
                        self.logger.info(f"✅ 视觉兜底成功: {template_name}")
                        return True
                        
            except Exception as e:
                self.logger.debug(f"⚠️ 视觉兜底异常: {e}")
        
        self.logger.warning("❌ 所有混合策略均失败")
        return False

    def _infer_template_names_from_selectors(self, selectors: List[str]) -> List[str]:
        """根据选择器推断可能的模板名称"""
        template_candidates = []
        
        for selector in selectors:
            selector_lower = selector.lower()
            
            # 登录相关
            if any(keyword in selector_lower for keyword in ['login', 'log-in', 'signin']):
                template_candidates.append('login_button')
            
            # 继续按钮
            elif any(keyword in selector_lower for keyword in ['continue', 'next', '继续']):
                template_candidates.append('continue_button')
            
            # 提交按钮
            elif any(keyword in selector_lower for keyword in ['submit', 'confirm', '提交']):
                template_candidates.append('submit_button')
                
            # 邮箱输入
            elif any(keyword in selector_lower for keyword in ['email', 'mail', '@']):
                template_candidates.append('email_input')
                
            # 密码输入
            elif 'password' in selector_lower:
                template_candidates.append('password_input')
        
        # 去重并保持顺序
        return list(dict.fromkeys(template_candidates))

    async def smart_fill(self, 
                        page: Page, 
                        selectors: Union[str, List[str]], 
                        value: str,
                        element_type: str = "input",
                        clear_first: bool = True,
                        timeout_ms: Optional[int] = None) -> bool:
        """
        智能填充 - 根据页面类型选择填充策略
        
        Args:
            page: Playwright页面对象
            selectors: 选择器字符串或列表
            value: 要填充的值
            element_type: 元素类型
            clear_first: 是否先清空
            timeout_ms: 超时时间
            
        Returns:
            bool: 填充是否成功
        """
        if isinstance(selectors, str):
            selectors = [selectors]
            
        current_url = page.url
        config = self._get_page_config(current_url)
        timeout_ms = timeout_ms or config.timeout_ms
        
        # 检查特殊选择器
        if config.special_selectors and element_type in config.special_selectors:
            special_selectors = config.special_selectors[element_type]
            selectors = special_selectors + selectors
        
        self.logger.info(f"📝 智能填充策略: {config.strategy.value}")
        
        # 根据策略选择填充方式
        if config.strategy in [InteractionStrategy.LOCATOR_PREFERRED, InteractionStrategy.LOCATOR_ONLY]:
            return await self._locator_fill_strategy(page, selectors, value, clear_first, timeout_ms)
        else:
            return await self._traditional_fill_strategy(page, selectors, value, clear_first, timeout_ms)

    async def _locator_fill_strategy(self, page: Page, selectors: List[str], value: str, clear_first: bool, timeout_ms: int) -> bool:
        """现代Locator填充策略"""
        for selector in selectors:
            try:
                locator = page.locator(selector).first()
                await locator.wait_for(state='editable', timeout=timeout_ms)
                
                if clear_first:
                    await locator.clear()
                    await asyncio.sleep(0.1)  # 短暂延迟
                
                await locator.fill(value)
                self.logger.info(f"✅ Locator填充成功: {selector}")
                return True
                
            except Exception as e:
                self.logger.debug(f"⚠️ Locator填充失败: {selector} - {e}")
                continue
        
        return False

    async def _traditional_fill_strategy(self, page: Page, selectors: List[str], value: str, clear_first: bool, timeout_ms: int) -> bool:
        """传统填充策略"""
        for selector in selectors:
            try:
                element = await page.wait_for_selector(selector, timeout=timeout_ms)
                if element and await element.is_editable():
                    if clear_first:
                        await element.click()
                        await element.fill("")
                        await asyncio.sleep(0.1)
                    
                    await element.fill(value)
                    self.logger.info(f"✅ 传统填充成功: {selector}")
                    return True
                    
            except Exception as e:
                self.logger.debug(f"⚠️ 传统填充失败: {selector} - {e}")
                continue
        
        return False

    def get_page_strategy(self, url: str) -> str:
        """获取页面使用的策略 (用于调试和监控)"""
        config = self._get_page_config(url)
        return config.strategy.value

    def add_page_config(self, name: str, config: PageConfig):
        """添加自定义页面配置"""
        self.page_configs[name] = config
        self.logger.info(f"➕ 添加页面配置: {name} - {config.strategy.value}")

    def update_page_config(self, page_type: str, **kwargs):
        """更新页面配置"""
        if page_type in self.page_configs:
            config = self.page_configs[page_type]
            for key, value in kwargs.items():
                if hasattr(config, key):
                    setattr(config, key, value)
            self.logger.info(f"🔄 更新页面配置: {page_type}")
        else:
            self.logger.warning(f"⚠️ 页面配置不存在: {page_type}")