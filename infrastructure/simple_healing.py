#!/usr/bin/env python3
"""
SimpleHealing - 简化版自愈机制
基于用户技术建议的完整修正版本

核心功能:
1. 支持参数回落的超时优化算法
2. 基于样本量和时间间隔的智能触发
3. 配置变更历史记录到独立CSV
4. 自适应成功率阈值调整

自愈策略:
- 性能良好时减小超时提升速度
- 性能不佳时增大超时保证成功率
- 基于95%分位数和成功率双重判断
- 所有配置变更自动记录审计轨迹
"""

import csv
import json
import logging
import statistics
import time
from pathlib import Path
from typing import Dict, List, Optional, Tuple

from infrastructure.simple_monitor import SimpleMonitor

class SimpleHealing:
    """
    简化版自愈机制
    
    设计原则:
    - 参数回落: 性能良好时自动减小超时提升速度
    - 智能触发: 基于数据量和时间间隔避免空跑
    - 变更追踪: 独立CSV记录所有配置变更历史
    - 安全范围: 超时参数限制在200-2000ms合理范围
    """
    
    def __init__(self, monitor: SimpleMonitor, detector_config):
        """
        初始化自愈机制
        
        Args:
            monitor: 监控数据收集器
            detector_config: Layer 0检测器配置 (需要有iframe_detection_timeout和host_discovery_timeout属性)
        """
        self.monitor = monitor
        self.config = detector_config
        self.logger = logging.getLogger('healing')
        self._last_run = 0
        
        # 配置历史文件
        self.config_history_file = Path("logs/config_history.csv")
        
        self.logger.debug("🛠️ SimpleHealing自愈机制已初始化")
        
    def should_run_healing(self) -> bool:
        """
        判断是否应该运行自愈 - 修正触发条件
        
        修正要点:
        - 样本量 >= 30 条记录才触发分析
        - 距离上次运行 > 10分钟避免频繁调整
        - 避免低流量场景空跑
        
        Returns:
            bool: 是否应该运行自愈分析
        """
        current_time = time.time()
        
        # 修正：样本量 >= 30 且距离上次运行 > 10分钟
        if len(self.monitor.records) < 30:
            self.logger.debug(f"样本量不足: {len(self.monitor.records)} < 30，跳过自愈")
            return False
            
        if current_time - self._last_run < 600:  # 10分钟
            remaining = int(600 - (current_time - self._last_run))
            self.logger.debug(f"距离上次运行时间过短，还需等待 {remaining} 秒")
            return False
            
        return True
        
    def analyze_and_optimize(self):
        """
        配置分析和优化 - 支持参数回落
        
        修正要点:
        - 支持增大和减小超时参数
        - 基于成功率和响应时间双重判断  
        - 配置变更记录到独立CSV文件
        - 安全的参数范围限制
        """
        if not self.should_run_healing():
            return
            
        self._last_run = time.time()
        self.logger.info("🔧 开始自愈分析和配置优化...")
        
        # 分析最近50条记录
        recent_records = list(self.monitor.records)[-50:]
        
        # 按操作类型分组
        by_operation = {}
        for record in recent_records:
            if record.operation not in by_operation:
                by_operation[record.operation] = []
            by_operation[record.operation].append(record)
        
        optimizations = {}
        config_changes = []
        
        # iframe检测优化 - 支持回落
        if "iframe_detection" in by_operation:
            changes = self._optimize_iframe_timeout(by_operation["iframe_detection"])
            if changes:
                optimizations.update(changes)
                config_changes.extend(changes.items())
        
        # 宿主发现优化 - 支持回落
        if "host_discovery" in by_operation:
            changes = self._optimize_host_timeout(by_operation["host_discovery"])
            if changes:
                optimizations.update(changes)
                config_changes.extend(changes.items())
        
        # element_location 连续失败检测 - 针对 passcode 页面
        if "element_location" in by_operation:
            failure_alert = self._check_element_location_failures(by_operation["element_location"])
            if failure_alert:
                self.logger.warning(f"🚨 element_location连续失败检测: {failure_alert}")
        
        # 应用优化并记录
        if optimizations:
            self._apply_optimizations(optimizations)
            self._log_config_changes(config_changes)
            self.logger.info(f"✅ 自愈优化完成: {len(optimizations)} 项配置已更新")
        else:
            self.logger.info("✅ 自愈分析完成: 当前配置已是最优，无需调整")
    
    def _optimize_iframe_timeout(self, records) -> Dict[str, int]:
        """
        优化iframe超时 - 支持增大和减小
        
        Args:
            records: iframe检测的监控记录列表
            
        Returns:
            Dict[str, int]: 优化后的配置变更
        """
        if len(records) < 10:
            self.logger.debug("iframe记录数量不足，跳过优化")
            return {}
            
        # 只统计成功的操作耗时
        durations = [r.duration_ms for r in records if r.success]
        if not durations:
            self.logger.warning("iframe检测无成功记录，跳过优化")
            return {}
            
        success_rate = sum(1 for r in records if r.success) / len(records)
        avg_time = statistics.mean(durations)
        current_timeout = self.config.iframe_detection_timeout
        
        self.logger.debug(f"iframe分析: 成功率{success_rate:.1%}, 平均耗时{avg_time:.1f}ms, 当前超时{current_timeout}ms")
        
        # 修正：针对iframe/host_discovery已达到100%成功率的情况优化阈值
        if success_rate >= 0.99 and avg_time < 0.3 * current_timeout:
            # 连续成功率极高且响应快，更积极地减小超时提升速度
            new_timeout = max(int(avg_time * 1.15), 150)  # 115%余量，最小150ms
            if new_timeout < current_timeout:
                self.logger.info(f"🔽 iframe超时优化(减小): {current_timeout}ms -> {new_timeout}ms (成功率{success_rate:.1%})")
                return {"iframe_detection_timeout": new_timeout}
                
        elif success_rate < 0.95 or avg_time > 0.8 * current_timeout:
            # 成功率低于95%或接近超时，增大超时
            new_timeout = min(int(avg_time * 1.5), 2000)  # 150%余量，最大2000ms
            if new_timeout > current_timeout:
                self.logger.info(f"🔺 iframe超时优化(增大): {current_timeout}ms -> {new_timeout}ms (成功率{success_rate:.1%})")
                return {"iframe_detection_timeout": new_timeout}
        
        self.logger.debug("iframe超时配置已是最优，无需调整")
        return {}
    
    def _optimize_host_timeout(self, records) -> Dict[str, int]:
        """
        优化宿主发现超时 - 逻辑类似iframe
        
        Args:
            records: 宿主发现的监控记录列表
            
        Returns:
            Dict[str, int]: 优化后的配置变更
        """
        if len(records) < 10:
            self.logger.debug("宿主发现记录数量不足，跳过优化")
            return {}
            
        durations = [r.duration_ms for r in records if r.success]
        if not durations:
            self.logger.warning("宿主发现无成功记录，跳过优化")
            return {}
            
        success_rate = sum(1 for r in records if r.success) / len(records)
        avg_time = statistics.mean(durations)
        current_timeout = self.config.host_discovery_timeout
        
        self.logger.debug(f"宿主发现分析: 成功率{success_rate:.1%}, 平均耗时{avg_time:.1f}ms, 当前超时{current_timeout}ms")
        
        if success_rate >= 0.99 and avg_time < 0.3 * current_timeout:
            new_timeout = max(int(avg_time * 1.15), 150)
            if new_timeout < current_timeout:
                self.logger.info(f"🔽 宿主超时优化(减小): {current_timeout}ms -> {new_timeout}ms (成功率{success_rate:.1%})")
                return {"host_discovery_timeout": new_timeout}
                
        elif success_rate < 0.95 or avg_time > 0.8 * current_timeout:
            new_timeout = min(int(avg_time * 1.5), 2000)
            if new_timeout > current_timeout:
                self.logger.info(f"🔺 宿主超时优化(增大): {current_timeout}ms -> {new_timeout}ms (成功率{success_rate:.1%})")
                return {"host_discovery_timeout": new_timeout}
        
        self.logger.debug("宿主发现超时配置已是最优，无需调整")
        return {}
    
    def _apply_optimizations(self, optimizations: Dict[str, int]):
        """
        应用配置优化
        
        Args:
            optimizations: 要应用的配置变更字典
        """
        for key, value in optimizations.items():
            old_value = getattr(self.config, key, 'unknown')
            setattr(self.config, key, value)
            self.logger.info(f"⚙️ 配置已更新: {key} = {old_value} -> {value}")
            
    def _log_config_changes(self, changes: List[Tuple[str, int]]):
        """
        记录配置变更到独立CSV
        
        Args:
            changes: 配置变更列表，每项为(参数名, 新值)的元组
        """
        try:
            # 确保目录存在
            self.config_history_file.parent.mkdir(exist_ok=True)
            
            file_exists = self.config_history_file.exists()
            
            with open(self.config_history_file, 'a', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                
                # 写入表头 (仅首次)
                if not file_exists:
                    writer.writerow(['timestamp', 'parameter', 'old_value', 'new_value', 'reason'])
                
                # 记录每个配置变更
                current_time = time.time()
                for param, new_value in changes:
                    # 获取旧值用于记录
                    old_value = getattr(self.config, param, 'unknown')
                    
                    writer.writerow([
                        current_time,
                        param,
                        old_value if isinstance(old_value, int) else str(old_value),
                        new_value,
                        'auto_healing'
                    ])
                    
            self.logger.info(f"📝 配置变更已记录到: {self.config_history_file}")
                    
        except Exception as e:
            self.logger.error(f"❌ 配置历史记录失败: {e}")
    
    def _check_element_location_failures(self, records) -> Optional[str]:
        """
        检测 element_location 连续失败并建议添加新模板
        
        Args:
            records: element_location 的监控记录列表
            
        Returns:
            Optional[str]: 如果检测到连续失败，返回警告信息；否则返回None
        """
        if len(records) < 5:
            return None
            
        # 检查最近的记录
        recent_records = records[-10:] if len(records) >= 10 else records
        
        # 计算失败率
        failed_count = sum(1 for r in recent_records if not r.success)
        failure_rate = failed_count / len(recent_records)
        
        # 检查连续失败
        consecutive_failures = 0
        for record in reversed(recent_records):
            if not record.success:
                consecutive_failures += 1
            else:
                break
        
        # 分析失败模式并建议解决方案
        if consecutive_failures >= 3:
            # 分析是否为特定页面（如passcode页面）的问题
            passcode_failures = 0
            for record in recent_records:
                if record.params and 'passcode' in str(record.params).lower():
                    if not record.success:
                        passcode_failures += 1
            
            if passcode_failures > 0:
                return f"连续{consecutive_failures}次失败，疑似passcode页面元素定位问题，建议添加新的选择器模板"
            else:
                return f"连续{consecutive_failures}次失败，整体失败率{failure_rate:.1%}，建议检查元素选择器配置"
        
        elif failure_rate > 0.5:
            return f"失败率过高({failure_rate:.1%})，建议优化元素定位策略"
        
        return None

    def get_healing_stats(self) -> Dict[str, any]:
        """
        获取自愈系统统计信息
        
        Returns:
            Dict[str, any]: 自愈统计数据
        """
        return {
            "last_run_timestamp": self._last_run,
            "time_since_last_run_seconds": time.time() - self._last_run,
            "current_config": {
                "iframe_detection_timeout": self.config.iframe_detection_timeout,
                "host_discovery_timeout": self.config.host_discovery_timeout
            },
            "monitor_records_count": len(self.monitor.records),
            "ready_for_healing": self.should_run_healing()
        }
    
    def force_healing_analysis(self):
        """强制执行自愈分析 (忽略时间和样本量限制)"""
        self.logger.warning("🔧 强制执行自愈分析...")
        
        # 临时保存上次运行时间
        original_last_run = self._last_run
        self._last_run = 0  # 重置以绕过时间检查
        
        try:
            self.analyze_and_optimize()
        finally:
            # 恢复真实的运行时间记录
            self._last_run = time.time()