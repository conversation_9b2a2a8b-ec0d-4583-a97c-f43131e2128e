#!/usr/bin/env python3
"""
反检测配置模块
基于MediaCrawler项目的最佳实践，提供全面的反检测配置选项
"""

import os
import random
from typing import List, Dict, Optional, Any
from dataclasses import dataclass, field


@dataclass
class AntiDetectionConfig:
    """反检测配置类"""
    
    # ========== 核心功能开关 ==========
    enable_cdp_mode: bool = True                    # 启用CDP模式
    enable_stealth_script: bool = True              # 启用隐身脚本
    enable_fingerprint_masking: bool = True         # 启用指纹伪装
    enable_behavior_simulation: bool = True         # 启用行为模拟
    enable_user_agent_rotation: bool = True         # 启用UA轮换
    
    # ========== CDP模式配置 ==========
    cdp_debug_port: int = 9222                      # CDP调试端口
    cdp_headless: bool = False                      # CDP模式是否无头
    auto_close_browser: bool = True                 # 程序结束时自动关闭浏览器
    browser_launch_timeout: int = 30                # 浏览器启动超时时间(秒)
    custom_browser_path: Optional[str] = None       # 自定义浏览器路径
    
    # ========== 行为模拟配置 ==========
    typing_wpm_range: tuple = (45, 50)             # 打字速度范围(WPM)
    typing_error_rate: float = 0.02                # 打字错误率
    mouse_movement_easing: bool = True              # 鼠标移动缓动
    human_delays_enabled: bool = True               # 人类延迟模拟
    
    # ========== 浏览器配置 ==========
    viewport_width: int = 1920                      # 视口宽度
    viewport_height: int = 1080                     # 视口高度
    default_timeout: int = 30000                    # 默认超时时间(毫秒)
    
    # ========== User Agent池 ==========
    desktop_user_agents: List[str] = field(default_factory=lambda: [
        # Chrome 120+ (最新版本)
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        
        # Chrome 119
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        
        # Chrome 118
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        
        # Chrome 117
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36",
        
        # Chrome 116
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.0.0 Safari/537.36",
        
        # Chrome 115
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        
        # Chrome 114
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36",
        
        # Chrome 113
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/113.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/113.0.0.0 Safari/537.36",
        
        # Chrome 112
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/112.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/112.0.0.0 Safari/537.36",
        
        # Chrome 111
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        
        # Chrome 110
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36",
    ])
    
    mobile_user_agents: List[str] = field(default_factory=lambda: [
        # iPhone
        "Mozilla/5.0 (iPhone; CPU iPhone OS 17_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Mobile/15E148 Safari/604.1",
        "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1",
        "Mozilla/5.0 (iPhone; CPU iPhone OS 16_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.5 Mobile/15E148 Safari/604.1",
        
        # iPad
        "Mozilla/5.0 (iPad; CPU OS 17_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Mobile/15E148 Safari/604.1",
        "Mozilla/5.0 (iPad; CPU OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1",
        
        # Android Chrome
        "Mozilla/5.0 (Linux; Android 14; Pixel 8) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36",
        "Mozilla/5.0 (Linux; Android 13; SM-S918B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36",
        "Mozilla/5.0 (Linux; Android 12; SM-G998B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36",
        
        # Samsung Browser
        "Mozilla/5.0 (Linux; Android 13; SAMSUNG SM-S918B) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/23.0 Chrome/********* Mobile Safari/537.36",
    ])
    
    # ========== 浏览器启动参数（基于MediaCrawler最佳实践）==========
    browser_args: List[str] = field(default_factory=lambda: [
        # 核心反检测参数
        "--disable-blink-features=AutomationControlled",    # 最重要：禁用自动化控制标记
        "--disable-infobars",                               # 禁用信息栏
        "--no-first-run",                                   # 禁用首次运行向导
        "--no-default-browser-check",                       # 禁用默认浏览器检查
        
        # WebDriver检测绕过
        "--disable-web-security",                           # 禁用Web安全检查
        "--disable-features=VizDisplayCompositor",          # 禁用显示合成器
        "--disable-features=TranslateUI",                   # 禁用翻译UI
        
        # 性能和稳定性优化
        "--disable-background-timer-throttling",            # 禁用后台定时器节流
        "--disable-backgrounding-occluded-windows",         # 禁用被遮挡窗口的后台处理
        "--disable-renderer-backgrounding",                 # 禁用渲染器后台处理
        "--disable-ipc-flooding-protection",                # 禁用IPC洪水保护
        "--disable-hang-monitor",                            # 禁用挂起监视器
        "--disable-prompt-on-repost",                       # 禁用重新发布提示
        "--disable-sync",                                    # 禁用同步
        
        # 资源和内存优化
        "--disable-dev-shm-usage",                          # 禁用/dev/shm使用（避免共享内存问题）
        "--no-sandbox",                                      # 禁用沙箱（在某些环境中需要）
        "--disable-gpu-process-crash-limit",                # 禁用GPU进程崩溃限制
        
        # 其他反检测参数
        "--disable-extensions-except",                       # 禁用扩展
        "--disable-plugins-discovery",                       # 禁用插件发现
        "--disable-preconnect",                             # 禁用预连接
        "--disable-print-preview",                          # 禁用打印预览
        
        # 网络优化
        "--aggressive-cache-discard",                       # 激进缓存丢弃
        "--disable-background-networking",                  # 禁用后台网络
        
        # 安全相关
        "--disable-client-side-phishing-detection",         # 禁用客户端钓鱼检测
        "--disable-component-update",                       # 禁用组件更新
        "--disable-default-apps",                           # 禁用默认应用
    ])
    
    @classmethod
    def from_env(cls) -> 'AntiDetectionConfig':
        """从环境变量创建配置"""
        return cls(
            enable_cdp_mode=os.getenv('ANTI_DETECTION_CDP_MODE', 'true').lower() == 'true',
            enable_stealth_script=os.getenv('ANTI_DETECTION_STEALTH_SCRIPT', 'true').lower() == 'true',
            enable_fingerprint_masking=os.getenv('ANTI_DETECTION_FINGERPRINT_MASKING', 'true').lower() == 'true',
            enable_behavior_simulation=os.getenv('ANTI_DETECTION_BEHAVIOR_SIMULATION', 'true').lower() == 'true',
            enable_user_agent_rotation=os.getenv('ANTI_DETECTION_UA_ROTATION', 'true').lower() == 'true',
            
            cdp_debug_port=int(os.getenv('ANTI_DETECTION_CDP_PORT', '9222')),
            cdp_headless=os.getenv('ANTI_DETECTION_CDP_HEADLESS', 'false').lower() == 'true',
            auto_close_browser=os.getenv('ANTI_DETECTION_AUTO_CLOSE_BROWSER', 'true').lower() == 'true',
            browser_launch_timeout=int(os.getenv('ANTI_DETECTION_BROWSER_TIMEOUT', '30')),
            custom_browser_path=os.getenv('ANTI_DETECTION_CUSTOM_BROWSER_PATH'),
            
            typing_error_rate=float(os.getenv('ANTI_DETECTION_TYPING_ERROR_RATE', '0.02')),
        )
    
    def get_random_user_agent(self, mobile: bool = False) -> str:
        """
        获取随机User Agent
        
        Args:
            mobile: 是否获取移动端UA
            
        Returns:
            随机选择的User Agent字符串
        """
        if mobile:
            return random.choice(self.mobile_user_agents)
        else:
            return random.choice(self.desktop_user_agents)
    
    def get_browser_args(self) -> List[str]:
        """获取浏览器启动参数"""
        return self.browser_args.copy()
    
    def get_typing_config(self) -> Dict[str, Any]:
        """获取打字配置"""
        return {
            'wpm_range': self.typing_wpm_range,
            'error_rate': self.typing_error_rate,
            'human_delays_enabled': self.human_delays_enabled
        }
    
    def get_viewport_config(self) -> Dict[str, int]:
        """获取视口配置"""
        return {
            'width': self.viewport_width,
            'height': self.viewport_height
        }
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'core_features': {
                'enable_cdp_mode': self.enable_cdp_mode,
                'enable_stealth_script': self.enable_stealth_script,
                'enable_fingerprint_masking': self.enable_fingerprint_masking,
                'enable_behavior_simulation': self.enable_behavior_simulation,
                'enable_user_agent_rotation': self.enable_user_agent_rotation,
            },
            'cdp_config': {
                'debug_port': self.cdp_debug_port,
                'headless': self.cdp_headless,
                'auto_close_browser': self.auto_close_browser,
                'launch_timeout': self.browser_launch_timeout,
                'custom_browser_path': self.custom_browser_path,
            },
            'behavior_config': {
                'typing_wpm_range': self.typing_wpm_range,
                'typing_error_rate': self.typing_error_rate,
                'mouse_movement_easing': self.mouse_movement_easing,
                'human_delays_enabled': self.human_delays_enabled,
            },
            'browser_config': {
                'viewport_width': self.viewport_width,
                'viewport_height': self.viewport_height,
                'default_timeout': self.default_timeout,
                'args_count': len(self.browser_args),
                'desktop_ua_count': len(self.desktop_user_agents),
                'mobile_ua_count': len(self.mobile_user_agents),
            }
        }
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"AntiDetectionConfig(CDP={self.enable_cdp_mode}, Stealth={self.enable_stealth_script}, Fingerprint={self.enable_fingerprint_masking}, Behavior={self.enable_behavior_simulation})"


# 预定义配置预设
class AntiDetectionPresets:
    """反检测配置预设"""
    
    @staticmethod
    def maximum_stealth() -> AntiDetectionConfig:
        """最大隐身配置 - 启用所有反检测功能"""
        return AntiDetectionConfig(
            enable_cdp_mode=True,
            enable_stealth_script=True,
            enable_fingerprint_masking=True,
            enable_behavior_simulation=True,
            enable_user_agent_rotation=True,
            cdp_headless=False,  # 非无头模式更隐蔽
            typing_error_rate=0.03,  # 稍高的错误率更真实
        )
    
    @staticmethod
    def balanced_performance() -> AntiDetectionConfig:
        """平衡性能配置 - 性能和隐身的平衡"""
        return AntiDetectionConfig(
            enable_cdp_mode=False,  # 使用标准模式提升性能
            enable_stealth_script=True,
            enable_fingerprint_masking=True,
            enable_behavior_simulation=True,
            enable_user_agent_rotation=True,
            cdp_headless=True,
            typing_error_rate=0.02,
        )
    
    @staticmethod
    def minimal_footprint() -> AntiDetectionConfig:
        """最小足迹配置 - 仅启用核心反检测功能"""
        return AntiDetectionConfig(
            enable_cdp_mode=False,
            enable_stealth_script=True,
            enable_fingerprint_masking=False,
            enable_behavior_simulation=False,
            enable_user_agent_rotation=True,
            cdp_headless=True,
            typing_error_rate=0.01,
        )
    
    @staticmethod
    def debugging_mode() -> AntiDetectionConfig:
        """调试模式配置 - 方便开发和调试"""
        return AntiDetectionConfig(
            enable_cdp_mode=False,
            enable_stealth_script=False,
            enable_fingerprint_masking=False,
            enable_behavior_simulation=False,
            enable_user_agent_rotation=False,
            cdp_headless=False,
            typing_error_rate=0.0,
        )