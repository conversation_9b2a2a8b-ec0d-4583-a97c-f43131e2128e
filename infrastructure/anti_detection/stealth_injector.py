#!/usr/bin/env python3
"""
隐身脚本注入器 - Stealth Script Injector
基于MediaCrawler项目的stealth.min.js，提供全面的反检测脚本注入功能
"""

import os
import logging
from typing import Union, Optional
from pathlib import Path
from playwright.sync_api import Page as SyncPage, BrowserContext as SyncBrowserContext
from playwright.async_api import Page as AsyncPage, BrowserContext as AsyncBrowserContext


class StealthInjector:
    """
    隐身脚本注入器
    负责注入反检测脚本，包括stealth.min.js和自定义的指纹伪装脚本
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.stealth_script_path = self._get_stealth_script_path()
        self.stealth_script_content: Optional[str] = None
        self._load_stealth_script()
    
    def _get_stealth_script_path(self) -> str:
        """获取stealth.min.js脚本路径"""
        current_dir = Path(__file__).parent
        stealth_path = current_dir / "stealth" / "stealth.min.js"
        
        if not stealth_path.exists():
            # 尝试其他可能的路径
            alternative_paths = [
                current_dir / "stealth.min.js",
                Path(__file__).parent.parent.parent / "libs" / "stealth.min.js",
                Path.cwd() / "libs" / "stealth.min.js"
            ]
            
            for path in alternative_paths:
                if path.exists():
                    return str(path)
                    
            self.logger.warning("⚠️ 未找到stealth.min.js文件，将使用内置反检测脚本")
            return ""
        
        return str(stealth_path)
    
    def _load_stealth_script(self) -> None:
        """加载stealth.min.js脚本内容"""
        if self.stealth_script_path and os.path.exists(self.stealth_script_path):
            try:
                with open(self.stealth_script_path, 'r', encoding='utf-8') as f:
                    self.stealth_script_content = f.read()
                self.logger.info(f"✅ 成功加载stealth脚本: {self.stealth_script_path}")
            except Exception as e:
                self.logger.error(f"❌ 加载stealth脚本失败: {e}")
                self.stealth_script_content = None
        else:
            self.logger.info("📝 使用内置反检测脚本")
            self.stealth_script_content = None
    
    async def inject_stealth_script(self, page: Union[SyncPage, AsyncPage]) -> bool:
        """
        注入stealth反检测脚本
        
        Args:
            page: 页面实例
            
        Returns:
            注入是否成功
        """
        try:
            # 检测是否为异步页面
            is_async = hasattr(page, 'evaluate') and hasattr(page.evaluate, '__await__')
            
            if self.stealth_script_content:
                # 使用MediaCrawler的stealth.min.js
                if is_async:
                    await page.evaluate(self.stealth_script_content)
                else:
                    page.evaluate(self.stealth_script_content)
                    
                self.logger.info("✅ MediaCrawler stealth脚本注入成功")
            else:
                # 使用内置反检测脚本
                await self._inject_builtin_stealth_script(page, is_async)
                self.logger.info("✅ 内置反检测脚本注入成功")
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ stealth脚本注入失败: {e}")
            return False
    
    async def _inject_builtin_stealth_script(self, page: Union[SyncPage, AsyncPage], is_async: bool) -> None:
        """注入内置的反检测脚本"""
        builtin_script = """
        (() => {
            // 隐藏webdriver属性
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
                configurable: true
            });
            
            // 移除webdriver标记
            delete navigator.__proto__.webdriver;
            
            // 伪装chrome对象
            if (!window.chrome) {
                window.chrome = {};
            }
            
            if (!window.chrome.runtime) {
                window.chrome.runtime = {
                    onConnect: undefined,
                    onMessage: undefined,
                    onInstalled: undefined
                };
            }
            
            if (!window.chrome.app) {
                window.chrome.app = {
                    isInstalled: false,
                    getDetails: function() { return null; },
                    runningState: function() { return 'cannot_run'; }
                };
            }
            
            if (!window.chrome.csi) {
                window.chrome.csi = function() {
                    return {
                        onloadT: Date.now(),
                        startE: Date.now(),
                        tran: 15
                    };
                };
            }
            
            if (!window.chrome.loadTimes) {
                window.chrome.loadTimes = function() {
                    return {
                        requestTime: Date.now() / 1000,
                        startLoadTime: Date.now() / 1000,
                        commitLoadTime: Date.now() / 1000,
                        finishDocumentLoadTime: Date.now() / 1000,
                        finishLoadTime: Date.now() / 1000,
                        firstPaintTime: Date.now() / 1000,
                        firstPaintAfterLoadTime: 0,
                        navigationType: 'Other'
                    };
                };
            }
            
            // 修复Function.prototype.toString
            const originalToString = Function.prototype.toString;
            Function.prototype.toString = function() {
                if (this === window.chrome.csi) {
                    return 'function csi() { [native code] }';
                }
                if (this === window.chrome.loadTimes) {
                    return 'function loadTimes() { [native code] }';
                }
                return originalToString.apply(this, arguments);
            };
            
            // 伪装permissions API
            if (navigator.permissions && navigator.permissions.query) {
                const originalQuery = navigator.permissions.query;
                navigator.permissions.query = function(parameters) {
                    return parameters.name === 'notifications' 
                        ? Promise.resolve({ state: Notification.permission })
                        : originalQuery.apply(navigator.permissions, arguments);
                };
            }
            
            // 隐藏automation相关属性
            if (window.outerHeight === 0 && window.outerWidth === 0) {
                Object.defineProperty(window, 'outerHeight', {
                    get: () => window.innerHeight,
                    configurable: true
                });
                Object.defineProperty(window, 'outerWidth', {
                    get: () => window.innerWidth,
                    configurable: true
                });
            }
            
            // 伪装plugins
            if (navigator.plugins.length === 0) {
                Object.defineProperty(navigator, 'plugins', {
                    get: () => ({
                        0: {
                            0: { type: "application/x-google-chrome-pdf", suffixes: "pdf", description: "Portable Document Format", enabledPlugin: null },
                            1: { type: "application/pdf", suffixes: "pdf", description: "Portable Document Format", enabledPlugin: null },
                            description: "Portable Document Format",
                            filename: "internal-pdf-viewer",
                            length: 2,
                            name: "Chrome PDF Plugin"
                        },
                        1: {
                            0: { type: "application/x-nacl", suffixes: "", description: "Native Client Executable", enabledPlugin: null },
                            1: { type: "application/x-pnacl", suffixes: "", description: "Portable Native Client Executable", enabledPlugin: null },
                            description: "",
                            filename: "internal-nacl-plugin",
                            length: 2,
                            name: "Native Client"
                        },
                        length: 2
                    }),
                    configurable: true
                });
            }
            
            // 移除可能的自动化检测特征
            ['__webdriver_evaluate', '__selenium_evaluate', '__webdriver_script_function', '__webdriver_script_func', '__webdriver_script_fn', '__fxdriver_evaluate', '__driver_unwrapped', '__webdriver_unwrapped', '__driver_evaluate', '__selenium_unwrapped', '__fxdriver_unwrapped'].forEach(prop => {
                delete window[prop];
            });
            
            // 伪装语言和平台信息
            if (navigator.languages.length === 0) {
                Object.defineProperty(navigator, 'languages', {
                    get: () => ['en-US', 'en'],
                    configurable: true
                });
            }
            
            console.log('🛡️ 内置反检测脚本已加载');
        })();
        """
        
        if is_async:
            await page.evaluate(builtin_script)
        else:
            page.evaluate(builtin_script)
    
    async def apply_fingerprint_masking(self, page: Union[SyncPage, AsyncPage]) -> bool:
        """
        应用指纹伪装
        
        Args:
            page: 页面实例
            
        Returns:
            应用是否成功
        """
        try:
            # 检测是否为异步页面
            is_async = hasattr(page, 'evaluate') and hasattr(page.evaluate, '__await__')
            
            # Canvas指纹随机化
            await self._apply_canvas_fingerprint_masking(page, is_async)
            
            # WebGL指纹伪装
            await self._apply_webgl_fingerprint_masking(page, is_async)
            
            # Audio指纹随机化
            await self._apply_audio_fingerprint_masking(page, is_async)
            
            # 硬件信息伪装
            await self._apply_hardware_fingerprint_masking(page, is_async)
            
            self.logger.info("✅ 指纹伪装应用成功")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 指纹伪装应用失败: {e}")
            return False
    
    async def _apply_canvas_fingerprint_masking(self, page: Union[SyncPage, AsyncPage], is_async: bool) -> None:
        """应用Canvas指纹伪装"""
        canvas_script = """
        (() => {
            const originalToDataURL = HTMLCanvasElement.prototype.toDataURL;
            const originalGetImageData = CanvasRenderingContext2D.prototype.getImageData;
            
            // 生成会话一致的噪音值
            const noiseValue = Math.random() * 0.001;
            
            HTMLCanvasElement.prototype.toDataURL = function(...args) {
                const context = this.getContext('2d');
                if (context) {
                    const imageData = context.getImageData(0, 0, this.width, this.height);
                    for (let i = 0; i < imageData.data.length; i += 4) {
                        imageData.data[i] += Math.floor(noiseValue * 255);
                        imageData.data[i + 1] += Math.floor(noiseValue * 255); 
                        imageData.data[i + 2] += Math.floor(noiseValue * 255);
                    }
                    context.putImageData(imageData, 0, 0);
                }
                return originalToDataURL.apply(this, args);
            };
            
            CanvasRenderingContext2D.prototype.getImageData = function(...args) {
                const imageData = originalGetImageData.apply(this, args);
                for (let i = 0; i < imageData.data.length; i += 4) {
                    imageData.data[i] += Math.floor(noiseValue * 255);
                    imageData.data[i + 1] += Math.floor(noiseValue * 255);
                    imageData.data[i + 2] += Math.floor(noiseValue * 255);
                }
                return imageData;
            };
        })();
        """
        
        if is_async:
            await page.evaluate(canvas_script)
        else:
            page.evaluate(canvas_script)
    
    async def _apply_webgl_fingerprint_masking(self, page: Union[SyncPage, AsyncPage], is_async: bool) -> None:
        """应用WebGL指纹伪装"""
        webgl_script = """
        (() => {
            const originalGetParameter = WebGLRenderingContext.prototype.getParameter;
            
            WebGLRenderingContext.prototype.getParameter = function(parameter) {
                // 伪装GPU信息
                if (parameter === 37445) { // VENDOR
                    return 'Intel Inc.';
                }
                if (parameter === 37446) { // RENDERER  
                    return 'Intel(R) UHD Graphics 630';
                }
                if (parameter === 7938) { // VERSION
                    return 'WebGL 1.0 (OpenGL ES 2.0 Chromium)';
                }
                if (parameter === 35724) { // SHADING_LANGUAGE_VERSION
                    return 'WebGL GLSL ES 1.0 (OpenGL ES GLSL ES 1.0 Chromium)';
                }
                return originalGetParameter.apply(this, arguments);
            };
            
            // 同样处理WebGL2
            if (window.WebGL2RenderingContext) {
                WebGL2RenderingContext.prototype.getParameter = WebGLRenderingContext.prototype.getParameter;
            }
        })();
        """
        
        if is_async:
            await page.evaluate(webgl_script)
        else:
            page.evaluate(webgl_script)
    
    async def _apply_audio_fingerprint_masking(self, page: Union[SyncPage, AsyncPage], is_async: bool) -> None:
        """应用Audio指纹伪装"""
        audio_script = """
        (() => {
            const originalCreateOscillator = AudioContext.prototype.createOscillator;
            
            AudioContext.prototype.createOscillator = function() {
                const oscillator = originalCreateOscillator.apply(this, arguments);
                const originalStart = oscillator.start;
                
                oscillator.start = function(...args) {
                    // 添加微小的频率变化
                    const noise = (Math.random() - 0.5) * 0.0001;
                    this.frequency.value += noise;
                    return originalStart.apply(this, args);
                };
                
                return oscillator;
            };
        })();
        """
        
        if is_async:
            await page.evaluate(audio_script)
        else:
            page.evaluate(audio_script)
    
    async def _apply_hardware_fingerprint_masking(self, page: Union[SyncPage, AsyncPage], is_async: bool) -> None:
        """应用硬件信息指纹伪装"""
        hardware_script = """
        (() => {
            // 伪装硬件并发数
            Object.defineProperty(navigator, 'hardwareConcurrency', {
                get: () => 8,
                configurable: true
            });
            
            // 伪装设备内存
            if (navigator.deviceMemory !== undefined) {
                Object.defineProperty(navigator, 'deviceMemory', {
                    get: () => 8,
                    configurable: true
                });
            }
            
            // 伪装屏幕信息
            Object.defineProperty(screen, 'width', {
                get: () => 1920,
                configurable: true
            });
            Object.defineProperty(screen, 'height', {
                get: () => 1080,
                configurable: true
            });
            Object.defineProperty(screen, 'availWidth', {
                get: () => 1920,
                configurable: true
            });
            Object.defineProperty(screen, 'availHeight', {
                get: () => 1040,
                configurable: true
            });
            Object.defineProperty(screen, 'colorDepth', {
                get: () => 24,
                configurable: true
            });
            Object.defineProperty(screen, 'pixelDepth', {
                get: () => 24,
                configurable: true
            });
            
            // 伪装设备像素比
            Object.defineProperty(window, 'devicePixelRatio', {
                get: () => 1,
                configurable: true
            });
        })();
        """
        
        if is_async:
            await page.evaluate(hardware_script)
        else:
            page.evaluate(hardware_script)
    
    async def inject_to_context(self, context: Union[SyncBrowserContext, AsyncBrowserContext]) -> bool:
        """
        向浏览器上下文注入反检测脚本
        
        Args:
            context: 浏览器上下文
            
        Returns:
            注入是否成功
        """
        try:
            # 检测是否为异步上下文
            is_async = hasattr(context, 'add_init_script') and hasattr(context.add_init_script, '__await__')
            
            if self.stealth_script_content:
                # 注入stealth.min.js
                if is_async:
                    await context.add_init_script(self.stealth_script_content)
                else:
                    context.add_init_script(self.stealth_script_content)
                    
                self.logger.info("✅ MediaCrawler stealth脚本已注入到上下文")
            else:
                # 注入内置脚本
                await self._inject_builtin_context_script(context, is_async)
                self.logger.info("✅ 内置反检测脚本已注入到上下文")
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 上下文脚本注入失败: {e}")
            return False
    
    async def _inject_builtin_context_script(self, context: Union[SyncBrowserContext, AsyncBrowserContext], is_async: bool) -> None:
        """向上下文注入内置反检测脚本"""
        context_script = """
        // 在所有页面加载前注入反检测代码
        Object.defineProperty(navigator, 'webdriver', {
            get: () => undefined,
            configurable: true
        });
        
        window.chrome = window.chrome || {
            runtime: {},
            app: { isInstalled: false },
            csi: function() { return {}; },
            loadTimes: function() { return {}; }
        };
        
        // 隐藏自动化特征
        ['__webdriver_evaluate', '__selenium_evaluate', '__webdriver_script_function'].forEach(prop => {
            delete window[prop];
        });
        """
        
        if is_async:
            await context.add_init_script(context_script)
        else:
            context.add_init_script(context_script)
    
    def get_stealth_status(self) -> dict:
        """获取隐身脚本状态"""
        return {
            "stealth_script_path": self.stealth_script_path,
            "stealth_script_loaded": self.stealth_script_content is not None,
            "script_size": len(self.stealth_script_content) if self.stealth_script_content else 0
        }