#!/usr/bin/env python3
"""
CDP管理器 - Chrome DevTools Protocol浏览器控制
基于MediaCrawler项目的CDP实现，提供真实浏览器控制能力
"""

import os
import platform
import subprocess
import time
import socket
import asyncio
import httpx
import logging
from typing import Optional, List, Tuple, Dict, Any, Union
from pathlib import Path
from playwright.sync_api import BrowserContext as Sync<PERSON>rowserContext
from playwright.async_api import BrowserContext as AsyncBrowserContext, Browser


class BrowserLauncher:
    """
    浏览器启动器，用于检测和启动用户的Chrome/Edge浏览器
    支持Windows、macOS和Linux系统
    """
    
    def __init__(self):
        self.system = platform.system()
        self.browser_process: Optional[subprocess.Popen] = None
        self.debug_port: Optional[int] = None
        self.logger = logging.getLogger(__name__)
        
    def detect_browser_paths(self) -> List[str]:
        """
        检测系统中可用的浏览器路径
        返回按优先级排序的浏览器路径列表
        """
        paths = []
        
        if self.system == "Windows":
            # Windows下的常见Chrome/Edge安装路径
            possible_paths = [
                # Chrome路径
                os.path.expandvars(r"%PROGRAMFILES%\Google\Chrome\Application\chrome.exe"),
                os.path.expandvars(r"%PROGRAMFILES(X86)%\Google\Chrome\Application\chrome.exe"),
                os.path.expandvars(r"%LOCALAPPDATA%\Google\Chrome\Application\chrome.exe"),
                # Edge路径
                os.path.expandvars(r"%PROGRAMFILES%\Microsoft\Edge\Application\msedge.exe"),
                os.path.expandvars(r"%PROGRAMFILES(X86)%\Microsoft\Edge\Application\msedge.exe"),
                # Chrome Beta/Dev/Canary
                os.path.expandvars(r"%LOCALAPPDATA%\Google\Chrome Beta\Application\chrome.exe"),
                os.path.expandvars(r"%LOCALAPPDATA%\Google\Chrome Dev\Application\chrome.exe"),
                os.path.expandvars(r"%LOCALAPPDATA%\Google\Chrome SxS\Application\chrome.exe"),
            ]
        elif self.system == "Darwin":  # macOS
            # macOS下的常见Chrome/Edge安装路径
            possible_paths = [
                # Chrome路径
                "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome",
                "/Applications/Google Chrome Beta.app/Contents/MacOS/Google Chrome Beta",
                "/Applications/Google Chrome Dev.app/Contents/MacOS/Google Chrome Dev",
                "/Applications/Google Chrome Canary.app/Contents/MacOS/Google Chrome Canary",
                # Edge路径
                "/Applications/Microsoft Edge.app/Contents/MacOS/Microsoft Edge",
                "/Applications/Microsoft Edge Beta.app/Contents/MacOS/Microsoft Edge Beta",
                "/Applications/Microsoft Edge Dev.app/Contents/MacOS/Microsoft Edge Dev",
                "/Applications/Microsoft Edge Canary.app/Contents/MacOS/Microsoft Edge Canary",
            ]
        else:
            # Linux等其他系统
            possible_paths = [
                "/usr/bin/google-chrome",
                "/usr/bin/google-chrome-stable",
                "/usr/bin/google-chrome-beta",
                "/usr/bin/google-chrome-unstable",
                "/usr/bin/chromium-browser",
                "/usr/bin/chromium",
                "/snap/bin/chromium",
                "/usr/bin/microsoft-edge",
                "/usr/bin/microsoft-edge-stable",
                "/usr/bin/microsoft-edge-beta",
                "/usr/bin/microsoft-edge-dev",
            ]
        
        # 检查路径是否存在且可执行
        for path in possible_paths:
            if os.path.isfile(path) and os.access(path, os.X_OK):
                paths.append(path)
                
        return paths
    
    def find_available_port(self, start_port: int = 9222) -> int:
        """查找可用的端口"""
        port = start_port
        while port < start_port + 100:  # 最多尝试100个端口
            try:
                with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                    s.bind(('localhost', port))
                    return port
            except OSError:
                port += 1
        
        raise RuntimeError(f"无法找到可用的端口，已尝试 {start_port} 到 {port-1}")
    
    def launch_browser(self, browser_path: str, debug_port: int, headless: bool = False, 
                      user_data_dir: Optional[str] = None, additional_args: List[str] = None) -> subprocess.Popen:
        """
        启动浏览器进程
        
        Args:
            browser_path: 浏览器可执行文件路径
            debug_port: 调试端口
            headless: 是否无头模式
            user_data_dir: 用户数据目录
            additional_args: 额外的启动参数
        """
        # 基本启动参数
        args = [
            browser_path,
            f"--remote-debugging-port={debug_port}",
            "--remote-debugging-address=0.0.0.0",  # 允许远程访问
            "--no-first-run",
            "--no-default-browser-check",
            "--disable-background-timer-throttling",
            "--disable-backgrounding-occluded-windows",
            "--disable-renderer-backgrounding",
            "--disable-features=TranslateUI",
            "--disable-ipc-flooding-protection",
            "--disable-hang-monitor",
            "--disable-prompt-on-repost",
            "--disable-sync",
            "--disable-web-security",  # 可能有助于某些网站的访问
            "--disable-features=VizDisplayCompositor",
            "--disable-dev-shm-usage",  # 避免共享内存问题
            "--no-sandbox",  # 在CDP模式下关闭沙箱
            
            # 关键反检测参数
            "--disable-blink-features=AutomationControlled",
            "--disable-infobars",
        ]
        
        # 无头模式
        if headless:
            args.extend([
                "--headless",
                "--disable-gpu",
            ])
        
        # 用户数据目录
        if user_data_dir:
            args.append(f"--user-data-dir={user_data_dir}")
        
        # 额外参数
        if additional_args:
            args.extend(additional_args)
        
        self.logger.info(f"🚀 启动浏览器: {browser_path}")
        self.logger.info(f"🔧 调试端口: {debug_port}")
        self.logger.info(f"👁️ 无头模式: {headless}")
        
        try:
            # 在Windows上，使用CREATE_NEW_PROCESS_GROUP避免Ctrl+C影响子进程
            if self.system == "Windows":
                process = subprocess.Popen(
                    args,
                    stdout=subprocess.DEVNULL,
                    stderr=subprocess.DEVNULL,
                    creationflags=subprocess.CREATE_NEW_PROCESS_GROUP
                )
            else:
                process = subprocess.Popen(
                    args,
                    stdout=subprocess.DEVNULL,
                    stderr=subprocess.DEVNULL,
                    preexec_fn=os.setsid  # 创建新的进程组
                )
            
            self.browser_process = process
            self.debug_port = debug_port
            return process
            
        except Exception as e:
            self.logger.error(f"❌ 启动浏览器失败: {e}")
            raise
    
    def wait_for_browser_ready(self, debug_port: int, timeout: int = 30) -> bool:
        """等待浏览器准备就绪"""
        self.logger.info(f"⏳ 等待浏览器在端口 {debug_port} 上准备就绪...")
        
        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                    s.settimeout(1)
                    result = s.connect_ex(('localhost', debug_port))
                    if result == 0:
                        self.logger.info(f"✅ 浏览器已在端口 {debug_port} 上准备就绪")
                        return True
            except Exception:
                pass
            
            time.sleep(0.5)
        
        self.logger.error(f"❌ 浏览器在 {timeout} 秒内未能准备就绪")
        return False
    
    def get_browser_info(self, browser_path: str) -> Tuple[str, str]:
        """获取浏览器信息（名称和版本）"""
        try:
            if "chrome" in browser_path.lower():
                name = "Google Chrome"
            elif "edge" in browser_path.lower() or "msedge" in browser_path.lower():
                name = "Microsoft Edge"
            elif "chromium" in browser_path.lower():
                name = "Chromium"
            else:
                name = "Unknown Browser"
            
            # 尝试获取版本信息
            try:
                result = subprocess.run([browser_path, "--version"], 
                                      capture_output=True, text=True, timeout=5)
                version = result.stdout.strip() if result.stdout else "Unknown Version"
            except:
                version = "Unknown Version"
            
            return name, version
            
        except Exception:
            return "Unknown Browser", "Unknown Version"
    
    def cleanup(self):
        """清理资源，关闭浏览器进程"""
        if self.browser_process:
            try:
                self.logger.info("🧹 正在关闭浏览器进程...")
                
                if self.system == "Windows":
                    # Windows下使用taskkill强制终止进程树
                    subprocess.run(["taskkill", "/F", "/T", "/PID", str(self.browser_process.pid)], 
                                 capture_output=True)
                else:
                    # Unix系统下终止进程组
                    os.killpg(os.getpgid(self.browser_process.pid), 9)
                
                self.browser_process = None
                self.logger.info("✅ 浏览器进程已关闭")
                
            except Exception as e:
                self.logger.warning(f"⚠️ 关闭浏览器进程时出错: {e}")


class CDPManager:
    """
    CDP浏览器管理器，负责启动和管理通过CDP连接的浏览器
    """

    def __init__(self):
        self.launcher = BrowserLauncher()
        self.browser: Optional[Browser] = None
        self.browser_context: Optional[Union[SyncBrowserContext, AsyncBrowserContext]] = None
        self.debug_port: Optional[int] = None
        self.logger = logging.getLogger(__name__)

    async def create_cdp_context(
        self,
        playwright=None,
        debug_port: int = 9222,
        headless: bool = False,
        user_agent: Optional[str] = None,
        proxy: Optional[Dict] = None,
        user_data_dir: Optional[str] = None,
        custom_browser_path: Optional[str] = None,
        additional_args: Optional[List[str]] = None
    ) -> Union[SyncBrowserContext, AsyncBrowserContext]:
        """
        创建CDP浏览器上下文
        
        Args:
            playwright: Playwright实例
            debug_port: CDP调试端口
            headless: 是否无头模式
            user_agent: 自定义User Agent
            proxy: 代理配置
            user_data_dir: 用户数据目录
            custom_browser_path: 自定义浏览器路径
            additional_args: 额外启动参数
            
        Returns:
            配置好的浏览器上下文
        """
        try:
            # 1. 获取浏览器路径
            browser_path = await self._get_browser_path(custom_browser_path)

            # 2. 获取可用端口
            self.debug_port = self.launcher.find_available_port(debug_port)

            # 3. 启动浏览器
            await self._launch_browser(browser_path, headless, user_data_dir, additional_args)

            # 4. 通过CDP连接
            await self._connect_via_cdp(playwright)

            # 5. 创建浏览器上下文
            browser_context = await self._create_browser_context(user_agent, proxy)

            self.browser_context = browser_context
            return browser_context

        except Exception as e:
            self.logger.error(f"❌ CDP浏览器上下文创建失败: {e}")
            await self.cleanup()
            raise

    async def _get_browser_path(self, custom_path: Optional[str] = None) -> str:
        """获取浏览器路径"""
        # 优先使用用户自定义路径
        if custom_path and os.path.isfile(custom_path):
            self.logger.info(f"🎯 使用自定义浏览器路径: {custom_path}")
            return custom_path

        # 自动检测浏览器路径
        browser_paths = self.launcher.detect_browser_paths()

        if not browser_paths:
            raise RuntimeError(
                "❌ 未找到可用的浏览器。请确保已安装Chrome或Edge浏览器，"
                "或提供自定义浏览器路径。"
            )

        browser_path = browser_paths[0]  # 使用第一个找到的浏览器
        browser_name, browser_version = self.launcher.get_browser_info(browser_path)

        self.logger.info(f"🔍 检测到浏览器: {browser_name} ({browser_version})")
        self.logger.info(f"📁 浏览器路径: {browser_path}")

        return browser_path

    async def _launch_browser(self, browser_path: str, headless: bool, 
                            user_data_dir: Optional[str] = None, 
                            additional_args: Optional[List[str]] = None):
        """启动浏览器进程"""
        # 设置用户数据目录
        if user_data_dir is None:
            user_data_dir = os.path.join(
                os.getcwd(),
                "browser_data",
                f"cdp_hulu_{int(time.time())}"
            )
            
        os.makedirs(user_data_dir, exist_ok=True)
        self.logger.info(f"📂 用户数据目录: {user_data_dir}")

        # 启动浏览器
        self.launcher.launch_browser(
            browser_path=browser_path,
            debug_port=self.debug_port,
            headless=headless,
            user_data_dir=user_data_dir,
            additional_args=additional_args
        )

        # 等待浏览器准备就绪
        if not self.launcher.wait_for_browser_ready(self.debug_port, timeout=30):
            raise RuntimeError(f"❌ 浏览器在 30 秒内未能启动")

        # 额外等待确保CDP服务完全启动
        await asyncio.sleep(2)

        # 测试CDP连接
        if not await self._test_cdp_connection(self.debug_port):
            self.logger.warning("⚠️ CDP连接测试失败，但将继续尝试连接")

    async def _test_cdp_connection(self, debug_port: int) -> bool:
        """测试CDP连接是否可用"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(f"http://localhost:{debug_port}/json", timeout=5)
                if response.status_code == 200:
                    self.logger.info(f"✅ CDP端口 {debug_port} 可访问")
                    return True
                else:
                    self.logger.warning(f"⚠️ CDP端口 {debug_port} 返回状态码: {response.status_code}")
                    return False
        except Exception as e:
            self.logger.warning(f"⚠️ CDP连接测试失败: {e}")
            return False

    async def _get_browser_websocket_url(self, debug_port: int) -> str:
        """获取浏览器的WebSocket连接URL"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"http://localhost:{debug_port}/json/version", timeout=10
                )
                if response.status_code == 200:
                    data = response.json()
                    ws_url = data.get("webSocketDebuggerUrl")
                    if ws_url:
                        self.logger.info(f"🔗 获取到浏览器WebSocket URL: {ws_url}")
                        return ws_url
                    else:
                        raise RuntimeError("❌ 未找到webSocketDebuggerUrl")
                else:
                    raise RuntimeError(f"❌ HTTP {response.status_code}: {response.text}")
        except Exception as e:
            self.logger.error(f"❌ 获取WebSocket URL失败: {e}")
            raise

    async def _connect_via_cdp(self, playwright):
        """通过CDP连接到浏览器"""
        try:
            # 获取正确的WebSocket URL
            ws_url = await self._get_browser_websocket_url(self.debug_port)
            self.logger.info(f"🔌 正在通过CDP连接到浏览器: {ws_url}")

            # 使用Playwright的connectOverCDP方法连接
            self.browser = await playwright.chromium.connect_over_cdp(ws_url)

            if self.browser.is_connected():
                self.logger.info("✅ 成功连接到浏览器")
                self.logger.info(f"📊 浏览器上下文数量: {len(self.browser.contexts)}")
            else:
                raise RuntimeError("❌ CDP连接失败")

        except Exception as e:
            self.logger.error(f"❌ CDP连接失败: {e}")
            raise

    async def _create_browser_context(
        self, user_agent: Optional[str] = None, proxy: Optional[Dict] = None
    ) -> Union[SyncBrowserContext, AsyncBrowserContext]:
        """创建或获取浏览器上下文"""
        if not self.browser:
            raise RuntimeError("❌ 浏览器未连接")

        # 获取现有上下文或创建新的上下文
        contexts = self.browser.contexts

        if contexts:
            # 使用现有的第一个上下文
            browser_context = contexts[0]
            self.logger.info("🔄 使用现有的浏览器上下文")
        else:
            # 创建新的上下文
            context_options = {
                "viewport": {"width": 1920, "height": 1080},
                "accept_downloads": True,
            }

            # 设置用户代理
            if user_agent:
                context_options["user_agent"] = user_agent
                self.logger.info(f"🎭 设置用户代理: {user_agent}")

            # 注意：CDP模式下代理设置可能不生效，因为浏览器已经启动
            if proxy:
                self.logger.warning(
                    "⚠️ 警告: CDP模式下代理设置可能不生效，"
                    "建议在浏览器启动前配置系统代理或浏览器代理扩展"
                )

            browser_context = await self.browser.new_context(**context_options)
            self.logger.info("🆕 创建新的浏览器上下文")

        return browser_context

    def is_connected(self) -> bool:
        """检查是否已连接到浏览器"""
        return self.browser is not None and self.browser.is_connected()

    async def get_browser_info(self) -> Dict[str, Any]:
        """获取浏览器信息"""
        if not self.browser:
            return {}

        try:
            version = self.browser.version
            contexts_count = len(self.browser.contexts)

            return {
                "version": version,
                "contexts_count": contexts_count,
                "debug_port": self.debug_port,
                "is_connected": self.is_connected(),
            }
        except Exception as e:
            self.logger.warning(f"⚠️ 获取浏览器信息失败: {e}")
            return {}

    async def cleanup(self):
        """清理资源"""
        try:
            # 关闭浏览器上下文
            if self.browser_context:
                try:
                    await self.browser_context.close()
                    self.browser_context = None
                    self.logger.info("🔒 浏览器上下文已关闭")
                except:
                    pass

            # 断开浏览器连接
            if self.browser:
                try:
                    await self.browser.close()
                    self.browser = None
                    self.logger.info("🔌 浏览器连接已断开")
                except:
                    pass

            # 关闭浏览器进程
            self.launcher.cleanup()

        except Exception as e:
            self.logger.error(f"❌ CDP管理器清理失败: {e}")