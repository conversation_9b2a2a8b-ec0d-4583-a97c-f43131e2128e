#!/usr/bin/env python3
"""
错误处理和降级机制 - Fallback Handler
基于MediaCrawler项目的错误处理策略，提供智能降级和重试机制
"""

import logging
import time
import random
from typing import Dict, Any, Optional, Callable, List, Tuple
from enum import Enum
from dataclasses import dataclass, field


class DetectionType(Enum):
    """检测类型枚举"""
    WEBDRIVER = "webdriver"
    FINGERPRINT = "fingerprint" 
    BEHAVIORAL = "behavioral"
    CAPTCHA = "captcha"
    RATE_LIMIT = "rate_limit"
    NETWORK = "network"
    UNKNOWN = "unknown"


class FallbackStrategy(Enum):
    """降级策略枚举"""
    RETRY_WITH_DELAY = "retry_with_delay"
    SWITCH_TO_CDP = "switch_to_cdp"
    RANDOMIZE_FINGERPRINT = "randomize_fingerprint"
    SLOW_DOWN_BEHAVIOR = "slow_down_behavior"
    CHANGE_USER_AGENT = "change_user_agent"
    SWITCH_TO_HEADLESS = "switch_to_headless"
    DISABLE_FEATURES = "disable_features"
    ABORT_OPERATION = "abort_operation"


@dataclass
class FallbackConfig:
    """降级配置"""
    max_retries: int = 3
    base_delay: float = 1.0
    max_delay: float = 30.0
    exponential_backoff: bool = True
    jitter: bool = True
    enabled_strategies: List[FallbackStrategy] = field(default_factory=lambda: [
        FallbackStrategy.RETRY_WITH_DELAY,
        FallbackStrategy.SWITCH_TO_CDP,
        FallbackStrategy.RANDOMIZE_FINGERPRINT,
        FallbackStrategy.SLOW_DOWN_BEHAVIOR,
        FallbackStrategy.CHANGE_USER_AGENT
    ])


@dataclass
class DetectionEvent:
    """检测事件"""
    detection_type: DetectionType
    message: str
    timestamp: float
    context: Dict[str, Any] = field(default_factory=dict)
    retry_count: int = 0
    severity: str = "medium"  # low, medium, high, critical


class FallbackHandler:
    """
    错误处理和降级机制处理器
    提供智能的错误恢复和策略调整功能
    """
    
    def __init__(self, config: Optional[FallbackConfig] = None):
        """
        初始化降级处理器
        
        Args:
            config: 降级配置，如果不提供则使用默认配置
        """
        self.logger = logging.getLogger(__name__)
        self.config = config or FallbackConfig()
        
        # 记录检测事件历史
        self.detection_history: List[DetectionEvent] = []
        
        # 成功率统计
        self.success_stats = {
            "total_attempts": 0,
            "successful_attempts": 0,
            "failed_attempts": 0,
            "fallback_successes": 0
        }
        
        # 策略效果统计
        self.strategy_stats = {strategy: {"attempts": 0, "successes": 0} 
                              for strategy in FallbackStrategy}
        
        self.logger.info("🛡️ 降级处理器初始化完成")
    
    async def handle_detection(
        self, 
        detection_type: DetectionType,
        message: str,
        context: Optional[Dict[str, Any]] = None,
        operation: Optional[Callable] = None,
        *args, **kwargs
    ) -> Tuple[bool, Any]:
        """
        处理检测事件并执行降级策略
        
        Args:
            detection_type: 检测类型
            message: 错误消息
            context: 上下文信息
            operation: 要重试的操作
            *args, **kwargs: 操作参数
            
        Returns:
            (是否成功, 操作结果)
        """
        event = DetectionEvent(
            detection_type=detection_type,
            message=message,
            timestamp=time.time(),
            context=context or {},
            severity=self._assess_severity(detection_type, message)
        )
        
        self.detection_history.append(event)
        self.success_stats["total_attempts"] += 1
        
        self.logger.warning(f"⚠️ 检测事件: {detection_type.value} - {message}")
        
        # 根据检测类型选择策略
        strategies = self._select_strategies(detection_type, event.severity)
        
        # 执行降级策略
        for strategy in strategies:
            if strategy not in self.config.enabled_strategies:
                continue
                
            try:
                success = await self._execute_strategy(strategy, event, operation, *args, **kwargs)
                
                self.strategy_stats[strategy]["attempts"] += 1
                
                if success:
                    self.strategy_stats[strategy]["successes"] += 1
                    self.success_stats["successful_attempts"] += 1
                    self.success_stats["fallback_successes"] += 1
                    
                    self.logger.info(f"✅ 降级策略成功: {strategy.value}")
                    return True, success
                    
            except Exception as e:
                self.logger.error(f"❌ 降级策略执行失败 {strategy.value}: {e}")
                continue
        
        # 所有策略都失败
        self.success_stats["failed_attempts"] += 1
        self.logger.error(f"❌ 所有降级策略都失败了")
        return False, None
    
    def _assess_severity(self, detection_type: DetectionType, message: str) -> str:
        """评估检测事件的严重程度"""
        # 关键词匹配
        critical_keywords = ["blocked", "banned", "suspended", "terminated"]
        high_keywords = ["detected", "captcha", "verification", "challenge"]
        medium_keywords = ["timeout", "rate limit", "slow down"]
        
        message_lower = message.lower()
        
        if any(keyword in message_lower for keyword in critical_keywords):
            return "critical"
        elif any(keyword in message_lower for keyword in high_keywords):
            return "high"
        elif any(keyword in message_lower for keyword in medium_keywords):
            return "medium"
        else:
            return "low"
    
    def _select_strategies(self, detection_type: DetectionType, severity: str) -> List[FallbackStrategy]:
        """根据检测类型和严重程度选择降级策略"""
        strategies = []
        
        # 根据检测类型选择主要策略
        if detection_type == DetectionType.WEBDRIVER:
            strategies.extend([
                FallbackStrategy.SWITCH_TO_CDP,
                FallbackStrategy.RANDOMIZE_FINGERPRINT,
                FallbackStrategy.CHANGE_USER_AGENT
            ])
        elif detection_type == DetectionType.FINGERPRINT:
            strategies.extend([
                FallbackStrategy.RANDOMIZE_FINGERPRINT,
                FallbackStrategy.SWITCH_TO_CDP,
                FallbackStrategy.CHANGE_USER_AGENT
            ])
        elif detection_type == DetectionType.BEHAVIORAL:
            strategies.extend([
                FallbackStrategy.SLOW_DOWN_BEHAVIOR,
                FallbackStrategy.RETRY_WITH_DELAY,
                FallbackStrategy.RANDOMIZE_FINGERPRINT
            ])
        elif detection_type == DetectionType.RATE_LIMIT:
            strategies.extend([
                FallbackStrategy.RETRY_WITH_DELAY,
                FallbackStrategy.SLOW_DOWN_BEHAVIOR
            ])
        elif detection_type == DetectionType.CAPTCHA:
            strategies.extend([
                FallbackStrategy.SLOW_DOWN_BEHAVIOR,
                FallbackStrategy.RETRY_WITH_DELAY,
                FallbackStrategy.RANDOMIZE_FINGERPRINT
            ])
        else:
            # 通用策略
            strategies.extend([
                FallbackStrategy.RETRY_WITH_DELAY,
                FallbackStrategy.CHANGE_USER_AGENT,
                FallbackStrategy.RANDOMIZE_FINGERPRINT
            ])
        
        # 根据严重程度调整策略
        if severity in ["critical", "high"]:
            # 严重情况下使用更激进的策略
            strategies.insert(0, FallbackStrategy.SWITCH_TO_CDP)
            strategies.append(FallbackStrategy.SWITCH_TO_HEADLESS)
        elif severity == "low":
            # 轻微情况下只使用温和策略
            strategies = [s for s in strategies if s in [
                FallbackStrategy.RETRY_WITH_DELAY,
                FallbackStrategy.SLOW_DOWN_BEHAVIOR
            ]]
        
        # 最后的选择
        if severity == "critical":
            strategies.append(FallbackStrategy.ABORT_OPERATION)
        
        return strategies
    
    async def _execute_strategy(
        self, 
        strategy: FallbackStrategy, 
        event: DetectionEvent,
        operation: Optional[Callable] = None,
        *args, **kwargs
    ) -> Any:
        """执行具体的降级策略"""
        self.logger.info(f"🔄 执行降级策略: {strategy.value}")
        
        if strategy == FallbackStrategy.RETRY_WITH_DELAY:
            return await self._retry_with_delay(event, operation, *args, **kwargs)
            
        elif strategy == FallbackStrategy.SWITCH_TO_CDP:
            return await self._switch_to_cdp(event, operation, *args, **kwargs)
            
        elif strategy == FallbackStrategy.RANDOMIZE_FINGERPRINT:
            return await self._randomize_fingerprint(event, operation, *args, **kwargs)
            
        elif strategy == FallbackStrategy.SLOW_DOWN_BEHAVIOR:
            return await self._slow_down_behavior(event, operation, *args, **kwargs)
            
        elif strategy == FallbackStrategy.CHANGE_USER_AGENT:
            return await self._change_user_agent(event, operation, *args, **kwargs)
            
        elif strategy == FallbackStrategy.SWITCH_TO_HEADLESS:
            return await self._switch_to_headless(event, operation, *args, **kwargs)
            
        elif strategy == FallbackStrategy.DISABLE_FEATURES:
            return await self._disable_features(event, operation, *args, **kwargs)
            
        elif strategy == FallbackStrategy.ABORT_OPERATION:
            self.logger.warning("🛑 执行中止操作策略")
            return False
            
        else:
            self.logger.warning(f"⚠️ 未知的降级策略: {strategy}")
            return False
    
    async def _retry_with_delay(self, event: DetectionEvent, operation: Optional[Callable], *args, **kwargs) -> Any:
        """带延迟重试策略"""
        if not operation:
            return False
            
        event.retry_count += 1
        
        if event.retry_count > self.config.max_retries:
            self.logger.warning(f"⚠️ 重试次数超过限制 ({self.config.max_retries})")
            return False
        
        # 计算延迟时间
        delay = self.config.base_delay
        
        if self.config.exponential_backoff:
            delay *= (2 ** (event.retry_count - 1))
        
        # 添加抖动
        if self.config.jitter:
            delay += random.uniform(0, delay * 0.1)
        
        # 限制最大延迟
        delay = min(delay, self.config.max_delay)
        
        self.logger.info(f"⏰ 延迟 {delay:.2f}s 后重试 (第 {event.retry_count} 次)")
        time.sleep(delay)
        
        try:
            # 检测是否为异步操作
            if hasattr(operation, '__await__'):
                result = await operation(*args, **kwargs)
            else:
                result = operation(*args, **kwargs)
            return result
        except Exception as e:
            self.logger.warning(f"⚠️ 重试失败: {e}")
            return False
    
    async def _switch_to_cdp(self, event: DetectionEvent, operation: Optional[Callable], *args, **kwargs) -> Any:
        """切换到CDP模式策略"""
        self.logger.info("🔄 尝试切换到CDP模式")
        
        # 这里需要与反检测服务集成，切换到CDP模式
        # 由于需要重新创建浏览器上下文，这个策略比较复杂
        # 在实际实现中，需要回调到上级服务进行模式切换
        
        event.context["fallback_to_cdp"] = True
        return True  # 返回True表示建议切换，具体实现由调用方处理
    
    async def _randomize_fingerprint(self, event: DetectionEvent, operation: Optional[Callable], *args, **kwargs) -> Any:
        """随机化指纹策略"""
        self.logger.info("🎭 随机化浏览器指纹")
        
        event.context["randomize_fingerprint"] = True
        event.context["new_fingerprint_profile"] = {
            "user_agent": "random",
            "viewport": "random", 
            "timezone": "random",
            "language": "random"
        }
        
        if operation:
            try:
                if hasattr(operation, '__await__'):
                    result = await operation(*args, **kwargs)
                else:
                    result = operation(*args, **kwargs)
                return result
            except Exception as e:
                self.logger.warning(f"⚠️ 指纹随机化后操作失败: {e}")
                return False
        
        return True
    
    async def _slow_down_behavior(self, event: DetectionEvent, operation: Optional[Callable], *args, **kwargs) -> Any:
        """放慢行为速度策略"""
        self.logger.info("🐌 放慢操作速度")
        
        event.context["behavior_multiplier"] = random.uniform(1.5, 3.0)
        event.context["additional_delays"] = True
        
        # 立即增加一个较长的等待
        delay = random.uniform(2.0, 5.0)
        self.logger.info(f"⏰ 额外等待 {delay:.2f}s")
        time.sleep(delay)
        
        if operation:
            try:
                if hasattr(operation, '__await__'):
                    result = await operation(*args, **kwargs)
                else:
                    result = operation(*args, **kwargs)
                return result
            except Exception as e:
                self.logger.warning(f"⚠️ 减速后操作失败: {e}")
                return False
        
        return True
    
    async def _change_user_agent(self, event: DetectionEvent, operation: Optional[Callable], *args, **kwargs) -> Any:
        """更换User Agent策略"""
        self.logger.info("🎭 更换User Agent")
        
        event.context["change_user_agent"] = True
        event.context["new_user_agent"] = "random"
        
        if operation:
            try:
                if hasattr(operation, '__await__'):
                    result = await operation(*args, **kwargs)
                else:
                    result = operation(*args, **kwargs)
                return result
            except Exception as e:
                self.logger.warning(f"⚠️ 更换UA后操作失败: {e}")
                return False
        
        return True
    
    async def _switch_to_headless(self, event: DetectionEvent, operation: Optional[Callable], *args, **kwargs) -> Any:
        """切换到无头模式策略"""
        self.logger.info("👻 切换到无头模式")
        
        event.context["switch_to_headless"] = True
        return True
    
    async def _disable_features(self, event: DetectionEvent, operation: Optional[Callable], *args, **kwargs) -> Any:
        """禁用某些功能策略"""
        self.logger.info("🚫 禁用某些浏览器功能")
        
        event.context["disable_features"] = [
            "images", "css", "javascript", "plugins"
        ]
        return True
    
    def get_success_rate(self) -> float:
        """获取成功率"""
        if self.success_stats["total_attempts"] == 0:
            return 0.0
        return self.success_stats["successful_attempts"] / self.success_stats["total_attempts"]
    
    def get_fallback_rate(self) -> float:
        """获取降级成功率"""
        if self.success_stats["successful_attempts"] == 0:
            return 0.0
        return self.success_stats["fallback_successes"] / self.success_stats["successful_attempts"]
    
    def get_strategy_effectiveness(self) -> Dict[str, float]:
        """获取各策略的有效性"""
        effectiveness = {}
        for strategy, stats in self.strategy_stats.items():
            if stats["attempts"] > 0:
                effectiveness[strategy.value] = stats["successes"] / stats["attempts"]
            else:
                effectiveness[strategy.value] = 0.0
        return effectiveness
    
    def get_detection_pattern(self) -> Dict[str, Any]:
        """分析检测模式"""
        if not self.detection_history:
            return {"total": 0, "patterns": {}}
        
        patterns = {}
        
        # 按类型统计
        type_counts = {}
        for event in self.detection_history:
            type_name = event.detection_type.value
            type_counts[type_name] = type_counts.get(type_name, 0) + 1
        
        # 按严重程度统计
        severity_counts = {}
        for event in self.detection_history:
            severity_counts[event.severity] = severity_counts.get(event.severity, 0) + 1
        
        # 时间分布
        recent_events = [e for e in self.detection_history if time.time() - e.timestamp < 3600]  # 最近1小时
        
        return {
            "total": len(self.detection_history),
            "by_type": type_counts,
            "by_severity": severity_counts,
            "recent_count": len(recent_events),
            "success_rate": self.get_success_rate(),
            "fallback_rate": self.get_fallback_rate()
        }
    
    def reset_stats(self) -> None:
        """重置统计数据"""
        self.detection_history.clear()
        self.success_stats = {
            "total_attempts": 0,
            "successful_attempts": 0,
            "failed_attempts": 0,
            "fallback_successes": 0
        }
        self.strategy_stats = {strategy: {"attempts": 0, "successes": 0} 
                              for strategy in FallbackStrategy}
        self.logger.info("📊 统计数据已重置")


# 全局降级处理器实例
_global_fallback_handler: Optional[FallbackHandler] = None


def get_fallback_handler(config: Optional[FallbackConfig] = None) -> FallbackHandler:
    """
    获取全局降级处理器实例（单例模式）
    
    Args:
        config: 配置对象，仅在首次调用时生效
        
    Returns:
        降级处理器实例
    """
    global _global_fallback_handler
    
    if _global_fallback_handler is None:
        _global_fallback_handler = FallbackHandler(config)
    
    return _global_fallback_handler


def reset_fallback_handler() -> None:
    """重置全局降级处理器实例"""
    global _global_fallback_handler
    _global_fallback_handler = None