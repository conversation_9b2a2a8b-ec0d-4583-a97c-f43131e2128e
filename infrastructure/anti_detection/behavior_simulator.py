#!/usr/bin/env python3
"""
增强行为模拟器 - Enhanced Behavior Simulator
基于MediaCrawler项目的人类行为模拟技术，集成缓动函数和高级轨迹生成
"""

import random
import time
import math
import logging
import numpy as np
from typing import Tuple, List, Dict, Optional, Union
from playwright.sync_api import Page as SyncPage
from playwright.async_api import Page as AsyncPage


class EasingFunctions:
    """
    缓动函数库 - 基于MediaCrawler项目的easing.py
    提供各种缓动函数用于自然的鼠标移动轨迹
    """
    
    @staticmethod
    def ease_out_quad(t: float) -> float:
        """二次方缓出"""
        return 1 - (1 - t) * (1 - t)
    
    @staticmethod
    def ease_out_cubic(t: float) -> float:
        """三次方缓出"""
        return 1 - pow(1 - t, 3)
    
    @staticmethod
    def ease_out_quart(t: float) -> float:
        """四次方缓出"""
        return 1 - pow(1 - t, 4)
    
    @staticmethod
    def ease_out_bounce(t: float) -> float:
        """弹跳缓出"""
        if t < 1 / 2.75:
            return 7.5625 * t * t
        elif t < 2 / 2.75:
            t -= 1.5 / 2.75
            return 7.5625 * t * t + 0.75
        elif t < 2.5 / 2.75:
            t -= 2.25 / 2.75
            return 7.5625 * t * t + 0.9375
        else:
            t -= 2.625 / 2.75
            return 7.5625 * t * t + 0.984375
    
    @staticmethod
    def ease_out_elastic(t: float) -> float:
        """弹性缓出"""
        if t == 0:
            return 0
        if t == 1:
            return 1
        
        c4 = (2 * math.pi) / 3
        return pow(2, -10 * t) * math.sin((t * 10 - 0.75) * c4) + 1
    
    @staticmethod
    def ease_in_out_sine(t: float) -> float:
        """正弦缓进缓出"""
        return -(math.cos(math.pi * t) - 1) / 2
    
    @staticmethod
    def ease_in_out_circ(t: float) -> float:
        """圆形缓进缓出"""
        if t < 0.5:
            return (1 - math.sqrt(1 - pow(2 * t, 2))) / 2
        else:
            return (math.sqrt(1 - pow(-2 * t + 2, 2)) + 1) / 2


class MouseTrajectoryGenerator:
    """
    鼠标轨迹生成器
    基于MediaCrawler的slider_util.py，生成自然的鼠标移动轨迹
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.easing = EasingFunctions()
    
    def generate_bezier_trajectory(
        self, 
        start: Tuple[int, int], 
        end: Tuple[int, int], 
        duration: float = 1.0,
        control_points: int = 2
    ) -> List[Tuple[int, int, float]]:
        """
        生成贝塞尔曲线轨迹
        
        Args:
            start: 起始点 (x, y)
            end: 结束点 (x, y)
            duration: 移动持续时间（秒）
            control_points: 控制点数量
            
        Returns:
            轨迹点列表 [(x, y, timestamp), ...]
        """
        # 生成控制点
        control_x = np.linspace(start[0], end[0], control_points + 2)
        control_y = np.linspace(start[1], end[1], control_points + 2)
        
        # 添加随机偏移使路径更自然
        for i in range(1, len(control_x) - 1):
            # 偏移量基于距离的20%
            max_offset = min(50, abs(end[0] - start[0]) * 0.2)
            control_x[i] += random.uniform(-max_offset, max_offset)
            control_y[i] += random.uniform(-max_offset, max_offset)
        
        # 生成轨迹点
        num_points = max(20, int(duration * 30))  # 30 FPS
        trajectory = []
        
        for i in range(num_points):
            t = i / (num_points - 1)
            
            # 应用缓动函数
            eased_t = self.easing.ease_out_quad(t)
            
            # 计算贝塞尔曲线点
            x = self._bezier_interpolate(control_x, eased_t)
            y = self._bezier_interpolate(control_y, eased_t)
            
            # 计算时间戳
            timestamp = t * duration
            
            trajectory.append((int(x), int(y), timestamp))
        
        return trajectory
    
    def generate_human_like_trajectory(
        self, 
        start: Tuple[int, int], 
        end: Tuple[int, int],
        movement_type: str = "normal"
    ) -> List[Tuple[int, int, float]]:
        """
        生成类人的鼠标移动轨迹
        
        Args:
            start: 起始点
            end: 结束点
            movement_type: 移动类型 ("normal", "precise", "quick", "hesitant")
            
        Returns:
            轨迹点列表
        """
        distance = math.sqrt((end[0] - start[0])**2 + (end[1] - start[1])**2)
        
        # 根据移动类型调整参数
        if movement_type == "precise":
            duration = 0.8 + distance / 500
            control_points = 1
            easing_func = self.easing.ease_in_out_sine
        elif movement_type == "quick":
            duration = 0.3 + distance / 1000
            control_points = 1
            easing_func = self.easing.ease_out_cubic
        elif movement_type == "hesitant":
            duration = 1.2 + distance / 300
            control_points = 3
            easing_func = self.easing.ease_out_elastic
        else:  # normal
            duration = 0.5 + distance / 800
            control_points = 2
            easing_func = self.easing.ease_out_quad
        
        # 生成基础贝塞尔轨迹
        trajectory = self.generate_bezier_trajectory(start, end, duration, control_points)
        
        # 应用选定的缓动函数
        enhanced_trajectory = []
        for i, (x, y, t) in enumerate(trajectory):
            progress = i / (len(trajectory) - 1)
            eased_progress = easing_func(progress)
            
            # 重新计算位置
            new_x = start[0] + (end[0] - start[0]) * eased_progress
            new_y = start[1] + (end[1] - start[1]) * eased_progress
            
            # 添加微小的自然抖动
            jitter_x = random.uniform(-1, 1)
            jitter_y = random.uniform(-1, 1)
            
            enhanced_trajectory.append((
                int(new_x + jitter_x), 
                int(new_y + jitter_y), 
                t
            ))
        
        return enhanced_trajectory
    
    def _bezier_interpolate(self, points: np.ndarray, t: float) -> float:
        """贝塞尔曲线插值计算"""
        n = len(points) - 1
        result = 0
        for i in range(n + 1):
            binomial = self._binomial_coefficient(n, i)
            result += binomial * (t ** i) * ((1 - t) ** (n - i)) * points[i]
        return result
    
    def _binomial_coefficient(self, n: int, k: int) -> int:
        """计算二项式系数"""
        if k > n - k:
            k = n - k
        result = 1
        for i in range(k):
            result = result * (n - i) // (i + 1)
        return result


class BehaviorSimulator:
    """
    增强的行为模拟器
    集成MediaCrawler的人类行为模拟技术和我们现有的45-50 WPM打字模拟
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.trajectory_generator = MouseTrajectoryGenerator()
        self.attached_pages = set()
        
        # 45-50 WPM打字配置
        self.wpm_range = (45, 50)
        self.base_wpm = random.uniform(*self.wpm_range)
        self.chars_per_second = (self.base_wpm * 5) / 60
        self.base_char_delay = 1.0 / self.chars_per_second
        
        # 键盘布局配置
        self._setup_keyboard_layout()
        
        self.logger.info(f"🎭 行为模拟器初始化: {self.base_wpm:.1f} WPM")
    
    def _setup_keyboard_layout(self):
        """设置QWERTY键盘布局和手指映射"""
        # QWERTY键盘布局
        self.keyboard_layout = {
            'row1': list('qwertyuiop'),
            'row2': list('asdfghjkl'),
            'row3': list('zxcvbnm'),
            'numbers': list('1234567890')
        }
        
        # 手指到键位的映射
        self.finger_mapping = {
            'left_pinky': ['q', 'a', 'z', '1'],
            'left_ring': ['w', 's', 'x', '2'],
            'left_middle': ['e', 'd', 'c', '3'],
            'left_index': ['r', 'f', 'v', 't', 'g', 'b', '4', '5'],
            'right_index': ['y', 'h', 'n', 'u', 'j', 'm', '6', '7'],
            'right_middle': ['i', 'k', '8'],
            'right_ring': ['o', 'l', '9'],
            'right_pinky': ['p', '0']
        }
        
        # 创建字符到手指的反向映射
        self.char_to_finger = {}
        for finger, chars in self.finger_mapping.items():
            for char in chars:
                self.char_to_finger[char.lower()] = finger
        
        # 相邻键位映射
        self.adjacent_keys = {
            'q': ['w', 'a'], 'w': ['q', 'e', 's'], 'e': ['w', 'r', 'd'],
            'r': ['e', 't', 'f'], 't': ['r', 'y', 'g'], 'y': ['t', 'u', 'h'],
            'u': ['y', 'i', 'j'], 'i': ['u', 'o', 'k'], 'o': ['i', 'p', 'l'],
            'p': ['o', 'l'],
            'a': ['q', 's', 'z'], 's': ['a', 'd', 'x', 'w'], 'd': ['s', 'f', 'c', 'e'],
            'f': ['d', 'g', 'v', 'r'], 'g': ['f', 'h', 'b', 't'], 'h': ['g', 'j', 'n', 'y'],
            'j': ['h', 'k', 'm', 'u'], 'k': ['j', 'l', 'i'], 'l': ['k', 'o', 'p'],
            'z': ['a', 'x'], 'x': ['z', 'c', 's'], 'c': ['x', 'v', 'd'],
            'v': ['c', 'b', 'f'], 'b': ['v', 'n', 'g'], 'n': ['b', 'm', 'h'],
            'm': ['n', 'j'],
            '1': ['2'], '2': ['1', '3'], '3': ['2', '4'], '4': ['3', '5'],
            '5': ['4', '6'], '6': ['5', '7'], '7': ['6', '8'], '8': ['7', '9'],
            '9': ['8', '0'], '0': ['9']
        }
    
    async def human_mouse_movement(
        self, 
        page: Union[SyncPage, AsyncPage], 
        selector: str,
        movement_type: str = "normal"
    ) -> None:
        """
        模拟人类鼠标移动到元素
        
        Args:
            page: Playwright页面实例
            selector: 目标元素选择器
            movement_type: 移动类型 ("normal", "precise", "quick", "hesitant")
        """
        try:
            # 检测是否为异步页面
            is_async = hasattr(page, 'evaluate') and hasattr(page.evaluate, '__await__')
            
            # 获取页面视口大小
            viewport = page.viewport_size
            if not viewport:
                viewport = {"width": 1920, "height": 1080}
            
            # 当前鼠标位置（假设在视口中心）
            current_pos = (viewport['width'] // 2, viewport['height'] // 2)
            
            # 获取目标元素位置
            element_box = page.locator(selector).bounding_box()
            if not element_box:
                self.logger.debug("❌ 无法获取元素位置，跳过鼠标移动模拟")
                return
                
            target_pos = (
                int(element_box['x'] + element_box['width'] // 2),
                int(element_box['y'] + element_box['height'] // 2)
            )
            
            # 生成人类化轨迹
            trajectory = self.trajectory_generator.generate_human_like_trajectory(
                current_pos, target_pos, movement_type
            )
            
            # 执行轨迹移动
            for i, (x, y, delay) in enumerate(trajectory):
                if i == 0:
                    continue
                    
                try:
                    # 移动鼠标
                    if is_async:
                        await page.mouse.move(x, y)
                    else:
                        page.mouse.move(x, y)
                    
                    # 随机停顿
                    if random.random() < 0.1:  # 10%概率停顿
                        time.sleep(random.uniform(0.01, 0.03))
                        
                except Exception as e:
                    self.logger.debug(f"鼠标移动中断: {e}")
                    break
            
            # 最终hover到目标元素
            page.locator(selector).hover()
            self.logger.debug(f"🎯 鼠标移动到 {selector} 完成 ({movement_type})")
            
        except Exception as e:
            self.logger.debug(f"❌ 鼠标移动模拟失败: {e}")
            # 降级到普通hover
            try:
                page.locator(selector).hover()
            except:
                pass
    
    async def human_typing_with_wpm(
        self, 
        page: Union[SyncPage, AsyncPage], 
        selector: str, 
        text: str, 
        simulate_errors: bool = True
    ) -> None:
        """
        基于45-50 WPM的真实打字模拟
        
        Args:
            page: Playwright页面实例
            selector: 输入元素选择器
            text: 要输入的文本
            simulate_errors: 是否模拟打字错误和修正
        """
        try:
            # 确保元素可见并聚焦
            locator = page.locator(selector)
            
            # 先模拟鼠标移动到输入框
            await self.human_mouse_movement(page, selector, "precise")
            
            # 点击聚焦
            locator.click()
            time.sleep(random.uniform(0.1, 0.3))
            
            if simulate_errors and len(text) > 3:
                await self._type_with_realistic_errors(page, selector, text)
            else:
                await self._type_normally_with_wpm(page, selector, text)
                
        except Exception as e:
            self.logger.warning(f"❌ WPM打字失败: {e}")
            # 降级到简单输入
            try:
                page.locator(selector).fill(text)
            except:
                self.logger.error("❌ 所有打字方法都失败了")
    
    async def _type_normally_with_wpm(self, page: Union[SyncPage, AsyncPage], selector: str, text: str) -> None:
        """正常45-50 WPM打字，无错误"""
        locator = page.locator(selector)
        
        for i, char in enumerate(text):
            try:
                # 使用Playwright的type方法逐字符输入
                locator.type(char)
                
                # 计算下一个字符的延迟
                next_char = text[i+1] if i+1 < len(text) else None
                delay = self._calculate_typing_delay(char, next_char)
                time.sleep(delay)
                
            except Exception as e:
                self.logger.debug(f"❌ 打字过程中出错 (字符 {i}): {e}")
                # 尝试一次性输入剩余文本
                remaining_text = text[i:]
                try:
                    locator.type(remaining_text)
                    break
                except:
                    self.logger.warning("❌ 打字失败，停止输入")
                    break
    
    async def _type_with_realistic_errors(self, page: Union[SyncPage, AsyncPage], selector: str, text: str) -> None:
        """模拟真实的打字错误和修正过程"""
        self.logger.info("🎭 模拟真实打字错误和修正过程...")
        locator = page.locator(selector)
        
        # 第一次输入：故意犯错并修正
        error_position = random.randint(2, max(3, len(text) - 2))
        self.logger.debug(f"计划在位置 {error_position} 犯错")
        
        # 正常输入到错误位置
        for i in range(error_position):
            char = text[i]
            locator.type(char)
            next_char = text[i+1] if i+1 < len(text) else None
            delay = self._calculate_typing_delay(char, next_char)
            time.sleep(delay)
        
        # 输入错误字符（相邻键位错误）
        correct_char = text[error_position]
        wrong_char = self._get_adjacent_key_error(correct_char)
        locator.type(wrong_char)
        self.logger.debug(f"输入错误字符: '{wrong_char}' (应为 '{correct_char}')")
        
        # 继续输入1-2个字符（模拟未立即发现错误）
        continue_count = random.randint(1, min(2, len(text) - error_position - 1))
        for i in range(continue_count):
            if error_position + 1 + i < len(text):
                char = text[error_position + 1 + i]
                locator.type(char)
                delay = self._calculate_typing_delay(char)
                time.sleep(delay)
        
        # 停顿（发现错误）
        discovery_pause = random.uniform(0.3, 0.8)
        time.sleep(discovery_pause)
        self.logger.debug(f"发现错误，停顿 {discovery_pause:.2f}s")
        
        # 删除错误部分（退格键）
        delete_count = continue_count + 1
        for i in range(delete_count):
            page.keyboard.press('Backspace')
            time.sleep(random.uniform(0.1, 0.2))
        
        # 重新输入正确内容（稍微更小心，速度稍慢）
        remaining_text = text[error_position:]
        for i, char in enumerate(remaining_text):
            locator.type(char)
            next_char = remaining_text[i+1] if i+1 < len(remaining_text) else None
            delay = self._calculate_typing_delay(char, next_char, error_correction=True)
            time.sleep(delay)
        
        # 第二次：清空重新输入（模拟检查密码后重新输入）
        time.sleep(random.uniform(0.5, 1.0))
        self.logger.debug("清空重新输入...")
        
        # 清空字段
        locator.fill("")
        time.sleep(random.uniform(0.2, 0.4))
        
        # 重新输入全部内容（更加小心，速度稍慢）
        for i, char in enumerate(text):
            locator.type(char)
            next_char = text[i+1] if i+1 < len(text) else None
            delay = self._calculate_typing_delay(char, next_char, error_correction=True)
            time.sleep(delay)
        
        self.logger.info("✅ 真实打字错误和修正模拟完成")
    
    def _calculate_typing_delay(self, current_char: str, next_char: str = None, 
                              error_correction: bool = False) -> float:
        """计算基于WPM和键盘布局的打字延迟"""
        # 基础延迟（45-50 WPM）
        delay = self.base_char_delay
        
        # 错误修正时打字更慢
        if error_correction:
            delay *= random.uniform(1.3, 1.8)
        
        # 键盘布局感知调整
        if next_char:
            current_finger = self.char_to_finger.get(current_char.lower())
            next_finger = self.char_to_finger.get(next_char.lower())
            
            if current_finger and next_finger:
                if current_finger == next_finger:
                    # 同一手指连续按键，需要更多时间
                    delay *= random.uniform(1.4, 1.8)
                elif self._are_adjacent_keys(current_char, next_char):
                    # 相邻键位，稍快一些
                    delay *= random.uniform(0.8, 1.0)
                elif self._are_same_hand(current_finger, next_finger):
                    # 同一只手，稍慢一些
                    delay *= random.uniform(1.1, 1.3)
                else:
                    # 不同手，可以并行，稍快一些
                    delay *= random.uniform(0.9, 1.1)
        
        # 特殊字符需要更多时间
        if current_char in '!@#$%^&*()_+{}[]|\\:";\'<>?,./ ':
            delay *= random.uniform(1.2, 1.6)
        
        # 大写字母需要Shift键
        if current_char.isupper():
            delay *= random.uniform(1.2, 1.5)
        
        # 数字相对较慢
        if current_char.isdigit():
            delay *= random.uniform(1.1, 1.4)
        
        # 添加自然的随机变化
        delay *= random.uniform(0.8, 1.2)
        
        # 确保延迟在合理范围内
        return max(0.05, min(delay, 0.8))
    
    def _are_adjacent_keys(self, char1: str, char2: str) -> bool:
        """判断两个字符是否在键盘上相邻"""
        char1_lower = char1.lower()
        char2_lower = char2.lower()
        return char2_lower in self.adjacent_keys.get(char1_lower, [])
    
    def _are_same_hand(self, finger1: str, finger2: str) -> bool:
        """判断两个手指是否在同一只手"""
        left_fingers = ['left_pinky', 'left_ring', 'left_middle', 'left_index']
        right_fingers = ['right_index', 'right_middle', 'right_ring', 'right_pinky']
        
        return (finger1 in left_fingers and finger2 in left_fingers) or \
               (finger1 in right_fingers and finger2 in right_fingers)
    
    def _get_adjacent_key_error(self, char: str) -> str:
        """获取键盘相邻位置的错误字符"""
        char_lower = char.lower()
        adjacent = self.adjacent_keys.get(char_lower, [char_lower])
        if adjacent:
            error_char = random.choice(adjacent)
            # 保持原字符的大小写
            return error_char.upper() if char.isupper() else error_char
        return char
    
    def attach_to_page(self, page: Union[SyncPage, AsyncPage]) -> None:
        """将行为模拟器附加到页面"""
        page_id = id(page)
        if page_id not in self.attached_pages:
            self.attached_pages.add(page_id)
            self.logger.debug(f"🔗 行为模拟器已附加到页面 {page_id}")
    
    def get_typing_stats(self) -> Dict[str, float]:
        """获取打字统计信息"""
        return {
            "base_wpm": self.base_wpm,
            "chars_per_second": self.chars_per_second,
            "base_char_delay": self.base_char_delay,
            "attached_pages": len(self.attached_pages)
        }