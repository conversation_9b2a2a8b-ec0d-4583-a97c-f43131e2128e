#!/usr/bin/env python3
"""
反检测服务 - 统一接口和服务编排
基于MediaCrawler项目的反检测技术，为Hulu自动化提供全面的反检测能力
"""

import logging
from typing import Optional, Dict, Any, Union
from playwright.sync_api import <PERSON>rowserContext as SyncBrowserContext, Page as SyncPage
from playwright.async_api import BrowserContext as AsyncBrowserContext, Page as AsyncPage

from .cdp_manager import CDPManager
from .stealth_injector import StealthInjector
from .behavior_simulator import BehaviorSimulator
from .config import AntiDetectionConfig


class AntiDetectionService:
    """
    反检测服务主类
    统一管理所有反检测功能，提供简洁的API接口
    """
    
    def __init__(self, config: Optional[AntiDetectionConfig] = None):
        """
        初始化反检测服务
        
        Args:
            config: 反检测配置，如果不提供则使用默认配置
        """
        self.logger = logging.getLogger(__name__)
        self.config = config or AntiDetectionConfig()
        
        # 初始化各个组件
        self.cdp_manager = CDPManager() if self.config.enable_cdp_mode else None
        self.stealth_injector = StealthInjector()
        self.behavior_simulator = BehaviorSimulator()
        
        self._is_active = False
        self.logger.info("🛡️ 反检测服务初始化完成")
    
    async def create_stealth_browser_context(
        self, 
        playwright=None,
        headless: bool = False,
        user_agent: Optional[str] = None,
        proxy: Optional[Dict] = None
    ) -> Union[SyncBrowserContext, AsyncBrowserContext]:
        """
        创建具有反检测能力的浏览器上下文
        
        Args:
            playwright: Playwright实例
            headless: 是否无头模式
            user_agent: 自定义User Agent
            proxy: 代理配置
            
        Returns:
            配置了反检测的浏览器上下文
        """
        try:
            if self.config.enable_cdp_mode and self.cdp_manager:
                self.logger.info("🔧 使用CDP模式创建浏览器上下文")
                context = await self.cdp_manager.create_cdp_context(
                    playwright=playwright,
                    headless=headless,
                    user_agent=user_agent,
                    proxy=proxy
                )
            else:
                self.logger.info("🔧 使用标准Playwright模式创建浏览器上下文")
                context = await self._create_standard_context(
                    playwright=playwright,
                    headless=headless,
                    user_agent=user_agent,
                    proxy=proxy
                )
            
            # 应用反检测配置
            await self._apply_anti_detection(context)
            self._is_active = True
            
            return context
            
        except Exception as e:
            self.logger.error(f"❌ 创建反检测浏览器上下文失败: {e}")
            raise
    
    async def enhance_page(self, page: Union[SyncPage, AsyncPage]) -> None:
        """
        为页面应用反检测增强
        
        Args:
            page: 需要增强的页面
        """
        try:
            # 注入隐身脚本
            if self.config.enable_stealth_script:
                await self.stealth_injector.inject_stealth_script(page)
            
            # 应用指纹伪装
            if self.config.enable_fingerprint_masking:
                await self.stealth_injector.apply_fingerprint_masking(page)
            
            # 应用行为增强
            if self.config.enable_behavior_simulation:
                self.behavior_simulator.attach_to_page(page)
            
            self.logger.info("✅ 页面反检测增强完成")
            
        except Exception as e:
            self.logger.warning(f"⚠️ 页面反检测增强部分失败: {e}")
    
    async def simulate_human_typing(
        self, 
        page: Union[SyncPage, AsyncPage], 
        selector: str, 
        text: str,
        simulate_errors: bool = True
    ) -> None:
        """
        模拟人类打字行为
        
        Args:
            page: 页面实例
            selector: 输入框选择器
            text: 要输入的文本
            simulate_errors: 是否模拟打字错误
        """
        await self.behavior_simulator.human_typing_with_wpm(
            page=page,
            selector=selector,
            text=text,
            simulate_errors=simulate_errors
        )
    
    async def simulate_human_mouse_movement(
        self, 
        page: Union[SyncPage, AsyncPage], 
        selector: str
    ) -> None:
        """
        模拟人类鼠标移动
        
        Args:
            page: 页面实例
            selector: 目标元素选择器
        """
        await self.behavior_simulator.human_mouse_movement(page, selector)
    
    def get_random_user_agent(self, mobile: bool = False) -> str:
        """
        获取随机User Agent
        
        Args:
            mobile: 是否获取移动端UA
            
        Returns:
            随机User Agent字符串
        """
        return self.config.get_random_user_agent(mobile)
    
    async def _create_standard_context(
        self,
        playwright=None,
        headless: bool = False,
        user_agent: Optional[str] = None,
        proxy: Optional[Dict] = None
    ) -> Union[SyncBrowserContext, AsyncBrowserContext]:
        """创建标准Playwright浏览器上下文"""
        if not playwright:
            raise ValueError("Playwright实例不能为空")
            
        # 构建浏览器启动参数
        launch_options = {
            "headless": headless,
            "args": self.config.get_browser_args()
        }
        
        # 构建上下文选项
        context_options = {
            "viewport": {"width": 1920, "height": 1080},
            "user_agent": user_agent or self.get_random_user_agent(),
        }
        
        if proxy:
            context_options["proxy"] = proxy
        
        # 启动浏览器和创建上下文
        browser = await playwright.chromium.launch(**launch_options)
        context = await browser.new_context(**context_options)
        
        return context
    
    async def _apply_anti_detection(self, context: Union[SyncBrowserContext, AsyncBrowserContext]) -> None:
        """对浏览器上下文应用反检测配置"""
        try:
            # 设置额外的浏览器特性
            if hasattr(context, 'add_init_script'):
                # 注入基础反检测脚本
                init_script = """
                // 隐藏webdriver属性
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => undefined,
                });
                
                // 伪装chrome对象
                window.chrome = {
                    runtime: {},
                    loadTimes: function() {},
                    csi: function() {},
                    app: {}
                };
                """
                await context.add_init_script(init_script)
                
            self.logger.debug("🔧 基础反检测配置已应用")
            
        except Exception as e:
            self.logger.warning(f"⚠️ 反检测配置应用失败: {e}")
    
    def is_active(self) -> bool:
        """检查反检测服务是否激活"""
        return self._is_active
    
    def get_config(self) -> AntiDetectionConfig:
        """获取当前配置"""
        return self.config
    
    def get_status(self) -> Dict[str, Any]:
        """获取服务状态"""
        return {
            "is_active": self._is_active,
            "cdp_mode_enabled": self.config.enable_cdp_mode,
            "stealth_script_enabled": self.config.enable_stealth_script,
            "fingerprint_masking_enabled": self.config.enable_fingerprint_masking,
            "behavior_simulation_enabled": self.config.enable_behavior_simulation,
            "cdp_manager_available": self.cdp_manager is not None,
        }
    
    async def cleanup(self) -> None:
        """清理资源"""
        try:
            if self.cdp_manager:
                await self.cdp_manager.cleanup()
                
            self._is_active = False
            self.logger.info("🧹 反检测服务资源清理完成")
            
        except Exception as e:
            self.logger.error(f"❌ 反检测服务清理失败: {e}")


# 全局单例实例
_global_anti_detection_service: Optional[AntiDetectionService] = None


def get_anti_detection_service(config: Optional[AntiDetectionConfig] = None) -> AntiDetectionService:
    """
    获取全局反检测服务实例（单例模式）
    
    Args:
        config: 配置对象，仅在首次调用时生效
        
    Returns:
        反检测服务实例
    """
    global _global_anti_detection_service
    
    if _global_anti_detection_service is None:
        _global_anti_detection_service = AntiDetectionService(config)
    
    return _global_anti_detection_service


def reset_anti_detection_service() -> None:
    """重置全局反检测服务实例"""
    global _global_anti_detection_service
    _global_anti_detection_service = None