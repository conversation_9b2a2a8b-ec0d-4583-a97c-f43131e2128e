#!/usr/bin/env python3
"""
SimpleMonitor - 简化版性能监控收集器
基于用户技术建议的完整修正版本

核心功能:
1. 异步环境友好的监控数据收集
2. 避免重复写入的CSV导出机制  
3. 非阻塞I/O的后台数据写入
4. 标准日志系统集成

监控指标:
- 操作耗时: iframe探测、宿主发现、元素定位
- 结果布尔值: 成功/失败状态
- 额外参数: timeout值等关键配置信息
"""

import asyncio
import csv
import json
import logging
import time
from collections import deque
from dataclasses import dataclass, asdict
from pathlib import Path
from typing import Optional, Dict, Any

@dataclass
class MonitoringRecord:
    """监控记录数据结构"""
    timestamp: float            # 时间戳
    operation: str             # 操作类型: "iframe_detection", "host_discovery", "element_location"  
    duration_ms: float         # 操作耗时(毫秒)
    success: bool             # 成功/失败布尔值
    params: Optional[str] = None  # 额外参数(JSON字符串，如timeout值)

class SimpleMonitor:
    """
    简化版监控收集器
    
    设计原则:
    - asyncio环境友好: 移除threading.Lock，使用asyncio机制
    - 避免重复写入: dump后清空deque，防止文件膨胀
    - 非阻塞I/O: CSV写入放到后台线程执行
    - 标准日志: 使用logging替代print输出
    """
    
    def __init__(self, max_records: int = 1000):
        """
        初始化监控器
        
        Args:
            max_records: 内存中保持的最大记录数量
        """
        self.records = deque(maxlen=max_records)
        # 修正：asyncio环境下移除threading.Lock，单线程无竞争
        self.csv_file = Path("logs/monitoring_data.csv")
        self._last_dump = time.time()
        self.logger = logging.getLogger('monitor')
        
        self.logger.debug("📊 SimpleMonitor监控器已初始化")
        
    def record(self, operation: str, duration_ms: float, success: bool, 
               params: Optional[Dict] = None):
        """
        记录监控数据 - 同步方法，避免不必要的async
        
        Args:
            operation: 操作类型 ("iframe_detection", "host_discovery", "element_location")
            duration_ms: 操作耗时(毫秒)
            success: 成功/失败布尔值
            params: 额外参数字典，将转换为JSON字符串存储
        """
        record = MonitoringRecord(
            timestamp=time.time(),
            operation=operation,
            duration_ms=duration_ms,
            success=success,
            params=json.dumps(params) if params else None
        )
        self.records.append(record)
        
        # 检查是否需要导出CSV (每5分钟)
        if time.time() - self._last_dump > 300:  # 5分钟
            # 修正：放到后台任务，不阻塞热路径
            asyncio.create_task(self._dump_async())
    
    async def _dump_async(self):
        """
        异步CSV导出 - 不阻塞主流程
        
        修正要点:
        - 避免重复写入: dump后清空deque
        - 使用asyncio.to_thread避免阻塞事件循环
        - 标准日志输出替代print
        """
        try:
            # 修正：避免重复写入，dump后清空deque
            pending = list(self.records)
            self.records.clear()
            
            if not pending:
                return
                
            # 使用asyncio.to_thread避免阻塞事件循环
            await asyncio.to_thread(self._write_csv, pending)
            self._last_dump = time.time()
            
        except Exception as e:
            self.logger.error(f"❌ CSV导出失败: {e}")
    
    def _write_csv(self, records):
        """
        同步CSV写入 - 在线程中执行
        
        Args:
            records: 要写入的监控记录列表
        """
        try:
            # 确保目录存在
            self.csv_file.parent.mkdir(exist_ok=True)
            
            # 检查是否需要写入表头
            file_exists = self.csv_file.exists()
            
            with open(self.csv_file, 'a', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                
                # 写入表头 (仅首次)
                if not file_exists:
                    writer.writerow(['timestamp', 'operation', 'duration_ms', 'success', 'params'])
                
                # 批量写入数据
                writer.writerows([
                    [r.timestamp, r.operation, r.duration_ms, r.success, r.params]
                    for r in records
                ])
            
            # 修正：使用标准日志而非print
            self.logger.info(f'📊 CSV导出完成: {len(records)} 条记录 -> {self.csv_file}')
            
        except Exception as e:
            self.logger.error(f"❌ CSV写入失败: {e}")
    
    def get_recent_stats(self, operation: str = None, last_minutes: int = 10) -> Dict[str, Any]:
        """
        获取最近的统计数据
        
        Args:
            operation: 指定操作类型，None表示所有操作
            last_minutes: 统计最近多少分钟的数据
            
        Returns:
            Dict[str, Any]: 统计结果
        """
        cutoff_time = time.time() - (last_minutes * 60)
        
        # 筛选记录
        recent_records = [
            r for r in self.records 
            if r.timestamp > cutoff_time and (operation is None or r.operation == operation)
        ]
        
        if not recent_records:
            return {
                "operation": operation or "all",
                "period_minutes": last_minutes,
                "count": 0,
                "success_rate": 0.0,
                "avg_duration_ms": 0.0
            }
        
        # 计算统计指标
        total_count = len(recent_records)
        success_count = sum(1 for r in recent_records if r.success)
        durations = [r.duration_ms for r in recent_records]
        
        return {
            "operation": operation or "all",
            "period_minutes": last_minutes,
            "count": total_count,
            "success_rate": success_count / total_count,
            "avg_duration_ms": sum(durations) / len(durations),
            "max_duration_ms": max(durations),
            "min_duration_ms": min(durations)
        }
    
    def force_dump(self):
        """强制导出当前所有数据到CSV"""
        if self.records:
            pending = list(self.records)
            self.records.clear()
            self._write_csv(pending)
            self._last_dump = time.time()
            self.logger.info(f"🔄 强制导出完成: {len(pending)} 条记录")
    
    def get_record_count(self) -> int:
        """获取当前内存中的记录数量"""
        return len(self.records)
    
    def clear_records(self):
        """清空所有内存记录 (谨慎使用)"""
        count = len(self.records)
        self.records.clear()
        self.logger.warning(f"🗑️ 已清空 {count} 条监控记录")