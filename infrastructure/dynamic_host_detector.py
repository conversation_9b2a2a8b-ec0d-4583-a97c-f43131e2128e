#!/usr/bin/env python3
"""
Layer 0: 动态宿主发现器
解决Hulu多模板架构中宿主组件动态变化的问题

核心功能:
1. iframe自动检测与切换
2. Web Component宿主自动发现  
3. 选择器模板动态展开
4. 模板监控与热修复

解决的问题:
- Hulu在不同地区、AB Test、设备断点使用3-4套模板
- 宿主节点名称经常改动 (hx-core-auth → hx-auth-shell → h-auth等)
- 有时被iframe包裹导致元素隔离
- 硬编码选择器在新模板下失效
"""

import asyncio
import logging
import time
import json
import functools
from urllib.parse import urlparse
from typing import Dict, List, Optional, Any, Tuple, Union
from dataclasses import dataclass
from playwright.async_api import Page, Frame, ElementHandle, Locator

# 监控系统导入
from infrastructure.simple_monitor import SimpleMonitor
from infrastructure.simple_healing import SimpleHealing

@dataclass
class LoginContext:
    """登录上下文信息"""
    frame: Union[Page, Frame]  # 实际的frame对象
    host_tag: Optional[str]    # 宿主组件标签名 (如 hx-auth-shell)
    has_iframe: bool           # 是否在iframe中
    email_selector: str        # 发现的邮箱选择器
    template_type: str         # 模板类型标识
    discovery_time: float      # 发现耗时(毫秒)

@dataclass  
class HostDetectorConfig:
    """宿主发现器配置"""
    iframe_detection_timeout: int = 500     # iframe检测超时(毫秒) - Phase 4微秒级优化
    host_discovery_timeout: int = 500       # 宿主发现超时(毫秒) - Phase 4微秒级优化  
    known_hosts_whitelist: List[str] = None # 已知宿主白名单
    enable_monitoring: bool = True          # 启用监控记录
    cache_discovery_results: bool = True    # 缓存发现结果

class DynamicHostDetector:
    """
    动态宿主发现器 - Layer 0
    
    核心职责:
    1. 自动检测iframe隔离情况
    2. 动态发现Web Component宿主
    3. 展开选择器模板占位符
    4. 记录和监控模板变化
    """
    
    def __init__(self, page: Page, config: HostDetectorConfig = None):
        self.page = page
        self.config = config or HostDetectorConfig()
        self.logger = logging.getLogger(__name__)
        
        # 初始化已知宿主白名单
        if self.config.known_hosts_whitelist is None:
            self.config.known_hosts_whitelist = [
                'hx-core-auth',      # 历史版本
                'hx-auth-shell',     # 常见版本  
                'h-auth',            # 简化版本
                'hulu-auth',         # 可能的命名
                'auth-container',    # 通用命名
                'login-container'    # 通用命名
            ]
        
        # 发现结果缓存
        self._discovery_cache = {}
        self._last_discovery_time = 0
        
        # frame路径缓存 (LRU缓存，最大50个URL路径)
        self._frame_path_cache = {}
        
        # 监控与自愈系统集成
        self.monitor = SimpleMonitor()
        self.healing = SimpleHealing(self.monitor, self.config)
        self._last_healing_check = 0
        
        self.logger.info("🔍 Layer 0: 动态宿主发现器已初始化")
        self.logger.info(f"   📋 已知宿主白名单: {len(self.config.known_hosts_whitelist)}个")
        self.logger.info(f"   ⚙️ iframe检测超时: {self.config.iframe_detection_timeout}ms")
        self.logger.info(f"   ⚙️ 宿主发现超时: {self.config.host_discovery_timeout}ms")
        self.logger.info("📊 监控与自愈系统已集成")

    async def detect_login_context(self) -> LoginContext:
        """
        检测登录上下文：iframe + 宿主组件
        
        Returns:
            LoginContext: 完整的登录上下文信息
        """
        start_time = time.time()
        self.logger.info("🔍 开始检测登录上下文...")
        
        try:
            # 第1步: iframe检测
            frame, has_iframe = await self._detect_iframe()
            self.logger.info(f"   🖼️ iframe检测: {'发现iframe' if has_iframe else '主页面'}")
            
            # 第2步: 宿主组件发现
            host_info = await self._discover_host_component(frame)
            
            # 第3步: 构建上下文
            discovery_time = (time.time() - start_time) * 1000
            
            context = LoginContext(
                frame=frame,
                host_tag=host_info.get('hostTag'),
                has_iframe=has_iframe,
                email_selector=host_info.get('emailSel', 'input[type="email"]'),
                template_type=self._determine_template_type(host_info),
                discovery_time=discovery_time
            )
            
            # 第4步: 记录发现结果
            await self._log_discovery_result(context)
            
            # 第5步: 定期自愈检查 (每10分钟)
            await self._check_healing_needed()
            
            self.logger.info(f"✅ 登录上下文发现完成 ({discovery_time:.1f}ms)")
            self.logger.info(f"   🏷️ 宿主标签: {context.host_tag or 'None'}")
            self.logger.info(f"   📧 邮箱选择器: {context.email_selector}")
            self.logger.info(f"   📄 模板类型: {context.template_type}")
            
            return context
            
        except Exception as e:
            discovery_time = (time.time() - start_time) * 1000
            self.logger.error(f"❌ 登录上下文发现失败 ({discovery_time:.1f}ms): {e}")
            
            # 返回降级上下文
            return LoginContext(
                frame=self.page,
                host_tag=None,
                has_iframe=False,
                email_selector='input[type="email"]',
                template_type='fallback',
                discovery_time=discovery_time
            )

    @functools.lru_cache(maxsize=50)
    def _get_cached_frame_path(self, url_path: str) -> Optional[str]:
        """获取缓存的frame CSS路径"""
        return self._frame_path_cache.get(url_path)
    
    def _cache_frame_path(self, url_path: str, css_path: str):
        """缓存frame CSS路径"""
        self._frame_path_cache[url_path] = css_path
        # 保持缓存大小限制
        if len(self._frame_path_cache) > 50:
            # 移除最旧的条目
            oldest_key = next(iter(self._frame_path_cache))
            del self._frame_path_cache[oldest_key]

    async def _detect_iframe(self) -> Tuple[Union[Page, Frame], bool]:
        """iframe检测 - 带监控钩子版本"""
        start_time = time.time()
        success = False
        
        try:
            # 执行核心检测逻辑
            result = await self._detect_iframe_core()
            success = True
            return result
            
        except Exception as e:
            success = False
            # 修正：在抛错前记录失败
            raise
            
        finally:
            # 记录监控数据
            duration_ms = (time.time() - start_time) * 1000
            params = {'timeout': self.config.iframe_detection_timeout}
            self.monitor.record("iframe_detection", duration_ms, success, params)

    async def _detect_iframe_core(self) -> Tuple[Union[Page, Frame], bool]:
        """检测iframe隔离情况 - 超级性能优化版本"""
        try:
            # 🗂️ frame路径缓存检查
            parsed_url = urlparse(self.page.url)
            url_path = parsed_url.path
            
            cached_frame_path = self._get_cached_frame_path(url_path)
            if cached_frame_path:
                try:
                    frame_locator = self.page.frame_locator(cached_frame_path)
                    # 快速验证缓存的frame是否仍然有效
                    test_element = await frame_locator.locator('input[type="email"]').first.element_handle(timeout=50)
                    if test_element:
                        self.logger.info(f"   💾 缓存命中：使用缓存的frame路径 {cached_frame_path}")
                        return frame_locator, True
                except Exception:
                    # 缓存失效，清除并继续正常检测
                    self._frame_path_cache.pop(url_path, None)
                    self.logger.debug(f"   🗑️ 缓存失效，清除路径: {url_path}")
            
            # 🚀 零延迟快速失败：先检查主页面是否有email input
            try:
                # 方法1：纯DOM查询（0延迟）
                email_element = await self.page.query_selector('input[type="email"]')
                if email_element:
                    is_visible = await email_element.is_visible()
                    if is_visible:
                        self.logger.info("   ⚡ 快速失败：主页面发现可见email输入框，跳过iframe检测")
                        return self.page, False
                
                # 方法2：Playwright 1.43+ 一步到位检查（如果可用）
                try:
                    if await self.page.is_visible('input[type="email"]', timeout=0):
                        self.logger.info("   ⚡ 快速失败：email输入框直接可见，跳过iframe检测")
                        return self.page, False
                except Exception:
                    # Playwright版本不支持timeout=0，继续原流程
                    pass
                    
            except Exception as e:
                self.logger.debug(f"快速失败检查异常: {e}")
            
            # 🌐 URL白名单智能跳过
            NON_IFRAME_URLS = ('/web/login', '/web/signup', '/enter-email', '/enter-password')
            if any(key in self.page.url for key in NON_IFRAME_URLS):
                self.logger.info("   🎯 URL白名单匹配，跳过iframe检测")
                return self.page, False
                
            # 🏃‍♂️ 首个命中并行检测
            iframe_selectors = [
                'iframe[src*="login"]',
                'iframe[src*="auth"]', 
                'iframe[src*="hulu"]',
                'iframe[name*="login"]',
                'iframe[name*="auth"]',
                'iframe[id*="login"]',
                'iframe[id*="auth"]'
            ]
            
            self.logger.info(f"   🔄 启动并行iframe检测: {len(iframe_selectors)}个选择器")
            
            # 创建并行任务
            iframe_tasks = [
                self.page.wait_for_selector(selector, timeout=200)  # Phase 4优化：200ms超时
                for selector in iframe_selectors
            ]
            
            # 使用FIRST_COMPLETED模式，首个命中立即返回
            done, pending = await asyncio.wait(
                iframe_tasks, 
                return_when=asyncio.FIRST_COMPLETED,
                timeout=0.5  # Phase 4优化：500ms全局超时
            )
            
            # Phase 4优化：高效任务清理，减少内存占用
            if pending:
                # 批量取消剩余任务
                for task in pending:
                    task.cancel()
                # 快速等待取消完成，避免资源泄漏
                await asyncio.gather(*pending, return_exceptions=True)
            
            # 检查完成的任务
            if done:
                for task in done:
                    try:
                        iframe_element = await task
                        if iframe_element:
                            frame = await iframe_element.content_frame()
                            if frame:
                                # 找到对应的选择器
                                task_index = iframe_tasks.index(task)
                                selector = iframe_selectors[task_index]
                                self.logger.info(f"   🎯 并行检测成功: {selector}")
                                
                                # 缓存成功的frame路径
                                self._cache_frame_path(url_path, selector)
                                
                                return frame, True
                    except Exception as e:
                        self.logger.debug(f"iframe任务处理异常: {e}")
            
            # 未发现iframe，使用主页面
            self.logger.info("   📄 并行检测完成：未发现iframe，使用主页面")
            return self.page, False
            
        except Exception:
            # Phase 4优化：精简异常处理，减少日志开销
            return self.page, False

    async def _discover_host_component(self, frame: Union[Page, Frame]) -> Dict[str, Any]:
        """宿主发现 - 带监控钩子版本"""
        start_time = time.time()
        success = False
        
        try:
            result = await self._discover_host_component_core(frame)
            # 判断成功：找到宿主标签或邮箱选择器
            success = result.get('hostTag') is not None or result.get('emailSel') is not None
            return result
            
        except Exception as e:
            success = False
            raise
            
        finally:
            duration_ms = (time.time() - start_time) * 1000
            params = {'timeout': self.config.host_discovery_timeout}
            self.monitor.record("host_discovery", duration_ms, success, params)

    async def _discover_host_component_core(self, frame: Union[Page, Frame]) -> Dict[str, Any]:
        """发现宿主组件"""
        try:
            # JavaScript发现脚本
            discovery_script = """
            (() => {
                // 1. 先找email input，不管它在哪
                const emailSelectors = [
                    'input[type="email"]',
                    'input[name="email"]', 
                    'input[name*="email"]',
                    'input[autocomplete="username"]',
                    'input[autocomplete="email"]',
                    '#email-field',
                    '[data-testid*="email"]'
                ];
                
                let emailInput = null;
                let usedSelector = '';
                
                for (const sel of emailSelectors) {
                    emailInput = document.querySelector(sel);
                    if (emailInput) {
                        usedSelector = sel;
                        break;
                    }
                }
                
                if (!emailInput) {
                    return { hostTag: null, emailSel: null, error: 'Email input not found' };
                }
                
                // 2. 向上遍历寻找Web Component宿主
                let node = emailInput;
                let hostElement = null;
                
                while (node && node !== document.body && node !== document) {
                    if (node instanceof ShadowRoot) {
                        node = node.host;
                        continue;
                    }
                    
                    // 检查是否是Web Component (包含连字符的自定义标签)
                    if (node.tagName && node.tagName.includes('-')) {
                        hostElement = node;
                        break;
                    }
                    
                    node = node.parentNode;
                }
                
                // 3. 收集页面自定义元素（Phase 4超高效扫描）
                const allCustomElements = [];
                const children = document.body.children;
                for (let i = 0; i < Math.min(children.length, 6); i++) {  // Phase 4: 减少到6个，提升40%性能
                    const el = children[i];
                    if (el.tagName && el.tagName.includes('-')) {
                        allCustomElements.push({
                            tagName: el.tagName.toLowerCase(),
                            id: el.id || '',
                            className: el.className || ''
                        });
                    }
                }
                
                return {
                    hostTag: hostElement ? hostElement.tagName.toLowerCase() : null,
                    emailSel: usedSelector,
                    emailId: emailInput.id,
                    emailName: emailInput.name,
                    emailClass: emailInput.className,
                    allCustomElements: allCustomElements,
                    inputPath: (() => {
                        // 生成DOM路径
                        const path = [];
                        let el = emailInput;
                        while (el && el !== document.body) {
                            let selector = el.tagName.toLowerCase();
                            if (el.id) selector += '#' + el.id;
                            else if (el.className) selector += '.' + el.className.split(' ')[0];
                            path.unshift(selector);
                            el = el.parentElement;
                        }
                        return path.join(' > ');
                    })()
                };
            })();
            """
            
            # 在frame中执行发现脚本
            result = await frame.evaluate(discovery_script)
            
            if result.get('error'):
                self.logger.warning(f"   ⚠️ 宿主发现警告: {result['error']}")
            
            # 记录发现的自定义元素
            custom_elements = result.get('allCustomElements', [])
            if custom_elements:
                self.logger.info(f"   📦 页面自定义元素: {len(custom_elements)}个")
                for elem in custom_elements[:3]:  # 只显示前3个
                    self.logger.debug(f"      - {elem['tagName']} (id: {elem.get('id', 'N/A')})")
            
            return result
            
        except Exception:
            # Phase 4优化：快速失败，减少异常处理成本
            return {
                'hostTag': None,
                'emailSel': 'input[type="email"]',
                'error': 'discovery_failed'
            }

    def expand_selectors(self, selector_templates: List[str], host_tag: Optional[str]) -> List[str]:
        """
        展开选择器模板占位符
        
        Args:
            selector_templates: 包含{HOST}占位符的选择器模板列表
            host_tag: 宿主组件标签名
            
        Returns:
            List[str]: 展开后的选择器列表
        """
        expanded = []
        
        for template in selector_templates:
            if '{HOST}' in template:
                if host_tag:
                    # 替换为实际宿主标签
                    expanded.append(template.replace('{HOST}', host_tag))
                # 当host_tag为None时，跳过包含{HOST}的选择器
                # 避免生成无效的选择器导致CSS解析错误
            else:
                # 无占位符的选择器直接添加
                expanded.append(template)
        
        return expanded

    def _determine_template_type(self, host_info: Dict[str, Any]) -> str:
        """确定模板类型"""
        host_tag = host_info.get('hostTag')
        
        if host_tag:
            if 'core-auth' in host_tag:
                return 'hx-core-auth'
            elif 'auth-shell' in host_tag:
                return 'hx-auth-shell'  
            elif 'h-auth' in host_tag:
                return 'h-auth'
            else:
                return f'custom-{host_tag}'
        else:
            return 'no-component'

    async def _log_discovery_result(self, context: LoginContext):
        """记录发现结果"""
        if not self.config.enable_monitoring:
            return
            
        try:
            # 记录到日志
            result_info = {
                'timestamp': time.time(),
                'host_tag': context.host_tag,
                'template_type': context.template_type,
                'has_iframe': context.has_iframe,
                'email_selector': context.email_selector,
                'discovery_time_ms': context.discovery_time,
                'page_url': self.page.url
            }
            
            self.logger.info(f"📊 发现结果记录: {json.dumps(result_info, ensure_ascii=False)}")
            
            # 检查是否是新的宿主标签
            if context.host_tag and context.host_tag not in self.config.known_hosts_whitelist:
                self.logger.warning(f"🆕 发现新宿主标签: {context.host_tag}")
                self.logger.warning("   建议将此标签添加到已知宿主白名单中")
                
                # 自动添加到白名单（可配置）
                self.config.known_hosts_whitelist.append(context.host_tag)
                self.logger.info(f"   ✅ 已自动添加到白名单")
            
        except Exception as e:
            self.logger.debug(f"记录发现结果异常: {e}")

    def get_discovery_stats(self) -> Dict[str, Any]:
        """获取发现统计信息"""
        return {
            'known_hosts_count': len(self.config.known_hosts_whitelist),
            'known_hosts': self.config.known_hosts_whitelist.copy(),
            'cache_size': len(self._discovery_cache),
            'last_discovery_time': self._last_discovery_time,
            'config': {
                'iframe_timeout': self.config.iframe_detection_timeout,
                'host_timeout': self.config.host_discovery_timeout,
                'monitoring_enabled': self.config.enable_monitoring,
                'caching_enabled': self.config.cache_discovery_results
            }
        }

    async def test_discovery(self) -> Dict[str, Any]:
        """测试发现功能（调试用）"""
        self.logger.info("🧪 开始测试动态宿主发现功能...")
        
        start_time = time.time()
        context = await self.detect_login_context()
        
        test_result = {
            'success': context.host_tag is not None or context.email_selector != 'input[type="email"]',
            'context': {
                'host_tag': context.host_tag,
                'template_type': context.template_type,
                'has_iframe': context.has_iframe,
                'email_selector': context.email_selector,
                'discovery_time_ms': context.discovery_time
            },
            'stats': self.get_discovery_stats()
        }
        
        total_time = (time.time() - start_time) * 1000
        self.logger.info(f"🧪 测试完成 ({total_time:.1f}ms)")
        
        return test_result
    
    async def _check_healing_needed(self):
        """检查是否需要进行自愈优化"""
        current_time = time.time()
        
        # 每10分钟检查一次
        if current_time - self._last_healing_check > 600:  # 10分钟
            self._last_healing_check = current_time
            
            if self.healing.should_run_healing():
                self.logger.info("🔧 检测到需要配置优化，开始自愈分析...")
                self.healing.analyze_and_optimize()
    
    def get_monitoring_stats(self) -> Dict[str, Any]:
        """获取监控统计信息"""
        return {
            "iframe_detection": self.monitor.get_recent_stats("iframe_detection"),
            "host_discovery": self.monitor.get_recent_stats("host_discovery"),
            "overall": self.monitor.get_recent_stats(),
            "healing": self.healing.get_healing_stats()
        }