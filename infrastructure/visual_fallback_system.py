#!/usr/bin/env python3
"""
视觉兜底系统 (Visual Fallback System)
基于OpenCV的图像模板匹配，处理A/B测试和选择器失效场景

功能:
- 基准模板管理
- 模糊图像匹配
- 点击坐标计算
- 自动模板更新
- A/B测试适应

应用场景:
- 按钮文本变化 ("登录" → "Sign In")
- A/B测试导致选择器失效  
- 动态加载内容的视觉识别
- 验证码图像识别
"""

import cv2
import numpy as np
import os
import json
import logging
import asyncio
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict
from enum import Enum
from playwright.async_api import Page
import tempfile
from pathlib import Path

class MatchConfidence(Enum):
    """匹配置信度等级"""
    VERY_HIGH = 0.95    # 非常高置信度
    HIGH = 0.85         # 高置信度
    MEDIUM = 0.75       # 中等置信度
    LOW = 0.65          # 低置信度（最小可接受）

@dataclass
class TemplateInfo:
    """模板信息"""
    name: str                           # 模板名称
    description: str                    # 描述
    image_path: str                     # 图像路径
    confidence_threshold: float = 0.75  # 置信度阈值
    click_offset: Tuple[int, int] = (0, 0)  # 点击偏移
    last_updated: str = ""              # 最后更新时间
    usage_count: int = 0                # 使用次数
    success_rate: float = 0.0           # 成功率

@dataclass 
class MatchResult:
    """匹配结果"""
    found: bool                         # 是否找到
    confidence: float                   # 置信度
    center_point: Optional[Tuple[int, int]] = None  # 中心点坐标
    click_point: Optional[Tuple[int, int]] = None   # 建议点击坐标
    bounding_box: Optional[Tuple[int, int, int, int]] = None  # 边界框
    template_used: Optional[str] = None  # 使用的模板

class VisualFallbackSystem:
    """
    视觉兜底系统
    
    当传统选择器失效时，使用OpenCV进行图像模板匹配
    提供可靠的最后兜底机制
    """
    
    def __init__(self, 
                 templates_dir: str = "screenshots/templates",
                 logger: Optional[logging.Logger] = None):
        self.logger = logger or logging.getLogger(__name__)
        self.templates_dir = Path(templates_dir)
        self.templates_dir.mkdir(parents=True, exist_ok=True)
        
        # 模板信息存储
        self.templates: Dict[str, TemplateInfo] = {}
        self.templates_config_path = self.templates_dir / "templates_config.json"
        
        # 匹配参数
        self.default_confidence = MatchConfidence.MEDIUM.value
        self.match_methods = [
            cv2.TM_CCOEFF_NORMED,    # 最常用
            cv2.TM_CCORR_NORMED,     # 备选1
            cv2.TM_SQDIFF_NORMED     # 备选2 (取反)
        ]
        
        # 加载现有模板配置
        self._load_templates_config()
        
        self.logger.info(f"🖼️ VisualFallbackSystem初始化完成，模板目录: {self.templates_dir}")
        self.logger.info(f"📋 已加载 {len(self.templates)} 个模板")

    def _load_templates_config(self):
        """加载模板配置"""
        try:
            if self.templates_config_path.exists():
                with open(self.templates_config_path, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                    for name, data in config_data.items():
                        self.templates[name] = TemplateInfo(**data)
                self.logger.debug(f"✅ 加载模板配置: {len(self.templates)}个")
        except Exception as e:
            self.logger.warning(f"⚠️ 加载模板配置失败: {e}")

    def _save_templates_config(self):
        """保存模板配置"""
        try:
            config_data = {name: asdict(template) for name, template in self.templates.items()}
            with open(self.templates_config_path, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
            self.logger.debug("✅ 模板配置已保存")
        except Exception as e:
            self.logger.error(f"❌ 保存模板配置失败: {e}")

    async def create_template(self, 
                            page: Page, 
                            template_name: str,
                            selector: str,
                            description: str = "",
                            confidence_threshold: float = None) -> bool:
        """
        创建新的视觉模板
        
        Args:
            page: Playwright页面对象
            template_name: 模板名称
            selector: 元素选择器
            description: 模板描述
            confidence_threshold: 置信度阈值
            
        Returns:
            bool: 创建是否成功
        """
        try:
            # 等待元素出现
            element = await page.wait_for_selector(selector, timeout=5000)
            if not element:
                self.logger.error(f"❌ 未找到元素: {selector}")
                return False
            
            # 获取元素边界框
            bounding_box = await element.bounding_box()
            if not bounding_box:
                self.logger.error(f"❌ 无法获取元素边界框: {selector}")
                return False
            
            # 截取元素图像
            element_screenshot = await element.screenshot()
            
            # 保存模板图像
            template_path = self.templates_dir / f"{template_name}.png"
            with open(template_path, 'wb') as f:
                f.write(element_screenshot)
            
            # 创建模板信息
            template_info = TemplateInfo(
                name=template_name,
                description=description or f"Template for {selector}",
                image_path=str(template_path),
                confidence_threshold=confidence_threshold or self.default_confidence,
                click_offset=(0, 0),  # 默认点击中心
                last_updated=str(int(asyncio.get_event_loop().time()))
            )
            
            self.templates[template_name] = template_info
            self._save_templates_config()
            
            self.logger.info(f"✅ 创建视觉模板: {template_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 创建模板失败: {template_name} - {e}")
            return False

    async def find_element_by_template(self, 
                                     page: Page, 
                                     template_name: str,
                                     confidence_override: Optional[float] = None) -> MatchResult:
        """
        使用模板查找页面元素
        
        Args:
            page: Playwright页面对象
            template_name: 模板名称
            confidence_override: 覆盖默认置信度
            
        Returns:
            MatchResult: 匹配结果
        """
        if template_name not in self.templates:
            self.logger.error(f"❌ 模板不存在: {template_name}")
            return MatchResult(found=False, confidence=0.0)
        
        template_info = self.templates[template_name]
        confidence_threshold = confidence_override or template_info.confidence_threshold
        
        try:
            # 获取页面截图
            screenshot_bytes = await page.screenshot(full_page=False)
            
            # 转换为OpenCV格式
            nparr = np.frombuffer(screenshot_bytes, np.uint8)
            page_image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
            
            # 加载模板图像
            if not os.path.exists(template_info.image_path):
                self.logger.error(f"❌ 模板文件不存在: {template_info.image_path}")
                return MatchResult(found=False, confidence=0.0)
            
            template_image = cv2.imread(template_info.image_path, cv2.IMREAD_COLOR)
            if template_image is None:
                self.logger.error(f"❌ 无法加载模板图像: {template_info.image_path}")
                return MatchResult(found=False, confidence=0.0)
            
            # 执行模板匹配
            best_match = self._perform_template_matching(
                page_image, template_image, confidence_threshold
            )
            
            if best_match.found:
                # 更新使用统计
                template_info.usage_count += 1
                self._save_templates_config()
                
                self.logger.info(f"✅ 模板匹配成功: {template_name} (置信度: {best_match.confidence:.3f})")
            else:
                self.logger.debug(f"⚠️ 模板匹配失败: {template_name} (最高置信度: {best_match.confidence:.3f})")
            
            best_match.template_used = template_name
            return best_match
            
        except Exception as e:
            self.logger.error(f"❌ 模板匹配异常: {template_name} - {e}")
            return MatchResult(found=False, confidence=0.0)

    def _perform_template_matching(self, 
                                 page_image: np.ndarray, 
                                 template_image: np.ndarray,
                                 confidence_threshold: float) -> MatchResult:
        """执行模板匹配"""
        best_confidence = 0.0
        best_location = None
        best_method = None
        
        # 尝试多种匹配方法
        for method in self.match_methods:
            try:
                result = cv2.matchTemplate(page_image, template_image, method)
                
                if method == cv2.TM_SQDIFF_NORMED:
                    # SQDIFF_NORMED: 0是完美匹配，需要取反
                    min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
                    confidence = 1.0 - min_val
                    location = min_loc
                else:
                    # 其他方法: 1是完美匹配
                    min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
                    confidence = max_val
                    location = max_loc
                
                if confidence > best_confidence:
                    best_confidence = confidence
                    best_location = location
                    best_method = method
                    
            except Exception as e:
                self.logger.debug(f"⚠️ 匹配方法失败 {method}: {e}")
                continue
        
        # 判断是否达到置信度阈值
        if best_confidence >= confidence_threshold and best_location is not None:
            # 计算中心点和点击点
            h, w = template_image.shape[:2]
            center_x = best_location[0] + w // 2
            center_y = best_location[1] + h // 2
            
            return MatchResult(
                found=True,
                confidence=best_confidence,
                center_point=(center_x, center_y),
                click_point=(center_x, center_y),  # 默认点击中心
                bounding_box=(best_location[0], best_location[1], w, h)
            )
        else:
            return MatchResult(
                found=False,
                confidence=best_confidence
            )

    async def smart_click_by_template(self, 
                                    page: Page, 
                                    template_name: str,
                                    confidence_override: Optional[float] = None) -> bool:
        """
        使用模板进行智能点击
        
        Args:
            page: Playwright页面对象
            template_name: 模板名称
            confidence_override: 覆盖默认置信度
            
        Returns:
            bool: 点击是否成功
        """
        match_result = await self.find_element_by_template(
            page, template_name, confidence_override
        )
        
        if not match_result.found or not match_result.click_point:
            return False
        
        try:
            # 执行点击
            x, y = match_result.click_point
            await page.mouse.click(x, y)
            
            self.logger.info(f"✅ 模板点击成功: {template_name} at ({x}, {y})")
            
            # 更新成功率统计
            if template_name in self.templates:
                template_info = self.templates[template_name]
                template_info.success_rate = (
                    template_info.success_rate * (template_info.usage_count - 1) + 1.0
                ) / template_info.usage_count
                self._save_templates_config()
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 模板点击失败: {template_name} - {e}")
            return False

    async def create_baseline_templates(self, page: Page) -> bool:
        """
        创建基线模板库
        
        为常用的UI元素创建基础模板
        """
        self.logger.info("🎯 开始创建基线模板库...")
        
        # 基线模板定义
        baseline_templates = {
            "login_button": {
                "selectors": [
                    'button:has-text("Log In")',
                    'button:has-text("LOGIN")', 
                    'a:has-text("Log In")',
                    'button[data-automation-id="login"]'
                ],
                "description": "通用登录按钮"
            },
            "continue_button": {
                "selectors": [
                    'button:has-text("Continue")',
                    'button:has-text("CONTINUE")',
                    'button:has-text("继续")'
                ],
                "description": "继续/下一步按钮"
            },
            "email_input": {
                "selectors": [
                    'input[type="email"]',
                    'input[name="email"]', 
                    'input[placeholder*="email"]'
                ],
                "description": "邮箱输入框"
            },
            "password_input": {
                "selectors": [
                    'input[type="password"]',
                    'input[name="password"]'
                ],
                "description": "密码输入框"
            }
        }
        
        created_count = 0
        
        for template_name, template_def in baseline_templates.items():
            try:
                # 尝试每个选择器
                element_found = False
                for selector in template_def["selectors"]:
                    try:
                        element = await page.wait_for_selector(selector, timeout=2000)
                        if element and await element.is_visible():
                            success = await self.create_template(
                                page, template_name, selector, template_def["description"]
                            )
                            if success:
                                created_count += 1
                                element_found = True
                                break
                    except Exception:
                        continue
                
                if not element_found:
                    self.logger.debug(f"⚠️ 未找到模板元素: {template_name}")
                    
            except Exception as e:
                self.logger.debug(f"⚠️ 创建基线模板失败: {template_name} - {e}")
        
        self.logger.info(f"✅ 基线模板创建完成: {created_count}/{len(baseline_templates)}")
        return created_count > 0

    def list_templates(self) -> Dict[str, Dict[str, Any]]:
        """列出所有可用模板"""
        return {
            name: {
                "description": template.description,
                "confidence_threshold": template.confidence_threshold,
                "usage_count": template.usage_count, 
                "success_rate": template.success_rate,
                "last_updated": template.last_updated,
                "image_exists": os.path.exists(template.image_path)
            }
            for name, template in self.templates.items()
        }

    def remove_template(self, template_name: str) -> bool:
        """移除模板"""
        if template_name not in self.templates:
            return False
        
        try:
            template_info = self.templates[template_name]
            
            # 删除图像文件
            if os.path.exists(template_info.image_path):
                os.remove(template_info.image_path)
            
            # 从配置中移除
            del self.templates[template_name]
            self._save_templates_config()
            
            self.logger.info(f"✅ 模板已移除: {template_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 移除模板失败: {template_name} - {e}")
            return False

    async def debug_match(self, 
                         page: Page, 
                         template_name: str,
                         save_debug_image: bool = True) -> Dict[str, Any]:
        """
        调试模板匹配
        
        返回详细的匹配信息用于调试
        """
        if template_name not in self.templates:
            return {"error": f"Template not found: {template_name}"}
        
        template_info = self.templates[template_name]
        
        try:
            # 获取页面截图
            screenshot_bytes = await page.screenshot(full_page=False)
            nparr = np.frombuffer(screenshot_bytes, np.uint8)
            page_image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
            
            # 加载模板
            template_image = cv2.imread(template_info.image_path, cv2.IMREAD_COLOR)
            
            debug_info = {
                "template_name": template_name,
                "page_image_shape": page_image.shape,
                "template_image_shape": template_image.shape,
                "confidence_threshold": template_info.confidence_threshold,
                "methods_tested": []
            }
            
            # 测试所有匹配方法
            for method in self.match_methods:
                try:
                    result = cv2.matchTemplate(page_image, template_image, method)
                    min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
                    
                    if method == cv2.TM_SQDIFF_NORMED:
                        confidence = 1.0 - min_val
                        location = min_loc
                    else:
                        confidence = max_val
                        location = max_loc
                    
                    debug_info["methods_tested"].append({
                        "method": method,
                        "confidence": confidence,
                        "location": location,
                        "meets_threshold": confidence >= template_info.confidence_threshold
                    })
                    
                except Exception as e:
                    debug_info["methods_tested"].append({
                        "method": method,
                        "error": str(e)
                    })
            
            # 保存调试图像
            if save_debug_image:
                debug_image_path = self.templates_dir / f"debug_{template_name}_{int(asyncio.get_event_loop().time())}.png"
                cv2.imwrite(str(debug_image_path), page_image)
                debug_info["debug_image_saved"] = str(debug_image_path)
            
            return debug_info
            
        except Exception as e:
            return {"error": str(e)}

# 集成到SmartInteractionController的helper函数
async def visual_fallback_click(page: Page, 
                              visual_system: VisualFallbackSystem,
                              template_names: List[str],
                              confidence_threshold: float = 0.75) -> bool:
    """
    视觉兜底点击函数
    
    当传统选择器失效时使用
    """
    for template_name in template_names:
        try:
            success = await visual_system.smart_click_by_template(
                page, template_name, confidence_threshold
            )
            if success:
                return True
        except Exception as e:
            logging.getLogger(__name__).debug(f"⚠️ 视觉兜底失败: {template_name} - {e}")
            continue
    
    return False