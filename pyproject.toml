[project]
name = "hulu-account-creator"
version = "2.1.0"
description = "<PERSON>lu Account Creator 智能决策支持系统 - 基于 Playwright 的浏览器自动化工具"
readme = "README_hulu.md"
requires-python = ">=3.8"
license = { text = "MIT" }
keywords = ["playwright", "automation", "hulu", "account-creator", "browser-automation"]
authors = [
    { name = "maidou_cat" }
]
dependencies = [
    "playwright>=1.40.0",
    "requests==2.31.0",
    "fake-useragent>=1.4.0",
    "numpy>=1.24.0",
    "flask>=2.3.0",
    "pyautogui>=0.9.54",
    "pyscreenshot>=3.1",
    "pillow>=9.0.0",
    "opencv-python>=4.8.0",
    "httpx>=0.28.1",
    "cryptography>=45.0.5",
    "pandas>=2.0.3",
    "aiohttp>=3.10.11",
    "pyyaml>=6.0",
    "structlog>=23.0.0",
    "prometheus-client>=0.19.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "black>=23.0.0",
    "ruff>=0.1.0",
]

[project.urls]
Homepage = "https://github.com/maidou_cat/hulu-account-creator"
Documentation = "https://github.com/maidou_cat/hulu-account-creator/blob/main/CLAUDE.md"
Issues = "https://github.com/maidou_cat/hulu-account-creator/issues"

[tool.uv]
dev-dependencies = [
    "pytest>=7.0.0",
    "black>=23.0.0",
    "ruff>=0.1.0",
]

[tool.black]
line-length = 88
target-version = ['py38']
include = '\.pyi?$'

[tool.ruff]
line-length = 88
target-version = "py38"
select = ["E", "F", "W", "I", "N", "UP", "B", "A", "C4", "SIM", "PIE"]
ignore = ["E203", "E501", "W503"]

[tool.ruff.per-file-ignores]
"tests/*" = ["S101"]

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = "test_*.py"
python_classes = "Test*"
python_functions = "test_*"
addopts = "-v --tb=short"
