#!/usr/bin/env python3
"""
死代码检测器 - Dead Code Detector
使用AST遍历方法分析Python项目中的死代码

功能:
- AST遍历收集函数和类定义
- 分析调用关系和引用关系
- 识别未被使用的代码
- 生成分类详细报告

作者: Claude Code
版本: 1.0.0
"""

import ast
import os
import sys
from pathlib import Path
from typing import Dict, List, Set, Tuple, Optional, Any
from dataclasses import dataclass, field
from collections import defaultdict
import json


@dataclass
class CodeDefinition:
    """代码定义信息"""
    name: str
    type: str  # 'function', 'class', 'method'
    file_path: str
    line_number: int
    module_name: str
    parent_class: Optional[str] = None
    is_private: bool = False
    is_magic: bool = False
    is_test: bool = False
    is_main: bool = False


@dataclass
class CodeReference:
    """代码引用信息"""
    name: str
    type: str  # 'call', 'attribute', 'name', 'import'
    file_path: str
    line_number: int
    context: str = ""


@dataclass
class DeadCodeReport:
    """死代码分析报告"""
    confirmed_dead: List[CodeDefinition] = field(default_factory=list)
    suspicious: List[CodeDefinition] = field(default_factory=list)
    preserved: List[CodeDefinition] = field(default_factory=list)
    duplicates: List[Tuple[CodeDefinition, CodeDefinition]] = field(default_factory=list)
    statistics: Dict[str, Any] = field(default_factory=dict)


class ASTDefinitionCollector(ast.NodeVisitor):
    """AST定义收集器"""
    
    def __init__(self, file_path: str, module_name: str):
        self.file_path = file_path
        self.module_name = module_name
        self.definitions: List[CodeDefinition] = []
        self.current_class: Optional[str] = None
        
    def visit_FunctionDef(self, node: ast.FunctionDef):
        """访问函数定义"""
        definition = CodeDefinition(
            name=node.name,
            type='method' if self.current_class else 'function',
            file_path=self.file_path,
            line_number=node.lineno,
            module_name=self.module_name,
            parent_class=self.current_class,
            is_private=node.name.startswith('_') and not node.name.startswith('__'),
            is_magic=node.name.startswith('__') and node.name.endswith('__'),
            is_test=node.name.startswith('test_') or 'test' in self.file_path.lower(),
            is_main=node.name == 'main'
        )
        self.definitions.append(definition)
        self.generic_visit(node)
    
    def visit_AsyncFunctionDef(self, node: ast.AsyncFunctionDef):
        """访问异步函数定义"""
        definition = CodeDefinition(
            name=node.name,
            type='async_method' if self.current_class else 'async_function',
            file_path=self.file_path,
            line_number=node.lineno,
            module_name=self.module_name,
            parent_class=self.current_class,
            is_private=node.name.startswith('_') and not node.name.startswith('__'),
            is_magic=node.name.startswith('__') and node.name.endswith('__'),
            is_test=node.name.startswith('test_') or 'test' in self.file_path.lower(),
            is_main=node.name == 'main'
        )
        self.definitions.append(definition)
        self.generic_visit(node)
    
    def visit_ClassDef(self, node: ast.ClassDef):
        """访问类定义"""
        definition = CodeDefinition(
            name=node.name,
            type='class',
            file_path=self.file_path,
            line_number=node.lineno,
            module_name=self.module_name,
            is_private=node.name.startswith('_'),
            is_test='test' in self.file_path.lower() or node.name.lower().startswith('test')
        )
        self.definitions.append(definition)
        
        # 进入类上下文
        old_class = self.current_class
        self.current_class = node.name
        self.generic_visit(node)
        self.current_class = old_class


class ASTReferenceCollector(ast.NodeVisitor):
    """AST引用收集器"""
    
    def __init__(self, file_path: str, module_name: str):
        self.file_path = file_path
        self.module_name = module_name
        self.references: List[CodeReference] = []
        
    def visit_Call(self, node: ast.Call):
        """访问函数调用"""
        if isinstance(node.func, ast.Name):
            # 直接函数调用: func()
            ref = CodeReference(
                name=node.func.id,
                type='call',
                file_path=self.file_path,
                line_number=node.lineno,
                context='direct_call'
            )
            self.references.append(ref)
        elif isinstance(node.func, ast.Attribute):
            # 方法调用: obj.method()
            ref = CodeReference(
                name=node.func.attr,
                type='call',
                file_path=self.file_path,
                line_number=node.lineno,
                context='method_call'
            )
            self.references.append(ref)
        
        self.generic_visit(node)
    
    def visit_Attribute(self, node: ast.Attribute):
        """访问属性访问"""
        ref = CodeReference(
            name=node.attr,
            type='attribute',
            file_path=self.file_path,
            line_number=node.lineno,
            context='attribute_access'
        )
        self.references.append(ref)
        self.generic_visit(node)
    
    def visit_Name(self, node: ast.Name):
        """访问名称引用"""
        if isinstance(node.ctx, ast.Load):
            ref = CodeReference(
                name=node.id,
                type='name',
                file_path=self.file_path,
                line_number=node.lineno,
                context='name_reference'
            )
            self.references.append(ref)
        self.generic_visit(node)
    
    def visit_Constant(self, node: ast.Constant):
        """访问常量（可能包含类型提示中的字符串引用）"""
        if isinstance(node.value, str):
            # 检查是否为类型提示中的类名引用
            if node.value.isidentifier() and node.value[0].isupper():
                ref = CodeReference(
                    name=node.value,
                    type='type_hint',
                    file_path=self.file_path,
                    line_number=node.lineno,
                    context='type_annotation'
                )
                self.references.append(ref)
        self.generic_visit(node)
    
    def visit_Import(self, node: ast.Import):
        """访问import语句"""
        for alias in node.names:
            name = alias.asname if alias.asname else alias.name.split('.')[0]
            ref = CodeReference(
                name=name,
                type='import',
                file_path=self.file_path,
                line_number=node.lineno,
                context=f'import {alias.name}'
            )
            self.references.append(ref)
        self.generic_visit(node)
    
    def visit_ImportFrom(self, node: ast.ImportFrom):
        """访问from...import语句"""
        for alias in node.names:
            name = alias.asname if alias.asname else alias.name
            ref = CodeReference(
                name=name,
                type='import',
                file_path=self.file_path,
                line_number=node.lineno,
                context=f'from {node.module} import {alias.name}'
            )
            self.references.append(ref)
        self.generic_visit(node)


class DeadCodeDetector:
    """死代码检测器主类"""
    
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.definitions: List[CodeDefinition] = []
        self.references: List[CodeReference] = []
        self.call_graph: Dict[str, Set[str]] = defaultdict(set)
        self.import_graph: Dict[str, Set[str]] = defaultdict(set)
        
        # 特殊文件模式
        self.test_patterns = ['test_', 'tests/', '/test', '_test.py']
        self.example_patterns = ['example', 'demo', 'sample']
        self.tool_patterns = ['main.py', 'cli.py', 'script', 'tool']
        
        # 框架约定方法
        self.framework_methods = {
            '__init__', '__str__', '__repr__', '__len__', '__iter__',
            'setUp', 'tearDown', 'setUpClass', 'tearDownClass',
            'test_', 'run', 'execute', 'handle', 'process'
        }
    
    def collect_python_files(self) -> List[Path]:
        """收集所有Python文件"""
        python_files = []
        for py_file in self.project_root.rglob('*.py'):
            # 跳过虚拟环境和node_modules
            if any(skip in str(py_file) for skip in ['venv', 'env', 'node_modules', '__pycache__']):
                continue
            python_files.append(py_file)
        return python_files
    
    def get_module_name(self, file_path: Path) -> str:
        """获取模块名"""
        try:
            relative_path = file_path.relative_to(self.project_root)
            module_parts = relative_path.with_suffix('').parts
            return '.'.join(module_parts)
        except ValueError:
            return file_path.stem
    
    def parse_file(self, file_path: Path) -> Tuple[List[CodeDefinition], List[CodeReference]]:
        """解析单个Python文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            tree = ast.parse(content, filename=str(file_path))
            module_name = self.get_module_name(file_path)
            
            # 收集定义
            def_collector = ASTDefinitionCollector(str(file_path), module_name)
            def_collector.visit(tree)
            
            # 收集引用
            ref_collector = ASTReferenceCollector(str(file_path), module_name)
            ref_collector.visit(tree)
            
            return def_collector.definitions, ref_collector.references
            
        except Exception as e:
            print(f"解析文件 {file_path} 时出错: {e}")
            return [], []
    
    def collect_all_definitions_and_references(self):
        """收集所有定义和引用"""
        python_files = self.collect_python_files()
        print(f"找到 {len(python_files)} 个Python文件")
        
        for file_path in python_files:
            definitions, references = self.parse_file(file_path)
            self.definitions.extend(definitions)
            self.references.extend(references)
        
        print(f"收集到 {len(self.definitions)} 个定义")
        print(f"收集到 {len(self.references)} 个引用")
    
    def build_reference_graph(self):
        """构建引用关系图"""
        # 按名称索引定义
        definitions_by_name = defaultdict(list)
        for definition in self.definitions:
            definitions_by_name[definition.name].append(definition)
        
        # 构建调用图
        for reference in self.references:
            if reference.name in definitions_by_name:
                self.call_graph[reference.name].add(reference.file_path)
        
        print(f"构建了 {len(self.call_graph)} 个引用关系")
    
    def is_special_case(self, definition: CodeDefinition) -> Tuple[bool, str]:
        """判断是否为特殊情况（应该保留）"""
        reasons = []
        
        # 入口点
        if definition.is_main or definition.name == 'main':
            reasons.append("入口点函数")
        
        # 魔术方法
        if definition.is_magic:
            reasons.append("魔术方法")
        
        # 测试相关
        if definition.is_test or any(pattern in definition.file_path.lower() for pattern in self.test_patterns):
            reasons.append("测试相关")
        
        # 示例和演示
        if any(pattern in definition.file_path.lower() for pattern in self.example_patterns):
            reasons.append("示例/演示代码")
        
        # 框架约定
        if any(definition.name.startswith(method) for method in self.framework_methods):
            reasons.append("框架约定方法")
        
        # if __name__ == "__main__" 中的代码
        if definition.file_path in [ref.file_path for ref in self.references if 'main' in ref.context]:
            reasons.append("主程序入口")
        
        return len(reasons) > 0, '; '.join(reasons)
    
    def find_duplicates(self) -> List[Tuple[CodeDefinition, CodeDefinition]]:
        """查找重复定义"""
        duplicates = []
        definitions_by_name = defaultdict(list)
        
        for definition in self.definitions:
            if definition.type in ['function', 'async_function', 'class']:
                definitions_by_name[definition.name].append(definition)
        
        for name, defs in definitions_by_name.items():
            if len(defs) > 1:
                # 排除同一文件内的重复（可能是重载）
                unique_files = {}
                for d in defs:
                    if d.file_path not in unique_files:
                        unique_files[d.file_path] = d
                    else:
                        duplicates.append((unique_files[d.file_path], d))
                
                # 不同文件的重复
                file_defs = list(unique_files.values())
                for i in range(len(file_defs)):
                    for j in range(i + 1, len(file_defs)):
                        duplicates.append((file_defs[i], file_defs[j]))
        
        return duplicates
    
    def analyze_dead_code(self) -> DeadCodeReport:
        """分析死代码"""
        report = DeadCodeReport()
        
        # 收集所有引用的名称
        referenced_names = set()
        for ref in self.references:
            referenced_names.add(ref.name)
        
        # 分析每个定义
        for definition in self.definitions:
            is_special, reason = self.is_special_case(definition)
            
            if is_special:
                # 特殊情况，保留
                report.preserved.append(definition)
            elif definition.name in referenced_names:
                # 被引用，保留
                report.preserved.append(definition)
            elif definition.is_private and definition.name not in referenced_names:
                # 私有方法未被引用，可疑
                report.suspicious.append(definition)
            else:
                # 确认的死代码
                report.confirmed_dead.append(definition)
        
        # 查找重复定义
        report.duplicates = self.find_duplicates()
        
        # 统计信息
        report.statistics = {
            'total_definitions': len(self.definitions),
            'total_references': len(self.references),
            'confirmed_dead': len(report.confirmed_dead),
            'suspicious': len(report.suspicious),
            'preserved': len(report.preserved),
            'duplicates': len(report.duplicates),
            'dead_code_ratio': len(report.confirmed_dead) / len(self.definitions),
            'files_analyzed': len(set(d.file_path for d in self.definitions))
        }
        
        return report
    
    def run_analysis(self) -> DeadCodeReport:
        """运行完整分析"""
        print("🔍 开始死代码检测分析...")
        
        # 第1步：收集定义和引用
        print("📚 收集定义和引用...")
        self.collect_all_definitions_and_references()
        
        # 第2步：构建引用图
        print("🕸️ 构建引用关系图...")
        self.build_reference_graph()
        
        # 第3步：分析死代码
        print("💀 分析死代码...")
        report = self.analyze_dead_code()
        
        print("✅ 分析完成!")
        return report
    
    def generate_report(self, report: DeadCodeReport, output_file: str = None):
        """生成报告"""
        print("\n" + "="*80)
        print("🔍 死代码检测报告")
        print("="*80)
        
        # 统计信息
        stats = report.statistics
        print(f"\n📊 统计信息:")
        print(f"  • 分析文件数: {stats['files_analyzed']}")
        print(f"  • 总定义数: {stats['total_definitions']}")
        print(f"  • 总引用数: {stats['total_references']}")
        print(f"  • 死代码比例: {stats['dead_code_ratio']:.1%}")
        
        # 确认的死代码
        if report.confirmed_dead:
            print(f"\n💀 确认的死代码 ({len(report.confirmed_dead)} 个):")
            for item in report.confirmed_dead:
                print(f"  • {item.type}: {item.name} ({item.file_path}:{item.line_number})")
        
        # 可疑代码
        if report.suspicious:
            print(f"\n⚠️ 可疑代码 ({len(report.suspicious)} 个):")
            for item in report.suspicious:
                print(f"  • {item.type}: {item.name} ({item.file_path}:{item.line_number})")
        
        # 重复定义
        if report.duplicates:
            print(f"\n🔄 重复定义 ({len(report.duplicates)} 对):")
            for item1, item2 in report.duplicates:
                print(f"  • {item1.name}: {item1.file_path}:{item1.line_number} <-> {item2.file_path}:{item2.line_number}")
        
        # 保留的代码（仅显示前10个）
        if report.preserved:
            print(f"\n✅ 保留的代码 (显示前10个，共{len(report.preserved)}个):")
            for item in report.preserved[:10]:
                print(f"  • {item.type}: {item.name} ({item.file_path}:{item.line_number})")
        
        # 输出到文件
        if output_file:
            report_data = {
                'statistics': report.statistics,
                'confirmed_dead': [
                    {
                        'name': d.name,
                        'type': d.type,
                        'file_path': d.file_path,
                        'line_number': d.line_number,
                        'module_name': d.module_name
                    } for d in report.confirmed_dead
                ],
                'suspicious': [
                    {
                        'name': d.name,
                        'type': d.type,
                        'file_path': d.file_path,
                        'line_number': d.line_number,
                        'module_name': d.module_name
                    } for d in report.suspicious
                ],
                'duplicates': [
                    {
                        'name': d1.name,
                        'locations': [
                            {'file': d1.file_path, 'line': d1.line_number},
                            {'file': d2.file_path, 'line': d2.line_number}
                        ]
                    } for d1, d2 in report.duplicates
                ]
            }
            
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, ensure_ascii=False, indent=2)
            print(f"\n📄 详细报告已保存至: {output_file}")


def main():
    """主函数"""
    if len(sys.argv) > 1:
        project_root = sys.argv[1]
    else:
        project_root = "."
    
    detector = DeadCodeDetector(project_root)
    report = detector.run_analysis()
    detector.generate_report(report, "dead_code_report.json")


if __name__ == "__main__":
    main()