"""
标准指纹管理模块

负责管理标准但一致的浏览器指纹池，包括Canvas、WebGL、音频等指纹特征。
采用基于种子的确定性选择算法，确保单会话内一致，跨会话变化。
"""

import hashlib
import logging
from typing import Dict, Any, Optional, List

logger = logging.getLogger(__name__)

class FingerprintManager:
    """标准指纹管理器
    
    提供标准指纹池选择能力，解决固定伪值容易被批量识别的问题。
    采用小规模指纹池降低聚类暴露风险。
    """
    
    def __init__(self, pool_size: int = 6):
        """初始化指纹管理器
        
        Args:
            pool_size: 指纹池大小，默认6个标准指纹
        """
        self.pool_size = pool_size
        self.fingerprint_cache = {}
        self._init_standard_fingerprint_pool()
        
        logger.info(f"指纹管理器初始化完成，池大小: {pool_size}")
    
    def _init_standard_fingerprint_pool(self) -> None:
        """初始化标准指纹池"""
        # 标准Canvas指纹池 - 基于真实设备数据
        self.standard_canvas_pool = [
            '2a4b3c9f8e7d6c5b4a394857362d1c0b',  # Chrome/Windows
            '5f7e9d8c6b5a49384726351d2c1b0a09',  # Chrome/Mac
            '8b7a69584736251d4c3b2a19180f0e06',  # Firefox/Windows
            '1e2f3d4c5b6a79685847362514130201',  # Firefox/Mac
            '4d5c6b7a89786754638251403f2e1d0c',  # Safari/Mac
            '7a8b9c0d1e2f3g4h5i6j7k8l9m0n1o2p'   # Edge/Windows
        ]
        
        # 标准WebGL指纹池
        self.standard_webgl_pool = [
            'ab3f8e7d6c5b4a394857362d1c0b9f8e',  # NVIDIA/Chrome
            'de5f9d8c6b5a49384726351d2c1b0a09',  # AMD/Chrome  
            'gh8b7a69584736251d4c3b2a19180f0e',  # Intel/Chrome
            'jk1e2f3d4c5b6a79685847362514130b',  # NVIDIA/Firefox
            'mn4d5c6b7a89786754638251403f2e1d',  # AMD/Firefox
            'pq7a8b9c0d1e2f3g4h5i6j7k8l9m0n1o'   # Intel/Safari
        ]
        
        # 标准音频指纹池
        self.standard_audio_pool = [
            'audio_44100_512_1d2c3b4a59687453',  # 44.1kHz/512
            'audio_48000_512_2e3d4c5b60798564',  # 48kHz/512
            'audio_44100_1024_3f4e5d6c71809675', # 44.1kHz/1024
            'audio_48000_1024_4g5f6e7d82910786', # 48kHz/1024
            'audio_44100_256_5h6g7f8e93021897',  # 44.1kHz/256
            'audio_48000_2048_6i7h8g9fa4132908'  # 48kHz/2048
        ]
    
    def generate_fingerprint(self, session_seed: str, device_template: Dict[str, Any]) -> Dict[str, Any]:
        """从标准池中选择一致的浏览器指纹
        
        Args:
            session_seed: 会话种子
            device_template: 设备模板配置
            
        Returns:
            标准指纹字典
        """
        # 检查缓存
        cache_key = f"{session_seed}_{hash(str(device_template))}"
        if cache_key in self.fingerprint_cache:
            logger.debug(f"从缓存获取指纹: {cache_key[:16]}...")
            return self.fingerprint_cache[cache_key]
        
        # 从标准池中选择指纹
        fingerprint = self._select_from_standard_pool(session_seed, device_template)
        
        # 缓存结果
        self.fingerprint_cache[cache_key] = fingerprint
        
        logger.info(f"选择标准指纹成功，种子: {session_seed[:8]}...")
        return fingerprint
    
    def _select_from_standard_pool(self, session_seed: str, device_template: Dict[str, Any]) -> Dict[str, Any]:
        """从标准池中选择指纹
        
        Args:
            session_seed: 会话种子
            device_template: 设备模板
            
        Returns:
            选择的指纹字典
        """
        # 基于会话种子确定性选择索引
        seed_hash = hashlib.sha256(session_seed.encode()).hexdigest()
        pool_index = int(seed_hash[:8], 16) % self.pool_size
        
        # 从各个标准池中选择
        canvas_hash = self.standard_canvas_pool[pool_index]
        webgl_hash = self.standard_webgl_pool[pool_index]
        audio_hash = self.standard_audio_pool[pool_index]
        
        # 构造完整指纹
        fingerprint = {
            'canvas': {'hash': canvas_hash},
            'webgl': {
                'vendor': device_template.get('webgl_vendor', 'Google Inc.'),
                'renderer': device_template.get('webgl_renderer', 'ANGLE (Generic GPU)'),
                'hash': webgl_hash
            },
            'audio': {'hash': audio_hash},
            'fonts': self._get_standard_fonts(device_template),
            'navigator': self._generate_navigator_fingerprint(device_template),
            'screen': device_template.get('screen', {}),
            'timezone': device_template.get('timezone', 'America/New_York'),
            'language': device_template.get('languages', ['en-US'])[0],
            'platform': device_template.get('platform', 'Win32')
        }
        
        return fingerprint
    
    def _get_standard_fonts(self, device_template: Dict[str, Any]) -> List[str]:
        """获取标准字体列表
        
        Args:
            device_template: 设备模板
            
        Returns:
            标准字体列表
        """
        platform = device_template.get('platform', 'Win32')
        
        if platform == 'Win32':
            return [
                'Arial', 'Helvetica', 'Times New Roman', 'Courier New', 'Verdana',
                'Georgia', 'Tahoma', 'Trebuchet MS', 'Segoe UI', 'Calibri'
            ]
        elif platform == 'MacIntel':
            return [
                'Arial', 'Helvetica', 'Times', 'Courier', 'Verdana',
                'Georgia', 'Helvetica Neue', 'San Francisco', 'Lucida Grande', 'Menlo'
            ]
        else:
            return [
                'Arial', 'Helvetica', 'Times', 'Courier', 'Verdana',
                'Georgia', 'Liberation Sans', 'DejaVu Sans', 'Ubuntu', 'Roboto'
            ]
    
    def _generate_navigator_fingerprint(self, device_template: Dict[str, Any]) -> Dict[str, Any]:
        """生成Navigator对象指纹
        
        Args:
            device_template: 设备模板
            
        Returns:
            Navigator指纹数据
        """
        return {
            'user_agent': device_template.get('ua', ''),
            'platform': device_template.get('platform', 'Win32'),
            'language': device_template.get('languages', ['en-US'])[0],
            'languages': device_template.get('languages', ['en-US']),
            'cookie_enabled': True,
            'do_not_track': None,
            'hardware_concurrency': device_template.get('cpu_cores', 8),
            'device_memory': device_template.get('memory', 8),
            'max_touch_points': 0 if not device_template.get('touch_support', False) else 10
        }
    
    def create_stealth_script(self, fingerprint: Dict[str, Any]) -> str:
        """创建stealth注入脚本
        
        Args:
            fingerprint: 指纹数据
            
        Returns:
            JavaScript注入脚本
        """
        import json
        
        script_template = '''
        // 标准指纹注入脚本
        (function() {
            'use strict';
            
            const fingerprint = ''' + json.dumps(fingerprint) + ''';
            
            // Canvas指纹伪装
            const originalGetContext = HTMLCanvasElement.prototype.getContext;
            HTMLCanvasElement.prototype.getContext = function(type, ...args) {
                const context = originalGetContext.call(this, type, ...args);
                if (type === '2d' && fingerprint.canvas) {
                    const originalToDataURL = this.toDataURL;
                    this.toDataURL = function(...args) {
                        const dataURL = originalToDataURL.apply(this, args);
                        // 注入一致的指纹哈希
                        return dataURL.replace(/data:image\/png;base64,/, 
                               'data:image/png;base64,' + fingerprint.canvas.hash.substring(0, 8));
                    };
                }
                return context;
            };
            
            // WebGL指纹伪装
            const originalGetParameter = WebGLRenderingContext.prototype.getParameter;
            WebGLRenderingContext.prototype.getParameter = function(parameter) {
                if (parameter === 0x1F00) { // VENDOR
                    return fingerprint.webgl?.vendor || 'Google Inc.';
                }
                if (parameter === 0x1F01) { // RENDERER
                    return fingerprint.webgl?.renderer || 'ANGLE (Generic GPU)';
                }
                return originalGetParameter.call(this, parameter);
            };
            
            // 屏幕信息伪装
            if (fingerprint.screen) {
                Object.defineProperty(screen, 'width', {
                    get: () => fingerprint.screen.width || 1920
                });
                Object.defineProperty(screen, 'height', {
                    get: () => fingerprint.screen.height || 1080
                });
                Object.defineProperty(screen, 'colorDepth', {
                    get: () => fingerprint.screen.color_depth || 24
                });
            }
            
            // Navigator信息伪装
            if (fingerprint.navigator) {
                Object.defineProperty(navigator, 'hardwareConcurrency', {
                    get: () => fingerprint.navigator.hardware_concurrency || 8
                });
                Object.defineProperty(navigator, 'deviceMemory', {
                    get: () => fingerprint.navigator.device_memory || 8
                });
            }
            
        })();
        '''
        
        return script_template
    
    def validate_fingerprint_consistency(self, fingerprint: Dict[str, Any]) -> Dict[str, bool]:
        """验证指纹一致性
        
        Args:
            fingerprint: 指纹数据
            
        Returns:
            验证结果字典
        """
        validation_results = {
            'canvas_hash_valid': bool(fingerprint.get('canvas', {}).get('hash')),
            'webgl_vendor_consistent': bool(fingerprint.get('webgl', {}).get('vendor')),
            'screen_dimensions_reasonable': True,
            'font_list_not_empty': bool(fingerprint.get('fonts')),
            'navigator_consistent': bool(fingerprint.get('navigator', {}).get('user_agent'))
        }
        
        # 检查屏幕尺寸合理性
        screen = fingerprint.get('screen', {})
        if screen:
            width = screen.get('width', 0)
            height = screen.get('height', 0)
            if width < 800 or width > 4000 or height < 600 or height > 3000:
                validation_results['screen_dimensions_reasonable'] = False
        
        return validation_results