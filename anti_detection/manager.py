"""
反检测系统管理器

统一管理反检测系统的所有模块，提供简单的集成接口。
"""

import asyncio
import logging
import time
from typing import Dict, Any, Optional
from playwright.async_api import Page

from .fingerprint_manager import FingerprintManager
from .behavior_simulator import BehaviorSimulator
from .session_state import SessionState
from .device_templates import DeviceTemplates
from .analytics import AntiDetectionAnalytics
from .feature_flags import FeatureFlags, ABTestManager

logger = logging.getLogger(__name__)

class AntiDetectionManager:
    """反检测系统管理器
    
    统一管理所有反检测模块，提供简单的集成接口和自动化管理功能。
    """
    
    def __init__(self, 
                 enable_analytics: bool = True,
                 enable_feature_flags: bool = True,
                 config_dir: str = "anti_detection"):
        """初始化反检测系统管理器
        
        Args:
            enable_analytics: 是否启用数据分析
            enable_feature_flags: 是否启用特性开关
            config_dir: 配置目录
        """
        self.config_dir = config_dir
        self.enable_analytics = enable_analytics
        self.enable_feature_flags = enable_feature_flags
        
        # 初始化核心模块
        self.device_templates = DeviceTemplates()
        self.fingerprint_manager = FingerprintManager(pool_size=6)
        self.behavior_simulator = BehaviorSimulator()
        self.session_state = SessionState()
        
        # 可选模块
        self.analytics = None
        self.feature_flags = None
        self.ab_test_manager = None
        
        if self.enable_analytics:
            self.analytics = AntiDetectionAnalytics(
                data_dir=f"{config_dir}/analytics"
            )
        
        if self.enable_feature_flags:
            self.feature_flags = FeatureFlags(
                config_file=f"{config_dir}/feature_flags_simple.json"
            )
            # 移除复杂的A/B测试管理器，仅保留应急开关
            self.ab_test_manager = None
        
        # 当前特性上下文
        self.feature_context: Dict[str, bool] = {}
        self.current_fingerprint: Optional[Dict[str, Any]] = None
        self.current_device_template: Optional[Dict[str, Any]] = None
        
        logger.info("反检测系统管理器初始化完成")
    
    def initialize_session(self) -> Dict[str, Any]:
        """初始化新会话
        
        Returns:
            会话初始化信息
        """
        # 检查是否需要轮换会话
        if self.session_state.should_rotate_session():
            old_session_id = self.session_state.session_id
            new_session_id = self.session_state.rotate_session()
            logger.info(f"会话已轮换: {old_session_id[:8]}... -> {new_session_id[:8]}...")
        
        session_id = self.session_state.session_id
        session_seed = self.session_state.get_session_seed()
        
        # 更新特性上下文
        if self.feature_flags:
            self.feature_context = self.feature_flags.create_feature_context(session_id)
            
            # 更新行为模拟器启用率
            if 'smart_typing' in self.feature_context:
                feature_status = self.feature_flags.get_feature_status('smart_typing')
                if feature_status:
                    self.behavior_simulator.enabled_ratio = feature_status['enabled_ratio']
        
        # 选择设备模板
        self.current_device_template = self.device_templates.select_consistent_template(session_seed)
        
        # 生成标准指纹（如果启用指纹管理）
        if self.feature_context.get('fingerprint_enabled', True):
            try:
                self.current_fingerprint = self.fingerprint_manager.generate_fingerprint(
                    session_seed, self.current_device_template
                )
                self.session_state.set_fingerprint_data(self.current_fingerprint)
                logger.info("使用标准指纹池")
            except Exception as e:
                logger.warning(f"标准指纹选择失败，跳过指纹伪装: {e}")
                self.current_fingerprint = None
        else:
            logger.info("指纹管理已禁用")
            self.current_fingerprint = None
        
        # 设置行为参数
        behavior_params = self.behavior_simulator.get_behavior_stats()
        self.session_state.set_behavior_params(behavior_params)
        
        session_info = {
            'session_id': session_id,
            'session_seed': session_seed,
            'device_template': self.current_device_template['name'],
            'feature_context': self.feature_context,
            'fingerprint_enabled': self.current_fingerprint is not None,
            'smart_typing_enabled': self.feature_context.get('smart_typing', True),
            'adaptive_interaction_enabled': self.feature_context.get('adaptive_interaction', True)
        }
        
        logger.info(f"会话初始化完成: {session_id[:8]}... "
                   f"特性启用: {sum(self.feature_context.values())}/{len(self.feature_context)}")
        
        return session_info
    
    async def configure_page(self, page: Page) -> bool:
        """配置页面的反检测设置
        
        Args:
            page: Playwright页面对象
            
        Returns:
            配置是否成功
        """
        try:
            # 应用设备模板配置
            if self.current_device_template:
                playwright_config = self.device_templates.create_playwright_config(
                    self.current_device_template
                )
                
                # 设置视口
                await page.set_viewport_size({
                    'width': playwright_config['viewport']['width'],
                    'height': playwright_config['viewport']['height']
                })
                
                # 设置地理位置和时区（如果需要）
                if playwright_config.get('timezone_id'):
                    await page.context.set_geolocation({
                        'latitude': 40.7128,  # 纽约默认坐标
                        'longitude': -74.0060
                    })
            
            # 注入标准指纹脚本（如果启用）
            if self.current_fingerprint:
                stealth_script = self.fingerprint_manager.create_stealth_script(self.current_fingerprint)
                await page.add_init_script(stealth_script)
                logger.debug("注入标准指纹脚本")
            
            # 注入传统stealth脚本作为补充
            await self._inject_legacy_stealth(page)
            
            logger.info("页面反检测配置完成")
            return True
            
        except Exception as e:
            logger.error(f"页面配置失败: {e}")
            return False
    
    async def _inject_legacy_stealth(self, page: Page) -> None:
        """注入传统stealth脚本作为补充
        
        Args:
            page: Playwright页面对象
        """
        # 基础的navigator补丁
        legacy_stealth_script = """
        // 基础stealth补丁
        (function() {
            'use strict';
            
            // 隐藏webdriver属性
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
            });
            
            // 修复chrome对象
            if (!window.chrome) {
                window.chrome = {
                    runtime: {},
                };
            }
            
            // 修复permissions查询
            const originalQuery = window.navigator.permissions.query;
            window.navigator.permissions.query = (parameters) => (
                parameters.name === 'notifications' ?
                    Promise.resolve({ state: Notification.permission }) :
                    originalQuery(parameters)
            );
            
            // 修复插件检测
            Object.defineProperty(navigator, 'plugins', {
                get: () => [1, 2, 3, 4, 5]
            });
            
        })();
        """
        
        await page.add_init_script(legacy_stealth_script)
    
    async def simulate_typing(self, page: Page, selector: str, text: str) -> bool:
        """模拟智能打字
        
        Args:
            page: Playwright页面对象
            selector: 输入框选择器
            text: 要输入的文本
            
        Returns:
            操作是否成功
        """
        try:
            start_time = time.time()
            
            await self.behavior_simulator.simulate_typing(
                page, selector, text, self.session_state.session_id
            )
            
            typing_time = time.time() - start_time
            
            # 记录打字统计
            if self.analytics:
                typing_stats = {
                    'text_length': len(text),
                    'typing_time': typing_time,
                    'wpm': (len(text.split()) / typing_time * 60) if typing_time > 0 else 0,
                    'enabled': self.feature_context.get('smart_typing', False)
                }
                
                # 更新会话行为参数
                current_params = self.session_state.get_behavior_params() or {}
                current_params.update({
                    'last_typing_stats': typing_stats,
                    'typing_interactions': current_params.get('typing_interactions', 0) + 1
                })
                self.session_state.set_behavior_params(current_params)
            
            logger.debug(f"打字模拟完成: {len(text)} 字符, 用时 {typing_time:.1f}s")
            return True
            
        except Exception as e:
            logger.error(f"打字模拟失败: {e}")
            return False
    
    async def simulate_page_interaction(self, page: Page) -> bool:
        """模拟自适应页面交互
        
        Args:
            page: Playwright页面对象
            
        Returns:
            操作是否成功
        """
        try:
            start_time = time.time()
            
            await self.behavior_simulator.simulate_adaptive_page_interaction(
                page, self.session_state.session_id
            )
            
            interaction_time = time.time() - start_time
            
            # 记录交互统计
            if self.analytics:
                interaction_stats = {
                    'interaction_time': interaction_time,
                    'enabled': self.feature_context.get('adaptive_interaction', False)
                }
                
                # 更新会话行为参数
                current_params = self.session_state.get_behavior_params() or {}
                current_params.update({
                    'last_interaction_stats': interaction_stats,
                    'page_interactions': current_params.get('page_interactions', 0) + 1
                })
                self.session_state.set_behavior_params(current_params)
            
            logger.debug(f"页面交互模拟完成，用时 {interaction_time:.1f}s")
            return True
            
        except Exception as e:
            logger.error(f"页面交互模拟失败: {e}")
            return False
    
    def record_attempt_result(self, success: bool, failure_reason: Optional[str] = None,
                            performance_metrics: Optional[Dict[str, float]] = None) -> None:
        """记录尝试结果用于分析
        
        Args:
            success: 是否成功
            failure_reason: 失败原因
            performance_metrics: 性能指标
        """
        if not self.analytics:
            return
        
        try:
            # 收集指纹数据
            fingerprint_data = self.current_fingerprint or {}
            
            # 收集行为数据
            behavior_params = self.session_state.get_behavior_params() or {}
            behavior_data = {
                'type': 'integrated_system',
                'feature_context': self.feature_context,
                'typing_stats': behavior_params.get('last_typing_stats', {}),
                'interaction_stats': behavior_params.get('last_interaction_stats', {}),
                'typing_interactions': behavior_params.get('typing_interactions', 0),
                'page_interactions': behavior_params.get('page_interactions', 0)
            }
            
            # 记录到分析系统
            self.analytics.record_attempt(
                session_id=self.session_state.session_id,
                fingerprint_data=fingerprint_data,
                behavior_data=behavior_data,
                success=success,
                failure_reason=failure_reason,
                performance_metrics=performance_metrics
            )
            
            # 检查是否需要自动回滚
            if self.feature_flags and not success:
                self._check_auto_rollback()
            
            logger.debug(f"记录尝试结果: 成功={success}")
            
        except Exception as e:
            logger.error(f"记录尝试结果失败: {e}")
    
    def _check_auto_rollback(self) -> None:
        """检查是否需要自动回滚特性"""
        if not self.analytics or not self.feature_flags:
            return
        
        try:
            # 分析最近1小时的成功率
            analysis_result = self.analytics.analyze_success_factors(recent_hours=1)
            
            if 'success_rate' in analysis_result:
                success_rate = analysis_result['success_rate']
                
                # 检查核心特性是否需要应急回滚
                for feature_name in ['fingerprint_enabled', 'smart_typing', 'adaptive_interaction']:
                    if self.feature_context.get(feature_name, True):
                        if self.feature_flags.auto_rollback_if_needed(feature_name, success_rate):
                            logger.warning(f"特性 {feature_name} 已应急回滚")
                            # 更新特性上下文
                            self.feature_context[feature_name] = False
                            
        except Exception as e:
            logger.error(f"自动回滚检查失败: {e}")
    
    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态信息
        
        Returns:
            系统状态字典
        """
        status = {
            'session_info': self.session_state.get_session_info(),
            'feature_context': self.feature_context,
            'device_template': self.current_device_template['name'] if self.current_device_template else None,
            'fingerprint_enabled': self.current_fingerprint is not None,
            'analytics_enabled': self.analytics is not None,
            'feature_flags_enabled': self.feature_flags is not None
        }
        
        # 添加特性状态详情
        if self.feature_flags:
            status['feature_details'] = self.feature_flags.get_all_features()
        
        # 添加分析摘要
        if self.analytics:
            try:
                analysis_result = self.analytics.analyze_success_factors(recent_hours=24)
                status['analytics_summary'] = {
                    'success_rate_24h': analysis_result.get('success_rate', 0),
                    'total_attempts_24h': analysis_result.get('total_attempts', 0)
                }
            except:
                status['analytics_summary'] = {'error': 'analysis_failed'}
        
        return status
    
    def generate_optimization_report(self) -> Optional[Dict[str, Any]]:
        """生成优化报告
        
        Returns:
            优化报告字典
        """
        if not self.analytics:
            return None
        
        try:
            # 分析最近7天的数据
            analysis_result = self.analytics.analyze_success_factors(recent_hours=24 * 7)
            recommendations = self.analytics.generate_optimization_recommendations(analysis_result)
            
            return {
                'analysis_period': '7_days',
                'analysis_result': analysis_result,
                'recommendations': recommendations,
                'system_status': self.get_system_status(),
                'generated_at': time.time()
            }
            
        except Exception as e:
            logger.error(f"生成优化报告失败: {e}")
            return {'error': str(e)}
    
    async def cleanup(self) -> None:
        """清理资源"""
        logger.info("反检测系统管理器清理完成")
    
    def __enter__(self):
        """上下文管理器入口"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        asyncio.create_task(self.cleanup())