"""
反检测系统核心模块

提供企业级浏览器自动化反检测能力，包括：
- 动态指纹生成与管理
- 智能行为模拟
- 会话状态一致性
- 数据驱动的优化分析

版本: v1.0.0
作者: Account Registrar Team
"""

from .fingerprint_manager import FingerprintManager
from .behavior_simulator import BehaviorSimulator
from .session_state import SessionState
from .device_templates import DeviceTemplates
from .analytics import AntiDetectionAnalytics
from .feature_flags import FeatureFlags, ABTestManager
from .manager import AntiDetectionManager

__version__ = "1.0.0"
__all__ = [
    "FingerprintManager",
    "BehaviorSimulator", 
    "SessionState",
    "DeviceTemplates",
    "AntiDetectionAnalytics",
    "FeatureFlags",
    "ABTestManager",
    "AntiDetectionManager"
]