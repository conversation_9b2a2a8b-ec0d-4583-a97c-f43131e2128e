"""
反检测分析模块

提供数据驱动的反检测效果分析和优化建议，包括：
- 实验数据收集和存储
- 成功因子分析
- 自动优化建议生成
- A/B测试结果评估
"""

import json
import time
import logging
from typing import Dict, Any, List, Optional, Tuple
from pathlib import Path
from dataclasses import dataclass, asdict
from datetime import datetime

# 可选依赖
try:
    import pandas as pd
    import numpy as np
    PANDAS_AVAILABLE = True
except ImportError:
    PANDAS_AVAILABLE = False
    pd = None
    np = None

logger = logging.getLogger(__name__)

@dataclass
class DetectionAttempt:
    """检测尝试记录"""
    timestamp: float
    session_id: str
    fingerprint_hash: str
    behavior_type: str
    success: bool
    failure_reason: Optional[str] = None
    response_time_ms: float = 0.0
    lcp_time_ms: float = 0.0
    typing_errors: int = 0
    page_interactions: int = 0
    
    # 指纹一致性指标
    canvas_consistency: float = 1.0
    webgl_consistency: float = 1.0
    ua_consistency: float = 1.0
    
    # 行为自然度指标
    typing_naturalness: float = 1.0
    interaction_naturalness: float = 1.0
    timing_naturalness: float = 1.0

class AntiDetectionAnalytics:
    """反检测分析器
    
    收集和分析反检测系统的效果数据，提供数据驱动的优化建议。
    """
    
    def __init__(self, data_dir: str = "logs/analytics"):
        """初始化分析器
        
        Args:
            data_dir: 数据存储目录
        """
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(parents=True, exist_ok=True)
        
        self.data_file = self.data_dir / "detection_attempts.jsonl"
        self.summary_file = self.data_dir / "analytics_summary.json"
        
        self.experiment_data: List[DetectionAttempt] = []
        self.load_existing_data()
        
        logger.info(f"反检测分析器初始化，数据目录: {data_dir}")
    
    def load_existing_data(self) -> None:
        """加载现有的实验数据"""
        if not self.data_file.exists():
            logger.info("未找到历史数据文件，从空白开始")
            return
        
        try:
            with open(self.data_file, 'r') as f:
                for line in f:
                    if line.strip():
                        data = json.loads(line.strip())
                        attempt = DetectionAttempt(**data)
                        self.experiment_data.append(attempt)
            
            logger.info(f"加载历史数据 {len(self.experiment_data)} 条记录")
            
        except Exception as e:
            logger.error(f"加载历史数据失败: {e}")
            self.experiment_data = []
    
    def record_attempt(self, 
                      session_id: str,
                      fingerprint_data: Dict[str, Any],
                      behavior_data: Dict[str, Any],
                      success: bool,
                      failure_reason: Optional[str] = None,
                      performance_metrics: Optional[Dict[str, float]] = None) -> None:
        """记录一次检测尝试
        
        Args:
            session_id: 会话ID
            fingerprint_data: 指纹数据
            behavior_data: 行为数据
            success: 是否成功
            failure_reason: 失败原因
            performance_metrics: 性能指标
        """
        # 生成指纹哈希
        fingerprint_hash = self._generate_fingerprint_hash(fingerprint_data)
        behavior_type = behavior_data.get('type', 'unknown')
        
        # 计算一致性和自然度指标
        consistency_scores = self._calculate_consistency_scores(fingerprint_data)
        naturalness_scores = self._calculate_naturalness_scores(behavior_data)
        
        # 创建记录
        attempt = DetectionAttempt(
            timestamp=time.time(),
            session_id=session_id,
            fingerprint_hash=fingerprint_hash,
            behavior_type=behavior_type,
            success=success,
            failure_reason=failure_reason,
            response_time_ms=performance_metrics.get('response_time_ms', 0.0) if performance_metrics else 0.0,
            lcp_time_ms=performance_metrics.get('lcp_time_ms', 0.0) if performance_metrics else 0.0,
            typing_errors=behavior_data.get('typing_errors', 0),
            page_interactions=behavior_data.get('page_interactions', 0),
            **consistency_scores,
            **naturalness_scores
        )
        
        # 添加到内存和持久化
        self.experiment_data.append(attempt)
        self._append_to_file(attempt)
        
        logger.debug(f"记录检测尝试: {session_id[:8]}... 成功={success}")
    
    def _generate_fingerprint_hash(self, fingerprint_data: Dict[str, Any]) -> str:
        """生成指纹哈希标识
        
        Args:
            fingerprint_data: 指纹数据
            
        Returns:
            指纹哈希字符串
        """
        key_features = [
            str(fingerprint_data.get('canvas', {}).get('hash', '')),
            str(fingerprint_data.get('webgl', {}).get('hash', '')),
            str(fingerprint_data.get('navigator', {}).get('user_agent', ''))[:50]
        ]
        
        combined = "_".join(key_features)
        return str(hash(combined))[:16]
    
    def _calculate_consistency_scores(self, fingerprint_data: Dict[str, Any]) -> Dict[str, float]:
        """计算指纹一致性得分
        
        Args:
            fingerprint_data: 指纹数据
            
        Returns:
            一致性得分字典
        """
        scores = {
            'canvas_consistency': 1.0,
            'webgl_consistency': 1.0,
            'ua_consistency': 1.0
        }
        
        # Canvas一致性检查
        canvas_data = fingerprint_data.get('canvas', {})
        if not canvas_data.get('hash'):
            scores['canvas_consistency'] = 0.0
        elif canvas_data.get('hash') == 'legacy_canvas_hash_fixed':
            scores['canvas_consistency'] = 0.5  # 固定值降分
        
        # WebGL一致性检查
        webgl_data = fingerprint_data.get('webgl', {})
        if not webgl_data.get('vendor') or not webgl_data.get('renderer'):
            scores['webgl_consistency'] = 0.0
        
        # UA一致性检查
        navigator_data = fingerprint_data.get('navigator', {})
        ua = navigator_data.get('user_agent', '')
        platform = navigator_data.get('platform', '')
        
        if not ua:
            scores['ua_consistency'] = 0.0
        elif ('Windows' in ua and platform != 'Win32') or ('Macintosh' in ua and platform != 'MacIntel'):
            scores['ua_consistency'] = 0.3  # 不匹配严重降分
        
        return scores
    
    def _calculate_naturalness_scores(self, behavior_data: Dict[str, Any]) -> Dict[str, float]:
        """计算行为自然度得分
        
        Args:
            behavior_data: 行为数据
            
        Returns:
            自然度得分字典
        """
        scores = {
            'typing_naturalness': 1.0,
            'interaction_naturalness': 1.0,
            'timing_naturalness': 1.0
        }
        
        # 打字自然度
        typing_stats = behavior_data.get('typing_stats', {})
        if typing_stats:
            wpm = typing_stats.get('wpm', 45)
            error_rate = typing_stats.get('error_rate', 0.03)
            
            # WPM合理性 (20-80 WPM为正常范围)
            if 20 <= wpm <= 80:
                scores['typing_naturalness'] *= 1.0
            elif wpm < 20 or wpm > 120:
                scores['typing_naturalness'] *= 0.3
            else:
                scores['typing_naturalness'] *= 0.8
            
            # 错误率合理性 (1%-10%为正常范围)
            if 0.01 <= error_rate <= 0.10:
                scores['typing_naturalness'] *= 1.0
            elif error_rate == 0:
                scores['typing_naturalness'] *= 0.2  # 完全无错误不自然
            else:
                scores['typing_naturalness'] *= 0.6
        
        # 交互自然度
        interaction_stats = behavior_data.get('interaction_stats', {})
        if interaction_stats:
            scroll_count = interaction_stats.get('scroll_count', 0)
            hover_count = interaction_stats.get('hover_count', 0)
            
            # 交互频率合理性
            if scroll_count > 0 and hover_count > 0:
                scores['interaction_naturalness'] *= 1.0
            elif scroll_count == 0 and hover_count == 0:
                scores['interaction_naturalness'] *= 0.4  # 无交互不自然
            else:
                scores['interaction_naturalness'] *= 0.8
        
        # 时间自然度
        timing_stats = behavior_data.get('timing_stats', {})
        if timing_stats:
            page_load_wait = timing_stats.get('page_load_wait', 0)
            total_time = timing_stats.get('total_time', 0)
            
            # 时间分配合理性
            if page_load_wait > 0 and total_time > page_load_wait:
                scores['timing_naturalness'] *= 1.0
            elif page_load_wait == 0:
                scores['timing_naturalness'] *= 0.6  # 无等待时间不自然
            else:
                scores['timing_naturalness'] *= 0.8
        
        return scores
    
    def _append_to_file(self, attempt: DetectionAttempt) -> None:
        """追加记录到文件
        
        Args:
            attempt: 检测尝试记录
        """
        try:
            with open(self.data_file, 'a') as f:
                json_line = json.dumps(asdict(attempt), ensure_ascii=False)
                f.write(json_line + '\n')
        except Exception as e:
            logger.error(f"写入数据文件失败: {e}")
    
    def analyze_success_factors(self, recent_hours: int = 24) -> Dict[str, Any]:
        """分析成功因子
        
        Args:
            recent_hours: 分析最近N小时的数据
            
        Returns:
            成功因子分析结果
        """
        if not self.experiment_data:
            logger.warning("无可分析数据")
            return {'error': 'no_data'}
        
        # 筛选最近数据
        cutoff_time = time.time() - (recent_hours * 3600)
        recent_data = [
            attempt for attempt in self.experiment_data 
            if attempt.timestamp >= cutoff_time
        ]
        
        if not recent_data:
            logger.warning(f"最近{recent_hours}小时无数据")
            return {'error': 'no_recent_data'}
        
        # 转换为DataFrame进行分析（如果pandas可用）
        if not PANDAS_AVAILABLE:
            logger.warning("pandas不可用，无法进行深度分析")
            return {'error': 'pandas_not_available'}
            
        df = pd.DataFrame([asdict(attempt) for attempt in recent_data])
        
        # 基础统计
        total_attempts = len(df)
        success_rate = df['success'].mean()
        
        # 特征重要性分析
        feature_importance = self._calculate_feature_importance(df)
        
        # 失败原因分析
        failure_analysis = self._analyze_failure_reasons(df)
        
        # 性能分析
        performance_analysis = self._analyze_performance_metrics(df)
        
        analysis_result = {
            'period_hours': recent_hours,
            'total_attempts': total_attempts,
            'success_rate': float(success_rate),
            'feature_importance': feature_importance,
            'failure_analysis': failure_analysis,
            'performance_analysis': performance_analysis,
            'timestamp': time.time()
        }
        
        # 保存分析结果
        self._save_analysis_summary(analysis_result)
        
        logger.info(f"完成成功因子分析: {total_attempts}次尝试, 成功率{success_rate:.1%}")
        return analysis_result
    
    def _calculate_feature_importance(self, df) -> Dict[str, float]:
        """计算特征重要性
        
        Args:
            df: 数据DataFrame
            
        Returns:
            特征重要性字典
        """
        feature_cols = [
            'canvas_consistency', 'webgl_consistency', 'ua_consistency',
            'typing_naturalness', 'interaction_naturalness', 'timing_naturalness',
            'response_time_ms', 'lcp_time_ms'
        ]
        
        importance_scores = {}
        
        for feature in feature_cols:
            if feature not in df.columns:
                continue
                
            try:
                # 使用相关系数作为重要性指标
                correlation = df[feature].corr(df['success'].astype(int))
                importance_scores[feature] = abs(correlation) if not pd.isna(correlation) else 0.0
                
            except Exception as e:
                logger.warning(f"计算特征{feature}重要性失败: {e}")
                importance_scores[feature] = 0.0
        
        return importance_scores
    
    def _analyze_failure_reasons(self, df) -> Dict[str, Any]:
        """分析失败原因
        
        Args:
            df: 数据DataFrame
            
        Returns:
            失败原因分析结果
        """
        failed_df = df[df['success'] == False]
        
        if failed_df.empty:
            return {'total_failures': 0, 'reasons': {}}
        
        # 失败原因统计
        reason_counts = failed_df['failure_reason'].value_counts().to_dict()
        
        # 失败趋势分析
        failed_df['hour'] = pd.to_datetime(failed_df['timestamp'], unit='s').dt.hour
        failure_by_hour = failed_df.groupby('hour').size().to_dict()
        
        return {
            'total_failures': len(failed_df),
            'failure_rate': float(len(failed_df) / len(df)),
            'reasons': {str(k): int(v) for k, v in reason_counts.items()},
            'failure_by_hour': {str(k): int(v) for k, v in failure_by_hour.items()}
        }
    
    def _analyze_performance_metrics(self, df) -> Dict[str, Any]:
        """分析性能指标
        
        Args:
            df: 数据DataFrame
            
        Returns:
            性能分析结果
        """
        metrics = {}
        
        # 响应时间分析
        if 'response_time_ms' in df.columns:
            response_times = df[df['response_time_ms'] > 0]['response_time_ms']
            if not response_times.empty:
                metrics['response_time'] = {
                    'mean': float(response_times.mean()),
                    'median': float(response_times.median()),
                    'p95': float(response_times.quantile(0.95)),
                    'max': float(response_times.max())
                }
        
        # LCP时间分析
        if 'lcp_time_ms' in df.columns:
            lcp_times = df[df['lcp_time_ms'] > 0]['lcp_time_ms']
            if not lcp_times.empty:
                metrics['lcp_time'] = {
                    'mean': float(lcp_times.mean()),
                    'median': float(lcp_times.median()),
                    'p95': float(lcp_times.quantile(0.95)),
                    'fast_count': int((lcp_times < 1500).sum()),
                    'slow_count': int((lcp_times > 3000).sum())
                }
        
        # 成功率vs性能相关性
        successful_df = df[df['success'] == True]
        failed_df = df[df['success'] == False]
        
        if not successful_df.empty and not failed_df.empty:
            metrics['success_performance_correlation'] = {
                'successful_avg_response': float(successful_df['response_time_ms'].mean()) if 'response_time_ms' in successful_df.columns else 0,
                'failed_avg_response': float(failed_df['response_time_ms'].mean()) if 'response_time_ms' in failed_df.columns else 0
            }
        
        return metrics
    
    def generate_optimization_recommendations(self, analysis_result: Optional[Dict[str, Any]] = None) -> Dict[str, List[str]]:
        """生成优化建议
        
        Args:
            analysis_result: 分析结果，如果为None则使用最新分析
            
        Returns:
            优化建议字典
        """
        if analysis_result is None:
            analysis_result = self.analyze_success_factors()
        
        if 'error' in analysis_result:
            return {'error': ['无足够数据生成建议']}
        
        recommendations = {
            'critical_fixes': [],
            'improvement_areas': [],
            'low_priority': [],
            'performance_optimizations': []
        }
        
        # 基于特征重要性生成建议
        feature_importance = analysis_result.get('feature_importance', {})
        success_rate = analysis_result.get('success_rate', 0)
        
        # 成功率太低的关键修复
        if success_rate < 0.8:
            recommendations['critical_fixes'].append('整体成功率过低，需要全面检查反检测策略')
        
        # 基于特征重要性的具体建议
        for feature, importance in feature_importance.items():
            if importance > 0.7:  # 高重要性特征
                if 'canvas' in feature:
                    recommendations['critical_fixes'].append('Canvas指纹一致性问题严重，需要优化动态指纹生成算法')
                elif 'webgl' in feature:
                    recommendations['critical_fixes'].append('WebGL指纹检测失效，需要检查设备模板配置')
                elif 'ua' in feature:
                    recommendations['critical_fixes'].append('User-Agent不一致问题，需要更新设备模板匹配逻辑')
                elif 'typing' in feature:
                    recommendations['critical_fixes'].append('打字行为模拟不自然，需要调整错误率和速度参数')
                    
            elif 0.4 < importance <= 0.7:  # 中等重要性
                if 'interaction' in feature:
                    recommendations['improvement_areas'].append('页面交互行为需要优化，增加自然度')
                elif 'timing' in feature:
                    recommendations['improvement_areas'].append('时间控制策略需要改进，基于页面性能调整')
                elif 'response_time' in feature:
                    recommendations['performance_optimizations'].append('响应时间影响检测结果，需要性能优化')
                    
            elif importance <= 0.4:  # 低重要性
                recommendations['low_priority'].append(f'{feature}对成功率影响较小，可作为后续优化项')
        
        # 基于失败原因的建议
        failure_analysis = analysis_result.get('failure_analysis', {})
        failure_reasons = failure_analysis.get('reasons', {})
        
        for reason, count in failure_reasons.items():
            if count > 5:  # 频繁失败原因
                if 'captcha' in reason.lower():
                    recommendations['critical_fixes'].append('验证码检测频繁，需要优化行为模拟策略')
                elif 'timeout' in reason.lower():
                    recommendations['performance_optimizations'].append('超时问题频繁，需要优化性能和稳定性')
                elif 'fingerprint' in reason.lower():
                    recommendations['critical_fixes'].append('指纹相关检测失败，需要检查指纹生成逻辑')
        
        # 性能优化建议
        performance_analysis = analysis_result.get('performance_analysis', {})
        response_time = performance_analysis.get('response_time', {})
        
        if response_time.get('p95', 0) > 10000:  # P95超过10秒
            recommendations['performance_optimizations'].append('响应时间P95过高，需要优化系统性能')
        
        logger.info(f"生成优化建议: {len(recommendations['critical_fixes'])}项关键修复")
        return recommendations
    
    def _save_analysis_summary(self, analysis_result: Dict[str, Any]) -> None:
        """保存分析摘要
        
        Args:
            analysis_result: 分析结果
        """
        try:
            summary_data = {
                'last_updated': datetime.now().isoformat(),
                'latest_analysis': analysis_result,
                'data_file': str(self.data_file),
                'total_records': len(self.experiment_data)
            }
            
            with open(self.summary_file, 'w') as f:
                json.dump(summary_data, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            logger.error(f"保存分析摘要失败: {e}")
    
    def get_dashboard_data(self) -> Dict[str, Any]:
        """获取仪表盘数据
        
        Returns:
            仪表盘数据字典
        """
        if not self.experiment_data:
            return {'error': 'no_data'}
        
        recent_24h = self.analyze_success_factors(24)
        recent_7d = self.analyze_success_factors(24 * 7)
        
        # 趋势数据
        df = pd.DataFrame([asdict(attempt) for attempt in self.experiment_data[-1000:]])  # 最近1000条记录
        df['date'] = pd.to_datetime(df['timestamp'], unit='s').dt.date
        
        daily_stats = df.groupby('date').agg({
            'success': ['count', 'mean'],
            'response_time_ms': 'mean',
            'canvas_consistency': 'mean',
            'typing_naturalness': 'mean'
        }).round(3)
        
        return {
            'summary': {
                'total_attempts': len(self.experiment_data),
                'success_rate_24h': recent_24h.get('success_rate', 0),
                'success_rate_7d': recent_7d.get('success_rate', 0),
                'latest_analysis': recent_24h
            },
            'trends': {
                'daily_stats': daily_stats.to_dict('index') if not daily_stats.empty else {}
            },
            'recommendations': self.generate_optimization_recommendations(recent_24h)
        }
    
    def export_data(self, format: str = 'csv', days: int = 7) -> str:
        """导出数据
        
        Args:
            format: 导出格式 ('csv', 'json')
            days: 导出最近N天的数据
            
        Returns:
            导出文件路径
        """
        cutoff_time = time.time() - (days * 24 * 3600)
        export_data = [
            attempt for attempt in self.experiment_data 
            if attempt.timestamp >= cutoff_time
        ]
        
        if not export_data:
            raise ValueError(f"最近{days}天无数据可导出")
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        if format.lower() == 'csv':
            df = pd.DataFrame([asdict(attempt) for attempt in export_data])
            export_file = self.data_dir / f"export_{timestamp}.csv"
            df.to_csv(export_file, index=False)
            
        elif format.lower() == 'json':
            export_file = self.data_dir / f"export_{timestamp}.json"
            with open(export_file, 'w') as f:
                json.dump([asdict(attempt) for attempt in export_data], f, indent=2, ensure_ascii=False)
        else:
            raise ValueError(f"不支持的格式: {format}")
        
        logger.info(f"导出数据到: {export_file}")
        return str(export_file)