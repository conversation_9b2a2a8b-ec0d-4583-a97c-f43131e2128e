"""
反检测系统配置管理

提供简单的配置管理和A/B测试控制界面。
"""

import json
import logging
from pathlib import Path
from typing import Dict, Any, Optional
from .feature_flags import FeatureFlags, ABTestManager

logger = logging.getLogger(__name__)

class ConfigManager:
    """配置管理器
    
    提供简化的配置管理和A/B测试控制接口。
    """
    
    def __init__(self, config_dir: str = "anti_detection"):
        """初始化配置管理器
        
        Args:
            config_dir: 配置目录
        """
        self.config_dir = Path(config_dir)
        self.config_dir.mkdir(parents=True, exist_ok=True)
        
        self.feature_flags = FeatureFlags(
            config_file=str(self.config_dir / "feature_flags.json")
        )
        self.ab_test_manager = ABTestManager(self.feature_flags)
        
        logger.info(f"配置管理器初始化完成: {config_dir}")
    
    def setup_initial_deployment(self) -> Dict[str, Any]:
        """设置初始部署配置（10%流量A/B测试）
        
        Returns:
            部署配置结果
        """
        deployment_config = {
            'dynamic_fingerprint': {'enabled': True, 'ratio': 0.1},
            'smart_typing': {'enabled': False, 'ratio': 0.0},
            'adaptive_interaction': {'enabled': False, 'ratio': 0.0},
            'enhanced_ua_consistency': {'enabled': True, 'ratio': 0.1}
        }
        
        results = {}
        
        for feature_name, config in deployment_config.items():
            if config['enabled']:
                success = self.feature_flags.enable_feature(feature_name, config['ratio'])
                results[feature_name] = {
                    'success': success,
                    'ratio': config['ratio'],
                    'status': 'ab_testing' if config['ratio'] < 1.0 else 'enabled'
                }
                
                if success and config['ratio'] < 1.0:
                    # 开始A/B测试
                    ab_success = self.ab_test_manager.start_ab_test(
                        feature_name, config['ratio'], duration_hours=72
                    )
                    results[feature_name]['ab_test_started'] = ab_success
                    
                logger.info(f"启用特性 {feature_name}: {config['ratio'] * 100:.1f}%流量")
            else:
                results[feature_name] = {
                    'success': True,
                    'ratio': 0.0,
                    'status': 'disabled'
                }
        
        logger.info("初始部署配置完成")
        return results
    
    def get_deployment_status(self) -> Dict[str, Any]:
        """获取部署状态
        
        Returns:
            部署状态字典
        """
        all_features = self.feature_flags.get_all_features()
        
        status = {
            'features': {},
            'ab_tests': {},
            'summary': {
                'total_features': len(all_features),
                'enabled_features': 0,
                'ab_testing_features': 0,
                'disabled_features': 0
            }
        }
        
        for feature_name, feature_info in all_features.items():
            status['features'][feature_name] = feature_info
            
            # 统计特性状态
            if feature_info['status'] == 'enabled':
                status['summary']['enabled_features'] += 1
            elif feature_info['status'] == 'ab_testing':
                status['summary']['ab_testing_features'] += 1
            else:
                status['summary']['disabled_features'] += 1
            
            # 获取A/B测试状态
            test_status = self.ab_test_manager.get_test_status(feature_name)
            if test_status:
                status['ab_tests'][feature_name] = test_status
        
        return status
    
    def update_feature_ratio(self, feature_name: str, new_ratio: float) -> Dict[str, Any]:
        """更新特性启用比例
        
        Args:
            feature_name: 特性名称
            new_ratio: 新的启用比例 (0.0-1.0)
            
        Returns:
            更新结果
        """
        if not 0.0 <= new_ratio <= 1.0:
            return {'success': False, 'error': 'Invalid ratio'}
        
        success = self.feature_flags.update_feature_ratio(feature_name, new_ratio)
        
        result = {
            'success': success,
            'feature_name': feature_name,
            'new_ratio': new_ratio,
            'status': self.feature_flags.get_feature_status(feature_name)
        }
        
        if success:
            logger.info(f"特性 {feature_name} 比例更新为 {new_ratio * 100:.1f}%")
        else:
            logger.error(f"特性 {feature_name} 比例更新失败")
        
        return result
    
    def rollback_feature(self, feature_name: str, reason: str = "Manual rollback") -> Dict[str, Any]:
        """回滚特性
        
        Args:
            feature_name: 特性名称
            reason: 回滚原因
            
        Returns:
            回滚结果
        """
        success = self.feature_flags.rollback_feature(feature_name, reason)
        
        result = {
            'success': success,
            'feature_name': feature_name,
            'reason': reason,
            'status': self.feature_flags.get_feature_status(feature_name)
        }
        
        if success:
            logger.warning(f"特性 {feature_name} 已回滚: {reason}")
        else:
            logger.error(f"特性 {feature_name} 回滚失败")
        
        return result
    
    def get_quick_commands(self) -> Dict[str, str]:
        """获取快速命令列表
        
        Returns:
            命令字典
        """
        return {
            'setup_initial': 'config.setup_initial_deployment()',
            'status': 'config.get_deployment_status()',
            'enable_10_percent': 'config.update_feature_ratio("feature_name", 0.1)',
            'enable_50_percent': 'config.update_feature_ratio("feature_name", 0.5)',
            'enable_100_percent': 'config.update_feature_ratio("feature_name", 1.0)',
            'disable_feature': 'config.update_feature_ratio("feature_name", 0.0)',
            'rollback_feature': 'config.rollback_feature("feature_name", "reason")',
            'gradual_rollout': '''
# 渐进式部署示例
config.update_feature_ratio("dynamic_fingerprint", 0.1)  # 10%
time.sleep(3600)  # 等待1小时
config.update_feature_ratio("dynamic_fingerprint", 0.3)  # 30%
time.sleep(3600)  # 等待1小时
config.update_feature_ratio("dynamic_fingerprint", 1.0)  # 100%
            '''.strip()
        }
    
    def create_monitoring_script(self) -> str:
        """创建监控脚本
        
        Returns:
            监控脚本内容
        """
        script_content = '''#!/usr/bin/env python3
"""
反检测系统监控脚本

定期检查系统状态并生成报告。
"""

import asyncio
import time
import json
from datetime import datetime
from pathlib import Path

# 导入反检测模块
from anti_detection.config_manager import ConfigManager
from anti_detection.analytics import AntiDetectionAnalytics

async def main():
    """主监控循环"""
    config = ConfigManager()
    analytics = AntiDetectionAnalytics()
    
    while True:
        try:
            print(f"\\n{'='*50}")
            print(f"监控时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"{'='*50}")
            
            # 获取部署状态
            status = config.get_deployment_status()
            print(f"特性状态: {status['summary']}")
            
            # 获取分析结果
            analysis = analytics.analyze_success_factors(recent_hours=1)
            if 'success_rate' in analysis:
                print(f"近1小时成功率: {analysis['success_rate'] * 100:.1f}%")
                print(f"总尝试数: {analysis['total_attempts']}")
                
                # 检查是否需要告警
                success_rate = analysis['success_rate']
                if success_rate < 0.8:
                    print(f"⚠️  警告: 成功率过低 ({success_rate * 100:.1f}%)")
                    
                    # 检查自动回滚
                    for feature_name in ['dynamic_fingerprint', 'smart_typing', 'adaptive_interaction']:
                        feature_status = config.feature_flags.get_feature_status(feature_name)
                        if feature_status and feature_status['enabled_ratio'] > 0:
                            if config.feature_flags.check_rollback_conditions(feature_name, success_rate):
                                print(f"🔄 自动回滚特性: {feature_name}")
                                config.feature_flags.auto_rollback_if_needed(feature_name, success_rate)
            
            # 生成优化建议
            recommendations = analytics.generate_optimization_recommendations(analysis)
            if recommendations.get('critical_fixes'):
                print(f"🚨 关键修复建议: {len(recommendations['critical_fixes'])} 项")
                for fix in recommendations['critical_fixes'][:3]:  # 显示前3项
                    print(f"   - {fix}")
            
            # 保存监控数据
            monitoring_data = {
                'timestamp': time.time(),
                'status': status,
                'analysis': analysis,
                'recommendations': recommendations
            }
            
            monitoring_file = Path('logs/monitoring_report.json')
            monitoring_file.parent.mkdir(parents=True, exist_ok=True)
            with open(monitoring_file, 'w') as f:
                json.dump(monitoring_data, f, indent=2)
            
            print(f"✅ 监控数据已保存到 {monitoring_file}")
            
        except KeyboardInterrupt:
            print("\\n监控中止")
            break
        except Exception as e:
            print(f"❌ 监控错误: {e}")
        
        # 等待下次检查（10分钟）
        await asyncio.sleep(600)

if __name__ == "__main__":
    asyncio.run(main())
'''
        
        # 保存脚本文件
        script_file = self.config_dir / "monitoring_script.py"
        with open(script_file, 'w') as f:
            f.write(script_content)
        
        logger.info(f"监控脚本已创建: {script_file}")
        return str(script_file)
    
    def export_config(self) -> Dict[str, Any]:
        """导出当前配置
        
        Returns:
            配置数据
        """
        return {
            'features': self.feature_flags.get_all_features(),
            'ab_tests': {
                name: self.ab_test_manager.get_test_status(name)
                for name in self.feature_flags.get_all_features().keys()
                if self.ab_test_manager.get_test_status(name)
            },
            'export_time': time.time()
        }

def create_quick_start_guide() -> str:
    """创建快速开始指南"""
    guide_content = """
# 反检测系统快速开始指南

## 1. 初始部署 (10%流量A/B测试)

```python
from anti_detection.config_manager import ConfigManager

config = ConfigManager()

# 设置初始部署配置
result = config.setup_initial_deployment()
print("部署结果:", result)
```

## 2. 监控部署状态

```python
# 获取当前状态
status = config.get_deployment_status()
print("特性状态:", status['summary'])
print("A/B测试:", list(status['ab_tests'].keys()))
```

## 3. 渐进式扩展

```python
# 逐步扩展流量
config.update_feature_ratio("dynamic_fingerprint", 0.3)  # 30%
# 观察效果后继续扩展
config.update_feature_ratio("dynamic_fingerprint", 0.7)  # 70%
# 最终全量部署
config.update_feature_ratio("dynamic_fingerprint", 1.0)  # 100%
```

## 4. 紧急回滚

```python
# 立即回滚有问题的特性
config.rollback_feature("feature_name", "成功率过低")
```

## 5. 启动监控

```bash
# 创建监控脚本
python -c "from anti_detection.config_manager import ConfigManager; ConfigManager().create_monitoring_script()"

# 运行监控
python anti_detection/monitoring_script.py
```

## 6. 分析和优化

```python
from anti_detection.analytics import AntiDetectionAnalytics

analytics = AntiDetectionAnalytics()

# 分析最近24小时数据
analysis = analytics.analyze_success_factors(recent_hours=24)
print(f"成功率: {analysis['success_rate'] * 100:.1f}%")

# 获取优化建议
recommendations = analytics.generate_optimization_recommendations(analysis)
print("关键修复:", recommendations['critical_fixes'])
```

## 预期改进效果

- **WebGL/Canvas指纹**: ★★☆☆☆ → ★★★★☆
- **键盘行为模拟**: ★★★☆☆ → ★★★★☆  
- **UA一致性**: ★★★☆☆ → ★★★★☆
- **页面交互**: ★★★☆☆ → ★★★★☆

## 安全保障

- 自动回滚：成功率低于阈值时自动禁用
- 降级机制：新系统失败时自动使用旧系统
- 监控告警：实时监控系统状态和性能
- 数据分析：基于真实数据持续优化
"""
    
    guide_file = Path("anti_detection/QUICK_START.md")
    with open(guide_file, 'w') as f:
        f.write(guide_content)
    
    return str(guide_file)