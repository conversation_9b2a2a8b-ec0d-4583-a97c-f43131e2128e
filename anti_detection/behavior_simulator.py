"""
智能行为模拟模块

提供人类化的键盘、鼠标、页面交互行为模拟，包括：
- 基于统计学习的打字错误和修正
- QWERTY布局感知的键盘延迟
- 自适应的页面交互行为
"""

import asyncio
import random
import time
import math
import logging
from typing import Dict, Any, List, Optional, Tuple
import numpy as np
from playwright.async_api import Page

logger = logging.getLogger(__name__)

class BehaviorSimulator:
    """智能行为模拟器
    
    模拟真实用户的键盘、鼠标和页面交互行为，提升反检测能力。
    """
    
    def __init__(self, enabled_ratio: float = 0.7):
        """初始化行为模拟器
        
        Args:
            enabled_ratio: 启用比例 (0.0-1.0)，用于A/B测试
        """
        self.enabled_ratio = enabled_ratio
        self.base_wpm = 45  # 基础打字速度 (words per minute)
        self.error_rate = 0.03  # 打字错误率 3%
        self.fatigue_factor = 1.0  # 疲劳因子
        self.session_start_time = time.time()
        
        # QWERTY键盘布局定义
        self.qwerty_layout = {
            'q': {'row': 0, 'col': 0, 'finger': 'left_pinky'},
            'w': {'row': 0, 'col': 1, 'finger': 'left_ring'},
            'e': {'row': 0, 'col': 2, 'finger': 'left_middle'},
            'r': {'row': 0, 'col': 3, 'finger': 'left_index'},
            't': {'row': 0, 'col': 4, 'finger': 'left_index'},
            'y': {'row': 0, 'col': 5, 'finger': 'right_index'},
            'u': {'row': 0, 'col': 6, 'finger': 'right_index'},
            'i': {'row': 0, 'col': 7, 'finger': 'right_middle'},
            'o': {'row': 0, 'col': 8, 'finger': 'right_ring'},
            'p': {'row': 0, 'col': 9, 'finger': 'right_pinky'},
            'a': {'row': 1, 'col': 0, 'finger': 'left_pinky'},
            's': {'row': 1, 'col': 1, 'finger': 'left_ring'},
            'd': {'row': 1, 'col': 2, 'finger': 'left_middle'},
            'f': {'row': 1, 'col': 3, 'finger': 'left_index'},
            'g': {'row': 1, 'col': 4, 'finger': 'left_index'},
            'h': {'row': 1, 'col': 5, 'finger': 'right_index'},
            'j': {'row': 1, 'col': 6, 'finger': 'right_index'},
            'k': {'row': 1, 'col': 7, 'finger': 'right_middle'},
            'l': {'row': 1, 'col': 8, 'finger': 'right_ring'},
            'z': {'row': 2, 'col': 0, 'finger': 'left_pinky'},
            'x': {'row': 2, 'col': 1, 'finger': 'left_ring'},
            'c': {'row': 2, 'col': 2, 'finger': 'left_middle'},
            'v': {'row': 2, 'col': 3, 'finger': 'left_index'},
            'b': {'row': 2, 'col': 4, 'finger': 'left_index'},
            'n': {'row': 2, 'col': 5, 'finger': 'right_index'},
            'm': {'row': 2, 'col': 6, 'finger': 'right_index'},
        }
        
        # 常见打字错误模式
        self.common_errors = {
            'adjacent_key': {  # 相邻按键错误
                'a': ['s', 'q', 'w'],
                's': ['a', 'd', 'w', 'e'],
                'd': ['s', 'f', 'e', 'r'],
                'f': ['d', 'g', 'r', 't'],
                'g': ['f', 'h', 't', 'y'],
                'h': ['g', 'j', 'y', 'u'],
                'j': ['h', 'k', 'u', 'i'],
                'k': ['j', 'l', 'i', 'o'],
                'l': ['k', 'o', 'p'],
                'q': ['w', 'a'],
                'w': ['q', 'e', 'a', 's'],
                'e': ['w', 'r', 's', 'd'],
                'r': ['e', 't', 'd', 'f'],
                't': ['r', 'y', 'f', 'g'],
                'y': ['t', 'u', 'g', 'h'],
                'u': ['y', 'i', 'h', 'j'],
                'i': ['u', 'o', 'j', 'k'],
                'o': ['i', 'p', 'k', 'l'],
                'p': ['o', 'l']
            },
            'double_press': 0.4,  # 重复按键概率
            'wrong_finger': 0.3   # 错误手指概率
        }
        
        logger.info(f"行为模拟器初始化，启用率: {enabled_ratio * 100:.1f}%")
    
    def is_enabled_for_session(self, session_id: str) -> bool:
        """检查当前会话是否启用智能行为模拟
        
        Args:
            session_id: 会话ID
            
        Returns:
            是否启用
        """
        session_hash = hash(session_id) % 100
        enabled = session_hash < (self.enabled_ratio * 100)
        
        if enabled:
            logger.debug(f"会话 {session_id[:8]}... 启用智能行为模拟")
        
        return enabled
    
    async def simulate_typing(self, page: Page, selector: str, text: str, session_id: str) -> None:
        """模拟智能打字行为
        
        Args:
            page: Playwright页面对象
            selector: 输入框选择器
            text: 要输入的文本
            session_id: 会话ID
        """
        if not self.is_enabled_for_session(session_id):
            # 降级到传统打字方式
            await self._legacy_typing(page, selector, text)
            return
        
        try:
            await self._smart_typing(page, selector, text)
        except Exception as e:
            logger.warning(f"智能打字失败，降级到传统方式: {e}")
            await self._legacy_typing(page, selector, text)
    
    async def _smart_typing(self, page: Page, selector: str, text: str) -> None:
        """智能打字实现
        
        Args:
            page: Playwright页面对象
            selector: 输入框选择器
            text: 要输入的文本
        """
        await page.click(selector)
        await asyncio.sleep(random.uniform(0.1, 0.3))  # 点击后停顿
        
        previous_char = ''
        typed_chars = 0
        
        for i, char in enumerate(text):
            # 更新疲劳因子
            self._update_fatigue_factor(typed_chars)
            
            # 检查是否需要打错
            error_info = self._should_make_error(char, previous_char, i)
            
            if error_info:
                # 打错字符
                await self._type_error_sequence(page, char, error_info)
            else:
                # 正常打字
                delay = self._calculate_keystroke_delay(char, previous_char)
                await page.keyboard.type(char, delay=delay)
            
            previous_char = char
            typed_chars += 1
            
            # 偶尔短暂停顿（思考时间）
            if random.random() < 0.05:  # 5%概率
                await asyncio.sleep(random.uniform(0.3, 1.2))
    
    async def _legacy_typing(self, page: Page, selector: str, text: str) -> None:
        """传统打字方式（降级）
        
        Args:
            page: Playwright页面对象
            selector: 输入框选择器
            text: 要输入的文本
        """
        await page.click(selector)
        await page.fill(selector, text)
        
        # 添加一些基础的人性化延迟
        base_delay = 60000 / (self.base_wpm * 5)  # ms per character
        total_delay = base_delay * len(text) * random.uniform(0.8, 1.2)
        await asyncio.sleep(total_delay / 1000)
    
    def _calculate_keystroke_delay(self, char: str, previous_char: str) -> int:
        """计算按键延迟
        
        Args:
            char: 当前字符
            previous_char: 前一个字符
            
        Returns:
            延迟时间（毫秒）
        """
        # 基础延迟（基于WPM）
        base_delay = 60000 / (self.base_wpm * 5)  # ms per character
        
        # 字符难度系数
        char_difficulty = self._get_character_difficulty(char, previous_char)
        
        # 疲劳影响
        fatigue_multiplier = self.fatigue_factor
        
        # 高斯噪声（标准差20ms）
        noise = np.random.normal(0, 20)
        
        # 计算最终延迟
        final_delay = base_delay * char_difficulty * fatigue_multiplier + noise
        
        # 限制在合理范围内 (50ms - 400ms)
        return max(50, min(400, int(final_delay)))
    
    def _get_character_difficulty(self, char: str, previous_char: str) -> float:
        """获取字符难度系数
        
        Args:
            char: 当前字符
            previous_char: 前一个字符
            
        Returns:
            难度系数
        """
        char_lower = char.lower()
        prev_lower = previous_char.lower()
        
        # 基础难度
        if char_lower in 'aeiou':  # 元音更容易
            base_difficulty = 0.8
        elif char_lower in 'qwerty':  # 上排常用键
            base_difficulty = 0.9
        elif char_lower in 'asdfgh':  # 中排主键区
            base_difficulty = 0.85
        elif char_lower in 'zxcvbn':  # 下排
            base_difficulty = 1.1
        elif char.isdigit():  # 数字
            base_difficulty = 1.2
        else:  # 特殊字符
            base_difficulty = 1.4
        
        # 连续按键惩罚
        if char_lower == prev_lower:
            base_difficulty *= 1.3  # 重复字符更难
        
        # 手指切换惩罚
        finger_switch_penalty = self._calculate_finger_switch_penalty(char_lower, prev_lower)
        
        return base_difficulty * finger_switch_penalty
    
    def _calculate_finger_switch_penalty(self, char: str, prev_char: str) -> float:
        """计算手指切换惩罚
        
        Args:
            char: 当前字符
            prev_char: 前一个字符
            
        Returns:
            惩罚系数
        """
        if not prev_char or char not in self.qwerty_layout or prev_char not in self.qwerty_layout:
            return 1.0
        
        curr_info = self.qwerty_layout[char]
        prev_info = self.qwerty_layout[prev_char]
        
        # 同一手指
        if curr_info['finger'] == prev_info['finger']:
            return 1.2
        
        # 相邻手指
        finger_distance = abs(
            ['left_pinky', 'left_ring', 'left_middle', 'left_index', 
             'right_index', 'right_middle', 'right_ring', 'right_pinky'].index(curr_info['finger']) -
            ['left_pinky', 'left_ring', 'left_middle', 'left_index',
             'right_index', 'right_middle', 'right_ring', 'right_pinky'].index(prev_info['finger'])
        )
        
        if finger_distance == 1:
            return 1.1  # 相邻手指稍微慢一点
        elif finger_distance >= 4:
            return 0.95  # 跨手更流畅
        
        return 1.0
    
    def _should_make_error(self, char: str, previous_char: str, position: int) -> Optional[Dict[str, Any]]:
        """判断是否应该制造打字错误
        
        Args:
            char: 当前字符
            previous_char: 前一个字符
            position: 字符位置
            
        Returns:
            错误信息字典，无错误返回None
        """
        # 基础错误率
        base_error_rate = self.error_rate
        
        # 位置影响（开头和结尾更容易出错）
        if position < 2:
            base_error_rate *= 1.5
        elif position == 0:
            base_error_rate *= 0.5  # 第一个字符较少出错
        
        # 疲劳影响
        base_error_rate *= self.fatigue_factor
        
        if random.random() > base_error_rate:
            return None
        
        # 选择错误类型
        error_types = ['adjacent_key', 'double_press', 'case_error']
        error_type = random.choice(error_types)
        
        return {
            'type': error_type,
            'original_char': char,
            'correction_delay': random.uniform(0.2, 0.8)
        }
    
    async def _type_error_sequence(self, page: Page, correct_char: str, error_info: Dict[str, Any]) -> None:
        """执行打字错误序列
        
        Args:
            page: Playwright页面对象
            correct_char: 正确字符
            error_info: 错误信息
        """
        error_type = error_info['type']
        correction_delay = error_info['correction_delay']
        
        if error_type == 'adjacent_key':
            # 相邻按键错误
            wrong_chars = self.common_errors['adjacent_key'].get(correct_char.lower(), [correct_char])
            wrong_char = random.choice(wrong_chars)
            
            # 打错字符
            delay = self._calculate_keystroke_delay(wrong_char, '')
            await page.keyboard.type(wrong_char, delay=delay)
            
            # 发现错误的停顿
            await asyncio.sleep(correction_delay)
            
            # 删除错误字符
            await page.keyboard.press('Backspace')
            await asyncio.sleep(random.uniform(0.1, 0.3))
            
            # 打正确字符
            delay = self._calculate_keystroke_delay(correct_char, wrong_char)
            await page.keyboard.type(correct_char, delay=delay)
            
        elif error_type == 'double_press':
            # 重复按键错误
            delay = self._calculate_keystroke_delay(correct_char, '')
            await page.keyboard.type(correct_char + correct_char, delay=delay)
            
            # 发现错误的停顿
            await asyncio.sleep(correction_delay)
            
            # 删除多余字符
            await page.keyboard.press('Backspace')
            
        elif error_type == 'case_error':
            # 大小写错误
            if correct_char.islower():
                wrong_char = correct_char.upper()
            elif correct_char.isupper():
                wrong_char = correct_char.lower()
            else:
                wrong_char = correct_char
            
            if wrong_char != correct_char:
                # 打错大小写
                delay = self._calculate_keystroke_delay(wrong_char, '')
                await page.keyboard.type(wrong_char, delay=delay)
                
                # 发现错误的停顿
                await asyncio.sleep(correction_delay)
                
                # 删除并重新输入
                await page.keyboard.press('Backspace')
                await asyncio.sleep(random.uniform(0.1, 0.3))
                
                delay = self._calculate_keystroke_delay(correct_char, wrong_char)
                await page.keyboard.type(correct_char, delay=delay)
            else:
                # 正常输入
                delay = self._calculate_keystroke_delay(correct_char, '')
                await page.keyboard.type(correct_char, delay=delay)
    
    def _update_fatigue_factor(self, typed_chars: int) -> None:
        """更新疲劳因子
        
        Args:
            typed_chars: 已输入字符数
        """
        # 基于输入字符数和时间的疲劳模型
        session_time = time.time() - self.session_start_time
        
        # 打字疲劳（每100字符增加2%）
        typing_fatigue = 1.0 + (typed_chars / 100) * 0.02
        
        # 时间疲劳（每10分钟增加5%）
        time_fatigue = 1.0 + (session_time / 600) * 0.05
        
        # 综合疲劳因子，最大不超过1.5
        self.fatigue_factor = min(1.5, typing_fatigue * time_fatigue)
    
    async def simulate_adaptive_page_interaction(self, page: Page, session_id: str) -> None:
        """模拟自适应页面交互行为
        
        Args:
            page: Playwright页面对象
            session_id: 会话ID
        """
        if not self.is_enabled_for_session(session_id):
            await self._legacy_page_interaction(page)
            return
        
        try:
            # 获取LCP性能指标
            lcp_time = await self._get_lcp_time(page)
            
            # 基于性能调整行为
            interaction_params = self._calculate_interaction_delays(lcp_time)
            
            # 执行自适应交互
            await self._execute_adaptive_behavior(page, interaction_params)
            
        except Exception as e:
            logger.warning(f"自适应页面交互失败，降级到传统方式: {e}")
            await self._legacy_page_interaction(page)
    
    async def simulate_human_click(self, page: Page, selector: str, session_id: str) -> None:
        """模拟人类点击行为
        
        Args:
            page: Playwright页面对象
            selector: 元素选择器
            session_id: 会话ID
        """
        if not self.is_enabled_for_session(session_id):
            # 降级到传统点击
            await page.click(selector)
            return
        
        try:
            # 获取元素位置
            element = await page.wait_for_selector(selector, timeout=5000)
            if not element:
                await page.click(selector)
                return
            
            box = await element.bounding_box()
            if not box:
                await page.click(selector)
                return
            
            # 计算点击目标位置（避免正中央）
            target_x = box['x'] + box['width'] * random.uniform(0.3, 0.7)
            target_y = box['y'] + box['height'] * random.uniform(0.3, 0.7)
            
            # 模拟鼠标移动轨迹
            await self._simulate_mouse_trajectory(page, target_x, target_y)
            
            # 短暂停顿（瞄准时间）
            await asyncio.sleep(random.uniform(0.1, 0.3))
            
            # 执行点击
            await page.mouse.click(target_x, target_y)
            
            # 点击后停顿
            await asyncio.sleep(random.uniform(0.05, 0.15))
            
        except Exception as e:
            logger.warning(f"人类点击模拟失败，降级到传统方式: {e}")
            await page.click(selector)
    
    async def _simulate_mouse_trajectory(self, page: Page, target_x: float, target_y: float) -> None:
        """模拟人类鼠标移动轨迹
        
        Args:
            page: Playwright页面对象
            target_x: 目标X坐标
            target_y: 目标Y坐标
        """
        # 获取当前鼠标位置（估算）
        current_x = random.uniform(100, 800)
        current_y = random.uniform(100, 600)
        
        # 计算距离和步数
        distance = math.sqrt((target_x - current_x)**2 + (target_y - current_y)**2)
        steps = max(3, int(distance / 50))  # 每50像素一步，最少3步
        
        # 生成贝塞尔曲线路径
        control_points = self._generate_bezier_path(
            current_x, current_y, target_x, target_y, steps
        )
        
        # 执行鼠标移动
        for i, (x, y) in enumerate(control_points):
            await page.mouse.move(x, y)
            
            # 不同阶段不同速度
            if i < len(control_points) * 0.3:  # 加速阶段
                delay = random.uniform(0.01, 0.03)
            elif i < len(control_points) * 0.8:  # 匀速阶段
                delay = random.uniform(0.005, 0.015)
            else:  # 减速阶段
                delay = random.uniform(0.015, 0.035)
            
            await asyncio.sleep(delay)
    
    def _generate_bezier_path(self, start_x: float, start_y: float, 
                             end_x: float, end_y: float, steps: int) -> List[Tuple[float, float]]:
        """生成贝塞尔曲线路径
        
        Args:
            start_x: 起始X坐标
            start_y: 起始Y坐标
            end_x: 结束X坐标
            end_y: 结束Y坐标
            steps: 路径步数
            
        Returns:
            路径点列表
        """
        # 生成控制点（模拟人类不完全直线的移动）
        mid_x = (start_x + end_x) / 2 + random.uniform(-20, 20)
        mid_y = (start_y + end_y) / 2 + random.uniform(-20, 20)
        
        points = []
        for i in range(steps):
            t = i / (steps - 1)
            
            # 二次贝塞尔曲线
            x = (1-t)**2 * start_x + 2*(1-t)*t * mid_x + t**2 * end_x
            y = (1-t)**2 * start_y + 2*(1-t)*t * mid_y + t**2 * end_y
            
            # 添加微小的随机抖动
            x += random.uniform(-2, 2)
            y += random.uniform(-2, 2)
            
            points.append((x, y))
        
        return points
    
    async def _get_lcp_time(self, page: Page) -> float:
        """获取页面LCP时间
        
        Args:
            page: Playwright页面对象
            
        Returns:
            LCP时间（毫秒）
        """
        try:
            lcp_time = await page.evaluate("""
                new Promise(resolve => {
                    let lcpTime = 0;
                    new PerformanceObserver((list) => {
                        const entries = list.getEntries();
                        if (entries.length > 0) {
                            lcpTime = entries[entries.length - 1].startTime;
                            resolve(lcpTime);
                        }
                    }).observe({entryTypes: ['largest-contentful-paint']});
                    
                    // 10秒超时保护
                    setTimeout(() => resolve(lcpTime || 3000), 10000);
                })
            """)
            
            logger.debug(f"页面LCP时间: {lcp_time:.1f}ms")
            return lcp_time
            
        except Exception as e:
            logger.warning(f"获取LCP时间失败: {e}")
            return 3000.0  # 默认值
    
    def _calculate_interaction_delays(self, lcp_time: float) -> Dict[str, float]:
        """基于LCP时间计算交互延迟
        
        Args:
            lcp_time: LCP时间（毫秒）
            
        Returns:
            交互延迟参数
        """
        if lcp_time < 1500:  # 快速连接
            return {
                'scroll_delay': random.uniform(0.8, 1.5),
                'reading_time': random.uniform(8, 15),
                'click_delay': random.uniform(0.3, 0.8),
                'hover_delay': random.uniform(0.1, 0.3)
            }
        elif lcp_time < 3000:  # 中等连接
            return {
                'scroll_delay': random.uniform(1.5, 2.5),
                'reading_time': random.uniform(12, 20),
                'click_delay': random.uniform(0.5, 1.2),
                'hover_delay': random.uniform(0.2, 0.5)
            }
        else:  # 慢速连接
            return {
                'scroll_delay': random.uniform(2.0, 4.0),
                'reading_time': random.uniform(15, 25),
                'click_delay': random.uniform(0.8, 1.8),
                'hover_delay': random.uniform(0.3, 0.8)
            }
    
    async def _execute_adaptive_behavior(self, page: Page, params: Dict[str, float]) -> None:
        """执行自适应行为
        
        Args:
            page: Playwright页面对象
            params: 交互参数
        """
        # 检查是否为welcome页面，跳过滚动行为
        current_url = page.url
        if "welcome" in current_url:
            logger.info("🚀 Welcome页面跳过自然滚动行为，仅执行阅读时间")
            # 仅执行阅读停留时间
            await asyncio.sleep(params['reading_time'])
            return
        
        # 自然滚动行为（非welcome页面）
        viewport_height = await page.evaluate("window.innerHeight")
        scroll_count = random.randint(2, 5)
        
        for _ in range(scroll_count):
            scroll_amount = random.randint(200, 600)
            await page.mouse.wheel(0, scroll_amount)
            await asyncio.sleep(params['scroll_delay'])
        
        # 阅读停留时间
        await asyncio.sleep(params['reading_time'])
        
        # 随机鼠标移动
        for _ in range(random.randint(1, 3)):
            x = random.randint(100, 800)
            y = random.randint(100, 600)
            await page.mouse.move(x, y)
            await asyncio.sleep(params['hover_delay'])
    
    async def _legacy_page_interaction(self, page: Page) -> None:
        """传统页面交互（降级）
        
        Args:
            page: Playwright页面对象
        """
        # 检查是否为welcome页面，跳过滚动行为
        current_url = page.url
        if "welcome" in current_url:
            logger.info("🚀 Welcome页面跳过传统滚动行为，仅停留")
            # 仅执行固定停留时间
            await asyncio.sleep(random.uniform(5, 8))
            return
        
        # 简单滚动（非welcome页面）
        for _ in range(3):
            await page.mouse.wheel(0, 300)
            await asyncio.sleep(1.0)
        
        # 固定停留时间
        await asyncio.sleep(random.uniform(9, 12))
    
    def get_behavior_stats(self) -> Dict[str, Any]:
        """获取行为统计信息
        
        Returns:
            行为统计字典
        """
        session_time = time.time() - self.session_start_time
        
        return {
            'enabled_ratio': self.enabled_ratio,
            'base_wpm': self.base_wpm,
            'error_rate': self.error_rate,
            'current_fatigue_factor': self.fatigue_factor,
            'session_duration_minutes': session_time / 60,
            'typing_patterns_count': len(self.qwerty_layout),
            'error_patterns_count': len(self.common_errors['adjacent_key'])
        }