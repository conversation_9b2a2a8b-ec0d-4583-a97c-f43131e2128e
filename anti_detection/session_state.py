"""
会话状态管理模块

负责维护浏览器会话的一致性状态，确保指纹和行为在单次会话内保持一致。
"""

import time
import hashlib
import logging
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)

class SessionState:
    """会话状态管理器
    
    维护单个浏览器会话的一致性状态，包括：
    - 指纹数据的会话级缓存
    - 行为参数的持久化
    - 会话超时和轮换管理
    """
    
    def __init__(self, session_duration: int = 3600):
        """初始化会话状态管理器
        
        Args:
            session_duration: 会话持续时间(秒)，默认1小时
        """
        self.session_duration = session_duration
        self.session_data: Dict[str, Any] = {}
        self.session_start_time = time.time()
        self.session_id = self._generate_session_id()
        
        logger.info(f"会话状态管理器初始化，ID: {self.session_id}")
    
    def _generate_session_id(self) -> str:
        """生成唯一的会话ID"""
        timestamp = str(time.time())
        random_str = str(hash(timestamp))
        return hashlib.md5(f"{timestamp}_{random_str}".encode()).hexdigest()[:16]
    
    def set_fingerprint_data(self, fingerprint_data: Dict[str, Any]) -> None:
        """设置指纹数据
        
        Args:
            fingerprint_data: 指纹数据字典
        """
        self.session_data['fingerprint'] = fingerprint_data
        logger.debug(f"会话 {self.session_id} 设置指纹数据")
    
    def get_fingerprint_data(self) -> Optional[Dict[str, Any]]:
        """获取指纹数据
        
        Returns:
            指纹数据字典，如果不存在则返回None
        """
        return self.session_data.get('fingerprint')
    
    def set_behavior_params(self, behavior_params: Dict[str, Any]) -> None:
        """设置行为参数
        
        Args:
            behavior_params: 行为参数字典
        """
        self.session_data['behavior'] = behavior_params
        logger.debug(f"会话 {self.session_id} 设置行为参数")
    
    def get_behavior_params(self) -> Optional[Dict[str, Any]]:
        """获取行为参数
        
        Returns:
            行为参数字典，如果不存在则返回None
        """
        return self.session_data.get('behavior')
    
    def should_rotate_session(self) -> bool:
        """检查是否需要轮换会话
        
        Returns:
            True if 需要轮换，False otherwise
        """
        current_time = time.time()
        session_age = current_time - self.session_start_time
        
        should_rotate = session_age > self.session_duration
        
        if should_rotate:
            logger.info(f"会话 {self.session_id} 达到轮换时间，持续时间: {session_age:.1f}s")
            
        return should_rotate
    
    def rotate_session(self) -> str:
        """轮换到新会话
        
        Returns:
            新的会话ID
        """
        old_session_id = self.session_id
        
        # 重置会话状态
        self.session_data.clear()
        self.session_start_time = time.time()
        self.session_id = self._generate_session_id()
        
        logger.info(f"会话已轮换: {old_session_id} -> {self.session_id}")
        return self.session_id
    
    def get_session_info(self) -> Dict[str, Any]:
        """获取会话信息摘要
        
        Returns:
            会话信息字典
        """
        current_time = time.time()
        session_age = current_time - self.session_start_time
        
        return {
            'session_id': self.session_id,
            'start_time': self.session_start_time,
            'age_seconds': session_age,
            'has_fingerprint': 'fingerprint' in self.session_data,
            'has_behavior_params': 'behavior' in self.session_data,
            'should_rotate': self.should_rotate_session()
        }
    
    def get_session_seed(self) -> str:
        """获取会话种子（用于确定性随机数生成）
        
        Returns:
            会话种子字符串
        """
        return f"{self.session_id}_{int(self.session_start_time)}"