"""
A/B测试和特性开关模块

提供灰度发布、A/B测试和特性开关功能，支持安全的渐进式功能部署。
"""

import json
import time
import logging
import hashlib
from typing import Dict, Any, Optional, List
from pathlib import Path
from dataclasses import dataclass, asdict
from enum import Enum

logger = logging.getLogger(__name__)

class FeatureStatus(Enum):
    """特性状态枚举"""
    DISABLED = "disabled"
    ENABLED = "enabled"
    AB_TESTING = "ab_testing"
    ROLLBACK = "rollback"

@dataclass
class FeatureConfig:
    """特性配置"""
    name: str
    status: FeatureStatus
    enabled_ratio: float = 0.0
    description: str = ""
    rollback_threshold: float = 0.8  # 成功率阈值，低于此值自动回滚
    created_at: float = 0.0
    updated_at: float = 0.0

class FeatureFlags:
    """特性开关管理器
    
    管理反检测系统的特性开关，支持A/B测试和自动回滚。
    """
    
    def __init__(self, config_file: str = "anti_detection/feature_flags.json"):
        """初始化特性开关管理器
        
        Args:
            config_file: 配置文件路径
        """
        self.config_file = Path(config_file)
        self.config_file.parent.mkdir(parents=True, exist_ok=True)
        
        # 默认特性配置
        self.default_features = {
            'dynamic_fingerprint': FeatureConfig(
                name='dynamic_fingerprint',
                status=FeatureStatus.DISABLED,
                enabled_ratio=0.0,
                description='动态指纹生成系统',
                rollback_threshold=0.85,
                created_at=time.time()
            ),
            'smart_typing': FeatureConfig(
                name='smart_typing',
                status=FeatureStatus.DISABLED,
                enabled_ratio=0.0,
                description='智能键盘行为模拟',
                rollback_threshold=0.80,
                created_at=time.time()
            ),
            'adaptive_interaction': FeatureConfig(
                name='adaptive_interaction',
                status=FeatureStatus.DISABLED,
                enabled_ratio=0.0,
                description='自适应页面交互',
                rollback_threshold=0.75,
                created_at=time.time()
            ),
            'enhanced_ua_consistency': FeatureConfig(
                name='enhanced_ua_consistency',
                status=FeatureStatus.DISABLED,
                enabled_ratio=0.0,
                description='增强UA一致性检查',
                rollback_threshold=0.85,
                created_at=time.time()
            )
        }
        
        self.features: Dict[str, FeatureConfig] = {}
        self.load_config()
        
        logger.info(f"特性开关管理器初始化，配置文件: {config_file}")
    
    def load_config(self) -> None:
        """加载特性配置"""
        if not self.config_file.exists():
            logger.info("配置文件不存在，使用默认配置")
            self.features = self.default_features.copy()
            self.save_config()
            return
        
        try:
            with open(self.config_file, 'r') as f:
                config_data = json.load(f)
            
            self.features = {}
            for name, data in config_data.items():
                # 处理enum类型
                if isinstance(data.get('status'), str):
                    data['status'] = FeatureStatus(data['status'])
                
                self.features[name] = FeatureConfig(**data)
            
            # 添加新的默认特性（如果不存在）
            for name, feature in self.default_features.items():
                if name not in self.features:
                    self.features[name] = feature
                    logger.info(f"添加新特性: {name}")
            
            logger.info(f"加载特性配置成功: {len(self.features)} 个特性")
            
        except Exception as e:
            logger.error(f"加载配置失败: {e}，使用默认配置")
            self.features = self.default_features.copy()
    
    def save_config(self) -> None:
        """保存特性配置"""
        try:
            config_data = {}
            for name, feature in self.features.items():
                feature_dict = asdict(feature)
                feature_dict['status'] = feature.status.value  # 转换enum为字符串
                config_data[name] = feature_dict
            
            with open(self.config_file, 'w') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
                
            logger.debug("特性配置已保存")
            
        except Exception as e:
            logger.error(f"保存配置失败: {e}")
    
    def is_enabled(self, feature_name: str, session_id: str) -> bool:
        """检查特性是否对指定会话启用
        
        Args:
            feature_name: 特性名称
            session_id: 会话ID
            
        Returns:
            是否启用
        """
        if feature_name not in self.features:
            logger.warning(f"未知特性: {feature_name}")
            return False
        
        feature = self.features[feature_name]
        
        # 检查特性状态
        if feature.status == FeatureStatus.DISABLED:
            return False
        elif feature.status == FeatureStatus.ENABLED:
            return True
        elif feature.status == FeatureStatus.ROLLBACK:
            return False
        elif feature.status == FeatureStatus.AB_TESTING:
            # 基于会话ID和启用比例决定
            session_hash = self._hash_session(session_id, feature_name)
            enabled = session_hash < feature.enabled_ratio
            
            if enabled:
                logger.debug(f"A/B测试启用特性 {feature_name} for session {session_id[:8]}...")
            
            return enabled
        
        return False
    
    def _hash_session(self, session_id: str, feature_name: str) -> float:
        """计算会话哈希值
        
        Args:
            session_id: 会话ID
            feature_name: 特性名称
            
        Returns:
            0-1之间的哈希值
        """
        combined = f"{session_id}_{feature_name}"
        hash_value = hashlib.md5(combined.encode()).hexdigest()
        # 取前8位转换为0-1的浮点数
        return int(hash_value[:8], 16) / (16**8 - 1)
    
    def enable_feature(self, feature_name: str, enabled_ratio: float = 1.0) -> bool:
        """启用特性
        
        Args:
            feature_name: 特性名称
            enabled_ratio: 启用比例 (0.0-1.0)
            
        Returns:
            操作是否成功
        """
        if feature_name not in self.features:
            logger.error(f"特性不存在: {feature_name}")
            return False
        
        feature = self.features[feature_name]
        
        if enabled_ratio >= 1.0:
            feature.status = FeatureStatus.ENABLED
            feature.enabled_ratio = 1.0
        else:
            feature.status = FeatureStatus.AB_TESTING
            feature.enabled_ratio = enabled_ratio
        
        feature.updated_at = time.time()
        self.save_config()
        
        logger.info(f"启用特性 {feature_name}，比例: {enabled_ratio * 100:.1f}%")
        return True
    
    def disable_feature(self, feature_name: str) -> bool:
        """禁用特性
        
        Args:
            feature_name: 特性名称
            
        Returns:
            操作是否成功
        """
        if feature_name not in self.features:
            logger.error(f"特性不存在: {feature_name}")
            return False
        
        feature = self.features[feature_name]
        feature.status = FeatureStatus.DISABLED
        feature.enabled_ratio = 0.0
        feature.updated_at = time.time()
        
        self.save_config()
        
        logger.info(f"禁用特性: {feature_name}")
        return True
    
    def rollback_feature(self, feature_name: str, reason: str = "") -> bool:
        """回滚特性
        
        Args:
            feature_name: 特性名称
            reason: 回滚原因
            
        Returns:
            操作是否成功
        """
        if feature_name not in self.features:
            logger.error(f"特性不存在: {feature_name}")
            return False
        
        feature = self.features[feature_name]
        feature.status = FeatureStatus.ROLLBACK
        feature.enabled_ratio = 0.0
        feature.updated_at = time.time()
        
        self.save_config()
        
        logger.warning(f"回滚特性 {feature_name}，原因: {reason}")
        return True
    
    def update_feature_ratio(self, feature_name: str, new_ratio: float) -> bool:
        """更新特性启用比例
        
        Args:
            feature_name: 特性名称
            new_ratio: 新的启用比例
            
        Returns:
            操作是否成功
        """
        if feature_name not in self.features:
            logger.error(f"特性不存在: {feature_name}")
            return False
        
        if not 0.0 <= new_ratio <= 1.0:
            logger.error(f"无效的启用比例: {new_ratio}")
            return False
        
        feature = self.features[feature_name]
        
        if new_ratio == 0.0:
            feature.status = FeatureStatus.DISABLED
        elif new_ratio == 1.0:
            feature.status = FeatureStatus.ENABLED
        else:
            feature.status = FeatureStatus.AB_TESTING
        
        feature.enabled_ratio = new_ratio
        feature.updated_at = time.time()
        
        self.save_config()
        
        logger.info(f"更新特性 {feature_name} 启用比例: {new_ratio * 100:.1f}%")
        return True
    
    def get_feature_status(self, feature_name: str) -> Optional[Dict[str, Any]]:
        """获取特性状态
        
        Args:
            feature_name: 特性名称
            
        Returns:
            特性状态字典
        """
        if feature_name not in self.features:
            return None
        
        feature = self.features[feature_name]
        return {
            'name': feature.name,
            'status': feature.status.value,
            'enabled_ratio': feature.enabled_ratio,
            'description': feature.description,
            'rollback_threshold': feature.rollback_threshold,
            'created_at': feature.created_at,
            'updated_at': feature.updated_at
        }
    
    def get_all_features(self) -> Dict[str, Dict[str, Any]]:
        """获取所有特性状态
        
        Returns:
            所有特性状态字典
        """
        return {
            name: self.get_feature_status(name)
            for name in self.features.keys()
        }
    
    def check_rollback_conditions(self, feature_name: str, success_rate: float) -> bool:
        """检查是否满足自动回滚条件
        
        Args:
            feature_name: 特性名称
            success_rate: 当前成功率
            
        Returns:
            是否需要回滚
        """
        if feature_name not in self.features:
            return False
        
        feature = self.features[feature_name]
        
        # 只有启用状态的特性才检查回滚
        if feature.status not in [FeatureStatus.ENABLED, FeatureStatus.AB_TESTING]:
            return False
        
        should_rollback = success_rate < feature.rollback_threshold
        
        if should_rollback:
            logger.warning(f"特性 {feature_name} 成功率 {success_rate:.1%} 低于阈值 {feature.rollback_threshold:.1%}")
        
        return should_rollback
    
    def auto_rollback_if_needed(self, feature_name: str, success_rate: float) -> bool:
        """自动回滚（如果需要）
        
        Args:
            feature_name: 特性名称
            success_rate: 当前成功率
            
        Returns:
            是否执行了回滚
        """
        if self.check_rollback_conditions(feature_name, success_rate):
            reason = f"成功率 {success_rate:.1%} 低于阈值 {self.features[feature_name].rollback_threshold:.1%}"
            return self.rollback_feature(feature_name, reason)
        
        return False
    
    def get_enabled_features_for_session(self, session_id: str) -> List[str]:
        """获取会话启用的特性列表
        
        Args:
            session_id: 会话ID
            
        Returns:
            启用的特性名称列表
        """
        enabled_features = []
        
        for feature_name in self.features.keys():
            if self.is_enabled(feature_name, session_id):
                enabled_features.append(feature_name)
        
        return enabled_features
    
    def create_feature_context(self, session_id: str) -> Dict[str, bool]:
        """创建特性上下文
        
        Args:
            session_id: 会话ID
            
        Returns:
            特性启用状态字典
        """
        context = {}
        
        for feature_name in self.features.keys():
            context[feature_name] = self.is_enabled(feature_name, session_id)
        
        logger.debug(f"会话 {session_id[:8]}... 特性上下文: {sum(context.values())}/{len(context)} 启用")
        
        return context

class ABTestManager:
    """A/B测试管理器"""
    
    def __init__(self, feature_flags: FeatureFlags):
        """初始化A/B测试管理器
        
        Args:
            feature_flags: 特性开关管理器
        """
        self.feature_flags = feature_flags
        self.test_results: Dict[str, Dict[str, Any]] = {}
        
        logger.info("A/B测试管理器初始化完成")
    
    def start_ab_test(self, feature_name: str, test_ratio: float, duration_hours: int = 72) -> bool:
        """开始A/B测试
        
        Args:
            feature_name: 特性名称
            test_ratio: 测试组比例
            duration_hours: 测试持续时间（小时）
            
        Returns:
            是否成功开始测试
        """
        if not self.feature_flags.update_feature_ratio(feature_name, test_ratio):
            return False
        
        test_config = {
            'start_time': time.time(),
            'duration_hours': duration_hours,
            'test_ratio': test_ratio,
            'end_time': time.time() + (duration_hours * 3600),
            'status': 'running'
        }
        
        self.test_results[feature_name] = test_config
        
        logger.info(f"开始A/B测试: {feature_name}，测试组比例: {test_ratio * 100:.1f}%，持续时间: {duration_hours}小时")
        return True
    
    def check_test_completion(self, feature_name: str) -> bool:
        """检查测试是否完成
        
        Args:
            feature_name: 特性名称
            
        Returns:
            测试是否完成
        """
        if feature_name not in self.test_results:
            return False
        
        test_config = self.test_results[feature_name]
        
        if test_config['status'] != 'running':
            return True
        
        return time.time() >= test_config['end_time']
    
    def stop_ab_test(self, feature_name: str, keep_enabled: bool = False) -> bool:
        """停止A/B测试
        
        Args:
            feature_name: 特性名称
            keep_enabled: 是否保持特性启用
            
        Returns:
            操作是否成功
        """
        if feature_name not in self.test_results:
            logger.warning(f"特性 {feature_name} 没有运行中的A/B测试")
            return False
        
        if keep_enabled:
            self.feature_flags.enable_feature(feature_name, 1.0)
        else:
            self.feature_flags.disable_feature(feature_name)
        
        self.test_results[feature_name]['status'] = 'completed'
        self.test_results[feature_name]['completed_at'] = time.time()
        
        logger.info(f"停止A/B测试: {feature_name}，保持启用: {keep_enabled}")
        return True
    
    def get_test_status(self, feature_name: str) -> Optional[Dict[str, Any]]:
        """获取测试状态
        
        Args:
            feature_name: 特性名称
            
        Returns:
            测试状态字典
        """
        if feature_name not in self.test_results:
            return None
        
        test_config = self.test_results[feature_name].copy()
        
        # 添加剩余时间
        if test_config['status'] == 'running':
            remaining_time = test_config['end_time'] - time.time()
            test_config['remaining_hours'] = max(0, remaining_time / 3600)
        
        return test_config