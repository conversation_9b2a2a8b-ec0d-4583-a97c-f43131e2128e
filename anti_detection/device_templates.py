"""
设备配置模板系统

提供一致的浏览器设备配置模板，确保User-Agent、平台信息、硬件参数等保持匹配。
基于真实设备数据和caniuse统计信息构建。
"""

import json
import random
import logging
from typing import Dict, Any, List, Optional

logger = logging.getLogger(__name__)

class DeviceTemplates:
    """设备配置模板管理器
    
    管理预定义的浏览器设备配置模板，确保所有浏览器属性相互匹配和一致。
    """
    
    def __init__(self):
        """初始化设备模板管理器"""
        self.templates = self._load_device_templates()
        logger.info(f"设备模板管理器初始化，共加载 {len(self.templates)} 个模板")
    
    def _load_device_templates(self) -> Dict[str, Dict[str, Any]]:
        """加载设备配置模板
        
        Returns:
            设备模板字典
        """
        return {
            'chrome_win10_desktop': {
                'name': 'Chrome Windows 10 Desktop',
                'ua': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'platform': 'Win32',
                'cpu_class': 'x86_64',
                'memory': 8,  # GB
                'screen': {
                    'width': 1920,
                    'height': 1080,
                    'color_depth': 24,
                    'pixel_depth': 24
                },
                'viewport': {
                    'width': 1920,
                    'height': 937  # 去除浏览器UI
                },
                'webgl_vendor': 'Google Inc. (NVIDIA)',
                'webgl_renderer': 'ANGLE (NVIDIA, NVIDIA GeForce RTX 3070 Direct3D11 vs_5_0 ps_5_0, D3D11)',
                'canvas_hash_base': 'chrome_win10_nvidia',
                'languages': ['en-US', 'en'],
                'timezone': 'America/New_York',
                'webrtc_support': True,
                'touch_support': False
            },
            
            'chrome_macos_desktop': {
                'name': 'Chrome macOS Desktop',
                'ua': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'platform': 'MacIntel',
                'cpu_class': 'x86_64',
                'memory': 8,  # GB
                'screen': {
                    'width': 1440,
                    'height': 900,
                    'color_depth': 24,
                    'pixel_depth': 24
                },
                'viewport': {
                    'width': 1440,
                    'height': 789
                },
                'webgl_vendor': 'Google Inc. (Apple)',
                'webgl_renderer': 'ANGLE (Apple, Apple M1 Pro, OpenGL 4.1)',
                'canvas_hash_base': 'chrome_macos_m1',
                'languages': ['en-US', 'en'],
                'timezone': 'America/Los_Angeles',
                'webrtc_support': True,
                'touch_support': False
            },
            
            'chrome_win10_laptop': {
                'name': 'Chrome Windows 10 Laptop',
                'ua': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'platform': 'Win32',
                'cpu_class': 'x86_64',
                'memory': 16,  # GB
                'screen': {
                    'width': 1366,
                    'height': 768,
                    'color_depth': 24,
                    'pixel_depth': 24
                },
                'viewport': {
                    'width': 1366,
                    'height': 685
                },
                'webgl_vendor': 'Google Inc. (Intel)',
                'webgl_renderer': 'ANGLE (Intel, Intel(R) UHD Graphics 620 Direct3D11 vs_5_0 ps_5_0, D3D11)',
                'canvas_hash_base': 'chrome_win10_intel',
                'languages': ['en-US', 'en'],
                'timezone': 'America/Chicago',
                'webrtc_support': True,
                'touch_support': False
            },
            
            'safari_macos': {
                'name': 'Safari macOS',
                'ua': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15',
                'platform': 'MacIntel',
                'cpu_class': 'x86_64',
                'memory': 8,  # GB
                'screen': {
                    'width': 1440,
                    'height': 900,
                    'color_depth': 24,
                    'pixel_depth': 24
                },
                'viewport': {
                    'width': 1440,
                    'height': 789
                },
                'webgl_vendor': 'Apple Inc.',
                'webgl_renderer': 'Apple M1 Pro',
                'canvas_hash_base': 'safari_macos_m1',
                'languages': ['en-US', 'en'],
                'timezone': 'America/Los_Angeles',
                'webrtc_support': True,
                'touch_support': False
            },
            
            'edge_win11': {
                'name': 'Edge Windows 11',
                'ua': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0',
                'platform': 'Win32',
                'cpu_class': 'x86_64',
                'memory': 16,  # GB
                'screen': {
                    'width': 1920,
                    'height': 1080,
                    'color_depth': 24,
                    'pixel_depth': 24
                },
                'viewport': {
                    'width': 1920,
                    'height': 937
                },
                'webgl_vendor': 'Google Inc. (AMD)',
                'webgl_renderer': 'ANGLE (AMD, AMD Radeon RX 6800 XT Direct3D11 vs_5_0 ps_5_0, D3D11)',
                'canvas_hash_base': 'edge_win11_amd',
                'languages': ['en-US', 'en'],
                'timezone': 'America/New_York',
                'webrtc_support': True,
                'touch_support': False
            }
        }
    
    def get_template_names(self) -> List[str]:
        """获取所有可用的模板名称
        
        Returns:
            模板名称列表
        """
        return list(self.templates.keys())
    
    def get_template(self, template_name: str) -> Optional[Dict[str, Any]]:
        """获取指定的设备模板
        
        Args:
            template_name: 模板名称
            
        Returns:
            设备模板字典，如果不存在则返回None
        """
        template = self.templates.get(template_name)
        if template:
            logger.debug(f"获取设备模板: {template_name}")
        else:
            logger.warning(f"设备模板不存在: {template_name}")
        
        return template
    
    def select_random_template(self) -> Dict[str, Any]:
        """随机选择一个设备模板
        
        Returns:
            随机选择的设备模板
        """
        template_name = random.choice(list(self.templates.keys()))
        logger.debug(f"随机选择设备模板: {template_name}")
        return self.templates[template_name].copy()
    
    def select_consistent_template(self, session_seed: str) -> Dict[str, Any]:
        """基于会话种子选择一致的设备模板
        
        Args:
            session_seed: 会话种子字符串
            
        Returns:
            一致选择的设备模板
        """
        template_names = list(self.templates.keys())
        # 使用会话种子确保相同种子选择相同模板
        index = hash(session_seed) % len(template_names)
        template_name = template_names[index]
        
        logger.debug(f"基于种子 {session_seed[:8]}... 选择模板: {template_name}")
        return self.templates[template_name].copy()
    
    def validate_template_consistency(self, template: Dict[str, Any]) -> Dict[str, bool]:
        """验证模板配置的一致性
        
        Args:
            template: 设备模板字典
            
        Returns:
            验证结果字典
        """
        validation_results = {
            'ua_platform_match': True,
            'screen_viewport_consistent': True,
            'webgl_vendor_consistent': True,
            'memory_reasonable': True
        }
        
        # 检查UA和平台匹配
        ua = template.get('ua', '')
        platform = template.get('platform', '')
        
        if 'Windows' in ua and platform != 'Win32':
            validation_results['ua_platform_match'] = False
        elif 'Macintosh' in ua and platform != 'MacIntel':
            validation_results['ua_platform_match'] = False
        
        # 检查屏幕和视口一致性
        screen = template.get('screen', {})
        viewport = template.get('viewport', {})
        
        if screen.get('width', 0) < viewport.get('width', 0):
            validation_results['screen_viewport_consistent'] = False
        if screen.get('height', 0) < viewport.get('height', 0):
            validation_results['screen_viewport_consistent'] = False
        
        # 检查WebGL供应商一致性
        webgl_vendor = template.get('webgl_vendor', '')
        if 'NVIDIA' in webgl_vendor and 'canvas_hash_base' in template:
            if 'nvidia' not in template['canvas_hash_base'].lower():
                validation_results['webgl_vendor_consistent'] = False
        
        # 检查内存合理性
        memory = template.get('memory', 0)
        if memory < 4 or memory > 64:  # 4GB - 64GB范围
            validation_results['memory_reasonable'] = False
        
        # 记录验证失败项
        failed_checks = [k for k, v in validation_results.items() if not v]
        if failed_checks:
            logger.warning(f"模板一致性验证失败: {failed_checks}")
        
        return validation_results
    
    def create_playwright_config(self, template: Dict[str, Any]) -> Dict[str, Any]:
        """基于设备模板创建Playwright配置
        
        Args:
            template: 设备模板字典
            
        Returns:
            Playwright配置字典
        """
        config = {
            'user_agent': template['ua'],
            'viewport': template['viewport'],
            'screen': template['screen'],
            'locale': template['languages'][0] if template['languages'] else 'en-US',
            'timezone_id': template['timezone'],
            'geolocation': None,  # 可根据需要设置
            'permissions': [],
            'extra_http_headers': {
                'Accept-Language': ','.join(template['languages']) + ',*;q=0.9'
            }
        }
        
        logger.debug(f"创建Playwright配置: {template.get('name', 'Unknown')}")
        return config