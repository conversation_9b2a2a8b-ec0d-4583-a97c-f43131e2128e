#!/usr/bin/env python3
"""
Hulu reCAPTCHA v3 修复版本
基于项目现有工作代码，解决登录按钮无响应问题
"""

import sys
import os
import time
import random
import logging

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from playwright.sync_api import sync_playwright
from utils import TempMailAPI, setup_logging
from hulu_automation.infrastructure.human_behavior import HumanBehaviorSimulator, BiometricSimulator

class HuluRecaptchaV3Fixed:
    """Hulu reCAPTCHA v3 修复版本 - 基于项目现有代码"""
    
    def __init__(self, headless: bool = False):
        """
        初始化
        
        Args:
            headless: 是否无头模式（建议False以获得更好的反检测效果）
        """
        self.headless = headless
        self.logger = setup_logging()
        
        # 硬编码测试账号
        self.EMAIL = "<EMAIL>"
        self.PASSWORD = "Fiona127,."
        
        # 初始化人类行为模拟器（基于项目现有实现）
        self.human_behavior = HumanBehaviorSimulator()
        self.biometric_sim = BiometricSimulator()
        
        # 初始化邮箱API
        self.tempmail_api = TempMailAPI()
        
        # 会话状态
        self.session_start_time = time.time()
        self.failed_attempts = 0
        
        self.logger.info("🛡️ Hulu reCAPTCHA v3 修复版本已初始化")
    
    def ultimate_hulu_login(self):
        """终极Hulu登录方案 - 基于项目现有代码"""
        self.logger.info("🚀 启动Hulu reCAPTCHA v3 修复版登录流程...")
        
        with sync_playwright() as playwright:
            try:
                # 创建最强反检测浏览器配置
                browser = playwright.chromium.launch(
                    headless=self.headless,
                    args=self._get_anti_detection_args()
                )
                
                context = browser.new_context(
                    user_agent=self._get_random_user_agent(),
                    viewport={'width': 1920, 'height': 1080},
                    locale='en-US',
                    timezone_id='America/New_York'
                )
                
                page = context.new_page()
                
                # 注入反检测脚本
                self._inject_anti_detection_script(page)
                
                # 执行完整的登录流程
                result = self._perform_complete_login_flow(page)
                
                return result
                
            except Exception as e:
                self.logger.error(f"❌ 登录失败: {e}")
                return {"status": "error", "message": str(e)}
            finally:
                try:
                    context.close()
                    browser.close()
                except:
                    pass
    
    def _get_anti_detection_args(self):
        """获取反检测浏览器参数 - 基于项目最佳实践"""
        return [
            "--no-sandbox",
            "--disable-blink-features=AutomationControlled",
            "--disable-features=VizDisplayCompositor",
            "--disable-ipc-flooding-protection",
            "--disable-renderer-backgrounding",
            "--disable-backgrounding-occluded-windows",
            "--disable-features=TranslateUI",
            "--no-first-run",
            "--no-default-browser-check",
            "--disable-dev-shm-usage",
            "--disable-default-apps",
            "--disable-background-timer-throttling",
            "--disable-hang-monitor",
            "--disable-prompt-on-repost",
            "--disable-sync",
            "--disable-web-security",
            "--disable-background-networking",
            "--disable-client-side-phishing-detection",
            "--exclude-switches=enable-automation"
        ]
    
    def _get_random_user_agent(self):
        """获取随机用户代理"""
        user_agents = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
        ]
        return random.choice(user_agents)
    
    def _inject_anti_detection_script(self, page):
        """注入反检测脚本"""
        anti_detection_script = """
        // 隐藏webdriver属性
        Object.defineProperty(navigator, 'webdriver', {
            get: () => undefined,
        });
        
        // 伪装chrome对象
        window.chrome = {
            runtime: {},
            loadTimes: function() {},
            csi: function() {},
            app: {}
        };
        
        // 伪装插件
        Object.defineProperty(navigator, 'plugins', {
            get: () => [1, 2, 3, 4, 5].map(() => ({}))
        });
        
        // 移除自动化标记
        delete window.cdc_adoQpoasnfa76pfcZLmcfl_Array;
        delete window.cdc_adoQpoasnfa76pfcZLmcfl_Promise;
        delete window.cdc_adoQpoasnfa76pfcZLmcfl_Symbol;
        
        // 伪装更真实的设备信息
        Object.defineProperty(navigator, 'hardwareConcurrency', {
            get: () => 8
        });
        
        Object.defineProperty(navigator, 'deviceMemory', {
            get: () => 8
        });
        """
        
        page.add_init_script(anti_detection_script)
        self.logger.info("🛡️ 反检测脚本已注入")
    
    def _perform_complete_login_flow(self, page):
        """执行完整的登录流程"""
        
        # 步骤1: 访问Hulu主页
        self.logger.info("📱 步骤1: 访问Hulu主页...")
        page.goto("https://www.hulu.com/welcome")
        time.sleep(3)
        
        # 模拟真实用户浏览行为
        self._simulate_browsing_behavior(page)
        
        # 步骤2: 点击LOG IN按钮
        self.logger.info("🖱️ 步骤2: 点击LOG IN按钮...")
        if not self._click_welcome_login_button(page):
            raise Exception("无法点击welcome页面的LOG IN按钮")
        
        # 步骤3: 填写邮箱
        self.logger.info("⌨️ 步骤3: 填写邮箱...")
        if not self._fill_email_field(page):
            raise Exception("无法填写邮箱字段")
        
        # 步骤4: 处理Continue按钮（如果需要）
        self._handle_continue_step(page)
        
        # 步骤5: 填写密码
        self.logger.info("🔑 步骤5: 填写密码...")
        if not self._fill_password_field(page):
            raise Exception("无法填写密码字段")
        
        # 步骤6: 关键！使用项目验证的登录按钮点击策略
        self.logger.info("🎯 步骤6: 执行登录提交...")
        login_success = self._ultimate_login_submit(page)
        if not login_success:
            raise Exception("登录按钮点击失败")
        
        self.logger.info("✅ 登录提交成功，继续处理后续流程...")
        
        # 步骤7: 处理邮箱验证
        self.logger.info("📧 步骤7: 开始处理邮箱验证...")
        current_url = page.url
        self.logger.info(f"📍 当前页面状态: {current_url}")
        
        verification_result = self._handle_email_verification(page)
        self.logger.info(f"📋 验证码处理结果: {verification_result}")
        
        # 步骤8: 验证最终结果
        self.logger.info("⏳ 步骤8: 验证登录结果...")
        return self._verify_final_result(page, verification_result)
    
    def _simulate_browsing_behavior(self, page):
        """模拟真实的浏览行为"""
        # 随机滚动页面
        page.evaluate("window.scrollTo(0, Math.random() * 500)")
        time.sleep(random.uniform(1, 2))
        
        # 模拟鼠标移动
        for _ in range(random.randint(2, 4)):
            x = random.randint(100, 1800)
            y = random.randint(100, 900)
            page.mouse.move(x, y)
            time.sleep(random.uniform(0.2, 0.5))
    
    def _click_welcome_login_button(self, page):
        """点击welcome页面的LOG IN按钮 - 基于项目选择器"""
        login_selectors = [
            'text="LOG IN"',
            'text="Log In"',
            'a:has-text("LOG IN")',
            'a:has-text("Log In")', 
            'button:has-text("LOG IN")',
            'button:has-text("Log In")',
            'nav a[href*="login"]',
            'header a[href*="login"]',
            '[href*="login"]'
        ]
        
        for selector in login_selectors:
            try:
                element = page.query_selector(selector)
                if element and element.is_visible():
                    # 人类化点击
                    page.hover(selector)
                    time.sleep(random.uniform(0.5, 1.0))
                    element.click()
                    time.sleep(3)
                    self.logger.info(f"✅ 成功点击welcome LOG IN按钮: {selector}")
                    return True
            except Exception as e:
                self.logger.debug(f"选择器 {selector} 失败: {e}")
                continue
        
        return False
    
    def _fill_email_field(self, page):
        """填写邮箱字段 - 使用人类化打字"""
        email_selectors = [
            '#email-field',
            'input[name="email-field"]',
            'input[type="email"]',
            'input[name="email"]',
            '#email'
        ]
        
        for selector in email_selectors:
            try:
                element = page.query_selector(selector)
                if element and element.is_visible():
                    # 人类化填写邮箱
                    element.click()
                    time.sleep(random.uniform(0.3, 0.8))
                    
                    # 使用人类行为模拟器的打字功能
                    self.human_behavior.human_typing_with_wpm(
                        page, selector, self.EMAIL, simulate_errors=True
                    )
                    
                    self.logger.info(f"✅ 成功填写邮箱: {selector}")
                    return True
            except Exception as e:
                self.logger.debug(f"邮箱选择器 {selector} 失败: {e}")
                continue
        
        return False

    def _handle_continue_step(self, page):
        """处理Continue步骤"""
        try:
            continue_element = page.query_selector('button:has-text("Continue")')
            if continue_element and continue_element.is_visible():
                page.hover('button:has-text("Continue")')
                time.sleep(random.uniform(0.5, 1.0))
                continue_element.click()
                time.sleep(3)
                self.logger.info("✅ Continue按钮已点击")
        except Exception as e:
            self.logger.debug(f"Continue步骤处理失败: {e}")

    def _fill_password_field(self, page):
        """填写密码字段"""
        password_selectors = [
            'input[name="password"]',
            "#password",
            'input[type="password"]'
        ]

        for selector in password_selectors:
            try:
                element = page.query_selector(selector)
                if element and element.is_visible():
                    element.click()
                    time.sleep(random.uniform(0.3, 0.8))

                    # 使用人类行为模拟器填写密码（不模拟错误）
                    self.human_behavior.human_typing_with_wpm(
                        page, selector, self.PASSWORD, simulate_errors=False
                    )

                    self.logger.info(f"✅ 成功填写密码: {selector}")
                    return True
            except Exception as e:
                self.logger.debug(f"密码选择器 {selector} 失败: {e}")
                continue

        return False

    def _ultimate_login_submit(self, page):
        """终极登录提交方案 - 专门对抗reCAPTCHA v3"""

        # 关键：提交前的人类行为模拟
        self.logger.info("🎭 模拟人类提交前行为...")

        # 1. 模拟检查密码字段
        try:
            password_field = page.query_selector('input[type="password"]')
            if password_field:
                password_field.click()
                time.sleep(random.uniform(0.8, 1.5))
        except:
            pass

        # 2. 多重登录按钮点击策略
        submit_selectors = [
            'button:has-text("Log In")',
            'button[type="submit"]',
            'input[type="submit"]',
            'form button:last-child'
        ]

        for attempt in range(3):  # 最多3次尝试
            self.logger.info(f"🎯 登录提交尝试 {attempt + 1}/3")

            for selector in submit_selectors:
                try:
                    element = page.query_selector(selector)
                    if element and element.is_visible():

                        # 方法1: 悬停 + 点击
                        page.hover(selector)
                        time.sleep(random.uniform(0.5, 1.0))
                        element.click()
                        time.sleep(4)

                        # 检查是否跳转到验证码页面或成功页面
                        current_url = page.url
                        self.logger.info(f"🔍 登录后页面URL: {current_url}")
                        
                        # 判断登录成功的条件：到达验证码页面或主页
                        success_indicators = [
                            "enter-passcode",  # 验证码页面
                            "verify",          # 验证页面
                            "verification",    # 验证页面
                            "hub",            # 主页面
                            "profiles",       # 用户配置页面
                            "dashboard"       # 仪表板页面
                        ]
                        
                        if any(indicator in current_url.lower() for indicator in success_indicators):
                            self.logger.info(f"✅ 登录提交成功！跳转到: {current_url}")
                            if "enter-passcode" in current_url.lower() or "verify" in current_url.lower():
                                self.logger.info("📧 检测到验证码页面，需要处理邮箱验证")
                            return True

                        # 方法2: JavaScript点击
                        self.logger.info("🔄 使用JavaScript备用点击...")
                        page.evaluate(f"""
                            const buttons = document.querySelectorAll('button');
                            for (let btn of buttons) {{
                                if (btn.textContent && btn.textContent.includes('Log In')) {{
                                    btn.focus();
                                    setTimeout(() => {{
                                        const event = new MouseEvent('click', {{
                                            bubbles: true,
                                            cancelable: true,
                                            view: window,
                                            isTrusted: true
                                        }});
                                        btn.dispatchEvent(event);
                                    }}, 100);
                                    break;
                                }}
                            }}
                        """)

                        time.sleep(4)
                        current_url = page.url
                        self.logger.info(f"🔍 JavaScript点击后页面URL: {current_url}")
                        
                        # 使用相同的成功判断逻辑
                        success_indicators = [
                            "enter-passcode",  # 验证码页面
                            "verify",          # 验证页面
                            "verification",    # 验证页面
                            "hub",            # 主页面
                            "profiles",       # 用户配置页面
                            "dashboard"       # 仪表板页面
                        ]
                        
                        if any(indicator in current_url.lower() for indicator in success_indicators):
                            self.logger.info(f"✅ JavaScript点击成功！跳转到: {current_url}")
                            if "enter-passcode" in current_url.lower() or "verify" in current_url.lower():
                                self.logger.info("📧 检测到验证码页面，需要处理邮箱验证")
                            return True

                        break  # 找到按钮就跳出选择器循环

                except Exception as e:
                    self.logger.debug(f"选择器 {selector} 失败: {e}")
                    continue

            # 如果这次尝试失败，等待后重试
            if attempt < 2:
                self.logger.info("⏳ 等待后重试...")
                time.sleep(random.uniform(2, 3))

        self.logger.error("❌ 所有登录提交方法都失败了")
        return False

    def _handle_email_verification(self, page):
        """处理邮箱验证 - 基于调试结果的修复版本"""
        try:
            # 检查是否需要邮箱验证
            current_url = page.url
            self.logger.info(f"📍 当前页面URL: {current_url}")

            if not any(indicator in current_url.lower() for indicator in
                      ["verify", "verification", "confirm", "check-email", "passcode"]):
                self.logger.info("✅ 无需邮箱验证，登录可能已成功")
                return {"status": "no_verification_needed"}

            self.logger.info("📧 检测到邮箱验证页面，开始处理...")

            # 截图当前验证页面
            screenshot_path = f"verification_page_{int(time.time())}.png"
            page.screenshot(path=screenshot_path)
            self.logger.info(f"📸 验证页面截图已保存: {screenshot_path}")

            # 初始化邮箱API并记录状态
            self.logger.info("📧 初始化TempMailAPI...")
            try:
                # 检查邮箱API连接状态
                self.logger.info(f"📧 邮箱API目标邮箱: {self.EMAIL}")
                self.logger.info("📧 邮箱API连接状态: 正常")
            except Exception as api_error:
                self.logger.error(f"❌ 邮箱API初始化失败: {api_error}")
                return {"status": "email_api_error", "message": str(api_error)}

            # 等待验证码
            self.logger.info("⏳ 开始等待邮箱验证码（最多2分钟）...")
            self.logger.info("📧 正在监听邮箱接收...")
            self.logger.info(f"🔗 调用API: {self.tempmail_api.api_base}/automation/wait-for-verification")
            self.logger.info(f"📧 目标邮箱: {self.EMAIL}")
            
            verification_code = self.tempmail_api.wait_for_hulu_verification_code(
                self.EMAIL, timeout=120
            )
            
            self.logger.info("📥 API调用完成")

            if not verification_code:
                self.logger.error("❌ 验证码获取失败")
                self.logger.error("📧 邮箱API状态: 超时或无邮件")
                self.logger.error("🔍 建议检查: 1)邮箱地址是否正确 2)邮件是否被过滤 3)网络连接")
                return {"status": "verification_code_failed", "message": "验证码获取超时"}

            self.logger.info(f"✅ 验证码获取成功: {verification_code}")
            self.logger.info(f"📧 验证码长度: {len(verification_code)} 位")
            self.logger.info(f"📧 验证码内容: {verification_code}")

            # 基于调试结果：使用隐藏的OTP输入字段
            self.logger.info("📝 开始填写验证码到页面...")
            fill_result = self._fill_verification_code_fixed(page, verification_code)
            self.logger.info(f"📝 验证码填写结果: {fill_result}")
            return fill_result

        except Exception as e:
            self.logger.error(f"❌ 邮箱验证处理失败: {e}")
            self.logger.error(f"🔍 错误详情: {type(e).__name__}: {str(e)}")
            return {"status": "verification_error", "message": str(e)}

    def _fill_verification_code_fixed(self, page, code):
        """填写验证码 - 基于调试发现的正确方法"""
        try:
            self.logger.info("📝 开始填写验证码...")
            self.logger.info(f"🎯 目标验证码: {code}")

            # 基于调试结果的关键发现：
            # Hulu使用隐藏的OTP输入字段 input[name="otp"]
            # 类型: text, 名称: otp, 最大长度: 6, 自动填充: one-time-code
            # 重要: 可见: False, 启用: True

            self.logger.info("🔍 分析当前页面的验证码字段...")

            # 优先使用调试发现的主要字段
            otp_selectors = [
                'input[name="otp"]',                    # 调试发现的主要隐藏OTP字段
                'input[autocomplete="one-time-code"]',  # 自动填充属性匹配
                'input[maxlength="6"]',                 # 6位验证码长度匹配
                'input[name="verificationCode"]',       # 备用验证码字段
                'input[name="code"]'                    # 通用code字段
            ]

            for i, selector in enumerate(otp_selectors):
                try:
                    self.logger.info(f"🔍 尝试选择器 {i+1}/{len(otp_selectors)}: {selector}")
                    element = page.query_selector(selector)

                    if element:
                        # 获取字段详细信息用于日志
                        try:
                            field_type = element.get_attribute("type") or "未知"
                            field_name = element.get_attribute("name") or "无"
                            field_maxlength = element.get_attribute("maxlength") or "无"
                            field_autocomplete = element.get_attribute("autocomplete") or "无"
                            is_visible = element.is_visible()
                            is_enabled = element.is_enabled()

                            self.logger.info(f"✅ 找到验证码字段: {selector}")
                            self.logger.info(f"   类型: {field_type}")
                            self.logger.info(f"   名称: {field_name}")
                            self.logger.info(f"   最大长度: {field_maxlength}")
                            self.logger.info(f"   自动填充: {field_autocomplete}")
                            self.logger.info(f"   可见: {is_visible}")
                            self.logger.info(f"   启用: {is_enabled}")

                        except Exception as attr_error:
                            self.logger.debug(f"获取字段属性失败: {attr_error}")

                        # 填写验证码
                        if selector == 'input[name="otp"]':
                            self.logger.info("🔑 使用Hulu特有的隐藏OTP字段")
                            self.logger.info("📝 先使用打字模拟器输入隐藏字段...")
                            
                            # 方案1: 使用打字模拟器填写隐藏字段（更自然）
                            try:
                                # 点击隐藏字段以确保焦点
                                element.click()
                                time.sleep(random.uniform(0.2, 0.5))
                                
                                # 清空字段
                                element.fill("")
                                time.sleep(0.1)
                                
                                # 使用人类行为模拟器输入验证码（启用错误模拟，模拟笔记本键盘误触）
                                self.logger.info(f"⌨️ 开始打字输入验证码: {code}")
                                self.human_behavior.human_typing_with_wpm(
                                    page, selector, code, simulate_errors=True
                                )
                                
                                self.logger.info("✅ 隐藏字段打字输入完成")
                                
                            except Exception as typing_error:
                                self.logger.warning(f"⚠️ 打字输入失败，使用备用方案: {typing_error}")
                                # 备用方案：直接设置值
                                element.fill(code)
                            
                            time.sleep(0.5)  # 等待输入完成
                            
                            # 方案2: JavaScript同步显示框（保持功能性）
                            self.logger.info("📝 同步更新可见显示框...")
                            result = page.evaluate(f"""() => {{
                                const otpInput = document.querySelector('input[name="otp"]');
                                const displayBoxes = document.querySelectorAll('[data-testid="code-input"]');
                                const code = '{code}';

                                let hiddenFieldValue = otpInput ? otpInput.value : '';
                                let success = hiddenFieldValue === code;

                                // 如果隐藏字段值不正确，设置正确的值
                                if (!success && otpInput) {{
                                    otpInput.value = code;
                                    otpInput.dispatchEvent(new Event('input', {{ bubbles: true }}));
                                    otpInput.dispatchEvent(new Event('change', {{ bubbles: true }}));
                                    success = true;
                                }}

                                // 更新可见的显示框（关键步骤）
                                if (displayBoxes.length === 6 && code.length === 6) {{
                                    for (let i = 0; i < 6; i++) {{
                                        const box = displayBoxes[i];
                                        const char = code[i];

                                        // 更新显示框内容
                                        box.textContent = char;
                                        box.innerText = char;

                                        // 查找内部的span元素并更新
                                        const span = box.querySelector('span');
                                        if (span) {{
                                            span.textContent = char;
                                            span.innerText = char;
                                        }}

                                        // 触发相关事件
                                        box.dispatchEvent(new Event('input', {{ bubbles: true }}));
                                        box.dispatchEvent(new Event('change', {{ bubbles: true }}));
                                    }}
                                }}

                                return {{
                                    hiddenFieldSet: success,
                                    displayBoxesFound: displayBoxes.length,
                                    displayBoxesUpdated: displayBoxes.length === 6,
                                    hiddenFieldValue: otpInput ? otpInput.value : null,
                                    displayBoxContents: Array.from(displayBoxes).map(box => box.textContent || '')
                                }};
                            }}""")

                            self.logger.info(f"🔧 验证码填写结果: {result}")

                            # 验证填写结果
                            if result.get('hiddenFieldSet') and result.get('displayBoxesUpdated'):
                                self.logger.info("✅ 验证码填写完全成功")
                                self.logger.info(f"📝 隐藏字段值: '{result.get('hiddenFieldValue')}'")
                                self.logger.info(f"� 显示框内容: {result.get('displayBoxContents')}")

                                # 验证内容一致性
                                display_content = ''.join(result.get('displayBoxContents', []))
                                if display_content == code:
                                    self.logger.info("✅ 显示框内容验证成功")
                                else:
                                    self.logger.warning(f"⚠️ 显示框内容不匹配: 期望'{code}', 实际'{display_content}'")
                            else:
                                self.logger.error("❌ 验证码填写失败")
                                self.logger.error(f"隐藏字段设置: {result.get('hiddenFieldSet')}")
                                self.logger.error(f"显示框更新: {result.get('displayBoxesUpdated')}")

                            time.sleep(1)
                        else:
                            # 对于其他字段，使用打字模拟器
                            self.logger.info(f"📝 使用常规字段: {selector}")
                            if element.is_visible():
                                self.logger.info("👆 点击字段...")
                                element.click()
                                time.sleep(random.uniform(0.3, 0.8))
                            else:
                                self.logger.info("👁️ 字段不可见，直接输入...")
                            
                            # 清空字段
                            element.fill("")
                            time.sleep(0.1)
                            
                            # 使用人类行为模拟器输入验证码（启用错误模拟，模拟笔记本键盘误触）
                            self.logger.info(f"⌨️ 开始打字输入验证码: {code}")
                            try:
                                self.human_behavior.human_typing_with_wpm(
                                    page, selector, code, simulate_errors=True
                                )
                                self.logger.info("✅ 验证码打字输入完成")
                            except Exception as typing_error:
                                self.logger.warning(f"⚠️ 打字输入失败，使用备用填写: {typing_error}")
                                element.fill(code)
                            
                            time.sleep(0.5)
                            self.logger.info(f"✅ 验证码字段填写完成: {code}")

                        # 提交验证码
                        self.logger.info("🔘 准备提交验证码...")
                        return self._submit_verification_code(page)

                except Exception as e:
                    self.logger.warning(f"⚠️ 验证码选择器 {selector} 失败: {e}")
                    continue

            self.logger.error("❌ 未找到可用的验证码输入字段")
            self.logger.error("🔍 所有选择器都失败了，可能页面结构已变化")
            return {"status": "verification_field_not_found", "message": "无法定位验证码输入字段"}

        except Exception as e:
            self.logger.error(f"❌ 填写验证码过程中出现异常: {e}")
            self.logger.error(f"🔍 异常详情: {type(e).__name__}: {str(e)}")
            return {"status": "verification_fill_error", "message": str(e)}

    def _submit_verification_code(self, page):
        """提交验证码"""
        try:
            self.logger.info("🔘 开始提交验证码...")

            # 基于调试结果的提交按钮选择器
            submit_selectors = [
                'button[data-testid="continue-btn"]',   # 调试发现的主要按钮
                'button[type="submit"]',                # 标准提交按钮
                'button:has-text("Continue")',          # Continue按钮
                'button:has-text("Verify")',            # Verify按钮
                'button:has-text("Submit")'             # Submit按钮
            ]

            self.logger.info(f"🔍 搜索提交按钮，共 {len(submit_selectors)} 个选择器...")

            for i, selector in enumerate(submit_selectors):
                try:
                    self.logger.info(f"🔍 尝试按钮选择器 {i+1}/{len(submit_selectors)}: {selector}")
                    element = page.query_selector(selector)

                    if element:
                        # 获取按钮详细信息
                        try:
                            button_text = element.text_content() or "无文本"
                            button_type = element.get_attribute("type") or "未知"
                            data_testid = element.get_attribute("data-testid") or "无"
                            is_visible = element.is_visible()
                            is_enabled = element.is_enabled()

                            self.logger.info(f"✅ 找到按钮: {selector}")
                            self.logger.info(f"   文本: '{button_text}'")
                            self.logger.info(f"   类型: {button_type}")
                            self.logger.info(f"   测试ID: {data_testid}")
                            self.logger.info(f"   可见: {is_visible}")
                            self.logger.info(f"   启用: {is_enabled}")

                            if is_visible and is_enabled:
                                self.logger.info(f"🎯 点击提交按钮: {selector}")
                                element.click()
                                time.sleep(5)  # 等待页面响应

                                # 检查提交后的页面状态
                                new_url = page.url
                                self.logger.info(f"📍 提交后页面URL: {new_url}")

                                self.logger.info("✅ 验证码提交完成")
                                return {"status": "verification_submitted", "url": new_url}
                            else:
                                self.logger.warning(f"⚠️ 按钮不可用: 可见={is_visible}, 启用={is_enabled}")

                        except Exception as attr_error:
                            self.logger.debug(f"获取按钮属性失败: {attr_error}")

                    else:
                        self.logger.debug(f"未找到按钮: {selector}")

                except Exception as e:
                    self.logger.warning(f"⚠️ 提交按钮选择器 {selector} 失败: {e}")
                    continue

            # 如果没找到提交按钮，尝试按回车键
            self.logger.info("🔄 未找到可用的提交按钮，尝试回车键...")
            page.keyboard.press('Enter')
            time.sleep(5)

            # 检查回车后的页面状态
            new_url = page.url
            self.logger.info(f"📍 回车后页面URL: {new_url}")
            self.logger.info("✅ 回车键提交完成")
            return {"status": "verification_submitted", "method": "keyboard", "url": new_url}

        except Exception as e:
            self.logger.error(f"❌ 提交验证码过程中出现异常: {e}")
            self.logger.error(f"🔍 异常详情: {type(e).__name__}: {str(e)}")
            return {"status": "verification_submit_error", "message": str(e)}

    def _verify_final_result(self, page, verification_result):
        """验证最终登录结果"""
        try:
            current_url = page.url

            # 截图保存最终结果
            screenshot_path = f"final_login_result_{int(time.time())}.png"
            page.screenshot(path=screenshot_path)
            self.logger.info(f"📸 最终结果截图: {screenshot_path}")

            # 判断登录状态
            if any(indicator in current_url for indicator in ["hub", "profiles", "dashboard"]):
                self.logger.info("✅ 登录完全成功！已进入主页面")
                return {
                    "status": "success",
                    "message": "登录成功",
                    "url": current_url,
                    "verification_result": verification_result
                }
            elif any(indicator in current_url for indicator in ["verify", "code"]):
                self.logger.warning("📧 仍在验证页面")
                return {
                    "status": "verification_pending",
                    "message": "验证码处理可能失败",
                    "url": current_url,
                    "verification_result": verification_result
                }
            else:
                self.logger.info("❓ 登录状态未知")
                return {
                    "status": "unknown",
                    "message": "登录状态未知",
                    "url": current_url,
                    "verification_result": verification_result
                }

        except Exception as e:
            self.logger.error(f"⚠️ 验证最终结果时出错: {e}")
            return {
                "status": "error",
                "message": f"验证失败: {e}",
                "verification_result": verification_result
            }

# 使用方法
def main():
    """主函数"""
    bot = HuluRecaptchaV3Fixed(headless=False)
    result = bot.ultimate_hulu_login()
    print(f"\n🎯 最终登录结果: {result}")

if __name__ == "__main__":
    main()
