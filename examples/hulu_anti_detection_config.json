{"description": "Hulu特定的反检测配置文件", "version": "1.0.0", "last_updated": "2025-07-09", "core_features": {"enable_cdp_mode": false, "enable_stealth_script": true, "enable_fingerprint_masking": true, "enable_behavior_simulation": true, "enable_user_agent_rotation": true}, "cdp_settings": {"cdp_debug_port": 9222, "cdp_headless": false, "auto_close_browser": true, "browser_launch_timeout": 30, "custom_browser_path": null}, "browser_args": ["--no-sandbox", "--disable-blink-features=AutomationControlled", "--disable-features=VizDisplayCompositor", "--disable-ipc-flooding-protection", "--disable-renderer-backgrounding", "--disable-backgrounding-occluded-windows", "--disable-features=TranslateUI", "--disable-features=BlinkGenPropertyTrees", "--no-first-run", "--no-default-browser-check", "--disable-dev-shm-usage", "--disable-extensions", "--disable-default-apps", "--disable-component-extensions-with-background-pages", "--disable-background-timer-throttling", "--disable-field-trial-config", "--disable-back-forward-cache", "--disable-hang-monitor", "--disable-prompt-on-repost", "--disable-sync", "--disable-translate", "--metrics-recording-only", "--safebrowsing-disable-auto-update", "--password-store=basic", "--use-mock-keychain", "--disable-web-security", "--disable-background-networking", "--disable-client-side-phishing-detection", "--disable-popup-blocking", "--enable-automation"], "user_agents": {"desktop_user_agents": ["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"], "mobile_user_agents": ["Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1", "Mozilla/5.0 (Android 14; Mobile; rv:109.0) Gecko/109.0 Firefox/119.0"]}, "behavior_simulation": {"typing_wpm_range": [45, 52], "typing_error_rate": 0.025, "mouse_movement_easing": "ease-in-out", "human_delays_enabled": true, "pause_between_actions": true}, "timeouts": {"default_timeout": 30000, "page_load_timeout": 45000, "navigation_timeout": 45000, "element_timeout": 10000}, "viewport": {"viewport_width": 1920, "viewport_height": 1080, "locale": "en-US", "timezone": "America/New_York"}, "proxy_settings": {"proxy_server": null, "proxy_username": null, "proxy_password": null}, "hulu_specific": {"description": "Hulu网站特定的优化设置", "recommended_headless": false, "recommended_viewport": {"width": 1920, "height": 1080}, "extra_headers": {"Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9", "Accept-Encoding": "gzip, deflate, br", "Accept-Language": "en-US,en;q=0.9", "Cache-Control": "max-age=0", "Sec-Fetch-Dest": "document", "Sec-Fetch-Mode": "navigate", "Sec-Fetch-Site": "none", "Sec-Fetch-User": "?1", "Upgrade-Insecure-Requests": "1"}, "login_selectors": {"login_button": ["text=\"LOG IN\"", "text=\"Log In\"", "a:has-text(\"LOG IN\")", "a:has-text(\"Log In\")", "button:has-text(\"LOG IN\")", "button:has-text(\"Log In\")", "nav a[href*=\"login\"]", "header a[href*=\"login\"]", "[href*=\"login\"]"], "email_input": ["input[type=\"email\"]", "input[name=\"email\"]", "input[placeholder*=\"email\"]", "input[placeholder*=\"Email\"]", "#email"], "password_input": ["input[type=\"password\"]", "input[name=\"password\"]", "input[placeholder*=\"password\"]", "input[placeholder*=\"Password\"]", "#password"], "submit_button": ["button[type=\"submit\"]", "input[type=\"submit\"]", "button:has-text(\"Log In\")", "button:has-text(\"LOGIN\")", "button:has-text(\"Sign In\")"]}, "success_indicators": ["hub", "profiles", "dashboard"], "verification_indicators": ["verify", "code", "verification", "confirm"]}, "debug_settings": {"enable_debug_mode": false, "save_screenshots": true, "log_console_messages": true, "log_network_requests": false, "screenshot_directory": "debug_screenshots"}, "performance_optimization": {"disable_images": false, "disable_css": false, "disable_javascript": false, "block_ads": true, "block_trackers": true}, "fallback_strategies": {"max_retries": 3, "retry_delay": 2000, "fallback_to_basic_mode": true, "emergency_headless_mode": true}}