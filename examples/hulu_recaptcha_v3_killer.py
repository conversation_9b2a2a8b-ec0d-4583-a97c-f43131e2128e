#!/usr/bin/env python3
"""
Hulu reCAPTCHA v3 终极对抗方案
专门针对你遇到的登录按钮无响应问题
"""

import asyncio
import random
import time
import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from playwright.async_api import async_playwright
from infrastructure.anti_detection.service import AntiDetectionService
from infrastructure.anti_detection.config import AntiDetectionConfig

class HuluRecaptchaKiller:
    """Hulu reCAPTCHA v3 终极对抗器"""
    
    def __init__(self):
        # 最大隐身配置 + reCAPTCHA v3 特殊优化
        self.config = AntiDetectionConfig(
            # 核心反检测功能全开
            enable_cdp_mode=False,  # CDP模式对reCAPTCHA v3效果不明显
            enable_stealth_script=True,
            enable_fingerprint_masking=True,
            enable_behavior_simulation=True,
            enable_user_agent_rotation=True,

            # 人类化行为参数（关键！）
            typing_wpm_range=(42, 48),  # 稍慢的打字速度更真实
            typing_error_rate=0.035,    # 增加错误率
            mouse_movement_easing=True,  # 启用鼠标移动缓动
            human_delays_enabled=True,

            # 超时设置
            default_timeout=45000,

            # 视口设置
            viewport_width=1920,
            viewport_height=1080,

            # reCAPTCHA v3 特殊优化参数
            browser_args=[
                "--no-sandbox",
                "--disable-blink-features=AutomationControlled",
                "--disable-features=VizDisplayCompositor",
                "--disable-ipc-flooding-protection",
                "--disable-renderer-backgrounding",
                "--disable-backgrounding-occluded-windows",
                "--disable-features=TranslateUI",
                "--no-first-run",
                "--no-default-browser-check",
                "--disable-dev-shm-usage",
                "--disable-default-apps",
                "--disable-background-timer-throttling",
                "--disable-hang-monitor",
                "--disable-prompt-on-repost",
                "--disable-sync",
                # reCAPTCHA v3 专用参数
                "--disable-web-security",
                "--disable-background-networking",
                "--disable-client-side-phishing-detection",
                "--disable-popup-blocking",
                "--exclude-switches=enable-automation"
            ]
        )
        
        self.anti_detection_service = AntiDetectionService(self.config)
        
        # 硬编码账号密码
        self.EMAIL = "<EMAIL>"
        self.PASSWORD = "Fiona127,."
    
    async def ultimate_hulu_login(self):
        """终极Hulu登录方案"""
        print("🚀 启动Hulu reCAPTCHA v3 终极对抗方案...")
        
        async with async_playwright() as playwright:
            try:
                # 创建最强隐身浏览器
                context = await self.anti_detection_service.create_stealth_browser_context(
                    playwright=playwright,
                    headless=False  # 非无头模式对reCAPTCHA v3更有效
                )
                
                page = await context.new_page()
                
                # 应用反检测增强
                await self.anti_detection_service.enhance_page(page)
                
                # 注入reCAPTCHA v3 专用对抗脚本
                await self._inject_recaptcha_killer_script(page)
                
                # 执行完整的人类化登录流程
                result = await self._perform_human_like_login(page)
                
                return result
                
            except Exception as e:
                print(f"❌ 登录失败: {e}")
                return {"status": "error", "message": str(e)}
            finally:
                await context.close()
                await self.anti_detection_service.cleanup()
    
    async def _inject_recaptcha_killer_script(self, page):
        """注入reCAPTCHA v3 专用对抗脚本"""
        recaptcha_killer_script = """
        // reCAPTCHA v3 终极对抗脚本
        
        // 1. 伪装更真实的鼠标事件
        const originalAddEventListener = EventTarget.prototype.addEventListener;
        EventTarget.prototype.addEventListener = function(type, listener, options) {
            if (type === 'mousedown' || type === 'mouseup' || type === 'click') {
                const wrappedListener = function(event) {
                    // 添加真实的鼠标事件属性
                    Object.defineProperty(event, 'isTrusted', { value: true });
                    return listener.call(this, event);
                };
                return originalAddEventListener.call(this, type, wrappedListener, options);
            }
            return originalAddEventListener.call(this, type, listener, options);
        };
        
        // 2. 伪装触摸事件
        Object.defineProperty(navigator, 'maxTouchPoints', {
            get: () => 1
        });
        
        // 3. 伪装设备方向
        Object.defineProperty(screen, 'orientation', {
            get: () => ({
                angle: 0,
                type: 'landscape-primary'
            })
        });
        
        // 4. 伪装网络连接
        Object.defineProperty(navigator, 'connection', {
            get: () => ({
                effectiveType: '4g',
                rtt: 50,
                downlink: 10,
                saveData: false
            })
        });
        
        // 5. 伪装电池状态
        Object.defineProperty(navigator, 'getBattery', {
            value: () => Promise.resolve({
                charging: true,
                chargingTime: 0,
                dischargingTime: Infinity,
                level: 0.8 + Math.random() * 0.2
            })
        });
        
        // 6. 伪装媒体设备
        Object.defineProperty(navigator.mediaDevices, 'enumerateDevices', {
            value: () => Promise.resolve([
                { deviceId: 'default', kind: 'audioinput', label: 'Default - Built-in Microphone' },
                { deviceId: 'default', kind: 'audiooutput', label: 'Default - Built-in Speakers' }
            ])
        });
        
        // 7. 移除reCAPTCHA检测标记
        delete window.cdc_adoQpoasnfa76pfcZLmcfl_Array;
        delete window.cdc_adoQpoasnfa76pfcZLmcfl_Promise;
        delete window.cdc_adoQpoasnfa76pfcZLmcfl_Symbol;
        delete window.cdc_adoQpoasnfa76pfcZLmcfl_JSON;
        delete window.cdc_adoQpoasnfa76pfcZLmcfl_Object;
        
        // 8. 伪装WebDriver属性
        Object.defineProperty(navigator, 'webdriver', {
            get: () => undefined
        });
        
        console.log('🛡️ reCAPTCHA v3 对抗脚本已激活');
        """
        
        await page.add_init_script(recaptcha_killer_script)
        print("🛡️ reCAPTCHA v3 对抗脚本已注入")
    
    async def _perform_human_like_login(self, page):
        """执行完整的人类化登录流程"""
        
        # 步骤1: 访问Hulu主页（模拟真实用户行为）
        print("📱 步骤1: 访问Hulu主页...")
        await page.goto("https://www.hulu.com", wait_until="networkidle")
        
        # 模拟真实用户浏览行为
        await self._simulate_browsing_behavior(page)
        
        # 步骤2: 点击LOG IN按钮
        print("🖱️ 步骤2: 人类化点击LOG IN...")
        await self._human_click_login_button(page)
        
        # 步骤3: 填写邮箱
        print("⌨️ 步骤3: 人类化输入邮箱...")
        await self._human_type_email(page)
        
        # 步骤4: 点击Continue（如果需要）
        await self._handle_continue_step(page)
        
        # 步骤5: 填写密码
        print("🔑 步骤5: 人类化输入密码...")
        await self._human_type_password(page)
        
        # 步骤6: 关键！人类化点击最终登录按钮
        print("🎯 步骤6: 终极人类化登录提交...")
        await self._ultimate_human_login_submit(page)
        
        # 步骤7: 等待和验证结果
        print("⏳ 步骤7: 等待登录结果...")
        return await self._verify_login_result(page)
    
    async def _simulate_browsing_behavior(self, page):
        """模拟真实的浏览行为"""
        # 随机滚动页面
        await page.evaluate("window.scrollTo(0, Math.random() * 500)")
        await page.wait_for_timeout(random.randint(1000, 2000))
        
        # 模拟鼠标在页面上的随机移动
        for _ in range(random.randint(2, 4)):
            x = random.randint(100, 1800)
            y = random.randint(100, 900)
            await page.mouse.move(x, y)
            await page.wait_for_timeout(random.randint(200, 500))
    
    async def _human_click_login_button(self, page):
        """人类化点击LOG IN按钮"""
        # 先移动鼠标到按钮附近
        login_selectors = [
            'text="LOG IN"',
            'text="Log In"', 
            'a:has-text("LOG IN")',
            'button:has-text("LOG IN")'
        ]
        
        for selector in login_selectors:
            try:
                # 先悬停
                await page.hover(selector, timeout=5000)
                await page.wait_for_timeout(random.randint(800, 1500))
                
                # 人类化点击
                await page.click(selector, delay=random.randint(50, 150))
                await page.wait_for_timeout(random.randint(2000, 3000))
                break
            except:
                continue
    
    async def _human_type_email(self, page):
        """人类化输入邮箱"""
        email_selectors = [
            'input[type="email"]',
            'input[name="email"]',
            '#email'
        ]
        
        for selector in email_selectors:
            try:
                await page.click(selector)
                await page.wait_for_timeout(random.randint(300, 800))
                
                # 使用反检测服务的人类化打字
                await self.anti_detection_service.simulate_human_typing(
                    page, selector, self.EMAIL, simulate_errors=True
                )
                break
            except:
                continue
    
    async def _handle_continue_step(self, page):
        """处理Continue步骤"""
        try:
            continue_button = page.locator('button:has-text("Continue")')
            if await continue_button.is_visible(timeout=3000):
                await page.hover('button:has-text("Continue")')
                await page.wait_for_timeout(random.randint(500, 1000))
                await page.click('button:has-text("Continue")', delay=random.randint(50, 150))
                await page.wait_for_timeout(random.randint(2000, 3000))
        except:
            pass
    
    async def _human_type_password(self, page):
        """人类化输入密码"""
        password_selectors = [
            'input[type="password"]',
            'input[name="password"]',
            '#password'
        ]
        
        for selector in password_selectors:
            try:
                await page.click(selector)
                await page.wait_for_timeout(random.randint(300, 800))
                
                # 密码输入不模拟错误
                await self.anti_detection_service.simulate_human_typing(
                    page, selector, self.PASSWORD, simulate_errors=False
                )
                break
            except:
                continue
    
    async def _ultimate_human_login_submit(self, page):
        """终极人类化登录提交 - 专门对抗reCAPTCHA v3"""
        
        # 关键：在提交前增加更多人类行为
        print("🎭 模拟人类提交前的犹豫行为...")
        
        # 1. 模拟检查输入内容
        await page.click('input[type="email"]')
        await page.wait_for_timeout(random.randint(500, 1000))
        await page.click('input[type="password"]')
        await page.wait_for_timeout(random.randint(800, 1500))
        
        # 2. 模拟鼠标在提交按钮附近徘徊
        submit_selectors = [
            'button:has-text("Log In")',
            'button[type="submit"]',
            'input[type="submit"]',
            'form button:last-child'
        ]
        
        for selector in submit_selectors:
            try:
                # 获取按钮位置
                button = page.locator(selector)
                if await button.is_visible(timeout=3000):
                    
                    # 模拟鼠标接近按钮
                    box = await button.bounding_box()
                    if box:
                        # 先移动到按钮附近
                        await page.mouse.move(box['x'] + box['width']/2 - 20, box['y'] + box['height']/2)
                        await page.wait_for_timeout(random.randint(300, 600))
                        
                        # 再移动到按钮上
                        await page.mouse.move(box['x'] + box['width']/2, box['y'] + box['height']/2)
                        await page.wait_for_timeout(random.randint(800, 1500))
                        
                        # 关键：使用原生鼠标事件而不是Playwright的click
                        print("🎯 执行终极人类化点击...")
                        
                        # 方法1: 先尝试悬停+点击
                        await page.hover(selector)
                        await page.wait_for_timeout(random.randint(500, 1000))
                        
                        # 方法2: 使用带延迟的点击
                        await page.click(selector, delay=random.randint(100, 200))
                        
                        # 方法3: 如果还不行，使用JavaScript触发
                        await page.wait_for_timeout(2000)
                        current_url = page.url
                        if "login" in current_url:  # 还在登录页面
                            print("🔄 使用备用点击方法...")
                            await page.evaluate(f"""
                                const button = document.querySelector('{selector}');
                                if (button) {{
                                    button.focus();
                                    setTimeout(() => {{
                                        button.click();
                                    }}, 100);
                                }}
                            """)
                        
                        break
            except Exception as e:
                print(f"⚠️ 选择器 {selector} 失败: {e}")
                continue
    
    async def _verify_login_result(self, page):
        """验证登录结果"""
        # 等待页面响应
        await page.wait_for_timeout(8000)
        
        current_url = page.url
        print(f"🔍 当前URL: {current_url}")
        
        # 截图保存结果
        await page.screenshot(path=f"login_result_{int(time.time())}.png")
        
        if "hub" in current_url or "profiles" in current_url:
            print("✅ 登录成功！已跳转到主页面")
            return {"status": "success", "message": "登录成功", "url": current_url}
        elif "verify" in current_url or "code" in current_url:
            print("📧 需要邮箱验证")
            return {"status": "verification_needed", "message": "需要邮箱验证", "url": current_url}
        elif "login" in current_url:
            print("⚠️ 仍在登录页面，可能需要进一步处理")
            return {"status": "still_login_page", "message": "仍在登录页面", "url": current_url}
        else:
            print("❓ 登录状态未知")
            return {"status": "unknown", "message": "登录状态未知", "url": current_url}

# 使用方法
async def main():
    killer = HuluRecaptchaKiller()
    result = await killer.ultimate_hulu_login()
    print(f"\n🎯 最终结果: {result}")

if __name__ == "__main__":
    asyncio.run(main())
