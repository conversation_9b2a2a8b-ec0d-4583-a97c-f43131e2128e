#!/usr/bin/env python3
"""
Quick-Wins集成示例 - 展示完整系统的使用方式

演示内容:
1. 统一选择器管理系统使用
2. 结构化日志集成
3. Prometheus指标收集
4. 健康检查系统集成
5. 与现有SimpleMonitor系统的配合

使用方法:
    python examples/quick_wins_integration_demo.py
"""

import asyncio
import sys
import time
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 导入Quick-Wins组件
from utils.locators import SelectorManager
from utils.logger import configure_logging, get_logger, get_task_logger
from metrics import get_metrics, get_integration
from infrastructure.simple_monitor import SimpleMonitor


class QuickWinsDemo:
    """Quick-Wins系统集成演示"""
    
    def __init__(self):
        """初始化演示系统"""
        
        # 1. 配置结构化日志
        configure_logging(log_level="INFO", output_format="dev")
        self.logger = get_logger("demo")
        
        # 2. 初始化选择器管理器
        self.selector_manager = SelectorManager()
        
        # 3. 初始化监控系统
        self.monitor = SimpleMonitor()
        
        # 4. 初始化Prometheus指标 (如果可用)
        self.metrics = get_metrics(port=9102, auto_start=False)  # 使用不同端口避免冲突
        self.integration = get_integration() if self.metrics else None
        
        # 5. 创建任务日志器
        self.task_logger = get_task_logger("demo_task")
        
        self.logger.info("🚀 Quick-Wins演示系统初始化完成")
    
    def demo_selector_management(self):
        """演示选择器管理系统"""
        self.logger.info("📋 === 选择器管理系统演示 ===")
        
        # 获取不同优先级的选择器
        email_selectors = self.selector_manager.get_selectors(
            'login_flow.email_fields', 
            priority='primary'
        )
        self.logger.info(f"🔍 主要邮箱选择器: {len(email_selectors)} 个")
        
        # 获取动态宿主选择器
        login_buttons = self.selector_manager.get_selectors(
            'login_flow.welcome_login_buttons',
            host='iframe[src*="hulu.com"]',
            priority='primary'
        )
        self.logger.info(f"🎯 动态宿主登录按钮: {len(login_buttons)} 个")
        
        # 获取健康检查流程
        flow = self.selector_manager.get_healthcheck_flow()
        self.logger.info(f"🏥 健康检查流程: {len(flow)} 个阶段")
        
        for step in flow[:3]:  # 显示前3个阶段
            self.logger.info(f"   - {step['phase']}: {step['description']}")
    
    def demo_structured_logging(self):
        """演示结构化日志系统"""
        self.logger.info("📊 === 结构化日志系统演示 ===")
        
        # 任务级别的结构化日志
        self.task_logger.log_phase_start("email_detection")
        
        # 模拟选择器查找
        start_time = time.time()
        time.sleep(0.1)  # 模拟查找耗时
        duration_ms = (time.time() - start_time) * 1000
        
        self.task_logger.log_selector_found(
            selector="#email-field",
            phase="email_detection",
            duration_ms=duration_ms,
            attempts=1,
            success_rate=1.0
        )
        
        self.task_logger.log_phase_success(
            phase="email_detection",
            duration_ms=duration_ms,
            selector="#email-field"
        )
        
        # 监控数据记录
        self.task_logger.log_monitoring_data(
            operation="element_location",
            duration_ms=duration_ms,
            success=True,
            selector_count=5,
            phase="email_detection"
        )
    
    def demo_monitoring_integration(self):
        """演示监控系统集成"""
        self.logger.info("📈 === 监控系统集成演示 ===")
        
        # 使用SimpleMonitor记录数据
        operations = [
            ("iframe_detection", 12.5, True),
            ("host_discovery", 45.2, True),
            ("element_location", 123.4, True),
            ("element_location", 156.7, False),  # 模拟失败
        ]
        
        for operation, duration_ms, success in operations:
            self.monitor.record(operation, duration_ms, success)
            self.logger.info(f"📊 记录监控数据: {operation} - {duration_ms}ms - {'成功' if success else '失败'}")
        
        # 获取统计数据
        stats = self.monitor.get_recent_stats()
        self.logger.info(f"📈 整体统计: 成功率 {stats['success_rate']:.1%}, 平均耗时 {stats['avg_duration_ms']:.1f}ms")
        
        # 如果Prometheus可用，记录指标
        if self.metrics:
            for operation, duration_ms, success in operations:
                if operation == "element_location":
                    self.metrics.record_selector_operation(
                        phase="email_detection",
                        selector_name="#email-field",
                        success=success,
                        duration_seconds=duration_ms / 1000.0
                    )
                else:
                    self.metrics.record_performance_metric(operation, duration_ms / 1000.0)
            
            self.logger.info("📊 Prometheus指标已更新")
    
    def demo_health_check_integration(self):
        """演示健康检查集成"""
        self.logger.info("🏥 === 健康检查系统演示 ===")
        
        # 获取网络拦截配置
        stubs = self.selector_manager.get_network_stubs()
        self.logger.info(f"🌐 网络拦截规则: {len(stubs)} 条")
        
        for stub in stubs:
            self.logger.info(f"   - {stub['pattern']} → {stub['response']['status']}")
        
        # 获取reCAPTCHA Mock脚本
        mock_script = self.selector_manager.get_recaptcha_mock_script()
        script_preview = mock_script.replace('\n', ' ').strip()[:100] + "..." if mock_script else "无"
        self.logger.info(f"🤖 reCAPTCHA Mock: {script_preview}")
        
        # 模拟健康检查结果
        if self.metrics:
            # 模拟一些健康检查指标
            self.metrics.record_task_completion("welcome_page", True, 1.2)
            self.metrics.record_task_completion("login_page", True, 2.1)
            self.metrics.update_system_health("healthy")
            self.metrics.update_active_tasks(1)
            
            self.logger.info("✅ 健康检查指标已更新")
    
    def demo_performance_monitoring(self):
        """演示性能监控"""
        self.logger.info("⚡ === 性能监控演示 ===")
        
        # 模拟不同的性能场景
        scenarios = [
            ("高性能场景", 0.05, True),
            ("正常场景", 0.15, True),
            ("慢速场景", 0.8, True),
            ("失败场景", 1.2, False),
        ]
        
        for scenario, duration, success in scenarios:
            # 记录到SimpleMonitor
            self.monitor.record("element_location", duration * 1000, success)
            
            # 记录到Prometheus
            if self.metrics:
                self.metrics.record_selector_operation(
                    phase="performance_test",
                    selector_name=f"test_selector_{scenario}",
                    success=success,
                    duration_seconds=duration
                )
            
            self.logger.info(f"⚡ {scenario}: {duration:.3f}s - {'成功' if success else '失败'}")
        
        # 获取记录数量（在导出前）
        record_count_before_dump = self.monitor.get_record_count()
        
        # 强制导出监控数据
        self.monitor.force_dump()
        self.logger.info(f"💾 监控数据已导出到CSV: {record_count_before_dump} 条记录")
    
    def run_demo(self):
        """运行完整演示"""
        self.logger.info("🎬 开始Quick-Wins系统完整演示")
        
        # 启动Prometheus服务器 (如果可用)
        if self.metrics and self.metrics.start_server():
            self.logger.info(f"🚀 Prometheus指标服务器已启动: http://localhost:9102/metrics")
        
        try:
            # 运行各个演示模块
            self.demo_selector_management()
            self.demo_structured_logging()
            self.demo_monitoring_integration()
            self.demo_health_check_integration()
            self.demo_performance_monitoring()
            
            # 最终统计 (数据已导出，显示总体统计)
            total_stats = self.monitor.get_recent_stats(last_minutes=60)  # 最近1小时的数据
            self.logger.info(f"📊 演示完成: 整体成功率 {total_stats['success_rate']:.1%}, 平均耗时 {total_stats['avg_duration_ms']:.1f}ms")
            
            if self.metrics and self.metrics.is_server_running():
                self.logger.info("💡 访问 http://localhost:9102/metrics 查看Prometheus指标")
                self.logger.info("💡 指标将保持可用10秒用于测试...")
                time.sleep(10)
            
            self.logger.info("✅ Quick-Wins系统演示完成")
            
        except Exception as e:
            self.logger.error(f"❌ 演示过程中出现异常: {e}")
            raise


async def async_demo():
    """异步演示函数"""
    demo = QuickWinsDemo()
    demo.run_demo()


def main():
    """主入口函数"""
    print("🎬 Quick-Wins集成系统演示")
    print("=" * 50)
    
    try:
        # 运行演示
        demo = QuickWinsDemo()
        demo.run_demo()
        
    except KeyboardInterrupt:
        print("\n🛑 用户中断演示")
    except Exception as e:
        print(f"\n💥 演示失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()