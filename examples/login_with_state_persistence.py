#!/usr/bin/env python3
"""
登录状态持久化使用示例
演示如何使用HuluBot的登录状态保存和恢复功能
"""

import sys
import os
import time

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils import setup_logging
from hulu_bot import <PERSON>luBot

def example_first_login():
    """示例1：首次登录并保存状态"""
    logger = setup_logging()
    logger.info("📝 示例1：首次登录并保存状态")
    logger.info("=" * 50)
    
    # 配置
    email = "<EMAIL>"
    password = "Fiona127,."
    state_file = "my_hulu_session.json"
    
    # 使用HuluBot进行登录
    with Hu<PERSON>Bot(headless=False, storage_state_path=state_file) as bot:
        logger.info("🚀 开始登录...")
        
        # 检查是否已经登录
        if bot.is_logged_in():
            logger.info("✅ 已处于登录状态")
        else:
            # 执行登录
            result = bot.login_account(email=email, password=password)
            
            if result['login_status'] == 'success':
                logger.info("✅ 登录成功!")
                logger.info(f"💾 状态已保存到: {state_file}")
            else:
                logger.error("❌ 登录失败")
                return False
    
    return True

def example_restore_login():
    """示例2：恢复保存的登录状态"""
    logger = setup_logging()
    logger.info("📝 示例2：恢复保存的登录状态")
    logger.info("=" * 50)
    
    state_file = "my_hulu_session.json"
    
    # 检查状态文件是否存在
    if not os.path.exists(state_file):
        logger.error(f"❌ 状态文件不存在: {state_file}")
        logger.info("💡 请先运行示例1进行首次登录")
        return False
    
    # 使用保存的状态创建新会话
    with HuluBot(headless=False, storage_state_path=state_file) as bot:
        logger.info("🔄 尝试恢复登录状态...")
        
        # 恢复会话
        if bot.restore_session_if_needed():
            logger.info("✅ 登录状态恢复成功!")
            
            # 显示当前状态
            current_url = bot.page.url
            logger.info(f"🌐 当前页面: {current_url}")
            
            # 测试页面操作
            logger.info("🧪 测试页面刷新...")
            bot.page.reload()
            time.sleep(3)
            
            if bot.is_logged_in():
                logger.info("✅ 刷新后仍保持登录状态")
            else:
                logger.warning("⚠️ 刷新后登录状态丢失")
                
        else:
            logger.warning("⚠️ 无法恢复登录状态，可能需要重新登录")
            return False
    
    return True

def example_state_management():
    """示例3：状态文件管理"""
    logger = setup_logging()
    logger.info("📝 示例3：状态文件管理")
    logger.info("=" * 50)
    
    state_file = "my_hulu_session.json"
    
    # 创建bot实例
    bot = HuluBot(storage_state_path=state_file)
    
    # 检查状态文件
    if bot._load_storage_state():
        logger.info(f"✅ 状态文件有效: {state_file}")
        
        # 显示文件信息
        file_size = os.path.getsize(state_file)
        file_age = time.time() - os.path.getmtime(state_file)
        logger.info(f"📁 文件大小: {file_size} 字节")
        logger.info(f"⏰ 文件年龄: {file_age/3600:.1f} 小时")
        
        # 询问是否清除状态
        user_input = input("\n🗑️ 是否清除保存的登录状态? (y/N): ")
        if user_input.lower() == 'y':
            if bot.clear_storage_state():
                logger.info("✅ 登录状态已清除")
            else:
                logger.error("❌ 清除登录状态失败")
        else:
            logger.info("ℹ️ 保持当前状态")
    else:
        logger.info(f"❌ 状态文件无效或不存在: {state_file}")

def main():
    """主函数 - 提供交互式菜单"""
    logger = setup_logging()
    logger.info("🎯 Hulu登录状态持久化示例")
    logger.info("=" * 50)
    
    while True:
        print("\n📋 请选择要运行的示例:")
        print("1. 首次登录并保存状态")
        print("2. 恢复保存的登录状态")
        print("3. 状态文件管理")
        print("4. 退出")
        
        try:
            choice = input("\n请输入选项 (1-4): ").strip()
            
            if choice == '1':
                example_first_login()
            elif choice == '2':
                example_restore_login()
            elif choice == '3':
                example_state_management()
            elif choice == '4':
                logger.info("👋 再见!")
                break
            else:
                print("❌ 无效选项，请重新选择")
                
        except KeyboardInterrupt:
            logger.info("\n⏹️ 用户中断程序")
            break
        except Exception as e:
            logger.error(f"❌ 执行出错: {e}")

if __name__ == "__main__":
    main()
