import asyncio
import logging
import time
import os
import platform
import subprocess
import random
import string
from playwright.async_api import async_playwright, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ontext
from typing import Optional
import aiohttp

# --------------------------------------------------------------------------
# CDP (Chrome DevTools Protocol) Mode Implementation
#
# This script is now designed to connect to an already running instance of
# Chrome that has been started with a remote debugging port. This is the
# core of the MediaCrawler anti-detection strategy.
#
# HOW TO LAUNCH CHROME FOR THIS SCRIPT:
#
# 1. Close all running instances of Chrome.
#
# 2. Start Chrome from your terminal with the remote debugging flag.
#    You can use a dedicated user profile to keep it separate from your main
#    browsing session.
#
# CORRECT CROSS-PLATFORM LAUNCH COMMANDS:
#
# === macOS (推荐方式) ===
# open -a "Google Chrome" --args --remote-debugging-port=9222 --user-data-dir="/tmp/chrome-profile-hulu"
#
# === Linux ===
# google-chrome --remote-debugging-port=9222 --user-data-dir="/tmp/chrome-profile-hulu"
# # 或者使用：
# google-chrome-stable --remote-debugging-port=9222 --user-data-dir="/tmp/chrome-profile-hulu"
#
# === Windows ===
# "C:\Program Files\Google\Chrome\Application\chrome.exe" --remote-debugging-port=9222 --user-data-dir="C:\temp\chrome-profile-hulu"
#
# 3. Keep this terminal window open and the browser running. Now, you can
#    run this Python script. It will connect to this browser instance.
#
# 4. Alternatively, use the auto-launch functionality (see AUTO_LAUNCH_CHROME below)
# --------------------------------------------------------------------------


# --- Chrome Launcher Utility ---
def get_chrome_launch_command(debug_port: int = 9222, user_data_dir: str = None) -> list:
    """
    获取当前系统的正确Chrome启动命令
    
    Args:
        debug_port: CDP调试端口
        user_data_dir: 用户数据目录路径
    
    Returns:
        Chrome启动命令的列表形式
    """
    system = platform.system()
    
    if user_data_dir is None:
        if system == "Windows":
            user_data_dir = "C:\\temp\\chrome-profile-hulu"
        else:
            user_data_dir = "/tmp/chrome-profile-hulu"
    
    if system == "Darwin":  # macOS
        # 使用 open -a 方式启动 (推荐)
        return [
            "open", "-a", "Google Chrome", "--args",
            f"--remote-debugging-port={debug_port}",
            f"--user-data-dir={user_data_dir}",
            "--no-first-run",
            "--disable-background-timer-throttling",
            "--disable-backgrounding-occluded-windows",
            "--disable-renderer-backgrounding",
            "--disable-features=TranslateUI",
            "--disable-blink-features=AutomationControlled",
            "--disable-infobars"
        ]
    elif system == "Linux":
        # Linux系统使用 google-chrome 命令
        return [
            "google-chrome",
            f"--remote-debugging-port={debug_port}",
            f"--user-data-dir={user_data_dir}",
            "--no-first-run",
            "--disable-background-timer-throttling",
            "--disable-backgrounding-occluded-windows", 
            "--disable-renderer-backgrounding",
            "--disable-features=TranslateUI",
            "--disable-blink-features=AutomationControlled",
            "--disable-infobars"
        ]
    elif system == "Windows":
        # Windows系统使用完整路径
        chrome_path = "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe"
        return [
            chrome_path,
            f"--remote-debugging-port={debug_port}",
            f"--user-data-dir={user_data_dir}",
            "--no-first-run",
            "--disable-background-timer-throttling",
            "--disable-backgrounding-occluded-windows",
            "--disable-renderer-backgrounding", 
            "--disable-features=TranslateUI",
            "--disable-blink-features=AutomationControlled",
            "--disable-infobars"
        ]
    else:
        raise OSError(f"不支持的操作系统: {system}")


def auto_launch_chrome(debug_port: int = 9222, user_data_dir: str = None) -> subprocess.Popen:
    """
    自动启动Chrome浏览器并返回进程对象
    
    Args:
        debug_port: CDP调试端口
        user_data_dir: 用户数据目录
    
    Returns:
        Chrome进程对象
    """
    command = get_chrome_launch_command(debug_port, user_data_dir)
    
    logging.info(f"🚀 自动启动Chrome: {' '.join(command)}")
    
    try:
        # 创建用户数据目录
        if user_data_dir:
            os.makedirs(user_data_dir, exist_ok=True)
        
        # 启动Chrome进程
        if platform.system() == "Windows":
            process = subprocess.Popen(
                command,
                stdout=subprocess.DEVNULL,
                stderr=subprocess.DEVNULL,
                creationflags=subprocess.CREATE_NEW_PROCESS_GROUP
            )
        else:
            process = subprocess.Popen(
                command,
                stdout=subprocess.DEVNULL,
                stderr=subprocess.DEVNULL,
                preexec_fn=os.setsid  # 创建新的进程组
            )
        
        logging.info(f"✅ Chrome已启动，进程ID: {process.pid}")
        return process
        
    except Exception as e:
        logging.error(f"❌ 启动Chrome失败: {e}")
        raise


# --- Mock Module Placeholders ---
class HuluError(Exception):
    """Custom base exception."""
    pass

class ElementNotFoundError(HuluError):
    """Thrown when an element cannot be found."""
    pass

class LoginError(HuluError):
    """Thrown during login process errors."""
    pass

class HuluSelectors:
    """Central repository for all Hulu page CSS selectors."""
    EMAIL_INPUT = "input[type=\"email\"]"
    PASSWORD_INPUT = "input[aria-label=\"Password\"]"
    LOGIN_BUTTON = "button[data-testid='login-button']"
    CONTINUE_BUTTON = "button[data-testid='continue-btn']"
    LOGIN_LINK = "a[data-testid='login-link']"  # Corrected to 'a' tag
    VERIFICATION_CODE_INPUT = "input[name='otp']"
    VERIFICATION_SUBMIT_BUTTON = "button[data-testid='continue-btn']"
    VERIFICATION_HEADER = "text='Check your email inbox'"
    USER_PROFILE_ICON = "div[data-testid='profile']"


class AntiDetectionService:
    """
    Manages the browser connection and anti-detection measures.
    Now uses CDP to connect to a real user browser.
    """
    def __init__(self, playwright: Playwright):
        self._playwright = playwright
        self._stealth_path = '/Users/<USER>/Desktop/workflow/Account Registrar/infrastructure/anti_detection/stealth/stealth.min.js'

    async def connect_to_browser(self, cdp_url: str) -> Browser:
        """Connects to a running browser instance via CDP."""
        logging.info(f"Attempting to connect to browser via CDP: {cdp_url}")
        try:
            browser = await self._playwright.chromium.connect_over_cdp(cdp_url)
            logging.info("Successfully connected to the browser.")
            return browser
        except Exception as e:
            logging.error(f"Failed to connect to CDP endpoint at {cdp_url}.")
            logging.error("Please ensure Chrome is running with the --remote-debugging-port flag.")
            raise ConnectionError(f"Could not connect to browser at {cdp_url}: {e}")

    async def new_page(self, context: BrowserContext) -> Page:
        """Creates a new, stealth-injected page in the given browser context."""
        logging.info("Creating a new anti-detection page in the existing context...")
        page = await context.new_page()
        if os.path.exists(self._stealth_path):
            await page.add_init_script(path=self._stealth_path)
            logging.info("Stealth.min.js has been injected.")
        else:
            logging.warning(f"Stealth script not found at: {self._stealth_path}")
        return page


class HumanizedElementLocator:
    """Robust element locator and interaction tool simulating human behavior."""
    def __init__(self, page: Page):
        self._page = page

    async def type_text(self, selector: str, text: str, delay: int = 120):
        # Implementation remains the same
        logging.info(f"Typing into '{selector}'...")
        try:
            if selector == HuluSelectors.EMAIL_INPUT:
                locator = self._page.get_by_role('textbox', name='Email')
            elif selector == HuluSelectors.PASSWORD_INPUT:
                locator = self._page.get_by_role('textbox', name='Password')
            else:
                locator = self._page.locator(selector)
            await locator.wait_for(state="visible", timeout=30000)
            await locator.click(delay=50)
            await locator.fill("")
            await locator.type(text, delay=delay)
            logging.info("Text input complete.")
        except Exception as e:
            raise ElementNotFoundError(f"Failed to type into element '{selector}': {e}")


    async def click_element(self, selector: str, timeout: int = 15000) -> bool:
        # Implementation remains the same
        logging.info(f"Attempting to humanize-click element '{selector}'...")
        try:
            if selector == HuluSelectors.LOGIN_LINK:
                locator = self._page.get_by_role('link', name='Log In')
            elif selector == HuluSelectors.LOGIN_BUTTON:
                locator = self._page.get_by_role('button', name='Log In')
            else:
                locator = self._page.locator(selector)
            
            await locator.wait_for(state="visible", timeout=timeout)
            await locator.hover()
            await asyncio.sleep(0.2)
            await locator.click(delay=100)
            logging.info(f"Element '{selector}' clicked successfully.")
            return True
        except Exception as e:
            logging.warning(f"Humanized click failed for '{selector}': {e}")
            raise ElementNotFoundError(f"Failed to click element '{selector}': {e}")


# --- Core Bot Implementation with CDP Persistence ---

class HuluLoginBot:
    """
    Automates Hulu login using a CDP-connected browser for persistence.
    """
    LOGIN_URL = "https://www.hulu.com"
    TEMPMAIL_API_BASE = "https://testkuroneko.xyz/api"

    def __init__(self, email: str, password: str, cdp_endpoint: str = "http://localhost:9222", 
                 auto_launch: bool = False, debug_port: int = 9222):
        self.email = email
        self.password = password
        self.cdp_endpoint = cdp_endpoint
        self.auto_launch = auto_launch
        self.debug_port = debug_port
        self.playwright: Optional[Playwright] = None
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None
        self.page: Optional[Page] = None
        self.locator: Optional[HumanizedElementLocator] = None
        self.anti_detection_service: Optional[AntiDetectionService] = None
        self.chrome_process: Optional[subprocess.Popen] = None

    async def _setup_and_connect(self):
        """Initializes Playwright and connects to the CDP browser."""
        logging.info("Initializing Playwright and preparing to connect...")
        
        # 如果启用自动启动，则先启动Chrome
        if self.auto_launch:
            logging.info("🚀 自动启动Chrome浏览器...")
            try:
                self.chrome_process = auto_launch_chrome(self.debug_port)
                # 等待Chrome启动完成
                await asyncio.sleep(3)
                logging.info("✅ Chrome启动完成，等待CDP连接...")
            except Exception as e:
                logging.error(f"❌ 自动启动Chrome失败: {e}")
                raise
        
        self.playwright = await async_playwright().start()
        self.anti_detection_service = AntiDetectionService(self.playwright)
        
        # 重试连接逻辑
        max_retries = 3
        for attempt in range(max_retries):
            try:
                self.browser = await self.anti_detection_service.connect_to_browser(self.cdp_endpoint)
                break
            except Exception as e:
                if attempt == max_retries - 1:
                    logging.error(f"❌ 连接CDP失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                    if self.auto_launch:
                        self._print_launch_instructions()
                    raise
                else:
                    logging.warning(f"⚠️ 连接CDP失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                    await asyncio.sleep(2)
        
        # The default context is the user's main browser profile, providing persistence
        self.context = self.browser.contexts[0]
        self.page = await self.anti_detection_service.new_page(self.context)
        self.locator = HumanizedElementLocator(self.page)
    
    def _print_launch_instructions(self):
        """打印当前系统的Chrome启动指令"""
        system = platform.system()
        logging.info("=" * 60)
        logging.info("🚀 Chrome启动失败，请手动启动Chrome浏览器")
        logging.info("=" * 60)
        
        if system == "Darwin":  # macOS
            cmd = f'open -a "Google Chrome" --args --remote-debugging-port={self.debug_port} --user-data-dir="/tmp/chrome-profile-hulu"'
            logging.info(f"macOS 启动命令:")
            logging.info(f"{cmd}")
        elif system == "Linux":
            cmd = f'google-chrome --remote-debugging-port={self.debug_port} --user-data-dir="/tmp/chrome-profile-hulu"'
            logging.info(f"Linux 启动命令:")
            logging.info(f"{cmd}")
        elif system == "Windows":
            cmd = f'"C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe" --remote-debugging-port={self.debug_port} --user-data-dir="C:\\temp\\chrome-profile-hulu"'
            logging.info(f"Windows 启动命令:")
            logging.info(f"{cmd}")
        
        logging.info("=" * 60)
        logging.info("请在新终端中执行上述命令，然后重新运行此脚本")
        logging.info("=" * 60)

    async def _click_login_link(self):
        """Clicks the login link on the homepage."""
        logging.info("Clicking the 'Log In' link...")
        await self.locator.click_element(HuluSelectors.LOGIN_LINK)
        await self.page.wait_for_load_state('networkidle')

    async def _fill_email(self):
        await self.locator.type_text(HuluSelectors.EMAIL_INPUT, self.email)

    async def _debug_page_state(self, step: str):
        """调试页面状态 - 截图和HTML分析"""
        try:
            # 截图
            screenshot_path = f"debug_{step}_{int(time.time())}.png"
            await self.page.screenshot(path=screenshot_path)
            logging.info(f"📸 {step} - 页面截图保存到: {screenshot_path}")
            
            # 获取页面标题和URL
            title = await self.page.title()
            url = self.page.url
            logging.info(f"📄 {step} - 页面标题: {title}")
            logging.info(f"🔗 {step} - 页面URL: {url}")
            
            # 检查关键元素
            await self._check_page_elements(step)
            
        except Exception as e:
            logging.warning(f"⚠️ {step} - 页面状态调试失败: {e}")

    async def _check_page_elements(self, step: str):
        """检查页面关键元素"""
        # 检查密码输入框
        password_selectors = [
            HuluSelectors.PASSWORD_INPUT,
            "input[type='password']",
            "input[name='password']",
            "input[aria-label*='password' i]",
            "input[placeholder*='password' i]"
        ]
        
        password_found = False
        for selector in password_selectors:
            try:
                element = await self.page.query_selector(selector)
                if element:
                    is_visible = await element.is_visible()
                    logging.info(f"🔍 {step} - 密码框 '{selector}': {'可见' if is_visible else '不可见'}")
                    if is_visible:
                        password_found = True
                        break
            except:
                continue
        
        if not password_found:
            logging.info(f"❌ {step} - 未找到可见的密码输入框")
        
        # 检查Continue按钮
        continue_selectors = [
            HuluSelectors.CONTINUE_BUTTON,
            "button[data-testid='continue-btn']",
            "button[type='submit']",
            "button:has-text('Continue')",
            "button:has-text('Next')",
            "input[type='submit']"
        ]
        
        continue_found = False
        for selector in continue_selectors:
            try:
                element = await self.page.query_selector(selector)
                if element:
                    is_visible = await element.is_visible()
                    text = await element.text_content() or ""
                    logging.info(f"🔍 {step} - Continue按钮 '{selector}': {'可见' if is_visible else '不可见'} - 文本: '{text.strip()}'")
                    if is_visible:
                        continue_found = True
                        break
            except:
                continue
        
        if not continue_found:
            logging.info(f"❌ {step} - 未找到可见的Continue按钮")

    async def _handle_continue_button(self):
        """处理Continue按钮点击"""
        continue_selectors = [
            "button[data-testid='continue-btn']",
            "button:has-text('Continue')",
            "button[type='submit']"
        ]
        
        for selector in continue_selectors:
            try:
                element = await self.page.query_selector(selector)
                if element and await element.is_visible():
                    logging.info(f"🔗 找到Continue按钮，正在点击: {selector}")
                    await element.click()
                    await asyncio.sleep(2)  # 等待页面响应
                    logging.info("✅ Continue按钮点击完成")
                    return True
            except Exception as e:
                logging.warning(f"⚠️ 点击Continue按钮失败 {selector}: {e}")
                continue
        
        logging.warning("❌ 未找到可点击的Continue按钮")
        return False

    async def _wait_for_password_field(self):
        """等待密码输入框出现"""
        password_selectors = [
            HuluSelectors.PASSWORD_INPUT,
            "input[type='password']",
            "input[name='password']",
            "input[aria-label*='password' i]"
        ]
        
        logging.info("⏳ 等待密码输入框出现...")
        
        # 等待最多30秒
        for attempt in range(30):
            for selector in password_selectors:
                try:
                    element = await self.page.query_selector(selector)
                    if element and await element.is_visible():
                        logging.info(f"✅ 密码输入框已出现: {selector}")
                        return True
                except:
                    continue
            
            await asyncio.sleep(1)
            
            # 每5秒检查一次页面状态
            if attempt % 5 == 4:
                logging.info(f"⏳ 等待密码框中... ({attempt + 1}/30秒)")
                await self._debug_page_state(f"等待密码框第{attempt + 1}秒")
        
        logging.error("❌ 密码输入框未在30秒内出现")
        raise LoginError("密码输入框未出现，可能需要检查邮箱有效性或页面流程")

    async def _fill_password(self):
        await self.locator.type_text(HuluSelectors.PASSWORD_INPUT, self.password)

    async def _click_login_button(self):
        await self.locator.click_element(HuluSelectors.LOGIN_BUTTON)

    async def _handle_verification_code(self) -> bool:
        # This implementation can be reused as is.
        # For brevity, it's assumed to be here.
        logging.info("Checking if email verification is needed...")
        # ... full implementation ...
        return True # Placeholder

    async def _verify_login_success(self):
        """Verifies that the login was ultimately successful."""
        logging.info("Verifying final login success...")
        try:
            await self.page.wait_for_selector(HuluSelectors.USER_PROFILE_ICON, state="visible", timeout=30000)
            logging.info("User profile icon detected. Login confirmed!")
            print("\n✅ Hulu login successful!")
        except Exception:
            logging.error("Could not find user profile icon on the final page.")
            await self.page.screenshot(path="cdp_login_failed.png")
            raise LoginError("Login failed: Did not land on user homepage.")

    async def login(self):
        """
        Executes the login flow with CDP for persistence.
        """
        try:
            await self._setup_and_connect()

            # 1. Navigate to Hulu and check if already logged in
            logging.info(f"Navigating to {self.LOGIN_URL} to check login status...")
            await self.page.goto(self.LOGIN_URL, wait_until="domcontentloaded", timeout=60000)

            try:
                await self.page.wait_for_selector(HuluSelectors.USER_PROFILE_ICON, state="visible", timeout=15000)
                logging.info("✅ Session is already active. No login required.")
                print("\n✅ Success: Logged in via existing browser session.")
                await self.page.screenshot(path="cdp_login_success.png")
                return  # Exit gracefully
            except Exception:
                logging.info("Not logged in. Proceeding with full login flow.")

            # 2. If not logged in, execute the full login process
            await self._click_login_link()
            await self._fill_email()
            
            # 添加调试并处理Continue按钮
            await self._debug_page_state("填写邮箱后")
            await self._handle_continue_button()
            
            # 等待密码框出现，而不是导航
            await self._wait_for_password_field()
            await self._fill_password()
            await self._click_login_button()

            verification_handled = await self._handle_verification_code()

            await self._verify_login_success()
            logging.info("Login successful. Session is now persisted in the browser.")

        except ConnectionError as e:
            print(f"\n❌ CONNECTION FAILED: {e}")
        except Exception as e:
            logging.error(f"A critical error occurred during the login process: {e}")
            print(f"\n❌ LOGIN PROCESS FAILED: {e}")
            if self.page:
                await self.page.screenshot(path="cdp_error_screenshot.png")
                logging.info("Error screenshot saved to cdp_error_screenshot.png")
        finally:
            await self._cleanup()

    async def _cleanup(self):
        """Disconnects from Playwright, optionally closing the browser."""
        if self.playwright:
            await self.playwright.stop()
        
        # 如果是自动启动的Chrome，则关闭它
        if self.auto_launch and self.chrome_process:
            try:
                logging.info("🧹 关闭自动启动的Chrome浏览器...")
                if platform.system() == "Windows":
                    # Windows下使用taskkill
                    subprocess.run(["taskkill", "/F", "/T", "/PID", str(self.chrome_process.pid)], 
                                 capture_output=True)
                else:
                    # Unix系统下终止进程组
                    os.killpg(os.getpgid(self.chrome_process.pid), 15)  # SIGTERM
                    await asyncio.sleep(1)
                    # 如果还在运行，强制终止
                    if self.chrome_process.poll() is None:
                        os.killpg(os.getpgid(self.chrome_process.pid), 9)  # SIGKILL
                
                logging.info("✅ Chrome浏览器已关闭")
            except Exception as e:
                logging.warning(f"⚠️ 关闭Chrome浏览器时出错: {e}")
        else:
            logging.info("Playwright connection closed. The browser remains open.")


async def main():
    """Main execution function."""
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

    # Configuration
    CDP_ENDPOINT = "http://localhost:9222"
    DEBUG_PORT = 9222
    AUTO_LAUNCH_CHROME = True  # 设置为True自动启动Chrome，False需要手动启动
    
    # Credentials
    test_email = "<EMAIL>"
    test_password = "Fiona127,."

    print("--- Hulu Login Bot (CDP Mode) ---")
    print(f"🌍 当前系统: {platform.system()}")
    print(f"🔗 CDP端点: {CDP_ENDPOINT}")
    
    if AUTO_LAUNCH_CHROME:
        print("🚀 自动启动模式: 启用")
        print("Chrome将自动启动，无需手动操作")
    else:
        print("🚀 自动启动模式: 禁用")
        print("请确保已手动启动Chrome并启用远程调试端口")
        print()
        print("手动启动命令:")
        system = platform.system()
        if system == "Darwin":  # macOS
            cmd = f'open -a "Google Chrome" --args --remote-debugging-port={DEBUG_PORT} --user-data-dir="/tmp/chrome-profile-hulu"'
            print(f"macOS: {cmd}")
        elif system == "Linux":
            cmd = f'google-chrome --remote-debugging-port={DEBUG_PORT} --user-data-dir="/tmp/chrome-profile-hulu"'
            print(f"Linux: {cmd}")
        elif system == "Windows":
            cmd = f'"C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe" --remote-debugging-port={DEBUG_PORT} --user-data-dir="C:\\temp\\chrome-profile-hulu"'
            print(f"Windows: {cmd}")
    
    print("\n开始登录流程...")
    
    bot = HuluLoginBot(
        email=test_email, 
        password=test_password, 
        cdp_endpoint=CDP_ENDPOINT,
        auto_launch=AUTO_LAUNCH_CHROME,
        debug_port=DEBUG_PORT
    )
    await bot.login()


if __name__ == "__main__":
    asyncio.run(main())