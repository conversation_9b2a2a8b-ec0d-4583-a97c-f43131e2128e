#!/usr/bin/env python3
"""
Hulu反检测快速开始示例
这个示例展示了如何快速将反检测功能集成到你的Hulu自动化项目中
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from playwright.async_api import async_playwright
from infrastructure.anti_detection.service import AntiDetectionService
from infrastructure.anti_detection.config import AntiDetectionPresets

class QuickStartHuluBot:
    """快速开始的Hulu机器人示例"""
    
    def __init__(self, headless: bool = False):
        """
        初始化机器人
        
        Args:
            headless: 是否无头模式（建议False以获得更好的反检测效果）
        """
        self.headless = headless
        
        # 使用最大隐身配置
        self.anti_detection_service = AntiDetectionService(
            AntiDetectionPresets.maximum_stealth()
        )
        
        print("🛡️ Hulu反检测机器人已初始化")
    
    async def login_to_hulu(self, email: str, password: str):
        """
        登录到Hulu账户
        
        Args:
            email: 登录邮箱
            password: 登录密码
        """
        print(f"🔐 开始登录Hulu账户: {email}")
        
        async with async_playwright() as playwright:
            try:
                # 创建隐身浏览器上下文
                context = await self.anti_detection_service.create_stealth_browser_context(
                    playwright=playwright,
                    headless=self.headless
                )
                
                page = await context.new_page()
                
                # 应用反检测增强
                await self.anti_detection_service.enhance_page(page)
                
                # 访问Hulu登录页面
                print("📱 访问Hulu网站...")
                await page.goto("https://www.hulu.com/welcome", wait_until="networkidle")
                
                # 点击登录按钮
                print("🖱️ 点击登录按钮...")
                await page.click('text="LOG IN"')
                await page.wait_for_timeout(2000)
                
                # 使用人类化行为输入邮箱
                print("⌨️ 输入邮箱地址...")
                await self.anti_detection_service.simulate_human_typing(
                    page, 'input[type="email"]', email, simulate_errors=True
                )
                
                await page.wait_for_timeout(1000)
                
                # 输入密码
                print("🔑 输入密码...")
                await self.anti_detection_service.simulate_human_typing(
                    page, 'input[type="password"]', password, simulate_errors=False
                )
                
                await page.wait_for_timeout(1000)
                
                # 提交登录表单
                print("📤 提交登录表单...")
                await page.click('button[type="submit"]')
                
                # 等待登录结果
                print("⏳ 等待登录结果...")
                await page.wait_for_timeout(5000)
                
                # 检查登录状态
                current_url = page.url
                if "hub" in current_url or "profiles" in current_url:
                    print("✅ 登录成功！已跳转到主页面")
                    result = {"status": "success", "message": "登录成功", "url": current_url}
                elif "verify" in current_url or "code" in current_url:
                    print("📧 需要邮箱验证")
                    result = {"status": "verification_needed", "message": "需要邮箱验证", "url": current_url}
                else:
                    print("⚠️ 登录状态未知，请检查")
                    result = {"status": "unknown", "message": "登录状态未知", "url": current_url}
                
                # 截图保存结果
                screenshot_path = f"login_result_{int(asyncio.get_event_loop().time())}.png"
                await page.screenshot(path=screenshot_path)
                print(f"📸 登录结果截图已保存: {screenshot_path}")
                
                return result
                
            except Exception as e:
                print(f"❌ 登录过程中出现错误: {e}")
                return {"status": "error", "message": str(e)}
            
            finally:
                await context.close()
                await self.anti_detection_service.cleanup()
    
    async def test_anti_detection(self):
        """
        测试反检测效果
        """
        print("🧪 开始测试反检测效果...")
        
        async with async_playwright() as playwright:
            try:
                context = await self.anti_detection_service.create_stealth_browser_context(
                    playwright=playwright,
                    headless=self.headless
                )
                
                page = await context.new_page()
                await self.anti_detection_service.enhance_page(page)
                
                # 测试机器人检测网站
                print("🔍 访问机器人检测网站...")
                await page.goto("https://bot.sannysoft.com/", wait_until="networkidle")
                await page.wait_for_timeout(3000)
                
                # 检查检测结果
                webdriver_detected = await page.evaluate("() => navigator.webdriver")
                chrome_present = await page.evaluate("() => !!window.chrome")
                
                if not webdriver_detected and chrome_present:
                    print("✅ 反检测测试通过！")
                    result = {"status": "passed", "webdriver_detected": False, "chrome_present": True}
                else:
                    print("⚠️ 反检测测试未完全通过")
                    result = {"status": "partial", "webdriver_detected": webdriver_detected, "chrome_present": chrome_present}
                
                # 截图保存测试结果
                await page.screenshot(path="anti_detection_test.png")
                print("📸 反检测测试截图已保存: anti_detection_test.png")
                
                return result
                
            except Exception as e:
                print(f"❌ 反检测测试失败: {e}")
                return {"status": "error", "message": str(e)}
            
            finally:
                await context.close()
                await self.anti_detection_service.cleanup()

async def main():
    """主函数 - 演示如何使用"""
    print("🚀 Hulu反检测快速开始示例")
    print("=" * 50)
    
    # 创建机器人实例
    bot = QuickStartHuluBot(headless=False)  # 设置为False以便观察过程
    
    # 选择要执行的操作
    print("\n请选择要执行的操作:")
    print("1. 测试反检测效果")
    print("2. 登录Hulu账户")
    print("3. 两者都执行")
    
    choice = input("\n请输入选择 (1/2/3): ").strip()
    
    if choice in ["1", "3"]:
        print("\n" + "="*30)
        print("🧪 执行反检测测试")
        print("="*30)
        
        test_result = await bot.test_anti_detection()
        print(f"\n测试结果: {test_result}")
    
    if choice in ["2", "3"]:
        print("\n" + "="*30)
        print("🔐 执行Hulu登录")
        print("="*30)
        
        # 获取登录凭据
        email = input("请输入邮箱地址: ").strip()
        if not email:
            email = "<EMAIL>"  # 默认测试邮箱
            print(f"使用默认邮箱: {email}")
        
        password = input("请输入密码: ").strip()
        if not password:
            password = "Fiona127,."  # 默认测试密码
            print("使用默认密码")
        
        login_result = await bot.login_to_hulu(email, password)
        print(f"\n登录结果: {login_result}")
    
    print("\n🎉 示例执行完成！")
    print("\n💡 提示:")
    print("- 查看生成的截图文件了解详细结果")
    print("- 如果登录需要邮箱验证，请手动完成验证步骤")
    print("- 可以调整配置参数以获得更好的效果")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n👋 程序被用户中断")
    except Exception as e:
        print(f"\n❌ 程序执行出错: {e}")
        import traceback
        traceback.print_exc()
