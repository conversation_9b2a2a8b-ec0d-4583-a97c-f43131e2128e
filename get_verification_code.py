#!/usr/bin/env python3
"""
简单的验证码获取工具

用法: python get_verification_code.py <EMAIL>
"""

import sys
import os
import requests
import json

def get_verification_code(email_address):
    """获取验证码"""
    
    # API配置
    api_base = os.getenv('API_BASE', 'https://testkuroneko.xyz/api')
    api_key = os.getenv('API_KEY')
    timeout = int(os.getenv('VERIFICATION_TIMEOUT', '300'))
    
    # 设置请求头
    headers = {"Content-Type": "application/json"}
    if api_key:
        headers["Authorization"] = f"Bearer {api_key}"
    
    # 请求数据
    request_data = {
        "email_address": email_address,
        "timeout_seconds": timeout,
        "poll_interval": 5
    }
    
    try:
        # 调用API
        response = requests.post(
            f"{api_base}/automation/wait-for-verification",
            json=request_data,
            headers=headers,
            timeout=timeout + 30,
            verify=False
        )
        
        response.raise_for_status()
        data = response.json()
        
        # 返回验证码
        if data.get("success") and data.get("data", {}).get("verification_codes"):
            return data["data"]["verification_codes"][0]
        else:
            return None
            
    except Exception:
        return None

def main():
    """主函数"""
    if len(sys.argv) != 2:
        print("用法: python get_verification_code.py <EMAIL>")
        sys.exit(1)
    
    email = sys.argv[1]
    code = get_verification_code(email)
    
    if code:
        print(code)
    else:
        print("ERROR: 未能获取验证码")
        sys.exit(1)

if __name__ == "__main__":
    main()
